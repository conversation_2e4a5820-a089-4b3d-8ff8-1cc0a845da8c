package vip.xiaonuo.telegrambot;

import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.telegram.telegrambots.bots.DefaultBotOptions;

import javax.annotation.Resource;

public class TelegramBotOptions extends DefaultBotOptions implements InitializingBean {
    private Logger logger = LoggerFactory.getLogger(TelegramBotOptions.class);
    @Resource
    private TelegramBotConfig telegramBotConfig;

    @Override
    public void afterPropertiesSet() throws Exception {
        if (StrUtil.isNotEmpty(telegramBotConfig.getProxyHost())) {
            setProxyType(ProxyType.HTTP);
            setProxyHost(telegramBotConfig.getProxyHost());
            setProxyPort(telegramBotConfig.getProxyPort());
            logger.info("【TelegramBotOptions】设置代理：{}:{}", telegramBotConfig.getProxyHost(), telegramBotConfig.getProxyPort());
        }
    }
}
