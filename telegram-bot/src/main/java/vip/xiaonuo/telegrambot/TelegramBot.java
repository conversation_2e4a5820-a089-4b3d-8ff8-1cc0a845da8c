package vip.xiaonuo.telegrambot;

import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.telegram.telegrambots.bots.DefaultBotOptions;
import org.telegram.telegrambots.bots.TelegramLongPollingBot;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.objects.Update;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;

import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class TelegramBot extends TelegramLongPollingBot implements InitializingBean {
    private Logger logger = LoggerFactory.getLogger(TelegramBot.class);
    @Resource
    private TelegramBotConfig telegramBotConfig;
    private ExecutorService executorService;

    public TelegramBot(DefaultBotOptions options) {
        super(options);
    }

    @Override
    public String getBotUsername() {
        return telegramBotConfig.getBotUsername();
    }

    @Override
    public String getBotToken() {
        return telegramBotConfig.getBotToken();
    }

    @Override
    public void onUpdateReceived(Update update) {
//        System.out.println("TestTelegramBot>>>>" + update);
        if (update.hasMyChatMember()) {
            logger.info("【TelegramBot】被邀请进入新群：" + update.getMyChatMember());
        }
//        if (update.hasMessage() && update.getMessage().hasText()) {
//            SendMessage message = new SendMessage(); // Create a SendMessage object with mandatory fields
//            message.setChatId(update.getMessage().getChatId().toString());
//            message.setChatId("-863975049");
//            message.setText(update.getMessage().getText());
//            try {
//                execute(message); // Call method to send the message
//            } catch (TelegramApiException e) {
//                e.printStackTrace();
//            }
//        }
    }

    public void sendBusMessage(String text) {
        sendMessage(TelegramSendChatTypeEnum.BUS, text);
    }

    public void sendDevMessage(String text) {
        sendMessage(TelegramSendChatTypeEnum.DEV, text);
    }

    public void sendMessage(TelegramSendChatTypeEnum sendChatType, String text) {
        if (StrUtil.isEmpty(getBotToken())) {
            return;
        }
        SendMessage message = new SendMessage(); // Create a SendMessage object with mandatory fields
        if (sendChatType == TelegramSendChatTypeEnum.BUS) {
            message.setChatId(telegramBotConfig.getBusChatId());
        }
        if (sendChatType == TelegramSendChatTypeEnum.DEV) {
            message.setChatId(telegramBotConfig.getDevChatId());
        }
        String msgPrefix = StrUtil.isNotEmpty(telegramBotConfig.getMsgPrefix()) ? ("<strong><u>" + telegramBotConfig.getMsgPrefix() + "</u></strong>") : "";
        message.setText(msgPrefix + text);
        message.setParseMode("html");
        if (StrUtil.isNotEmpty(message.getChatId())) {
            executorService.execute(() -> {
                try {
                    execute(message); // Call method to send the message
                } catch (TelegramApiException e) {
                    logger.error("【TelegramBot】发送消息异常", e);
                }
            });
        }
    }

    public TelegramBotConfig getTelegramBotConfig() {
        return telegramBotConfig;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        executorService = Executors.newFixedThreadPool(2);
    }
}
