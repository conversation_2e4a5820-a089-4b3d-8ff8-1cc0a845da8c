CREATE TABLE `tt_rich_text` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `lang` varchar(255) NOT NULL COMMENT '语言',
  `type` tinyint(4) NOT NULL COMMENT '类型(1:注册协议)',
  `content` longtext COMMENT '内容',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='富文本信息';

ALTER TABLE tt_spot_rule ADD `symbol` varchar(32) DEFAULT null COMMENT '交易类型表示,如ETH_USDT' AFTER id;
