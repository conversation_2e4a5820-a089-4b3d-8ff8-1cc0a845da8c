/*
 Navicat Premium Data Transfer

 Source Server         : oracle本地数据库
 Source Server Type    : Oracle
 Source Server Version : 110200
 Source Host           : localhost:1521
 Source Schema         : SNOWY-LAYUI-PUB-ORACLE

 Target Server Type    : Oracle
 Target Server Version : 110200
 File Encoding         : 65001

 Date: 15/04/2021 14:53:53
*/


-- ----------------------------
-- Table structure for SYS_APP
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP" (
  "ID" NUMBER(20,0) NOT NULL,
  "NAME" NVARCHAR2(100) NOT NULL,
  "CODE" NVARCHAR2(50) NOT NULL,
  "ACTIVE" NVARCHAR2(1),
  "STATUS" NUMBER(4,0) NOT NULL,
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP"."ID" IS '主键id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP"."NAME" IS '应用名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP"."CODE" IS '编码';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP"."ACTIVE" IS '是否默认激活（Y-是，N-否）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP"."STATUS" IS '状态（字典 0正常 1停用 2删除）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP"."UPDATE_USER" IS '修改人';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP" IS '系统应用表';

-- ----------------------------
-- Records of SYS_APP
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP" VALUES ('1265476890672672821', '系统应用', 'system', 'Y', '0', TO_DATE('2020-03-25 19:07:00', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-11-24 17:45:28', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP" VALUES ('1265476890672672822', '业务应用', 'business', 'Y', '2', TO_DATE('2020-03-26 08:40:33', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-11-24 11:13:47', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP" VALUES ('1338863594871304193', '系统工具', 'systool', 'N', '0', TO_DATE('2020-12-15 23:08:51', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);

-- ----------------------------
-- Table structure for SYS_AREA
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" (
  "ID" NUMBER(20,0) NOT NULL,
  "LEVEL_CODE" NUMBER(4,0),
  "PARENT_CODE" NVARCHAR2(20),
  "AREA_CODE" NVARCHAR2(20),
  "ZIP_CODE" NVARCHAR2(6),
  "CITY_CODE" NVARCHAR2(6),
  "NAME" NVARCHAR2(50),
  "SHORT_NAME" NVARCHAR2(50),
  "MERGER_NAME" NVARCHAR2(50),
  "PINYIN" NVARCHAR2(30),
  "LNG" NUMBER,
  "LAT" NUMBER
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA"."LEVEL_CODE" IS '层级';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA"."PARENT_CODE" IS '父级行政代码';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA"."AREA_CODE" IS '行政代码';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA"."ZIP_CODE" IS '邮政编码';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA"."CITY_CODE" IS '区号';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA"."NAME" IS '名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA"."SHORT_NAME" IS '简称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA"."MERGER_NAME" IS '组合名';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA"."PINYIN" IS '拼音';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA"."LNG" IS '经度';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA"."LAT" IS '纬度';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" IS '中国行政地区表';

-- ----------------------------
-- Records of SYS_AREA
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_CODE_GENERATE
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE" (
  "ID" NUMBER(20,0) NOT NULL,
  "AUTHOR_NAME" NVARCHAR2(255) NOT NULL,
  "CLASS_NAME" NVARCHAR2(255) NOT NULL,
  "TABLE_PREFIX" NVARCHAR2(255) NOT NULL,
  "GENERATE_TYPE" NVARCHAR2(255) NOT NULL,
  "TABLE_NAME" NVARCHAR2(255) NOT NULL,
  "PACKAGE_NAME" NVARCHAR2(255),
  "BUS_NAME" NVARCHAR2(255),
  "TABLE_COMMENT" NVARCHAR2(255),
  "CREATE_USER" NUMBER(20,0),
  "CREATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE"."AUTHOR_NAME" IS '作者姓名';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE"."CLASS_NAME" IS '类名';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE"."TABLE_PREFIX" IS '是否移除表前缀';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE"."GENERATE_TYPE" IS '生成位置类型';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE"."TABLE_NAME" IS '数据库表名';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE"."PACKAGE_NAME" IS '包名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE"."BUS_NAME" IS '业务名';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE"."TABLE_COMMENT" IS '功能名';
COMMENT ON COLUMN "SNOWY-PUB-ORACLE"."SYS_CODE_GENERATE"."APP_CODE" IS '所属应用';
COMMENT ON COLUMN "SNOWY-PUB-ORACLE"."SYS_CODE_GENERATE"."MENU_PID" IS '菜单上级';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE"."UPDATE_TIME" IS '更新时间';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE" IS '代码生成基础配置';

-- ----------------------------
-- Records of SYS_CODE_GENERATE
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_CODE_GENERATE_CONFIG
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG" (
  "ID" NUMBER(20,0) NOT NULL,
  "CODE_GEN_ID" NUMBER(20,0),
  "COLUMN_NAME" NVARCHAR2(255),
  "JAVA_NAME" NVARCHAR2(255),
  "DATA_TYPE" NVARCHAR2(255),
  "COLUMN_COMMENT" NVARCHAR2(255),
  "JAVA_TYPE" NVARCHAR2(255),
  "EFFECT_TYPE" NVARCHAR2(255),
  "DICT_TYPE_CODE" NVARCHAR2(255),
  "WHETHER_TABLE" NVARCHAR2(255),
  "WHETHER_ADD_UPDATE" NVARCHAR2(255),
  "WHETHER_RETRACT" NVARCHAR2(255),
  "WHETHER_REQUIRED" NVARCHAR2(255),
  "QUERY_WHETHER" NVARCHAR2(255),
  "QUERY_TYPE" NVARCHAR2(255),
  "COLUMN_KEY" NVARCHAR2(255),
  "COLUMN_KEY_NAME" NVARCHAR2(255),
  "WHETHER_COMMON" NVARCHAR2(255),
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."CODE_GEN_ID" IS '代码生成主表ID';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."COLUMN_NAME" IS '数据库字段名';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."JAVA_NAME" IS 'java类字段名';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."DATA_TYPE" IS '物理类型';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."COLUMN_COMMENT" IS '字段描述';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."JAVA_TYPE" IS 'java类型';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."EFFECT_TYPE" IS '作用类型（字典）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."DICT_TYPE_CODE" IS '字典code';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."WHETHER_TABLE" IS '列表展示';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."WHETHER_ADD_UPDATE" IS '增改';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."WHETHER_RETRACT" IS '列表是否缩进（字典）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."WHETHER_REQUIRED" IS '是否必填（字典）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."QUERY_WHETHER" IS '是否是查询条件';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."QUERY_TYPE" IS '查询方式';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."COLUMN_KEY" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."COLUMN_KEY_NAME" IS '主外键名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."WHETHER_COMMON" IS '是否是通用字段';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG"."UPDATE_USER" IS '修改人';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG" IS '代码生成详细配置';

-- ----------------------------
-- Records of SYS_CODE_GENERATE_CONFIG
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_CONFIG
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" (
  "ID" NUMBER(20,0) NOT NULL,
  "NAME" NVARCHAR2(100) NOT NULL,
  "CODE" NVARCHAR2(50) NOT NULL,
  "VALUE" NVARCHAR2(255) NOT NULL,
  "SYS_FLAG" NCHAR(1) NOT NULL,
  "REMARK" NVARCHAR2(255),
  "STATUS" NUMBER(4,0) NOT NULL,
  "GROUP_CODE" NVARCHAR2(255) NOT NULL,
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG"."NAME" IS '名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG"."CODE" IS '编码';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG"."VALUE" IS '值';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG"."SYS_FLAG" IS '是否是系统参数（Y-是，N-否）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG"."REMARK" IS '备注';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG"."STATUS" IS '状态（字典 0正常 1停用 2删除）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG"."GROUP_CODE" IS '常量所属分类的编码，来自于“常量的分类”字典';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG"."UPDATE_USER" IS '更新人';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" IS '系统参数配置表';

-- ----------------------------
-- Records of SYS_CONFIG
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1265117443880853506', 'jwt密钥', 'SNOWY_JWT_SECRET', 'snowy', 'Y', '（重要）jwt密钥，默认为空，自行设置', '0', 'DEFAULT', TO_DATE('2020-05-26 06:35:19', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1265117443880853507', '默认密码', 'SNOWY_DEFAULT_PASSWORD', '123456', 'Y', '默认密码', '0', 'DEFAULT', TO_DATE('2020-05-26 06:37:56', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1265117443880853508', 'token过期时间', 'SNOWY_TOKEN_EXPIRE', '86400', 'Y', 'token过期时间（单位：秒）', '0', 'DEFAULT', TO_DATE('2020-05-27 11:54:49', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1265117443880853509', 'session会话过期时间', 'SNOWY_SESSION_EXPIRE', '7200', 'Y', 'session会话过期时间（单位：秒）', '0', 'DEFAULT', TO_DATE('2020-05-27 11:54:49', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1265117443880853519', '阿里云短信keyId', 'SNOWY_ALIYUN_SMS_ACCESSKEY_ID', '你的keyId', 'Y', '阿里云短信keyId', '0', 'ALIYUN_SMS', TO_DATE('2020-06-07 16:27:11', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1269547042242371585', '阿里云短信secret', 'SNOWY_ALIYUN_SMS_ACCESSKEY_SECRET', '你的secret', 'Y', '阿里云短信secret', '0', 'ALIYUN_SMS', TO_DATE('2020-06-07 16:29:37', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1269547130041737217', '阿里云短信签名', 'SNOWY_ALIYUN_SMS_SIGN_NAME', 'Snowy快速开发平台', 'Y', '阿里云短信签名', '0', 'ALIYUN_SMS', TO_DATE('2020-06-07 16:29:58', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1269547279530926081', '阿里云短信-登录模板号', 'SNOWY_ALIYUN_SMS_LOGIN_TEMPLATE_CODE', 'SMS_1877123456', 'Y', '阿里云短信-登录模板号', '0', 'ALIYUN_SMS', TO_DATE('2020-06-07 16:30:33', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1269547410879750145', '阿里云短信默认失效时间', 'SNOWY_ALIYUN_SMS_INVALIDATE_MINUTES', '5', 'Y', '阿里云短信默认失效时间（单位：分钟）', '0', 'ALIYUN_SMS', TO_DATE('2020-06-07 16:31:04', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1269575927357071361', '腾讯云短信secretId', 'SNOWY_TENCENT_SMS_SECRET_ID', '你的secretId', 'Y', '腾讯云短信secretId', '0', 'TENCENT_SMS', TO_DATE('2020-06-07 18:24:23', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1269575991693500418', '腾讯云短信secretKey', 'SNOWY_TENCENT_SMS_SECRET_KEY', '你的secretkey', 'Y', '腾讯云短信secretKey', '0', 'TENCENT_SMS', TO_DATE('2020-06-07 18:24:39', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1269576044084551682', '腾讯云短信sdkAppId', 'SNOWY_TENCENT_SMS_SDK_APP_ID', '1400375123', 'Y', '腾讯云短信sdkAppId', '0', 'TENCENT_SMS', TO_DATE('2020-06-07 18:24:51', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1269576089294954497', '腾讯云短信签名', 'SNOWY_TENCENT_SMS_SIGN', 'Snowy快速开发平台', 'Y', '腾讯云短信签名', '0', 'TENCENT_SMS', TO_DATE('2020-06-07 18:25:02', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1270378172860403713', '邮箱host', 'SNOWY_EMAIL_HOST', 'smtp.126.com', 'Y', '邮箱host', '0', 'EMAIL', TO_DATE('2020-06-09 23:32:14', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1270378295543795714', '邮箱用户名', 'SNOWY_EMAIL_USERNAME', '<EMAIL>', 'Y', '邮箱用户名', '0', 'EMAIL', TO_DATE('2020-06-09 23:32:43', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1270378340510928897', '邮箱密码', 'SNOWY_EMAIL_PASSWORD', '你的邮箱密码', 'Y', '邮箱密码', '0', 'EMAIL', TO_DATE('2020-06-09 23:32:54', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1270378527358783489', '邮箱端口', 'SNOWY_EMAIL_PORT', '465', 'Y', '邮箱端口', '0', 'EMAIL', TO_DATE('2020-06-09 23:33:38', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1270378790035460097', '邮箱是否开启ssl', 'SNOWY_EMAIL_SSL', 'true', 'Y', '邮箱是否开启ssl', '0', 'EMAIL', TO_DATE('2020-06-09 23:34:41', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1270380786649972737', '邮箱发件人', 'SNOWY_EMAIL_FROM', '<EMAIL>', 'Y', '邮箱发件人', '0', 'EMAIL', TO_DATE('2020-06-09 23:42:37', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1270380786649972738', 'win本地上传文件路径', 'SNOWY_FILE_UPLOAD_PATH_FOR_WINDOWS', 'd:/tmp', 'Y', 'win本地上传文件路径', '0', 'FILE_PATH', TO_DATE('2020-06-09 23:42:37', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1270380786649972739', 'linux/mac本地上传文件路径', 'SNOWY_FILE_UPLOAD_PATH_FOR_LINUX', '/tmp', 'Y', 'linux/mac本地上传文件路径', '0', 'FILE_PATH', TO_DATE('2020-06-09 23:42:37', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1270380786649982740', 'Snowy演示环境', 'SNOWY_DEMO_ENV_FLAG', 'false', 'Y', 'Snowy演示环境的开关，true-打开，false-关闭，如果演示环境开启，则只能读数据不能写数据', '0', 'DEFAULT', TO_DATE('2020-06-09 23:42:37', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-01-22 17:50:33', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1270380786649982741', 'Snowy放开XSS过滤的接口', 'SNOWY_UN_XSS_FILTER_URL', '/demo/xssfilter,/demo/unxss', 'Y', '多个url可以用英文逗号隔开', '0', 'DEFAULT', TO_DATE('2020-06-09 23:42:37', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1270380786649982742', '单用户登陆的开关', 'SNOWY_ENABLE_SINGLE_LOGIN', 'false', 'Y', '单用户登陆的开关，true-打开，false-关闭，如果一个人登录两次，就会将上一次登陆挤下去', '0', 'DEFAULT', TO_DATE('2020-06-09 23:42:37', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1280694281648070659', '阿里云定位api接口地址', 'SNOWY_IP_GEO_API', 'http://api01.aliyun.venuscn.com/ip?ip=%s', 'Y', '阿里云定位api接口地址', '0', 'DEFAULT', TO_DATE('2020-07-20 10:44:46', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1280694281648070660', '阿里云定位appCode', 'SNOWY_IP_GEO_APP_CODE', '461535aabeae4f34861884d392f5d452', 'Y', '阿里云定位appCode', '0', 'DEFAULT', TO_DATE('2020-07-20 10:44:46', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1288309751255412737', 'Oauth用户登录的开关', 'SNOWY_ENABLE_OAUTH_LOGIN', 'false', 'Y', 'Oauth用户登录的开关', '0', 'OAUTH', TO_DATE('2020-07-29 11:05:55', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1288310043346743297', 'Oauth码云登录ClientId', 'SNOWY_OAUTH_GITEE_CLIENT_ID', '你的clientId', 'Y', 'Oauth码云登录ClientId', '0', 'OAUTH', TO_DATE('2020-07-29 11:07:05', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1288310157876408321', 'Oauth码云登录ClientSecret', 'SNOWY_OAUTH_GITEE_CLIENT_SECRET', '你的clientSecret', 'Y', 'Oauth码云登录ClientSecret', '0', 'OAUTH', TO_DATE('2020-07-29 11:07:32', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1288310280056483841', 'Oauth码云登录回调地址', 'SNOWY_OAUTH_GITEE_REDIRECT_URI', 'http://127.0.0.1:81/oauth/callback/gitee', 'Y', 'Oauth码云登录回调地址', '0', 'OAUTH', TO_DATE('2020-07-29 11:08:01', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1288358228593221636', '是否开启验证码', 'SNOWY_CAPTCHA_OPEN', 'true', 'Y', '是否开启验证码', '0', 'DEFAULT', TO_DATE('2020-09-03 17:45:58', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-09-23 22:23:38', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" VALUES ('1375337991869853697', 'OnlyOffice服务地址', 'SNOWY_ONLY_OFFICE_SERVICE_URL', 'http://82.156.81.108:9001', 'Y', 'OnlyOffice服务地址，开发时请使用局域网内地址', '0', 'DEFAULT', TO_DATE('2021-03-26 14:45:04', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-03-26 14:51:44', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');

-- ----------------------------
-- Table structure for SYS_DICT_DATA
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" (
  "ID" NUMBER(20,0) NOT NULL,
  "TYPE_ID" NUMBER(20,0) NOT NULL,
  "VALUE" NCLOB NOT NULL,
  "CODE" NVARCHAR2(50) NOT NULL,
  "SORT" NUMBER(11,0) NOT NULL,
  "REMARK" NVARCHAR2(255),
  "STATUS" NUMBER(4,0) NOT NULL,
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA"."TYPE_ID" IS '字典类型id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA"."VALUE" IS '值';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA"."CODE" IS '编码';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA"."SORT" IS '排序';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA"."REMARK" IS '备注';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA"."STATUS" IS '状态（字典 0正常 1停用 2删除）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA"."UPDATE_USER" IS '更新人';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" IS '系统字典值表';

-- ----------------------------
-- Records of SYS_DICT_DATA
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265216536659087357', '1265216211667636234', '男', '1', '100', '男性', '0', TO_DATE('2020-04-01 10:23:29', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265216536659087358', '1265216211667636234', '女', '2', '100', '女性', '0', TO_DATE('2020-04-01 10:23:49', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265216536659087359', '1265216211667636234', '未知', '3', '100', '未知性别', '0', TO_DATE('2020-04-01 10:24:01', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265216536659087361', '1265216211667636235', '默认常量', 'DEFAULT', '100', '默认常量，都以SNOWY_开头的', '0', TO_DATE('2020-04-14 23:25:45', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265216536659087363', '1265216211667636235', '阿里云短信', 'ALIYUN_SMS', '100', '阿里云短信配置', '0', TO_DATE('2020-04-14 23:25:45', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265216536659087364', '1265216211667636235', '腾讯云短信', 'TENCENT_SMS', '100', '腾讯云短信', '0', TO_DATE('2020-04-14 23:25:45', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265216536659087365', '1265216211667636235', '邮件配置', 'EMAIL', '100', '邮箱配置', '0', TO_DATE('2020-04-14 23:25:45', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265216536659087366', '1265216211667636235', '文件上传路径', 'FILE_PATH', '100', '文件上传路径', '0', TO_DATE('2020-04-14 23:25:45', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265216536659087367', '1265216211667636235', 'Oauth配置', 'OAUTH', '100', 'Oauth配置', '0', TO_DATE('2020-04-14 23:25:45', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265216617500102656', '1265216211667636226', '正常', '0', '100', '正常', '0', TO_DATE('2020-05-26 17:41:44', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265216617500102657', '1265216211667636226', '停用', '1', '100', '停用', '0', TO_DATE('2020-05-26 17:42:03', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265216938389524482', '1265216211667636226', '删除', '2', '100', '删除', '0', TO_DATE('2020-05-26 17:43:19', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-12-02 14:14:06', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265217669028892673', '1265217074079453185', '否', 'N', '100', '否', '0', TO_DATE('2020-05-26 17:46:14', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265217706584690689', '1265217074079453185', '是', 'Y', '100', '是', '0', TO_DATE('2020-05-26 17:46:23', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265220776437731330', '1265217846770913282', '登录', '1', '100', '登录', '0', TO_DATE('2020-05-26 17:58:34', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265220806070489090', '1265217846770913282', '登出', '2', '100', '登出', '0', TO_DATE('2020-05-26 17:58:41', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265221129564573697', '1265221049302372354', '目录', '0', '100', '目录', '0', TO_DATE('2020-05-26 17:59:59', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265221163119005697', '1265221049302372354', '菜单', '1', '100', '菜单', '0', TO_DATE('2020-05-26 18:00:07', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265221188091891713', '1265221049302372354', '按钮', '2', '100', '按钮', '0', TO_DATE('2020-05-26 18:00:13', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265466389204967426', '1265466149622128641', '未发送', '0', '100', '未发送', '0', TO_DATE('2020-05-27 10:14:33', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265466432670539778', '1265466149622128641', '发送成功', '1', '100', '发送成功', '0', TO_DATE('2020-05-27 10:14:43', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265466486097584130', '1265466149622128641', '发送失败', '2', '100', '发送失败', '0', TO_DATE('2020-05-27 10:14:56', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265466530477514754', '1265466149622128641', '失效', '3', '100', '失效', '0', TO_DATE('2020-05-27 10:15:07', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265466835009150978', '1265466752209395713', '无', '0', '100', '无', '0', TO_DATE('2020-05-27 10:16:19', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265466874758569986', '1265466752209395713', '组件', '1', '100', '组件', '0', TO_DATE('2020-05-27 10:16:29', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265466925476093953', '1265466752209395713', '内链', '2', '100', '内链', '0', TO_DATE('2020-05-27 10:16:41', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265466962209808385', '1265466752209395713', '外链', '3', '100', '外链', '0', TO_DATE('2020-05-27 10:16:50', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265467428423475202', '1265467337566461954', '系统权重', '1', '100', '系统权重', '0', TO_DATE('2020-05-27 10:18:41', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265467503090475009', '1265467337566461954', '业务权重', '2', '100', '业务权重', '0', TO_DATE('2020-05-27 10:18:59', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265468138431062018', '1265468028632571905', '全部数据', '1', '100', '全部数据', '0', TO_DATE('2020-05-27 10:21:30', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265468194928336897', '1265468028632571905', '本部门及以下数据', '2', '100', '本部门及以下数据', '0', TO_DATE('2020-05-27 10:21:44', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265468241992622082', '1265468028632571905', '本部门数据', '3', '100', '本部门数据', '0', TO_DATE('2020-05-27 10:21:55', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265468273634451457', '1265468028632571905', '仅本人数据', '4', '100', '仅本人数据', '0', TO_DATE('2020-05-27 10:22:02', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265468302046666753', '1265468028632571905', '自定义数据', '5', '100', '自定义数据', '0', TO_DATE('2020-05-27 10:22:09', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265468508100239362', '1265468437904367618', 'app', '1', '100', 'app', '0', TO_DATE('2020-05-27 10:22:58', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265468543433056258', '1265468437904367618', 'pc', '2', '100', 'pc', '0', TO_DATE('2020-05-27 10:23:07', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1265468576874242050', '1265468437904367618', '其他', '3', '100', '其他', '0', TO_DATE('2020-05-27 10:23:15', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617233011335170', '1275617093517172738', '其它', '0', '100', '其它', '0', TO_DATE('2020-06-24 10:30:23', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617295355469826', '1275617093517172738', '增加', '1', '100', '增加', '0', TO_DATE('2020-06-24 10:30:38', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617348610547714', '1275617093517172738', '删除', '2', '100', '删除', '0', TO_DATE('2020-06-24 10:30:50', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617395515449346', '1275617093517172738', '编辑', '3', '100', '编辑', '0', TO_DATE('2020-06-24 10:31:02', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617433612312577', '1275617093517172738', '更新', '4', '100', '更新', '0', TO_DATE('2020-06-24 10:31:11', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617472707420161', '1275617093517172738', '查询', '5', '100', '查询', '0', TO_DATE('2020-06-24 10:31:20', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617502973517826', '1275617093517172738', '详情', '6', '100', '详情', '0', TO_DATE('2020-06-24 10:31:27', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617536959963137', '1275617093517172738', '树', '7', '100', '树', '0', TO_DATE('2020-06-24 10:31:35', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617619524837377', '1275617093517172738', '导入', '8', '100', '导入', '0', TO_DATE('2020-06-24 10:31:55', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617651816783873', '1275617093517172738', '导出', '9', '100', '导出', '0', TO_DATE('2020-06-24 10:32:03', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617683475390465', '1275617093517172738', '授权', '10', '100', '授权', '0', TO_DATE('2020-06-24 10:32:10', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617709928865793', '1275617093517172738', '强退', '11', '100', '强退', '0', TO_DATE('2020-06-24 10:32:17', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617739091861505', '1275617093517172738', '清空', '12', '100', '清空', '0', TO_DATE('2020-06-24 10:32:23', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1275617788601425921', '1275617093517172738', '修改状态', '13', '100', '修改状态', '0', TO_DATE('2020-06-24 10:32:35', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1277774590944317441', '1277774529430654977', '阿里云', '1', '100', '阿里云', '0', TO_DATE('2020-06-30 09:22:57', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1277774666055913474', '1277774529430654977', '腾讯云', '2', '100', '腾讯云', '0', TO_DATE('2020-06-30 09:23:15', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1277774695168577538', '1277774529430654977', 'minio', '3', '100', 'minio', '0', TO_DATE('2020-06-30 09:23:22', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1277774726835572737', '1277774529430654977', '本地', '4', '100', '本地', '0', TO_DATE('2020-06-30 09:23:29', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1278607123583868929', '1278606951432855553', '运行', '1', '100', '运行', '0', TO_DATE('2020-07-02 16:31:08', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1278607162943217666', '1278606951432855553', '停止', '2', '100', '停止', '0', TO_DATE('2020-07-02 16:31:18', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1278939265862004738', '1278911800547147777', '通知', '1', '100', '通知', '0', TO_DATE('2020-07-03 14:30:57', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1278939319922388994', '1278911800547147777', '公告', '2', '100', '公告', '0', TO_DATE('2020-07-03 14:31:10', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1278939399001796609', '1278911952657776642', '草稿', '0', '100', '草稿', '0', TO_DATE('2020-07-03 14:31:29', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1278939432686252034', '1278911952657776642', '发布', '1', '100', '发布', '0', TO_DATE('2020-07-03 14:31:37', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1278939458804183041', '1278911952657776642', '撤回', '2', '100', '撤回', '0', TO_DATE('2020-07-03 14:31:43', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1278939485878415362', '1278911952657776642', '删除', '3', '100', '删除', '0', TO_DATE('2020-07-03 14:31:50', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1291390260160299009', '1291390159941599233', '是', 'true', '100', '是', '2', TO_DATE('2020-08-06 23:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1291390315437031426', '1291390159941599233', '否', 'false', '100', '否', '2', TO_DATE('2020-08-06 23:06:59', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1334692875968200706', '1334692775158104065', '未读', '0', '100', '未读', '0', TO_DATE('2020-12-04 10:55:54', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1334692932540973057', '1334692775158104065', '已读', '1', '100', '已读', '0', TO_DATE('2020-12-04 10:56:07', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1340987649476034562', '1340987441098817537', '下载压缩包', '1', '1', '下载压缩包', '0', TO_DATE('2020-12-21 19:49:05', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1340987713699217410', '1340987441098817537', '生成到本地项目', '2', '2', '本地项目', '0', TO_DATE('2020-12-21 19:49:20', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-12-21 20:30:32', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358094655567454210', '1358094419419750401', '输入框', 'input', '100', '输入框', '0', TO_DATE('2021-02-07 00:46:13', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-02-08 01:01:28', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358094740510498817', '1358094419419750401', '时间选择', 'datepicker', '100', '时间选择', '0', TO_DATE('2021-02-07 00:46:33', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-02-08 01:04:07', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358094793149014017', '1358094419419750401', '下拉框', 'select', '100', '下拉框', '0', TO_DATE('2021-02-07 00:46:46', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358095496009506817', '1358094419419750401', '单选框', 'radio', '100', '单选框', '0', TO_DATE('2021-02-07 00:49:33', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358095673084633090', '1358094419419750401', '开关', 'switch', '100', '开关', '2', TO_DATE('2021-02-07 00:50:15', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-02-11 19:07:18', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358458689433190402', '1358457818733428737', '等于', 'eq', '1', '等于', '0', TO_DATE('2021-02-08 00:52:45', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-02-13 23:35:36', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358458785168179202', '1358457818733428737', '模糊', 'like', '2', '模糊', '0', TO_DATE('2021-02-08 00:53:08', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-02-13 23:35:46', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358460475682406401', '1358094419419750401', '多选框', 'checkbox', '100', '多选框', '0', TO_DATE('2021-02-08 00:59:51', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358460819019743233', '1358094419419750401', '数字输入框', 'inputnumber', '100', '数字输入框', '0', TO_DATE('2021-02-08 01:01:13', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358470210267725826', '1358470065111252994', 'Long', 'Long', '100', 'Long', '0', TO_DATE('2021-02-08 01:38:32', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358470239351029762', '1358470065111252994', 'String', 'String', '100', 'String', '0', TO_DATE('2021-02-08 01:38:39', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358470265640927233', '1358470065111252994', 'Date', 'Date', '100', 'Date', '0', TO_DATE('2021-02-08 01:38:45', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358470300168437761', '1358470065111252994', 'Integer', 'Integer', '100', 'Integer', '0', TO_DATE('2021-02-08 01:38:53', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358470697377415169', '1358470065111252994', 'boolean', 'boolean', '100', 'boolean', '0', TO_DATE('2021-02-08 01:40:28', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-02-08 01:40:47', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358471133434036226', '1358470065111252994', 'int', 'int', '100', 'int', '0', TO_DATE('2021-02-08 01:42:12', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358471188291338241', '1358470065111252994', 'double', 'double', '100', 'double', '0', TO_DATE('2021-02-08 01:42:25', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358756511688761346', '1358457818733428737', '大于', 'gt', '3', '大于', '0', TO_DATE('2021-02-08 20:36:12', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-02-13 23:45:24', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358756547159990274', '1358457818733428737', '小于', 'lt', '4', '大于', '0', TO_DATE('2021-02-08 20:36:20', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-02-13 23:45:29', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358756609990664193', '1358457818733428737', '不等于', 'ne', '7', '不等于', '0', TO_DATE('2021-02-08 20:36:35', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-02-13 23:45:46', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358756685030957057', '1358457818733428737', '大于等于', 'ge', '5', '大于等于', '0', TO_DATE('2021-02-08 20:36:53', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-02-13 23:45:35', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1358756800525312001', '1358457818733428737', '小于等于', 'le', '6', '小于等于', '0', TO_DATE('2021-02-08 20:37:20', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-02-13 23:45:40', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1360529773814083586', '1358094419419750401', '文本域', 'textarea', '100', '文本域', '0', TO_DATE('2021-02-13 18:02:30', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" VALUES ('1360606105914732545', '1358457818733428737', '不为空', 'isNotNull', '8', '不为空', '0', TO_DATE('2021-02-13 23:05:49', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-02-13 23:45:50', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');

-- ----------------------------
-- Table structure for SYS_DICT_TYPE
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" (
  "ID" NUMBER(20,0) NOT NULL,
  "NAME" NVARCHAR2(100) NOT NULL,
  "CODE" NVARCHAR2(50) NOT NULL,
  "SORT" NUMBER(11,0) NOT NULL,
  "REMARK" NVARCHAR2(255),
  "STATUS" NUMBER(4,0) NOT NULL,
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE"."NAME" IS '名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE"."CODE" IS '编码';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE"."SORT" IS '排序';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE"."REMARK" IS '备注';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE"."STATUS" IS '状态（字典 0正常 1停用 2删除）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE"."UPDATE_USER" IS '更新人';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" IS '系统字典类型表';

-- ----------------------------
-- Records of SYS_DICT_TYPE
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1265216211667636226', '通用状态', 'common_status', '100', '通用状态', '0', TO_DATE('2020-05-26 17:40:26', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-06-08 11:31:47', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1265216211667636234', '性别', 'sex', '100', '性别字典', '0', TO_DATE('2020-04-01 10:12:30', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1265216211667636235', '常量的分类', 'consts_type', '100', '常量的分类', '0', TO_DATE('2020-04-14 23:24:13', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1265217074079453185', '是否', 'yes_or_no', '100', '是否', '0', TO_DATE('2020-05-26 17:43:52', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1265217846770913282', '访问类型', 'vis_type', '100', '访问类型', '0', TO_DATE('2020-05-26 17:46:56', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1265221049302372354', '菜单类型', 'menu_type', '100', '菜单类型', '0', TO_DATE('2020-05-26 17:59:39', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1265466149622128641', '发送类型', 'send_type', '100', '发送类型', '0', TO_DATE('2020-05-27 10:13:36', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1265466752209395713', '打开方式', 'open_type', '100', '打开方式', '0', TO_DATE('2020-05-27 10:16:00', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1265467337566461954', '菜单权重', 'menu_weight', '100', '菜单权重', '0', TO_DATE('2020-05-27 10:18:19', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1265468028632571905', '数据范围类型', 'data_scope_type', '100', '数据范围类型', '0', TO_DATE('2020-05-27 10:21:04', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1265468437904367618', '短信发送来源', 'sms_send_source', '100', '短信发送来源', '0', TO_DATE('2020-05-27 10:22:42', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1275617093517172738', '操作类型', 'op_type', '100', '操作类型', '0', TO_DATE('2020-06-24 10:29:50', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1277774529430654977', '文件存储位置', 'file_storage_location', '100', '文件存储位置', '0', TO_DATE('2020-06-30 09:22:42', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1278606951432855553', '运行状态', 'run_status', '100', '定时任务运行状态', '0', TO_DATE('2020-07-02 16:30:27', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1278911800547147777', '通知公告类型', 'notice_type', '100', '通知公告类型', '0', TO_DATE('2020-07-03 12:41:49', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1278911952657776642', '通知公告状态', 'notice_status', '100', '通知公告状态', '0', TO_DATE('2020-07-03 12:42:25', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1291390159941599233', '是否boolean', 'yes_true_false', '100', '是否boolean', '2', TO_DATE('2020-08-06 23:06:22', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1334692775158104065', '阅读状态', 'read_status', '100', '阅读状态', '0', TO_DATE('2020-12-04 10:55:30', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1340987441098817537', '代码生成方式', 'code_gen_create_type', '100', '代码生成方式', '0', TO_DATE('2020-12-21 19:48:15', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1358094419419750401', '代码生成作用类型', 'code_gen_effect_type', '100', '代码生成作用类型', '0', TO_DATE('2021-02-07 00:45:16', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-02-08 00:47:48', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1358457818733428737', '代码生成查询类型', 'code_gen_query_type', '100', '代码生成查询类型', '0', TO_DATE('2021-02-08 00:49:18', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" VALUES ('1358470065111252994', '代码生成java类型', 'code_gen_java_type', '100', '代码生成java类型', '0', TO_DATE('2021-02-08 01:37:57', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);

-- ----------------------------
-- Table structure for SYS_EMP
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP" (
  "ID" NUMBER(20,0) NOT NULL,
  "JOB_NUM" NVARCHAR2(100),
  "ORG_ID" NUMBER(20,0) NOT NULL,
  "ORG_NAME" NVARCHAR2(100) NOT NULL
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP"."JOB_NUM" IS '工号';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP"."ORG_ID" IS '所属机构id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP"."ORG_NAME" IS '所属机构名称';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP" IS '员工表';

-- ----------------------------
-- Records of SYS_EMP
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP" VALUES ('1275735541155614721', NULL, '1265476890672672769', '华夏集团北京分公司');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP" VALUES ('1280700700074041345', NULL, '1265476890672672771', '研发部');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP" VALUES ('1280709549107552257', '100', '1265476890672672770', '华夏集团成都分公司');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP" VALUES ('1332142087677001730', NULL, '1265476890672672769', '华夏集团北京分公司');

-- ----------------------------
-- Table structure for SYS_EMP_EXT_ORG_POS
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS" (
  "ID" NUMBER(20,0) NOT NULL,
  "EMP_ID" NUMBER(20,0) NOT NULL,
  "ORG_ID" NUMBER(20,0) NOT NULL,
  "POS_ID" NUMBER(20,0) NOT NULL
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS"."EMP_ID" IS '员工id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS"."ORG_ID" IS '机构id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS"."POS_ID" IS '岗位id';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS" IS '员工附属机构岗位表';

-- ----------------------------
-- Records of SYS_EMP_EXT_ORG_POS
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS" VALUES ('1332141421759938561', '1280700700074041345', '1265476890672672772', '1265476890672672789');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS" VALUES ('1332141421776715778', '1280700700074041345', '1265476890672672773', '1265476890672672790');

-- ----------------------------
-- Table structure for SYS_EMP_POS
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS" (
  "ID" NUMBER(20,0) NOT NULL,
  "EMP_ID" NUMBER(20,0) NOT NULL,
  "POS_ID" NUMBER(20,0) NOT NULL
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS"."EMP_ID" IS '员工id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS"."POS_ID" IS '职位id';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS" IS '员工职位关联表';

-- ----------------------------
-- Records of SYS_EMP_POS
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS" VALUES ('1281042262003867649', '1280709549107552257', '1265476890672672787');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS" VALUES ('1332141421789298689', '1280700700074041345', '1265476890672672790');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS" VALUES ('1332141567281315841', '1275735541155614721', '1265476890672672787');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS" VALUES ('1332214497600180226', '1332142087677001730', '1265476890672672788');

-- ----------------------------
-- Table structure for SYS_FILE_INFO
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO" (
  "ID" NUMBER(20,0) NOT NULL,
  "FILE_LOCATION" NUMBER(4,0) NOT NULL,
  "FILE_BUCKET" NVARCHAR2(1000),
  "FILE_ORIGIN_NAME" NVARCHAR2(100) NOT NULL,
  "FILE_SUFFIX" NVARCHAR2(50),
  "FILE_SIZE_KB" NUMBER(20,0),
  "FILE_SIZE_INFO" NVARCHAR2(100),
  "FILE_OBJECT_NAME" NVARCHAR2(100) NOT NULL,
  "FILE_PATH" NVARCHAR2(1000),
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO"."ID" IS '主键id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO"."FILE_LOCATION" IS '文件存储位置（1:阿里云，2:腾讯云，3:minio，4:本地）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO"."FILE_BUCKET" IS '文件仓库';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO"."FILE_ORIGIN_NAME" IS '文件名称（上传时候的文件名）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO"."FILE_SUFFIX" IS '文件后缀';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO"."FILE_SIZE_KB" IS '文件大小kb';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO"."FILE_SIZE_INFO" IS '文件大小信息，计算后的';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO"."FILE_OBJECT_NAME" IS '存储到bucket的名称（文件唯一标识id）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO"."FILE_PATH" IS '存储路径';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO"."CREATE_USER" IS '创建用户';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO"."UPDATE_USER" IS '修改用户';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO" IS '文件信息表';

-- ----------------------------
-- Records of SYS_FILE_INFO
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_MENU
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" (
  "ID" NUMBER(20,0) DEFAULT 1 NOT NULL,
  "PID" NUMBER(20,0) NOT NULL,
  "PIDS" NCLOB NOT NULL,
  "NAME" NVARCHAR2(100) NOT NULL,
  "CODE" NVARCHAR2(50) NOT NULL,
  "TYPE" NUMBER(4,0) DEFAULT 1 NOT NULL,
  "ICON" NVARCHAR2(255),
  "ROUTER" NVARCHAR2(255),
  "PERMISSION" NVARCHAR2(255),
  "APPLICATION" NVARCHAR2(50) NOT NULL,
  "WEIGHT" NUMBER(4,0),
  "SORT" NUMBER(11,0) NOT NULL,
  "REMARK" NVARCHAR2(255),
  "STATUS" NUMBER(4,0) DEFAULT 0 NOT NULL,
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."PID" IS '父id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."PIDS" IS '父ids';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."NAME" IS '名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."CODE" IS '编码';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."TYPE" IS '菜单类型（字典 0目录 1菜单 2按钮）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."ICON" IS '图标';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."ROUTER" IS '路由地址';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."PERMISSION" IS '权限标识';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."APPLICATION" IS '应用分类（应用编码）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."WEIGHT" IS '权重（字典 1系统权重 2业务权重）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."SORT" IS '排序';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."REMARK" IS '备注';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."STATUS" IS '状态（字典 0正常 1停用 2删除）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU"."UPDATE_USER" IS '修改人';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" IS '系统菜单表';

-- ----------------------------
-- Records of SYS_MENU
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255311', '0', '[0],', '主控面板', 'system_index', '0', 'layui-icon-home', NULL, NULL, 'system', '1', '1', NULL, '0', TO_DATE('2020-05-25 02:19:24', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255321', '1264622039642255311', '[0],[1264622039642255311],', '分析页', 'system_index_dashboard', '1', NULL, '/other/dashboardHtml', NULL, 'system', '1', '1', NULL, '0', TO_DATE('2020-05-25 02:21:55', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255331', '1264622039642255311', '[0],[1264622039642255311],', '工作台', 'system_index_workplace', '1', NULL, '/other/workplaceHtml', NULL, 'system', '1', '2', NULL, '0', TO_DATE('2020-05-25 02:23:48', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255332', '1264622039642255311', '[0],[1264622039642255311],', '控制台', 'system_index_console', '1', NULL, '/other/consoleHtml', NULL, 'system', '1', '3', NULL, '0', TO_DATE('2020-05-25 02:23:48', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255341', '0', '[0],', '组织架构', 'sys_mgr', '0', 'layui-icon-user', NULL, NULL, 'system', '1', '2', NULL, '0', TO_DATE('2020-03-27 15:58:16', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255351', '1264622039642255341', '[0],[1264622039642255341],', '用户管理', 'sys_user_mgr', '1', NULL, '/sysUser/index', NULL, 'system', '1', '3', NULL, '0', TO_DATE('2020-03-27 16:10:21', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255361', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户查询', 'sys_user_mgr_page', '2', NULL, NULL, 'sysUser:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 16:36:49', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255371', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户编辑', 'sys_user_mgr_edit', '2', NULL, NULL, 'sysUser:edit', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 12:20:23', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255381', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户增加', 'sys_user_mgr_add', '2', NULL, NULL, 'sysUser:add', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 16:37:35', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255391', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户删除', 'sys_user_mgr_delete', '2', NULL, NULL, 'sysUser:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 16:37:58', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255401', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户详情', 'sys_user_mgr_detail', '2', NULL, NULL, 'sysUser:detail', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 16:38:25', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255411', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户导出', 'sys_user_mgr_export', '2', NULL, NULL, 'sysUser:export', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 12:21:59', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255421', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户选择器', 'sys_user_mgr_selector', '2', NULL, NULL, 'sysUser:selector', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-03 13:30:14', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255431', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户授权角色', 'sys_user_mgr_grant_role', '2', NULL, NULL, 'sysUser:grantRole', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 09:22:01', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255441', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户拥有角色', 'sys_user_mgr_own_role', '2', NULL, NULL, 'sysUser:ownRole', 'system', '1', '100', NULL, '0', TO_DATE('2020-05-29 14:27:22', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255451', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户授权数据', 'sys_user_mgr_grant_data', '2', NULL, NULL, 'sysUser:grantData', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 09:22:13', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255461', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户拥有数据', 'sys_user_mgr_own_data', '2', NULL, NULL, 'sysUser:ownData', 'system', '1', '100', NULL, '0', TO_DATE('2020-05-29 14:27:41', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255471', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户更新信息', 'sys_user_mgr_update_info', '2', NULL, NULL, 'sysUser:updateInfo', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 16:19:32', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255481', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户修改密码', 'sys_user_mgr_update_pwd', '2', NULL, NULL, 'sysUser:updatePwd', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 16:20:25', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255491', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户修改状态', 'sys_user_mgr_change_status', '2', NULL, NULL, 'sysUser:changeStatus', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-23 11:13:14', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255501', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户修改头像', 'sys_user_mgr_update_avatar', '2', NULL, NULL, 'sysUser:updateAvatar', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 12:21:42', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255511', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户重置密码', 'sys_user_mgr_reset_pwd', '2', NULL, NULL, 'sysUser:resetPwd', 'system', '1', '100', NULL, '0', TO_DATE('2020-05-29 15:01:51', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255521', '1264622039642255341', '[0],[1264622039642255341],', '机构管理', 'sys_org_mgr', '1', NULL, '/sysOrg/index', NULL, 'system', '1', '4', NULL, '0', TO_DATE('2020-03-27 17:15:39', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255531', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构查询', 'sys_org_mgr_page', '2', NULL, NULL, 'sysOrg:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 17:17:37', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255541', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构列表', 'sys_org_mgr_list', '2', NULL, NULL, 'sysOrg:list', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 11:54:26', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255551', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构增加', 'sys_org_mgr_add', '2', NULL, NULL, 'sysOrg:add', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 17:19:53', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255561', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构编辑', 'sys_org_mgr_edit', '2', NULL, NULL, 'sysOrg:edit', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 11:54:37', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255571', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构删除', 'sys_org_mgr_delete', '2', NULL, NULL, 'sysOrg:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 17:20:48', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255581', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构详情', 'sys_org_mgr_detail', '2', NULL, NULL, 'sysOrg:detail', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 17:21:15', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255591', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构树', 'sys_org_mgr_tree', '2', NULL, NULL, 'sysOrg:tree', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 17:21:58', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255601', '1264622039642255341', '[0],[1264622039642255341],', '职位管理', 'sys_pos_mgr', '1', NULL, '/sysPos/index', NULL, 'system', '1', '5', NULL, '0', TO_DATE('2020-03-27 18:38:31', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255611', '1264622039642255601', '[0],[1264622039642255341],[1264622039642255601],', '职位查询', 'sys_pos_mgr_page', '2', NULL, NULL, 'sysPos:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 18:41:48', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255621', '1264622039642255601', '[0],[1264622039642255341],[1264622039642255601],', '职位列表', 'sys_pos_mgr_list', '2', NULL, NULL, 'sysPos:list', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 11:55:57', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255631', '1264622039642255601', '[0],[1264622039642255341],[1264622039642255601],', '职位增加', 'sys_pos_mgr_add', '2', NULL, NULL, 'sysPos:add', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 18:42:20', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255641', '1264622039642255601', '[0],[1264622039642255341],[1264622039642255601],', '职位编辑', 'sys_pos_mgr_edit', '2', NULL, NULL, 'sysPos:edit', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 11:56:08', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255651', '1264622039642255601', '[0],[1264622039642255341],[1264622039642255601],', '职位删除', 'sys_pos_mgr_delete', '2', NULL, NULL, 'sysPos:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 18:42:39', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255661', '1264622039642255601', '[0],[1264622039642255341],[1264622039642255601],', '职位详情', 'sys_pos_mgr_detail', '2', NULL, NULL, 'sysPos:detail', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 18:43:00', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255671', '0', '[0],', '权限管理', 'auth_manager', '0', 'layui-icon-auz', NULL, NULL, 'system', '1', '3', NULL, '0', TO_DATE('2020-07-15 15:51:57', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255681', '1264622039642255671', '[0],[1264622039642255671],', '应用管理', 'sys_app_mgr', '1', NULL, '/sysApp/index', NULL, 'system', '1', '6', NULL, '0', TO_DATE('2020-03-27 16:40:21', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255691', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '应用查询', 'sys_app_mgr_page', '2', NULL, NULL, 'sysApp:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 16:41:58', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255701', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '应用列表', 'sys_app_mgr_list', '2', NULL, NULL, 'sysApp:list', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 10:04:59', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255711', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '应用增加', 'sys_app_mgr_add', '2', NULL, NULL, 'sysApp:add', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 16:44:10', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255721', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '应用编辑', 'sys_app_mgr_edit', '2', NULL, NULL, 'sysApp:edit', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 10:04:34', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255731', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '应用删除', 'sys_app_mgr_delete', '2', NULL, NULL, 'sysApp:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 17:14:29', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255741', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '应用详情', 'sys_app_mgr_detail', '2', NULL, NULL, 'sysApp:detail', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 17:14:56', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255751', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '设为默认应用', 'sys_app_mgr_set_as_default', '2', NULL, NULL, 'sysApp:setAsDefault', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 17:14:56', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255761', '1264622039642255671', '[0],[1264622039642255671],', '菜单管理', 'sys_menu_mgr', '1', NULL, '/sysMenu/index', NULL, 'system', '1', '7', NULL, '0', TO_DATE('2020-03-27 18:44:35', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255771', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单列表', 'sys_menu_mgr_page', '2', NULL, NULL, 'sysMenu:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 18:45:20', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255781', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单增加', 'sys_menu_mgr_add', '2', NULL, NULL, 'sysMenu:add', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 18:45:37', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255791', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单编辑', 'sys_menu_mgr_edit', '2', NULL, NULL, 'sysMenu:edit', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 11:52:00', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255801', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单删除', 'sys_menu_mgr_delete', '2', NULL, NULL, 'sysMenu:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 18:46:01', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255811', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单详情', 'sys_menu_mgr_detail', '2', NULL, NULL, 'sysMenu:detail', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 18:46:22', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255821', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单授权树', 'sys_menu_mgr_grant_tree', '2', NULL, NULL, 'sysMenu:treeForGrant', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-03 09:50:31', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255831', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单树', 'sys_menu_mgr_tree', '2', NULL, NULL, 'sysMenu:tree', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-27 18:47:50', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255841', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单切换', 'sys_menu_mgr_change', '2', NULL, NULL, 'sysMenu:change', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-03 09:51:43', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255851', '1264622039642255671', '[0],[1264622039642255671],', '角色管理', 'sys_role_mgr', '1', NULL, '/sysRole/index', NULL, 'system', '1', '8', NULL, '0', TO_DATE('2020-03-28 16:01:09', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255861', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色查询', 'sys_role_mgr_page', '2', NULL, NULL, 'sysRole:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-28 16:02:09', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255871', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色增加', 'sys_role_mgr_add', '2', NULL, NULL, 'sysRole:add', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-28 16:02:27', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255881', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色编辑', 'sys_role_mgr_edit', '2', NULL, NULL, 'sysRole:edit', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 11:57:27', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255891', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色删除', 'sys_role_mgr_delete', '2', NULL, NULL, 'sysRole:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-28 16:02:46', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255901', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色详情', 'sys_role_mgr_detail', '2', NULL, NULL, 'sysRole:detail', 'system', '1', '100', NULL, '0', TO_DATE('2020-03-28 16:03:01', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255911', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色下拉', 'sys_role_mgr_drop_down', '2', NULL, NULL, 'sysRole:dropDown', 'system', '1', '100', NULL, '0', TO_DATE('2020-05-29 15:45:39', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255921', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色授权菜单', 'sys_role_mgr_grant_menu', '2', NULL, NULL, 'sysRole:grantMenu', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 09:16:27', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255931', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色拥有菜单', 'sys_role_mgr_own_menu', '2', NULL, NULL, 'sysRole:ownMenu', 'system', '1', '100', NULL, '0', TO_DATE('2020-05-29 14:21:54', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255941', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色授权数据', 'sys_role_mgr_grant_data', '2', NULL, NULL, 'sysRole:grantData', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 09:16:56', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255951', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色拥有数据', 'sys_role_mgr_own_data', '2', NULL, NULL, 'sysRole:ownData', 'system', '1', '100', NULL, '0', TO_DATE('2020-05-29 14:23:08', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255961', '0', '[0],', '开发管理', 'system_tools', '0', 'layui-icon-code-circle', NULL, NULL, 'system', '1', '4', NULL, '0', TO_DATE('2020-05-25 02:10:55', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255971', '1264622039642255961', '[0],[1264622039642255961],', '系统配置', 'system_tools_config', '1', NULL, '/sysConfig/index', NULL, 'system', '1', '9', NULL, '0', TO_DATE('2020-05-25 02:12:56', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255981', '1264622039642255971', '[0],[1264622039642255961],[1264622039642255971],', '配置查询', 'system_tools_config_page', '2', NULL, NULL, 'sysConfig:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-05-27 17:02:22', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642255991', '1264622039642255971', '[0],[1264622039642255961],[1264622039642255971],', '配置列表', 'system_tools_config_list', '2', NULL, NULL, 'sysConfig:list', 'system', '1', '100', NULL, '0', TO_DATE('2020-05-27 17:02:42', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256001', '1264622039642255971', '[0],[1264622039642255961],[1264622039642255971],', '配置增加', 'system_tools_config_add', '2', NULL, NULL, 'sysConfig:add', 'system', '1', '100', NULL, '0', TO_DATE('2020-05-27 17:03:31', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256011', '1264622039642255971', '[0],[1264622039642255961],[1264622039642255971],', '配置编辑', 'system_tools_config_edit', '2', NULL, NULL, 'sysConfig:edit', 'system', '1', '100', NULL, '0', TO_DATE('2020-05-27 17:03:55', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256021', '1264622039642255971', '[0],[1264622039642255961],[1264622039642255971],', '配置删除', 'system_tools_config_delete', '2', NULL, NULL, 'sysConfig:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-05-27 17:03:44', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256031', '1264622039642255971', '[0],[1264622039642255961],[1264622039642255971],', '配置详情', 'system_tools_config_detail', '2', NULL, NULL, 'sysConfig:detail', 'system', '1', '100', NULL, '0', TO_DATE('2020-05-27 17:02:59', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256041', '1264622039642255961', '[0],[1264622039642255961],', '邮件发送', 'sys_email_mgr', '1', NULL, '/sysEmail/index', NULL, 'system', '1', '10', NULL, '0', TO_DATE('2020-07-02 11:44:21', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256051', '1264622039642256041', '[0],[1264622039642255961],[1264622039642256041],', '发送文本邮件', 'sys_email_mgr_send_email', '2', NULL, NULL, 'sysEmail:sendEmail', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 11:45:39', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256061', '1264622039642256041', '[0],[1264622039642255961],[1264622039642256041],', '发送html邮件', 'sys_email_mgr_send_email_html', '2', NULL, NULL, 'sysEmail:sendEmailHtml', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 11:45:57', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256071', '1264622039642255961', '[0],[1264622039642255961],', '短信管理', 'sys_sms_mgr', '1', NULL, '/sysSms/index', NULL, 'system', '1', '11', NULL, '0', TO_DATE('2020-07-02 12:00:12', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256081', '1264622039642256071', '[0],[1264622039642255961],[1264622039642256071],', '短信发送查询', 'sys_sms_mgr_page', '2', NULL, NULL, 'sysSms:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 12:16:56', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256091', '1264622039642256071', '[0],[1264622039642255961],[1264622039642256071],', '发送验证码短信', 'sys_sms_mgr_send_login_message', '2', NULL, NULL, 'sysSms:sendLoginMessage', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 12:02:31', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256101', '1264622039642256071', '[0],[1264622039642255961],[1264622039642256071],', '验证短信验证码', 'sys_sms_mgr_validate_message', '2', NULL, NULL, 'sysSms:validateMessage', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 12:02:50', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256111', '1264622039642255961', '[0],[1264622039642255961],', '字典管理', 'sys_dict_mgr', '1', NULL, '/sysDictType/index', NULL, 'system', '1', '12', NULL, '0', TO_DATE('2020-04-01 11:17:26', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256121', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型查询', 'sys_dict_mgr_dict_type_page', '2', NULL, NULL, 'sysDictType:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 11:20:22', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256131', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型列表', 'sys_dict_mgr_dict_type_list', '2', NULL, NULL, 'sysDictType:list', 'system', '1', '100', NULL, '0', TO_DATE('2020-05-29 15:12:35', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256141', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型增加', 'sys_dict_mgr_dict_type_add', '2', NULL, NULL, 'sysDictType:add', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 11:19:58', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256151', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型删除', 'sys_dict_mgr_dict_type_delete', '2', NULL, NULL, 'sysDictType:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 11:21:30', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256161', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型编辑', 'sys_dict_mgr_dict_type_edit', '2', NULL, NULL, 'sysDictType:edit', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 11:21:42', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256171', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型详情', 'sys_dict_mgr_dict_type_detail', '2', NULL, NULL, 'sysDictType:detail', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 11:22:06', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256181', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型下拉', 'sys_dict_mgr_dict_type_drop_down', '2', NULL, NULL, 'sysDictType:dropDown', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 11:22:23', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256191', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型修改状态', 'sys_dict_mgr_dict_type_change_status', '2', NULL, NULL, 'sysDictType:changeStatus', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-23 11:15:50', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256201', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值查询', 'sys_dict_mgr_dict_page', '2', NULL, NULL, 'sysDictData:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 11:23:11', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256211', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值列表', 'sys_dict_mgr_dict_list', '2', NULL, NULL, 'sysDictData:list', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 11:24:58', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256221', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值增加', 'sys_dict_mgr_dict_add', '2', NULL, NULL, 'sysDictData:add', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 11:22:51', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256231', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值删除', 'sys_dict_mgr_dict_delete', '2', NULL, NULL, 'sysDictData:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 11:23:26', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256241', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值编辑', 'sys_dict_mgr_dict_edit', '2', NULL, NULL, 'sysDictData:edit', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 11:24:21', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256251', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值详情', 'sys_dict_mgr_dict_detail', '2', NULL, NULL, 'sysDictData:detail', 'system', '1', '100', NULL, '0', TO_DATE('2020-04-01 11:24:42', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256261', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值修改状态', 'sys_dict_mgr_dict_change_status', '2', NULL, NULL, 'sysDictData:changeStatus', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-23 11:17:53', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256271', '1264622039642255961', '[0],[1264622039642255961],', '接口文档', 'sys_swagger_mgr', '1', NULL, '/doc.html', NULL, 'system', '1', '13', NULL, '0', TO_DATE('2020-07-02 12:16:56', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256281', '0', '[0],', '日志管理', 'sys_log_mgr', '0', 'layui-icon-log', NULL, NULL, 'system', '1', '5', NULL, '0', TO_DATE('2020-04-01 09:25:01', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256291', '1264622039642256281', '[0],[1264622039642256281],', '访问日志', 'sys_log_mgr_vis_log', '1', NULL, '/sysVisLog/index', NULL, 'system', '1', '14', NULL, '0', TO_DATE('2020-04-01 09:26:40', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256301', '1264622039642256291', '[0],[1264622039642256281],[1264622039642256291],', '访问日志查询', 'sys_log_mgr_vis_log_page', '2', NULL, NULL, 'sysVisLog:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 09:55:51', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256311', '1264622039642256291', '[0],[1264622039642256281],[1264622039642256291],', '访问日志清空', 'sys_log_mgr_vis_log_delete', '2', NULL, NULL, 'sysVisLog:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 09:56:57', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256321', '1264622039642256281', '[0],[1264622039642256281],', '操作日志', 'sys_log_mgr_op_log', '1', NULL, '/sysOpLog/index', NULL, 'system', '1', '15', NULL, '0', TO_DATE('2020-04-01 09:26:59', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256331', '1264622039642256321', '[0],[1264622039642256281],[1264622039642256321],', '操作日志查询', 'sys_log_mgr_op_log_page', '2', NULL, NULL, 'sysOpLog:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 09:57:39', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256341', '1264622039642256321', '[0],[1264622039642256281],[1264622039642256321],', '操作日志清空', 'sys_log_mgr_op_log_delete', '2', NULL, NULL, 'sysOpLog:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-02 09:58:13', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256351', '0', '[0],', '系统监控', 'sys_monitor_mgr', '0', 'layui-icon-chart-screen', NULL, NULL, 'system', '1', '6', NULL, '0', TO_DATE('2020-06-05 16:00:50', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256361', '1264622039642256351', '[0],[1264622039642256351],', '服务监控', 'sys_monitor_mgr_machine_monitor', '1', NULL, '/sysMachine/index', NULL, 'system', '1', '16', NULL, '0', TO_DATE('2020-06-05 16:02:38', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256371', '1264622039642256361', '[0],[1264622039642256351],[1264622039642256361],', '服务监控查询', 'sys_monitor_mgr_machine_monitor_query', '2', NULL, NULL, 'sysMachine:query', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-05 16:05:33', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256381', '1264622039642256351', '[0],[1264622039642256351],', '在线用户', 'sys_monitor_mgr_online_user', '1', NULL, '/sysOnlineUser/index', NULL, 'system', '1', '17', NULL, '0', TO_DATE('2020-06-05 16:01:55', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256391', '1264622039642256381', '[0],[1264622039642256351],[1264622039642256381],', '在线用户列表', 'sys_monitor_mgr_online_user_list', '2', NULL, NULL, 'sysOnlineUser:list', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-05 16:03:46', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256401', '1264622039642256381', '[0],[1264622039642256351],[1264622039642256381],', '在线用户强退', 'sys_monitor_mgr_online_user_force_exist', '2', NULL, NULL, 'sysOnlineUser:forceExist', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-05 16:04:16', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256411', '1264622039642256351', '[0],[1264622039642256351],', '数据监控', 'sys_monitor_mgr_druid', '1', NULL, '/druid', NULL, 'system', '1', '18', NULL, '0', TO_DATE('2020-06-28 16:15:07', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-09-13 09:39:10', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256421', '0', '[0],', '通知公告', 'sys_notice', '0', 'layui-icon-speaker', NULL, NULL, 'system', '1', '7', NULL, '0', TO_DATE('2020-06-29 15:41:53', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256431', '1264622039642256421', '[0],[1264622039642256421],', '公告管理', 'sys_notice_mgr', '1', NULL, '/sysNotice/index', NULL, 'system', '1', '19', NULL, '0', TO_DATE('2020-06-29 15:44:24', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256441', '1264622039642256431', '[0],[1264622039642256421],[1264622039642256431],', '公告查询', 'sys_notice_mgr_page', '2', NULL, NULL, 'sysNotice:page', 'system', '1', '100', NULL, '2', TO_DATE('2020-06-29 15:45:30', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256451', '1264622039642256431', '[0],[1264622039642256421],[1264622039642256431],', '公告增加', 'sys_notice_mgr_add', '2', NULL, NULL, 'sysNotice:add', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-29 15:45:57', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256461', '1264622039642256431', '[0],[1264622039642256421],[1264622039642256431],', '公告编辑', 'sys_notice_mgr_edit', '2', NULL, NULL, 'sysNotice:edit', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-29 15:46:22', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256471', '1264622039642256431', '[0],[1264622039642256421],[1264622039642256431],', '公告删除', 'sys_notice_mgr_delete', '2', NULL, NULL, 'sysNotice:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-29 15:46:11', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256481', '1264622039642256431', '[0],[1264622039642256421],[1264622039642256431],', '公告查看', 'sys_notice_mgr_detail', '2', NULL, NULL, 'sysNotice:detail', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-29 15:46:33', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256491', '1264622039642256431', '[0],[1264622039642256421],[1264622039642256431],', '公告修改状态', 'sys_notice_mgr_changeStatus', '2', NULL, NULL, 'sysNotice:changeStatus', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-29 15:46:50', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256501', '1264622039642256421', '[0],[1264622039642256421],', '已收公告', 'sys_notice_mgr_received', '1', NULL, '/sysNotice/receivedPage', NULL, 'system', '1', '20', NULL, '0', TO_DATE('2020-06-29 16:32:53', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256511', '1264622039642256501', '[0],[1264622039642256421],[1264622039642256501],', '已收公告查询', 'sys_notice_mgr_received_page', '2', NULL, NULL, 'sysNotice:received', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-29 16:33:43', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256521', '0', '[0],', '文件管理', 'sys_file_mgr', '0', 'layui-icon-file', NULL, NULL, 'system', '1', '8', NULL, '0', TO_DATE('2020-06-24 17:31:10', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256531', '1264622039642256521', '[0],[1264622039642256521],', '系统文件', 'sys_file_mgr_sys_file', '1', NULL, '/sysFileInfo/index', NULL, 'system', '1', '21', NULL, '0', TO_DATE('2020-06-24 17:32:57', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256541', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '文件查询', 'sys_file_mgr_sys_file_page', '2', NULL, NULL, 'sysFileInfo:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-24 17:35:38', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256551', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '文件列表', 'sys_file_mgr_sys_file_list', '2', NULL, NULL, 'sysFileInfo:list', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-24 17:35:49', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256561', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '文件删除', 'sys_file_mgr_sys_file_delete', '2', NULL, NULL, 'sysFileInfo:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-24 17:36:11', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256571', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '文件详情', 'sys_file_mgr_sys_file_detail', '2', NULL, NULL, 'sysFileInfo:detail', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-24 17:36:01', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256581', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '文件上传', 'sys_file_mgr_sys_file_upload', '2', NULL, NULL, 'sysFileInfo:upload', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-24 17:34:29', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256591', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '文件下载', 'sys_file_mgr_sys_file_download', '2', NULL, NULL, 'sysFileInfo:download', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-24 17:34:55', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256601', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '图片预览', 'sys_file_mgr_sys_file_preview', '2', NULL, NULL, 'sysFileInfo:preview', 'system', '1', '100', NULL, '0', TO_DATE('2020-06-24 17:35:19', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256602', '1264622039642256521', '[0],[1264622039642256521],', '在线文档', 'sys_file_mgr_sys_online_file', '1', NULL, '/sysFileInfo/onlineIndex', NULL, 'system', '1', '21', NULL, '0', TO_DATE('2020-06-24 17:32:57', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256611', '0', '[0],', '定时任务', 'sys_timers', '0', 'layui-icon-console', NULL, NULL, 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:17:20', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256621', '1264622039642256611', '[0],[1264622039642256611],', '任务管理', 'sys_timers_mgr', '1', NULL, '/sysTimers/index', NULL, 'system', '1', '22', NULL, '0', TO_DATE('2020-07-01 17:18:53', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256631', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务查询', 'sys_timers_mgr_page', '2', NULL, NULL, 'sysTimers:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:19:43', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256641', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务列表', 'sys_timers_mgr_list', '2', NULL, NULL, 'sysTimers:list', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:19:56', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256651', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务详情', 'sys_timers_mgr_detail', '2', NULL, NULL, 'sysTimers:detail', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:20:10', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256661', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务增加', 'sys_timers_mgr_add', '2', NULL, NULL, 'sysTimers:add', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:20:23', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256671', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务删除', 'sys_timers_mgr_delete', '2', NULL, NULL, 'sysTimers:delete', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:20:33', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256681', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务编辑', 'sys_timers_mgr_edit', '2', NULL, NULL, 'sysTimers:edit', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:20:43', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256691', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务可执行列表', 'sys_timers_mgr_get_action_classes', '2', NULL, NULL, 'sysTimers:getActionClasses', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:22:16', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256701', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务启动', 'sys_timers_mgr_start', '2', NULL, NULL, 'sysTimers:start', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:22:32', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256711', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务关闭', 'sys_timers_mgr_stop', '2', NULL, NULL, 'sysTimers:stop', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:22:43', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256721', '0', '[0],', '系统区域', 'sys_area', '0', 'layui-icon-console', NULL, NULL, 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:17:20', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256731', '1264622039642256721', '[0],[1264622039642256721],', '区域管理', 'sys_area_mgr', '1', NULL, '/sysArea/index', NULL, 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:17:20', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256741', '1264622039642256721', '[0],[1264622039642256721],', '区域选择', 'sys_area_select', '1', NULL, '/sysArea/select', NULL, 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:17:20', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1264622039642256751', '1264622039642256731', '[0],[1264622039642256721],[1264622039642256731],', '系统区域查询', 'sys_area_mgr_page', '2', NULL, NULL, 'sysArea:page', 'system', '1', '100', NULL, '0', TO_DATE('2020-07-01 17:19:43', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" VALUES ('1338864007892807681', '0', '[0],', '代码生成', 'code_generate', '1', 'layui-icon-app', '/codeGenerate/index', NULL, 'systool', '1', '1', '代码生成1级菜单', '0', TO_DATE('2020-12-15 23:10:29', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-12-15 23:54:55', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');

-- ----------------------------
-- Table structure for SYS_MESSAGE
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE" (
  "ID" NUMBER(20,0) NOT NULL,
  "TITLE" NVARCHAR2(50),
  "CONTENT" NCLOB,
  "TYPE" NUMBER(11,0),
  "SEND_TYPE" NUMBER(11,0),
  "BUSINESS_DATA" NVARCHAR2(1000),
  "SEND_TIME" DATE
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE"."ID" IS '消息id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE"."TITLE" IS '消息标题';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE"."CONTENT" IS '消息内容';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE"."TYPE" IS '消息类别，字典（1通知 2私信 3待办）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE"."SEND_TYPE" IS '发送类别，字典（1直接发送 2定时发送）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE"."BUSINESS_DATA" IS '业务数据，JSON格式';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE"."SEND_TIME" IS '发送时间';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE" IS '消息表';

-- ----------------------------
-- Records of SYS_MESSAGE
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_MESSAGE_USER
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER" (
  "ID" NUMBER(20,0) NOT NULL,
  "MESSAGE_ID" NUMBER(20,0) NOT NULL,
  "SENDER_ID" NUMBER(20,0) NOT NULL,
  "RECEIVER_ID" NUMBER(20,0) NOT NULL,
  "STATUS" NUMBER(4,0) NOT NULL
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER"."MESSAGE_ID" IS '消息id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER"."SENDER_ID" IS '发送人id，系统发送则为-1';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER"."RECEIVER_ID" IS '接收人id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER"."STATUS" IS '状态（字典 0未读 1已读）';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER" IS '消息人员关联表';

-- ----------------------------
-- Records of SYS_MESSAGE_USER
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_NOTICE
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE" (
  "ID" NUMBER(20,0) NOT NULL,
  "TITLE" NVARCHAR2(1000),
  "CONTENT" NCLOB,
  "TYPE" NUMBER(4,0) NOT NULL,
  "PUBLIC_USER_ID" NUMBER(20,0) NOT NULL,
  "PUBLIC_USER_NAME" NVARCHAR2(100) NOT NULL,
  "PUBLIC_ORG_ID" NUMBER(20,0),
  "PUBLIC_ORG_NAME" NVARCHAR2(50),
  "PUBLIC_TIME" DATE,
  "CANCEL_TIME" DATE,
  "STATUS" NUMBER(4,0) NOT NULL,
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."TITLE" IS '标题';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."CONTENT" IS '内容';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."TYPE" IS '类型（字典 1通知 2公告）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."PUBLIC_USER_ID" IS '发布人id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."PUBLIC_USER_NAME" IS '发布人姓名';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."PUBLIC_ORG_ID" IS '发布机构id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."PUBLIC_ORG_NAME" IS '发布机构名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."PUBLIC_TIME" IS '发布时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."CANCEL_TIME" IS '撤回时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."STATUS" IS '状态（字典 0草稿 1发布 2撤回 3删除）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE"."UPDATE_USER" IS '修改人';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE" IS '通知表';

-- ----------------------------
-- Records of SYS_NOTICE
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_NOTICE_USER
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE_USER";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE_USER" (
  "ID" NUMBER(20,0) NOT NULL,
  "NOTICE_ID" NUMBER(20,0) NOT NULL,
  "USER_ID" NUMBER(20,0) NOT NULL,
  "STATUS" NUMBER(4,0) NOT NULL,
  "READ_TIME" DATE
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE_USER"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE_USER"."NOTICE_ID" IS '通知公告id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE_USER"."USER_ID" IS '用户id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE_USER"."STATUS" IS '状态（字典 0未读 1已读）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE_USER"."READ_TIME" IS '阅读时间';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE_USER" IS '系统用户数据范围表';

-- ----------------------------
-- Records of SYS_NOTICE_USER
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_OAUTH_USER
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER" (
  "ID" NUMBER(20,0) NOT NULL,
  "UUID" NVARCHAR2(255) NOT NULL,
  "ACCESS_TOKEN" NVARCHAR2(255),
  "NICK_NAME" NVARCHAR2(255),
  "AVATAR" NVARCHAR2(500),
  "BLOG" NVARCHAR2(255),
  "COMPANY" NVARCHAR2(255),
  "LOCATION" NVARCHAR2(255),
  "EMAIL" NVARCHAR2(255),
  "GENDER" NVARCHAR2(50),
  "SOURCE" NVARCHAR2(255),
  "REMARK" NVARCHAR2(255),
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."UUID" IS '第三方平台的用户唯一id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."ACCESS_TOKEN" IS '用户授权的token';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."NICK_NAME" IS '昵称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."AVATAR" IS '头像';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."BLOG" IS '用户网址';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."COMPANY" IS '所在公司';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."LOCATION" IS '位置';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."EMAIL" IS '邮箱';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."GENDER" IS '性别';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."SOURCE" IS '用户来源';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."REMARK" IS '用户备注（各平台中的用户个人介绍）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."CREATE_USER" IS '创建用户';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER"."UPDATE_USER" IS '更新用户';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER" IS '第三方认证用户信息表';

-- ----------------------------
-- Records of SYS_OAUTH_USER
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_OP_LOG
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG" (
  "ID" NUMBER(20,0) NOT NULL,
  "NAME" NVARCHAR2(50),
  "OP_TYPE" NUMBER(4,0),
  "SUCCESS" NCHAR(1),
  "MESSAGE" NCLOB,
  "IP" NVARCHAR2(255),
  "LOCATION" NVARCHAR2(255),
  "BROWSER" NVARCHAR2(255),
  "OS" NVARCHAR2(255),
  "URL" NVARCHAR2(500),
  "CLASS_NAME" NVARCHAR2(500),
  "METHOD_NAME" NVARCHAR2(500),
  "REQ_METHOD" NVARCHAR2(255),
  "PARAM" NCLOB,
  "RESULT" NCLOB,
  "OP_TIME" DATE,
  "ACCOUNT" NVARCHAR2(50)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."NAME" IS '名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."OP_TYPE" IS '操作类型';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."SUCCESS" IS '是否执行成功（Y-是，N-否）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."MESSAGE" IS '具体消息';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."IP" IS 'ip';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."LOCATION" IS '地址';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."BROWSER" IS '浏览器';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."OS" IS '操作系统';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."URL" IS '请求地址';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."CLASS_NAME" IS '类名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."METHOD_NAME" IS '方法名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."REQ_METHOD" IS '请求方式（GET POST PUT DELETE)';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."PARAM" IS '请求参数';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."RESULT" IS '返回结果';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."OP_TIME" IS '操作时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG"."ACCOUNT" IS '操作账号';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG" IS '系统操作日志表';

-- ----------------------------
-- Records of SYS_OP_LOG
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_ORG
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" (
  "ID" NUMBER(20,0) NOT NULL,
  "PID" NUMBER(20,0) NOT NULL,
  "PIDS" NCLOB NOT NULL,
  "NAME" NVARCHAR2(100) NOT NULL,
  "CODE" NVARCHAR2(50) NOT NULL,
  "SORT" NUMBER(11,0) NOT NULL,
  "REMARK" NVARCHAR2(255),
  "STATUS" NUMBER(4,0) DEFAULT 0 NOT NULL,
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG"."PID" IS '父id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG"."PIDS" IS '父ids';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG"."NAME" IS '名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG"."CODE" IS '编码';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG"."SORT" IS '排序';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG"."REMARK" IS '描述';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG"."STATUS" IS '状态（字典 0正常 1停用 2删除）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG"."UPDATE_USER" IS '更新人';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" IS '系统组织机构表';

-- ----------------------------
-- Records of SYS_ORG
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" VALUES ('1265476890651701250', '0', '[0],', '华夏集团', 'hxjt', '100', '华夏集团总公司', '0', TO_DATE('2020-03-26 16:50:53', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" VALUES ('1265476890672672769', '1265476890651701250', '[0],[1265476890651701250],', '华夏集团北京分公司', 'hxjt_bj', '100', '华夏集团北京分公司', '0', TO_DATE('2020-03-26 16:55:42', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" VALUES ('1265476890672672770', '1265476890651701250', '[0],[1265476890651701250],', '华夏集团成都分公司', 'hxjt_cd', '100', '华夏集团成都分公司', '0', TO_DATE('2020-03-26 16:56:02', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" VALUES ('1265476890672672771', '1265476890672672769', '[0],[1265476890651701250],[1265476890672672769],', '研发部', 'hxjt_bj_yfb', '100', '华夏集团北京分公司研发部', '0', TO_DATE('2020-03-26 16:56:36', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" VALUES ('1265476890672672772', '1265476890672672769', '[0],[1265476890651701250],[1265476890672672769],', '企划部', 'hxjt_bj_qhb', '100', '华夏集团北京分公司企划部', '0', TO_DATE('2020-03-26 16:57:06', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" VALUES ('1265476890672672773', '1265476890672672770', '[0],[1265476890651701250],[1265476890672672770],', '市场部', 'hxjt_cd_scb', '100', '华夏集团成都分公司市场部', '0', TO_DATE('2020-03-26 16:57:35', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" VALUES ('1265476890672672774', '1265476890672672770', '[0],[1265476890651701250],[1265476890672672770],', '财务部', 'hxjt_cd_cwb', '100', '华夏集团成都分公司财务部', '0', TO_DATE('2020-03-26 16:58:01', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" VALUES ('1265476890672672775', '1265476890672672773', '[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],', '市场部二部', 'hxjt_cd_scb_2b', '100', '华夏集团成都分公司市场部二部', '0', TO_DATE('2020-04-06 15:36:50', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', NULL, NULL);

-- ----------------------------
-- Table structure for SYS_POS
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS" (
  "ID" NUMBER(20,0) NOT NULL,
  "NAME" NVARCHAR2(100) NOT NULL,
  "CODE" NVARCHAR2(50) NOT NULL,
  "SORT" NUMBER(11,0) NOT NULL,
  "REMARK" NVARCHAR2(255),
  "STATUS" NUMBER(4,0) DEFAULT 0 NOT NULL,
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS"."NAME" IS '名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS"."CODE" IS '编码';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS"."SORT" IS '排序';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS"."REMARK" IS '备注';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS"."STATUS" IS '状态（字典 0正常 1停用 2删除）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS"."UPDATE_USER" IS '更新人';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS" IS '系统职位表';

-- ----------------------------
-- Records of SYS_POS
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS" VALUES ('1265476890672672787', '总经理', 'zjl', '100', '总经理职位', '0', TO_DATE('2020-03-26 19:28:54', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-06-02 21:01:04', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS" VALUES ('1265476890672672788', '副总经理', 'fzjl', '100', '副总经理职位', '0', TO_DATE('2020-03-26 19:29:57', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-11-24 11:49:37', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS" VALUES ('1265476890672672789', '部门经理', 'bmjl', '100', '部门经理职位', '0', TO_DATE('2020-03-26 19:31:49', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-11-24 11:49:48', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS" VALUES ('1265476890672672790', '工作人员', 'gzry', '100', '工作人员职位', '0', TO_DATE('2020-05-27 11:32:00', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-06-01 10:51:35', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');

-- ----------------------------
-- Table structure for SYS_ROLE
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE" (
  "ID" NUMBER(20,0) NOT NULL,
  "NAME" NVARCHAR2(100) NOT NULL,
  "CODE" NVARCHAR2(50) NOT NULL,
  "SORT" NUMBER(11,0) NOT NULL,
  "DATA_SCOPE_TYPE" NUMBER(4,0) DEFAULT 1 NOT NULL,
  "REMARK" NVARCHAR2(255),
  "STATUS" NUMBER(4,0) DEFAULT 0 NOT NULL,
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE"."ID" IS '主键id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE"."NAME" IS '名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE"."CODE" IS '编码';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE"."SORT" IS '序号';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE"."DATA_SCOPE_TYPE" IS '数据范围类型（字典 1全部数据 2本部门及以下数据 3本部门数据 4仅本人数据 5自定义数据）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE"."REMARK" IS '备注';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE"."STATUS" IS '状态（字典 0正常 1停用 2删除）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE"."UPDATE_USER" IS '更新人';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE" IS '系统角色表';

-- ----------------------------
-- Records of SYS_ROLE
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE" VALUES ('1265476890672672817', '组织架构管理员', 'ent_manager_role', '100', '2', '组织架构管理员', '0', TO_DATE('2020-04-02 19:27:26', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-12-09 23:16:09', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE" VALUES ('1265476890672672818', '权限管理员', 'auth_role', '101', '5', '权限管理员', '0', TO_DATE('2020-04-02 19:28:40', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-07-16 10:52:21', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');

-- ----------------------------
-- Table structure for SYS_ROLE_DATA_SCOPE
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" (
  "ID" NUMBER(20,0) NOT NULL,
  "ROLE_ID" NUMBER(20,0) NOT NULL,
  "ORG_ID" NUMBER(20,0) NOT NULL
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE"."ROLE_ID" IS '角色id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE"."ORG_ID" IS '机构id';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" IS '系统角色数据范围表';

-- ----------------------------
-- Records of SYS_ROLE_DATA_SCOPE
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" VALUES ('1277435908822102018', '1265476890672672818', '1265476890651701250');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" VALUES ('1277435909635796993', '1265476890672672818', '1265476890672672769');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" VALUES ('1277435910432714754', '1265476890672672818', '1265476890672672771');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" VALUES ('1277435911233826818', '1265476890672672818', '1265476890672672772');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" VALUES ('1277435912018161666', '1265476890672672818', '1265476890672672770');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" VALUES ('1277435912810885122', '1265476890672672818', '1265476890672672773');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" VALUES ('1277435913595219970', '1265476890672672818', '1265476890672672775');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" VALUES ('1277435914392137730', '1265476890672672818', '1265476890672672774');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" VALUES ('1292060127645429762', '1265476890672672819', '1265476890672672774');

-- ----------------------------
-- Table structure for SYS_ROLE_MENU
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" (
  "ID" NUMBER(20,0) NOT NULL,
  "ROLE_ID" NUMBER(20,0) NOT NULL,
  "MENU_ID" NUMBER(20,0) NOT NULL
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU"."ROLE_ID" IS '角色id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU"."MENU_ID" IS '菜单id';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" IS '系统角色菜单表';

-- ----------------------------
-- Records of SYS_ROLE_MENU
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578614542338', '1265476890672672817', '1264622039642255311');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578622930945', '1265476890672672817', '1264622039642255321');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578627125250', '1265476890672672817', '1264622039642255331');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578631319553', '1265476890672672817', '1264622039642255341');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578635513858', '1265476890672672817', '1264622039642255351');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578643902465', '1265476890672672817', '1264622039642255361');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578648096769', '1265476890672672817', '1264622039642255371');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578652291073', '1265476890672672817', '1264622039642255381');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578660679682', '1265476890672672817', '1264622039642255391');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578664873985', '1265476890672672817', '1264622039642255401');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578669068289', '1265476890672672817', '1264622039642255411');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578673262594', '1265476890672672817', '1264622039642255421');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578677456898', '1265476890672672817', '1264622039642255431');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578677456899', '1265476890672672817', '1264622039642255441');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578685845505', '1265476890672672817', '1264622039642255451');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578690039810', '1265476890672672817', '1264622039642255461');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578698428417', '1265476890672672817', '1264622039642255471');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578702622722', '1265476890672672817', '1264622039642255481');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578706817026', '1265476890672672817', '1264622039642255491');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578706817027', '1265476890672672817', '1264622039642255501');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578715205634', '1265476890672672817', '1264622039642255511');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578719399938', '1265476890672672817', '1264622039642255521');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578719399939', '1265476890672672817', '1264622039642255531');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578723594241', '1265476890672672817', '1264622039642255541');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578744565761', '1265476890672672817', '1264622039642255551');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578748760065', '1265476890672672817', '1264622039642255561');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578752954370', '1265476890672672817', '1264622039642255571');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578761342977', '1265476890672672817', '1264622039642255581');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578765537282', '1265476890672672817', '1264622039642255591');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578769731585', '1265476890672672817', '1264622039642255601');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578790703105', '1265476890672672817', '1264622039642255621');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578794897409', '1265476890672672817', '1264622039642255631');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578803286017', '1265476890672672817', '1264622039642255641');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578807480322', '1265476890672672817', '1264622039642255651');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578811674626', '1265476890672672817', '1264622039642255661');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578815868930', '1265476890672672817', '1264622039642255611');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578820063233', '1265476890672672817', '1264622039642256421');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578824257538', '1265476890672672817', '1264622039642256481');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578828451841', '1265476890672672817', '1264622039642256501');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553578832646145', '1265476890672672817', '1264622039642256511');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649833824258', '1265476890672672818', '1264622039642255671');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649838018562', '1265476890672672818', '1264622039642255681');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649842212865', '1265476890672672818', '1264622039642255691');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649846407169', '1265476890672672818', '1264622039642255701');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649850601473', '1265476890672672818', '1264622039642255711');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649850601474', '1265476890672672818', '1264622039642255721');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649854795777', '1265476890672672818', '1264622039642255731');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649858990082', '1265476890672672818', '1264622039642255741');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649863184386', '1265476890672672818', '1264622039642255751');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649867378690', '1265476890672672818', '1264622039642255761');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649871572993', '1265476890672672818', '1264622039642255771');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649875767297', '1265476890672672818', '1264622039642255781');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649879961601', '1265476890672672818', '1264622039642255791');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649884155906', '1265476890672672818', '1264622039642255801');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649888350209', '1265476890672672818', '1264622039642255811');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649892544514', '1265476890672672818', '1264622039642255821');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649892544515', '1265476890672672818', '1264622039642255831');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649896738818', '1265476890672672818', '1264622039642255841');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649900933121', '1265476890672672818', '1264622039642255851');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649905127426', '1265476890672672818', '1264622039642255881');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649909321730', '1265476890672672818', '1264622039642255891');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649909321731', '1265476890672672818', '1264622039642255901');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649913516034', '1265476890672672818', '1264622039642255911');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649917710338', '1265476890672672818', '1264622039642255921');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649921904641', '1265476890672672818', '1264622039642255931');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649926098946', '1265476890672672818', '1264622039642255941');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649930293249', '1265476890672672818', '1264622039642255951');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649934487554', '1265476890672672818', '1264622039642255861');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649938681858', '1265476890672672818', '1264622039642255871');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649942876162', '1265476890672672818', '1264622039642256421');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649947070465', '1265476890672672818', '1264622039642256481');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649951264770', '1265476890672672818', '1264622039642256501');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" VALUES ('1352553649955459074', '1265476890672672818', '1264622039642256511');

-- ----------------------------
-- Table structure for SYS_SMS
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS" (
  "ID" NUMBER(20,0) NOT NULL,
  "PHONE_NUMBERS" NVARCHAR2(200) NOT NULL,
  "VALIDATE_CODE" NVARCHAR2(255),
  "TEMPLATE_CODE" NVARCHAR2(255),
  "BIZ_ID" NVARCHAR2(255),
  "STATUS" NUMBER(4,0) NOT NULL,
  "SOURCE" NUMBER(4,0) NOT NULL,
  "INVALID_TIME" DATE,
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS"."PHONE_NUMBERS" IS '手机号';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS"."VALIDATE_CODE" IS '短信验证码';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS"."TEMPLATE_CODE" IS '短信模板ID';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS"."BIZ_ID" IS '回执id，可根据该id查询具体的发送状态';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS"."STATUS" IS '发送状态（字典 0 未发送，1 发送成功，2 发送失败，3 失效）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS"."SOURCE" IS '来源（字典 1 app， 2 pc， 3 其他）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS"."INVALID_TIME" IS '失效时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS"."UPDATE_USER" IS '更新人';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS" IS '短信信息发送表';

-- ----------------------------
-- Records of SYS_SMS
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_TIMERS
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS" (
  "ID" NUMBER(20,0) NOT NULL,
  "TIMER_NAME" NVARCHAR2(255),
  "ACTION_CLASS" NVARCHAR2(255),
  "CRON" NVARCHAR2(255),
  "JOB_STATUS" NUMBER(4,0),
  "REMARK" NVARCHAR2(1000),
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS"."ID" IS '定时器id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS"."TIMER_NAME" IS '任务名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS"."ACTION_CLASS" IS '执行任务的class的类名（实现了TimerTaskRunner接口的类的全称）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS"."CRON" IS '定时任务表达式';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS"."JOB_STATUS" IS '状态（字典 1运行  2停止）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS"."REMARK" IS '备注信息';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS"."UPDATE_USER" IS '更新人';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS" IS '定时任务';

-- ----------------------------
-- Records of SYS_TIMERS
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS" VALUES ('1288760324837851137', '定时同步缓存常量', 'vip.xiaonuo.admin.modular.timer.tasks.RefreshConstantsTaskRunner', '0 0/1 * * * ?', '1', '定时同步sys_config表的数据到缓存常量中', TO_DATE('2020-07-30 16:56:20', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-07-30 16:58:52', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS" VALUES ('1304971718170832898', '定时打印一句话', 'vip.xiaonuo.admin.modular.timer.tasks.SystemOutTaskRunner', '0 0 * * * ? *', '1', '定时打印一句话', TO_DATE('2020-09-13 10:34:37', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2020-09-23 20:37:48', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808');

-- ----------------------------
-- Table structure for SYS_USER
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER" (
  "ID" NUMBER(20,0) NOT NULL,
  "ACCOUNT" NVARCHAR2(50) NOT NULL,
  "PASSWORD" NVARCHAR2(100) NOT NULL,
  "NICK_NAME" NVARCHAR2(50),
  "NAME" NVARCHAR2(100) NOT NULL,
  "AVATAR" NUMBER(20,0),
  "BIRTHDAY" DATE,
  "SEX" NUMBER(4,0) NOT NULL,
  "EMAIL" NVARCHAR2(50),
  "PHONE" NVARCHAR2(50),
  "TEL" NVARCHAR2(50),
  "LAST_LOGIN_IP" NVARCHAR2(100),
  "LAST_LOGIN_TIME" DATE,
  "ADMIN_TYPE" NUMBER(4,0) DEFAULT 0 NOT NULL,
  "STATUS" NUMBER(4,0) DEFAULT 0 NOT NULL,
  "CREATE_TIME" DATE,
  "CREATE_USER" NUMBER(20,0),
  "UPDATE_TIME" DATE,
  "UPDATE_USER" NUMBER(20,0)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."ACCOUNT" IS '账号';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."PASSWORD" IS '密码';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."NICK_NAME" IS '昵称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."NAME" IS '姓名';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."AVATAR" IS '头像';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."BIRTHDAY" IS '生日';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."SEX" IS '性别(字典 1男 2女 3未知)';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."EMAIL" IS '邮箱';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."PHONE" IS '手机';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."TEL" IS '电话';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."LAST_LOGIN_IP" IS '最后登陆IP';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."LAST_LOGIN_TIME" IS '最后登陆时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."ADMIN_TYPE" IS '管理员类型（0超级管理员 1非管理员）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."STATUS" IS '状态（字典 0正常 1冻结 2删除）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."CREATE_USER" IS '创建人';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER"."UPDATE_USER" IS '更新人';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER" IS '系统用户表';

-- ----------------------------
-- Records of SYS_USER
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER" VALUES ('1265476890672672808', 'superAdmin', '$2a$09$PiCiFNspSlTBE9CakVs8ZOqx0xa03X9wOm01gMasHch4929TpEWCC', '超级管理员', '超级管理员', NULL, TO_DATE('2020-03-18 00:00:00', 'SYYYY-MM-DD HH24:MI:SS'), '1', '<EMAIL>', '15228937093', '1234567890', '110.184.43.91', TO_DATE('2021-01-22 17:50:10', 'SYYYY-MM-DD HH24:MI:SS'), '1', '0', TO_DATE('2020-05-29 16:39:28', 'SYYYY-MM-DD HH24:MI:SS'), '-1', TO_DATE('2021-01-22 17:50:10', 'SYYYY-MM-DD HH24:MI:SS'), '-1');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER" VALUES ('1275735541155614721', 'yubaoshan', '$2a$10$rWns4Vzwlz4/ELVHsgFz1.JfZ93FXMjC2DMIFQLTO6kTIDOSJWpy2', 'Await', '俞宝山', NULL, TO_DATE('1992-10-03 00:00:00', 'SYYYY-MM-DD HH24:MI:SS'), '1', '<EMAIL>', '18200001102', NULL, '110.184.43.91', TO_DATE('2021-01-22 17:49:51', 'SYYYY-MM-DD HH24:MI:SS'), '2', '0', TO_DATE('2020-06-24 18:20:30', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-01-22 17:49:51', 'SYYYY-MM-DD HH24:MI:SS'), '-1');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER" VALUES ('1280709549107552257', 'xuyuxiang', '$2a$09$PiCiFNspSlTBE9CakVs8ZOqx0xa03X9wOm01gMasHch4929TpEWCC', '就是那个锅', '徐玉祥', NULL, TO_DATE('2020-07-01 00:00:00', 'SYYYY-MM-DD HH24:MI:SS'), '1', NULL, '18200001100', NULL, '110.184.43.91', TO_DATE('2021-01-22 17:48:40', 'SYYYY-MM-DD HH24:MI:SS'), '2', '0', TO_DATE('2020-07-08 11:45:26', 'SYYYY-MM-DD HH24:MI:SS'), '1265476890672672808', TO_DATE('2021-01-22 17:48:40', 'SYYYY-MM-DD HH24:MI:SS'), '-1');

-- ----------------------------
-- Table structure for SYS_USER_DATA_SCOPE
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE" (
  "ID" NUMBER(20,0) NOT NULL,
  "USER_ID" NUMBER(20,0) NOT NULL,
  "ORG_ID" NUMBER(20,0) NOT NULL
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE"."USER_ID" IS '用户id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE"."ORG_ID" IS '机构id';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE" IS '系统用户数据范围表';

-- ----------------------------
-- Records of SYS_USER_DATA_SCOPE
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE" VALUES ('1277459951742840834', '1266277099455635457', '1265476890672672770');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE" VALUES ('1277459952577507330', '1266277099455635457', '1265476890672672773');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE" VALUES ('1277459953424756737', '1266277099455635457', '1265476890672672775');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE" VALUES ('1277459954267811841', '1266277099455635457', '1265476890672672774');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE" VALUES ('1280712071570366466', '1275735541155614721', '1265476890672672769');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE" VALUES ('1280712071570366467', '1275735541155614721', '1265476890672672771');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE" VALUES ('1280712071578755074', '1275735541155614721', '1265476890672672772');

-- ----------------------------
-- Table structure for SYS_USER_ROLE
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_ROLE";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_ROLE" (
  "ID" NUMBER(20,0) NOT NULL,
  "USER_ID" NUMBER(20,0) NOT NULL,
  "ROLE_ID" NUMBER(20,0) NOT NULL
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_ROLE"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_ROLE"."USER_ID" IS '用户id';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_ROLE"."ROLE_ID" IS '角色id';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_ROLE" IS '系统用户角色表';

-- ----------------------------
-- Records of SYS_USER_ROLE
-- ----------------------------
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_ROLE" VALUES ('1283596900713574402', '1275735541155614721', '1265476890672672817');
INSERT INTO "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_ROLE" VALUES ('1283596949627547649', '1280709549107552257', '1265476890672672818');

-- ----------------------------
-- Table structure for SYS_VIS_LOG
-- ----------------------------
DROP TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG";
CREATE TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG" (
  "ID" NUMBER(20,0) NOT NULL,
  "NAME" NVARCHAR2(50),
  "SUCCESS" NCHAR(1),
  "MESSAGE" NCLOB,
  "IP" NVARCHAR2(255),
  "LOCATION" NVARCHAR2(255),
  "BROWSER" NVARCHAR2(255),
  "OS" NVARCHAR2(255),
  "VIS_TYPE" NUMBER(4,0) NOT NULL,
  "VIS_TIME" DATE,
  "ACCOUNT" NVARCHAR2(50)
)
TABLESPACE "SYSTEM"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG"."ID" IS '主键';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG"."NAME" IS '名称';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG"."SUCCESS" IS '是否执行成功（Y-是，N-否）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG"."MESSAGE" IS '具体消息';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG"."IP" IS 'ip';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG"."LOCATION" IS '地址';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG"."BROWSER" IS '浏览器';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG"."OS" IS '操作系统';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG"."VIS_TYPE" IS '操作类型（字典 1登入 2登出）';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG"."VIS_TIME" IS '访问时间';
COMMENT ON COLUMN "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG"."ACCOUNT" IS '访问账号';
COMMENT ON TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG" IS '系统访问日志表';

-- ----------------------------
-- Records of SYS_VIS_LOG
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table SYS_APP
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP" ADD CONSTRAINT "SYS_C008696" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_APP
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP" ADD CONSTRAINT "SYS_C008569" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP" ADD CONSTRAINT "SYS_C008571" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP" ADD CONSTRAINT "SYS_C008575" CHECK ("CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_APP" ADD CONSTRAINT "SYS_C008578" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_AREA
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ADD CONSTRAINT "SYS_C008693" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_AREA
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ADD CONSTRAINT "SYS_C008567" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ADD CONSTRAINT "SYS_C008572" CHECK ("LEVEL_CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ADD CONSTRAINT "SYS_C008574" CHECK ("PARENT_CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ADD CONSTRAINT "SYS_C008577" CHECK ("AREA_CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ADD CONSTRAINT "SYS_C008580" CHECK ("ZIP_CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ADD CONSTRAINT "SYS_C008582" CHECK ("CITY_CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ADD CONSTRAINT "SYS_C008584" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ADD CONSTRAINT "SYS_C008585" CHECK ("SHORT_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ADD CONSTRAINT "SYS_C008586" CHECK ("MERGER_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ADD CONSTRAINT "SYS_C008587" CHECK ("PINYIN" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ADD CONSTRAINT "SYS_C008588" CHECK ("LNG" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ADD CONSTRAINT "SYS_C008589" CHECK ("LAT" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table SYS_AREA
-- ----------------------------
CREATE INDEX "SNOWY-LAYUI-PUB-ORACLE"."IDX_PARENT_CODE"
  ON "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ("PARENT_CODE" ASC)
  LOGGING
  TABLESPACE "SYSTEM"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
);
CREATE UNIQUE INDEX "SNOWY-LAYUI-PUB-ORACLE"."UK_CODE"
  ON "SNOWY-LAYUI-PUB-ORACLE"."SYS_AREA" ("AREA_CODE" ASC)
  LOGGING
  TABLESPACE "SYSTEM"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table SYS_CODE_GENERATE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE" ADD CONSTRAINT "SYS_C008695" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_CODE_GENERATE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE" ADD CONSTRAINT "SYS_C008570" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE" ADD CONSTRAINT "SYS_C008573" CHECK ("AUTHOR_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE" ADD CONSTRAINT "SYS_C008576" CHECK ("CLASS_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE" ADD CONSTRAINT "SYS_C008579" CHECK ("TABLE_PREFIX" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE" ADD CONSTRAINT "SYS_C008581" CHECK ("GENERATE_TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE" ADD CONSTRAINT "SYS_C008583" CHECK ("TABLE_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_CODE_GENERATE_CONFIG
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG" ADD CONSTRAINT "SYS_C008694" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_CODE_GENERATE_CONFIG
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CODE_GENERATE_CONFIG" ADD CONSTRAINT "SYS_C008568" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_CONFIG
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" ADD CONSTRAINT "SYS_C008706" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_CONFIG
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" ADD CONSTRAINT "SYS_C008590" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" ADD CONSTRAINT "SYS_C008591" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" ADD CONSTRAINT "SYS_C008592" CHECK ("CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" ADD CONSTRAINT "SYS_C008593" CHECK ("VALUE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" ADD CONSTRAINT "SYS_C008594" CHECK ("SYS_FLAG" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" ADD CONSTRAINT "SYS_C008595" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_CONFIG" ADD CONSTRAINT "SYS_C008596" CHECK ("GROUP_CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_DICT_DATA
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" ADD CONSTRAINT "SYS_C008721" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_DICT_DATA
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" ADD CONSTRAINT "SYS_C008687" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" ADD CONSTRAINT "SYS_C008688" CHECK ("TYPE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" ADD CONSTRAINT "SYS_C008689" CHECK ("VALUE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" ADD CONSTRAINT "SYS_C008690" CHECK ("CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" ADD CONSTRAINT "SYS_C008691" CHECK ("SORT" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_DATA" ADD CONSTRAINT "SYS_C008692" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_DICT_TYPE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" ADD CONSTRAINT "SYS_C008701" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_DICT_TYPE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" ADD CONSTRAINT "SYS_C008611" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" ADD CONSTRAINT "SYS_C008612" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" ADD CONSTRAINT "SYS_C008613" CHECK ("CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" ADD CONSTRAINT "SYS_C008614" CHECK ("SORT" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_DICT_TYPE" ADD CONSTRAINT "SYS_C008615" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_EMP
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP" ADD CONSTRAINT "SYS_C008697" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_EMP
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP" ADD CONSTRAINT "SYS_C008597" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP" ADD CONSTRAINT "SYS_C008598" CHECK ("ORG_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP" ADD CONSTRAINT "SYS_C008599" CHECK ("ORG_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_EMP_EXT_ORG_POS
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS" ADD CONSTRAINT "SYS_C008698" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_EMP_EXT_ORG_POS
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS" ADD CONSTRAINT "SYS_C008600" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS" ADD CONSTRAINT "SYS_C008601" CHECK ("EMP_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS" ADD CONSTRAINT "SYS_C008602" CHECK ("ORG_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_EXT_ORG_POS" ADD CONSTRAINT "SYS_C008603" CHECK ("POS_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_EMP_POS
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS" ADD CONSTRAINT "SYS_C008699" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_EMP_POS
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS" ADD CONSTRAINT "SYS_C008604" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS" ADD CONSTRAINT "SYS_C008605" CHECK ("EMP_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_EMP_POS" ADD CONSTRAINT "SYS_C008606" CHECK ("POS_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_FILE_INFO
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO" ADD CONSTRAINT "SYS_C008700" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_FILE_INFO
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO" ADD CONSTRAINT "SYS_C008607" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO" ADD CONSTRAINT "SYS_C008608" CHECK ("FILE_LOCATION" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO" ADD CONSTRAINT "SYS_C008609" CHECK ("FILE_ORIGIN_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_FILE_INFO" ADD CONSTRAINT "SYS_C008610" CHECK ("FILE_OBJECT_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_MENU
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" ADD CONSTRAINT "SYS_C008713" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_MENU
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" ADD CONSTRAINT "SYS_C008617" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" ADD CONSTRAINT "SYS_C008618" CHECK ("PID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" ADD CONSTRAINT "SYS_C008619" CHECK ("PIDS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" ADD CONSTRAINT "SYS_C008620" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" ADD CONSTRAINT "SYS_C008621" CHECK ("CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" ADD CONSTRAINT "SYS_C008622" CHECK ("TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" ADD CONSTRAINT "SYS_C008623" CHECK ("APPLICATION" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" ADD CONSTRAINT "SYS_C008624" CHECK ("SORT" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MENU" ADD CONSTRAINT "SYS_C008625" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_MESSAGE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE" ADD CONSTRAINT "SYS_C008702" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_MESSAGE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE" ADD CONSTRAINT "SYS_C008616" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_MESSAGE_USER
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER" ADD CONSTRAINT "SYS_C008703" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_MESSAGE_USER
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER" ADD CONSTRAINT "SYS_C008626" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER" ADD CONSTRAINT "SYS_C008627" CHECK ("MESSAGE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER" ADD CONSTRAINT "SYS_C008628" CHECK ("SENDER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER" ADD CONSTRAINT "SYS_C008629" CHECK ("RECEIVER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_MESSAGE_USER" ADD CONSTRAINT "SYS_C008630" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_NOTICE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE" ADD CONSTRAINT "SYS_C008704" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_NOTICE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE" ADD CONSTRAINT "SYS_C008631" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE" ADD CONSTRAINT "SYS_C008632" CHECK ("TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE" ADD CONSTRAINT "SYS_C008633" CHECK ("PUBLIC_USER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE" ADD CONSTRAINT "SYS_C008634" CHECK ("PUBLIC_USER_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE" ADD CONSTRAINT "SYS_C008635" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_NOTICE_USER
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE_USER" ADD CONSTRAINT "SYS_C008705" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_NOTICE_USER
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE_USER" ADD CONSTRAINT "SYS_C008636" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE_USER" ADD CONSTRAINT "SYS_C008637" CHECK ("NOTICE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE_USER" ADD CONSTRAINT "SYS_C008638" CHECK ("USER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_NOTICE_USER" ADD CONSTRAINT "SYS_C008639" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_OAUTH_USER
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER" ADD CONSTRAINT "SYS_C008707" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_OAUTH_USER
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER" ADD CONSTRAINT "SYS_C008640" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_OAUTH_USER" ADD CONSTRAINT "SYS_C008641" CHECK ("UUID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_OP_LOG
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG" ADD CONSTRAINT "SYS_C008708" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_OP_LOG
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_OP_LOG" ADD CONSTRAINT "SYS_C008642" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_ORG
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" ADD CONSTRAINT "SYS_C008709" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_ORG
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" ADD CONSTRAINT "SYS_C008643" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" ADD CONSTRAINT "SYS_C008644" CHECK ("PID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" ADD CONSTRAINT "SYS_C008645" CHECK ("PIDS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" ADD CONSTRAINT "SYS_C008646" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" ADD CONSTRAINT "SYS_C008647" CHECK ("CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" ADD CONSTRAINT "SYS_C008648" CHECK ("SORT" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ORG" ADD CONSTRAINT "SYS_C008649" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_POS
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS" ADD CONSTRAINT "SYS_C008710" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_POS
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS" ADD CONSTRAINT "SYS_C008650" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS" ADD CONSTRAINT "SYS_C008651" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS" ADD CONSTRAINT "SYS_C008652" CHECK ("CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS" ADD CONSTRAINT "SYS_C008653" CHECK ("SORT" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS" ADD CONSTRAINT "SYS_C008654" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table SYS_POS
-- ----------------------------
CREATE UNIQUE INDEX "SNOWY-LAYUI-PUB-ORACLE"."CODE_UNI"
  ON "SNOWY-LAYUI-PUB-ORACLE"."SYS_POS" ("CODE" ASC)
  LOGGING
  TABLESPACE "SYSTEM"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  FREELISTS 1
  FREELIST GROUPS 1
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table SYS_ROLE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE" ADD CONSTRAINT "SYS_C008711" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_ROLE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE" ADD CONSTRAINT "SYS_C008655" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE" ADD CONSTRAINT "SYS_C008656" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE" ADD CONSTRAINT "SYS_C008657" CHECK ("CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE" ADD CONSTRAINT "SYS_C008658" CHECK ("SORT" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE" ADD CONSTRAINT "SYS_C008659" CHECK ("DATA_SCOPE_TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE" ADD CONSTRAINT "SYS_C008660" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_ROLE_DATA_SCOPE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" ADD CONSTRAINT "SYS_C008712" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_ROLE_DATA_SCOPE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" ADD CONSTRAINT "SYS_C008661" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" ADD CONSTRAINT "SYS_C008662" CHECK ("ROLE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_DATA_SCOPE" ADD CONSTRAINT "SYS_C008663" CHECK ("ORG_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_ROLE_MENU
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" ADD CONSTRAINT "SYS_C008716" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_ROLE_MENU
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" ADD CONSTRAINT "SYS_C008664" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" ADD CONSTRAINT "SYS_C008665" CHECK ("ROLE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_ROLE_MENU" ADD CONSTRAINT "SYS_C008666" CHECK ("MENU_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_SMS
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS" ADD CONSTRAINT "SYS_C008714" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_SMS
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS" ADD CONSTRAINT "SYS_C008667" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS" ADD CONSTRAINT "SYS_C008668" CHECK ("PHONE_NUMBERS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS" ADD CONSTRAINT "SYS_C008669" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_SMS" ADD CONSTRAINT "SYS_C008670" CHECK ("SOURCE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_TIMERS
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS" ADD CONSTRAINT "SYS_C008715" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_TIMERS
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_TIMERS" ADD CONSTRAINT "SYS_C008671" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_USER
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER" ADD CONSTRAINT "SYS_C008717" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_USER
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER" ADD CONSTRAINT "SYS_C008672" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER" ADD CONSTRAINT "SYS_C008673" CHECK ("ACCOUNT" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER" ADD CONSTRAINT "SYS_C008674" CHECK ("PASSWORD" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER" ADD CONSTRAINT "SYS_C008675" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER" ADD CONSTRAINT "SYS_C008676" CHECK ("SEX" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER" ADD CONSTRAINT "SYS_C008677" CHECK ("ADMIN_TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER" ADD CONSTRAINT "SYS_C008678" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_USER_DATA_SCOPE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE" ADD CONSTRAINT "SYS_C008719" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_USER_DATA_SCOPE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE" ADD CONSTRAINT "SYS_C008679" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE" ADD CONSTRAINT "SYS_C008680" CHECK ("USER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_DATA_SCOPE" ADD CONSTRAINT "SYS_C008681" CHECK ("ORG_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_USER_ROLE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_ROLE" ADD CONSTRAINT "SYS_C008718" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_USER_ROLE
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_ROLE" ADD CONSTRAINT "SYS_C008682" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_ROLE" ADD CONSTRAINT "SYS_C008683" CHECK ("USER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_USER_ROLE" ADD CONSTRAINT "SYS_C008684" CHECK ("ROLE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_VIS_LOG
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG" ADD CONSTRAINT "SYS_C008720" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table SYS_VIS_LOG
-- ----------------------------
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG" ADD CONSTRAINT "SYS_C008685" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SNOWY-LAYUI-PUB-ORACLE"."SYS_VIS_LOG" ADD CONSTRAINT "SYS_C008686" CHECK ("VIS_TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
