package com.binance.api.client.domain.event;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TickerEvent {
    @JsonProperty("e")
    private String eventType;
    @JsonProperty("E")
    private long eventTime;
    @JsonProperty("s")
    private String symbol;
    private String h;
    private String l;
    private String c;
    private String q;// 24小时内成交额
    private String v;// 24小时内成交量
    private String o; // 整整24小时前，向后数的第一次成交价格
    //24小时价格变化(百分比)
    @JsonProperty("P")
    private String cp;
    //24小时价格变化
    @JsonProperty("p")
    private String p;
    // 整整24小时之前，向前数的最后一次成交价格
    private String x;
}
