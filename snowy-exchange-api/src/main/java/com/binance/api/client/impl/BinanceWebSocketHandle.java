package com.binance.api.client.impl;

import java.io.Closeable;

/**
 * <AUTHOR>
 * @since 2018/10/23 19:38
 */
public abstract class BinanceWebSocketHandle implements Closeable {

    private BinanceApiWebSocketListener webSocketListener;

    public BinanceWebSocketHandle(BinanceApiWebSocketListener webSocketListener) {
        this.webSocketListener = webSocketListener;
    }

    public boolean isClosed(){
        return webSocketListener.isClosed();
    }
}
