package aacoin.trade.exchange.rest.dto;

import java.math.BigDecimal;
import java.util.Date;

public class OrderTradeDTO {

    private String id;
    private Date transTime;
    private BigDecimal tradePrice;
    private BigDecimal tradeQuantity;
    private String orderId;
    private String externalSymbol;

    public String getExternalSymbol() {
        return externalSymbol;
    }

    public void setExternalSymbol(String externalSymbol) {
        this.externalSymbol = externalSymbol;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getTransTime() {
        return transTime;
    }

    public void setTransTime(Date transTime) {
        this.transTime = transTime;
    }

    public BigDecimal getTradePrice() {
        return tradePrice;
    }

    public void setTradePrice(BigDecimal tradePrice) {
        this.tradePrice = tradePrice;
    }

    public BigDecimal getTradeQuantity() {
        return tradeQuantity;
    }

    public void setTradeQuantity(BigDecimal tradeQuantity) {
        this.tradeQuantity = tradeQuantity;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
