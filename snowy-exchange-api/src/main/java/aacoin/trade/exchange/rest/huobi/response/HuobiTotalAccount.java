package aacoin.trade.exchange.rest.huobi.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class HuobiTotalAccount {
    /**
     * id : 100009
     * type : spot
     * state : working
     * user-id : 1000
     */

    private int id;
    private String type;
    private String state;
    @JsonProperty("user_id")
    private int userid;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public int getUserid() {
        return userid;
    }

    public void setUserid(int userid) {
        this.userid = userid;
    }
}
