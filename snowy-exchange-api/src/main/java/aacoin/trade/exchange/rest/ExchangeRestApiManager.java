package aacoin.trade.exchange.rest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class ExchangeRestApiManager {
    @Autowired
    private List<AbstractExchangeRestApi> restApiList;
    private Map<String, AbstractExchangeRestApi> restApiMap = new HashMap<>();

    public ExchangeRestApi getExchangeApi(String exchangeCode) {
        AbstractExchangeRestApi abstractExchangeApi = restApiMap.get(exchangeCode);
        if (abstractExchangeApi == null) {
            abstractExchangeApi = restApiList.stream().filter(e -> {
                Exchange annotation = e.getClass().getAnnotation(Exchange.class);
                return annotation != null && annotation.value().equalsIgnoreCase(exchangeCode);
            }).findFirst().orElse(null);
            if(abstractExchangeApi != null){
                restApiMap.put(exchangeCode, abstractExchangeApi);
            }
        }
        Objects.requireNonNull(abstractExchangeApi, "获取不到交易所" + exchangeCode);
        return abstractExchangeApi;
    }
}
