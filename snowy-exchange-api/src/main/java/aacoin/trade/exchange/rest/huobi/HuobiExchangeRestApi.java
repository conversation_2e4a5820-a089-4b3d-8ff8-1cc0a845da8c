package aacoin.trade.exchange.rest.huobi;


import aacoin.trade.exchange.CallFailedException;
import aacoin.trade.exchange.rest.*;
import aacoin.trade.exchange.rest.dto.OrderDetailDTO;
import aacoin.trade.exchange.rest.dto.OrderTradeDTO;
import aacoin.trade.exchange.rest.huobi.response.*;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import vip.xiaonuo.core.enums.KLineType;
import vip.xiaonuo.core.pojo.dto.KLineDepthsDTO;
import vip.xiaonuo.core.pojo.dto.TradeDTO;
import vip.xiaonuo.core.util.MathUtil;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Exchange(Const.EXCHANGE_CODE_HUOBI)
public class HuobiExchangeRestApi extends AbstractExchangeRestApi {

    private static final Logger logger = LoggerFactory.getLogger(HuobiExchangeRestApi.class);

    private ExternalConfig externalConfig;

    private Integer accountId;


    @Override
    public String getExchangeCode() {
        return Const.EXCHANGE_CODE_HUOBI;
    }

    public HuobiExchangeRestApi(ExternalConfig externalConfig) {
        this.externalConfig = externalConfig;
    }

    private ExternalConfig.ExchangeApiConfig getApiConfig() {
        return externalConfig.getApiConfig(Const.EXCHANGE_CODE_HUOBI);
    }

    private HuobiPrivateExchangeApi getHuobiPrivateExchangeApi() {
        ExternalConfig.ExchangeApiConfig apiConfig = getApiConfig();
        OkHttpClient privateOkHttpClient = OkHttpClientFactory.builder(Const.SET_PROXY)
                .addInterceptor(new RequestInterceptor(
                        new HuobiSignHandler(apiConfig.getAccessKey(), apiConfig.getSecretKey())))
                .build();

        Retrofit.Builder builder = new Retrofit.Builder();
        Retrofit retrofit = builder.baseUrl(Const.HUOBI_USER_DATA_REST_URL)
                .addConverterFactory(GsonConverterFactory.create())
                .client(privateOkHttpClient)
                .build();
        return retrofit.create(HuobiPrivateExchangeApi.class);
    }

    private HuobiPublicApiService getHuobiPublicApiService() {
        OkHttpClient okHttpClient = OkHttpClientFactory.builder(Const.SET_PROXY)
                .addInterceptor(new RequestInterceptor(
                        new HuobiSignHandler(null, null))).build();

        Retrofit.Builder builder = new Retrofit.Builder();
        Retrofit retrofit = builder.baseUrl(Const.HUOBI_MARKET_DATA_REST_URL)
                .addConverterFactory(GsonConverterFactory.create())
                .client(okHttpClient)
                .build();
        return retrofit.create(HuobiPublicApiService.class);
    }

    private final Object lock = new Object();

    private Integer getAccountId() throws IOException {
        if (accountId == null) {
            synchronized (lock) {
                if (accountId == null) {
                    HuobiBaseResponse<List<HuobiTotalAccount>> baseResponse = getHuobiPrivateExchangeApi().getTotalAccount().execute().body();
                    checkException(baseResponse);
                    HuobiTotalAccount totalAccount = baseResponse.getData().get(0);
                    accountId = totalAccount.getId();
                }
            }
        }
        return accountId;
    }

    @Override
    public List<List<BigDecimal>> getKLines(String currencyCode, String baseCurrencyCode, KLineType kLineType, Integer limit) {

        String value = KLinePeriodMapping.getHuobiPeriod(kLineType);

        if (value == null) {
            throw new CallFailedException("火币交易所KLine不存在" + kLineType.getCode());
        }

        HuobiBaseResponse<List<HuobiKLine>> huobiBaseResponse;
        try {
            huobiBaseResponse = getHuobiPublicApiService().kline((currencyCode + baseCurrencyCode).toLowerCase(),
                    value, limit == null ? 1000 : limit).execute().body();
        } catch (IOException e) {
            logger.error("火币获取k线数据异常", e);
            throw new CallFailedException(e);
        }
        checkException(huobiBaseResponse);
        return huobiBaseResponse.getData()
                .stream()
                .map(huobiKLine -> {
                    BigDecimal ts = new BigDecimal(huobiKLine.getId() * 1000);
                    BigDecimal open = scale8(new BigDecimal(huobiKLine.getOpen()));
                    BigDecimal close = scale8(new BigDecimal(huobiKLine.getClose()));
                    BigDecimal low = scale8(new BigDecimal(huobiKLine.getLow()));
                    BigDecimal high = scale8(new BigDecimal(huobiKLine.getHigh()));
                    BigDecimal vol = scale8(new BigDecimal(huobiKLine.getAmount()));
                    return Arrays.asList(ts, scale8(open), scale8(high), scale8(low), scale8(close), scale8(vol));
                }).collect(Collectors.toList());
    }

    @Override
    public List<TradeDTO> getRecentTrades(String currencyCode, String baseCurrencyCode, Integer limit) {
        try {
            HuobiBaseResponse<List<HuobiTrade>> baseResponse =
                    getHuobiPublicApiService().trade((currencyCode + baseCurrencyCode).toLowerCase(), limit == null ? 50 : limit).execute().body();
            checkException(baseResponse);
            return baseResponse.getData().stream()
                    .map(huobiTrade -> {
                        HuobiTrade.DataBean dataBean = huobiTrade.getData().get(0);
                        TradeDTO tradeDTO = new TradeDTO();
                        tradeDTO.setPriceDecimal(6);
                        tradeDTO.setQuantityDecimal(4);
                        tradeDTO.setQuantity(new BigDecimal(dataBean.getAmount()));
                        tradeDTO.setPrice(new BigDecimal(dataBean.getPrice()));
                        tradeDTO.setTransTime(Long.parseLong(dataBean.getTs()));
                        tradeDTO.setMainMarket(dataBean.getDirection());
                        return tradeDTO;
                    }).collect(Collectors.toList());
        } catch (IOException e) {
            logger.error("火币获取成交失败", e);
            throw new CallFailedException(e);
        }
    }

    @Override
    public KLineDepthsDTO getOrderDepthData(String currencyCode, String baseCurrencyCode) {

        HuobiDepthResponse queryDepth;
        try {
            queryDepth = getHuobiPublicApiService().queryDepth((currencyCode + baseCurrencyCode).toLowerCase(), "step0").execute().body();
        } catch (IOException e) {
            logger.error("火币请求OrderDepth异常", e);
            throw new CallFailedException(e);
        }
        checkException(queryDepth);

        KLineDepthsDTO kLineDepthsDTO = new KLineDepthsDTO();
        kLineDepthsDTO.setAsks(queryDepth.getTick().getAsks().stream().map(strings -> strings.stream().map(BigDecimal::new).toArray(BigDecimal[]::new)).collect(Collectors.toList()));
        kLineDepthsDTO.setBids(queryDepth.getTick().getBids().stream().map(strings -> strings.stream().map(BigDecimal::new).toArray(BigDecimal[]::new)).collect(Collectors.toList()));

        return kLineDepthsDTO;
    }

    @Override
    public String placeOrder(String currencyCode, String baseCurrencyCode, BigDecimal price, BigDecimal quantity, OrderSide orderSide) {
        try {
            HuobiBaseResponse<String> baseResponse = getHuobiPrivateExchangeApi().doOrder(String.valueOf(getAccountId()),
                    quantity.toPlainString(), price.toPlainString(), (currencyCode + baseCurrencyCode).toLowerCase(),
                    orderSide == OrderSide.BUY ? "buy-limit" : "sell-limit").execute().body();
            checkException(baseResponse);
            return baseResponse.getData();
        } catch (IOException e) {
            logger.error("火币下单失败", e);
            throw new CallFailedException(e);
        }
    }

    @Override
    public void cancelOrder(String currencyCode, String baseCurrencyCode, String orderId) {
        try {
            HuobiBaseResponse<String> body = getHuobiPrivateExchangeApi().cancelOrder(orderId).execute().body();
            checkException(body);
        } catch (IOException e) {
            logger.error("火币订单撤销失败", e);
            throw new CallFailedException(e);
        }
    }

    @Override
    public List<OrderTradeDTO> getOrderTradeDetail(String orderId) {
        try {
            HuobiBaseResponse<List<HuobiOrderTradeDetail>> body = getHuobiPrivateExchangeApi().queryOrderTradeDetail(orderId).execute().body();
            checkException(body);
            return body.getData().stream().map(this::convertToTradeDTO).collect(Collectors.toList());
        } catch (IOException e) {
            logger.error("火币获取订单成交详情失败:" + orderId, e);
            throw new CallFailedException(e);
        }
    }

    private OrderTradeDTO convertToTradeDTO(HuobiOrderTradeDetail e) {
        OrderTradeDTO orderTradeDTO = new OrderTradeDTO();
        orderTradeDTO.setId(String.valueOf(e.getMatchid()));
        orderTradeDTO.setOrderId(String.valueOf(e.getOrderid()));
        orderTradeDTO.setTradePrice(new BigDecimal(e.getPrice()));
        orderTradeDTO.setTradeQuantity(new BigDecimal(e.getFilledamount()));
        orderTradeDTO.setTransTime(new Date(e.getCreatedat()));
        orderTradeDTO.setExternalSymbol(e.getSymbol());
        return orderTradeDTO;
    }

    @Override
    public List<OrderTradeDTO> getMarketTradeDetail(String currencyCode, String baseCurrencyCode, Date startDate, Date endDate) {
        try {
            HuobiBaseResponse<List<HuobiOrderTradeDetail>> body = getHuobiPrivateExchangeApi().queryMarketTradeDetail().execute().body();
            checkException(body);
            return body.getData().stream().map(this::convertToTradeDTO).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("火币获取交易对成交详情失败:", e);
            throw new CallFailedException(e);
        }
    }

    @Override
    public OrderDetailDTO getOrderDetail(String currencyCode, String baseCurrencyCode, String orderId) {
        try {
            HuobiBaseResponse<HuobiOrderDetail> baseResponse = getHuobiPrivateExchangeApi().queryOrderDetail(orderId).execute().body();
            checkException(baseResponse);
            HuobiOrderDetail data = baseResponse.getData();

            OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
            orderDetailDTO.setOrderId(String.valueOf(data.getId()));
            orderDetailDTO.setCreatedTime(new Date(data.getCreatedat()));
            String order_state = data.getState();
            orderDetailDTO.setStatus(OrderDetailDTO.STATUS_OPEN);
            if ("partial-canceled".equalsIgnoreCase(order_state) || "canceled".equalsIgnoreCase(order_state)) {
                //撤单
                orderDetailDTO.setStatus(OrderDetailDTO.STATUS_CANCELLED);
            }

            String type = data.getType();
            orderDetailDTO.setOrderType(StringUtils.startsWithIgnoreCase(type, "buy") ? "buy" : "sell");
            orderDetailDTO.setFilledQuantity(new BigDecimal(data.getFieldamount()));
            orderDetailDTO.setPrice(new BigDecimal(data.getPrice()));
            orderDetailDTO.setQuantity(new BigDecimal(data.getAmount()));
            orderDetailDTO.setExternalSymbol(data.getSymbol());
            return orderDetailDTO;
        } catch (IOException e) {
            logger.error("火币获取订单详情失败:" + orderId, e);
            throw new CallFailedException(e);
        }
    }

    private static void checkException(HuobiBaseResponse<?> baseResponse) {
        if (baseResponse == null) {
            throw new CallFailedException("火币结果返回错误");
        }
        if (!"ok".equals(baseResponse.getStatus())) {
            throw new CallFailedException(baseResponse.getErrMsg());
        }
    }

    private static BigDecimal scale8(BigDecimal value) {
        value = value == null ? new BigDecimal(0) : value;
        return MathUtil.stripZeros(value.setScale(8, RoundingMode.FLOOR));
    }
}
