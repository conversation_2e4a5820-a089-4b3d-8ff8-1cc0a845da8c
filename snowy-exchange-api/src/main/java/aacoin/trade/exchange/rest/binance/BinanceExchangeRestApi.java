package aacoin.trade.exchange.rest.binance;

import aacoin.trade.exchange.rest.*;
import aacoin.trade.exchange.rest.dto.OrderDetailDTO;
import aacoin.trade.exchange.rest.dto.OrderTradeDTO;
import com.binance.api.client.BinanceApiClientFactory;
import com.binance.api.client.BinanceApiRestClient;
import com.binance.api.client.domain.OrderStatus;
import com.binance.api.client.domain.TimeInForce;
import com.binance.api.client.domain.account.NewOrder;
import com.binance.api.client.domain.account.NewOrderResponse;
import com.binance.api.client.domain.account.Order;
import com.binance.api.client.domain.account.Trade;
import com.binance.api.client.domain.account.request.CancelOrderRequest;
import com.binance.api.client.domain.account.request.OrderStatusRequest;
import com.binance.api.client.domain.general.ExchangeInfo;
import com.binance.api.client.domain.market.AggTrade;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.OrderBook;
import org.apache.commons.lang3.math.NumberUtils;
import vip.xiaonuo.core.enums.KLineType;
import vip.xiaonuo.core.pojo.dto.KLineDepthsDTO;
import vip.xiaonuo.core.pojo.dto.TradeDTO;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Exchange(Const.EXCHANGE_CODE_BINANCE)
public class BinanceExchangeRestApi extends AbstractExchangeRestApi {

    private ExternalConfig externalConfig;
//    @Autowired
//    private RateLimitChecker rateLimitChecker;

    @Override
    public String getExchangeCode() {
        return Const.EXCHANGE_CODE_BINANCE;
    }

    public BinanceExchangeRestApi(ExternalConfig externalConfig) {
        this.externalConfig = externalConfig;
    }

    private BinanceApiRestClient getBinanceApiRestClient() {
        ExternalConfig.ExchangeApiConfig binanceConfig = externalConfig.getApiConfig(Const.EXCHANGE_CODE_BINANCE);
        BinanceApiClientFactory binanceApiClientFactory = BinanceApiClientFactory.newInstance(
                binanceConfig.getAccessKey(), binanceConfig.getSecretKey());
        return binanceApiClientFactory.newRestClient();
    }

    public ExchangeInfo getExchangeInfo() {
        return getBinanceApiRestClient().getExchangeInfo();
    }

    @Override
    public List<List<BigDecimal>> getKLines(String currencyCode, String baseCurrencyCode, KLineType kLineType, Integer limit) {
        List<Candlestick> candlestickBars = getBinanceApiRestClient().getCandlestickBars(symbol(currencyCode, baseCurrencyCode),
                KLinePeriodMapping.getBinancePeriod(kLineType), limit, null, null);
        candlestickBars = candlestickBars == null ? new ArrayList<>() : candlestickBars;
        return candlestickBars.stream().map(e -> Arrays.asList(BigDecimal.valueOf(e.getOpenTime()),
                NumberUtils.createBigDecimal(e.getOpen()),
                NumberUtils.createBigDecimal(e.getHigh()),
                NumberUtils.createBigDecimal(e.getLow()),
                NumberUtils.createBigDecimal(e.getClose()),
                NumberUtils.createBigDecimal(e.getVolume()))).collect(Collectors.toList());
    }

    private static String symbol(String currencyCode, String baseCurrencyCode) {
        currencyCode = "bch".equalsIgnoreCase(currencyCode) ? "bcc" : currencyCode;
        return (currencyCode + baseCurrencyCode).toUpperCase();
    }

    @Override
    public List<TradeDTO> getRecentTrades(String currencyCode, String baseCurrencyCode, Integer limit) {
        List<AggTrade> trades = getBinanceApiRestClient().getAggTrades(symbol(currencyCode, baseCurrencyCode));
        trades = trades == null ? new ArrayList<>() : trades;

        List<TradeDTO> tradeDTOS = trades.stream().map(e -> {
            TradeDTO tradeDTO = new TradeDTO();
            tradeDTO.setQuantity(new BigDecimal(e.getQuantity()));
            tradeDTO.setPrice(new BigDecimal(e.getPrice()));
            tradeDTO.setTransTime(e.getTradeTime());
            tradeDTO.setMainMarket(e.isBuyerMaker() ? "sell" : "buy");
            return tradeDTO;
        }).collect(Collectors.toList());
        Collections.reverse(tradeDTOS);

        return tradeDTOS;
    }

    @Override
    public KLineDepthsDTO getOrderDepthData(String currencyCode, String baseCurrencyCode) {
        OrderBook orderBook = getBinanceApiRestClient().getOrderBook(symbol(currencyCode, baseCurrencyCode), 100);
        KLineDepthsDTO depthsDTO = new KLineDepthsDTO();
        depthsDTO.setAsks(orderBook.getAsks().stream().map(e ->
                new BigDecimal[]{NumberUtils.createBigDecimal(e.getPrice()),
                        NumberUtils.createBigDecimal(e.getQty())}).collect(Collectors.toList()));
        depthsDTO.setBids(orderBook.getBids().stream().map(e ->
                new BigDecimal[]{NumberUtils.createBigDecimal(e.getPrice()),
                        NumberUtils.createBigDecimal(e.getQty())}).collect(Collectors.toList()));
        return depthsDTO;
    }

    @Override
    public String placeOrder(String currencyCode, String baseCurrencyCode, BigDecimal price, BigDecimal quantity, OrderSide orderSide) {
//        rateLimitChecker.limit(RateLimitChecker.LimitPath.BINANCE_ORDER);
        NewOrder newOrder = new NewOrder(symbol(currencyCode, baseCurrencyCode), orderSide == OrderSide.BUY ?
                com.binance.api.client.domain.OrderSide.BUY : com.binance.api.client.domain.OrderSide.SELL,
                com.binance.api.client.domain.OrderType.LIMIT, TimeInForce.GTC, quantity.toPlainString(), price.toPlainString());
        NewOrderResponse newOrderResponse = getBinanceApiRestClient().newOrder(newOrder);
        return newOrderResponse.getOrderId().toString();
    }

    @Override
    public List<OrderTradeDTO> getMarketTradeDetail(String currencyCode, String baseCurrencyCode, Date startDate, Date endDate) {
        List<Trade> myTrades = getBinanceApiRestClient().getMyTrades(symbol(currencyCode, baseCurrencyCode),
                startDate == null ? null : startDate.getTime(), endDate == null ? null : endDate.getTime(), null);
        myTrades = myTrades == null ? new ArrayList<>() : myTrades;
        return myTrades.stream().map(e -> {
            OrderTradeDTO orderTradeDTO = new OrderTradeDTO();
            orderTradeDTO.setId(String.valueOf(e.getId()));
            orderTradeDTO.setOrderId(String.valueOf(e.getOrderId()));
            orderTradeDTO.setTradePrice(new BigDecimal(e.getPrice()));
            orderTradeDTO.setTradeQuantity(new BigDecimal(e.getQty()));
            orderTradeDTO.setTransTime(new Date(e.getTime()));
            orderTradeDTO.setExternalSymbol(e.getSymbol());
            return orderTradeDTO;

        }).collect(Collectors.toList());
    }

    @Override
    public void cancelOrder(String currencyCode, String baseCurrencyCode, String orderId) {
        CancelOrderRequest request = new CancelOrderRequest(symbol(currencyCode, baseCurrencyCode), Long.parseLong(orderId));
        getBinanceApiRestClient().cancelOrder(request);
    }

    @Override
    public List<OrderTradeDTO> getOrderTradeDetail(String orderId) {
        throw new UnsupportedOperationException();
    }

    @Override
    public OrderDetailDTO getOrderDetail(String currencyCode, String baseCurrencyCode, String orderId) {
        OrderStatusRequest orderStatusRequest = new OrderStatusRequest(symbol(currencyCode, baseCurrencyCode), Long.parseLong(orderId));
        Order orderStatus = getBinanceApiRestClient().getOrderStatus(orderStatusRequest);
        OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
        orderDetailDTO.setQuantity(new BigDecimal(orderStatus.getOrigQty()));
        orderDetailDTO.setPrice(new BigDecimal(orderStatus.getPrice()));
        orderDetailDTO.setOrderId(orderId);
        orderDetailDTO.setCreatedTime(orderDetailDTO.getCreatedTime());
        orderDetailDTO.setStatus(OrderDetailDTO.STATUS_OPEN);
        if (orderStatus.getStatus() == OrderStatus.CANCELED) {
            //撤单
            orderDetailDTO.setStatus(OrderDetailDTO.STATUS_CANCELLED);
        }
        com.binance.api.client.domain.OrderSide orderSide = orderStatus.getSide();
        orderDetailDTO.setOrderType(orderSide == com.binance.api.client.domain.OrderSide.BUY ? "buy" : "sell");
        orderDetailDTO.setFilledQuantity(new BigDecimal(orderStatus.getExecutedQty()));
        orderDetailDTO.setExternalSymbol(orderStatus.getSymbol());
        return orderDetailDTO;
    }
}
