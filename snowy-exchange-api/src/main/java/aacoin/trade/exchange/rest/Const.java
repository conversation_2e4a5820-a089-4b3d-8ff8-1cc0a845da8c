package aacoin.trade.exchange.rest;

public final class Const {
    public static final String API_REQUIRE_SIGN_NAME = "API_REQUIRE_SIGN";
    public static final String API_REQUIRE_SIGN_VALUE = "1";
    public static final String API_REQUIRE_SIGN = API_REQUIRE_SIGN_NAME + ": " + API_REQUIRE_SIGN_VALUE;
    public static Boolean SET_PROXY = true;

    public static final String HUOBI_USER_DATA_REST_URL = "https://api.huobi.pro/v1/";
    public static final String HUOBI_MARKET_DATA_REST_URL = "https://api.huobi.pro/market/";
    public static final String HUOBI_USER_DATA_WS_URL = "wss://api.huobi.br.com:443/ws/v1";
    public static final String HUOBI_MARKET_DATA_WS_URL = "wss://api.huobi.br.com/ws";
    public static final String BINANCE_MARKET_DATA_WS_URL = "wss://stream.binance.com:9443/stream?streams=%s";


    public static final String EXCHANGE_CODE_BINANCE = "binance";
    public static final String EXCHANGE_CODE_HUOBI = "huobi";

}
