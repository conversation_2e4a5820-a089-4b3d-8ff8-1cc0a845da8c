package aacoin.trade.exchange.rest;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2018/9/29 10:09
 */
@Component
@ConfigurationProperties(prefix = "external")
public class ExternalConfig {
    private Boolean proxy;
    private List<ExchangeApiConfig> apiConfigs;

    public Boolean getProxy() {
        return proxy;
    }

    public void setProxy(Boolean proxy) {
        if (proxy != null) {
            Const.SET_PROXY = proxy;
        }
        this.proxy = proxy;
    }

    public List<ExchangeApiConfig> getApiConfigs() {
        return apiConfigs;
    }

    public void setApiConfigs(List<ExchangeApiConfig> apiConfigs) {
        this.apiConfigs = apiConfigs;
    }

    public ExchangeApiConfig getApiConfig(String exchangeCode) {
        if (!StringUtils.hasText(exchangeCode)) {
            throw new RuntimeException("exchange code is mandatory");
        }
        if (CollectionUtils.isEmpty(apiConfigs)) {
            throw new RuntimeException("no api config found for " + exchangeCode);
        }
        Optional<ExchangeApiConfig> apiConfig = apiConfigs.stream().filter(e -> exchangeCode.equalsIgnoreCase(e.getExchangeCode())).findFirst();
        if (!apiConfig.isPresent()) {
            throw new RuntimeException("no qualified api config for " + exchangeCode);
        }
        return apiConfig.get();
    }

    public static class ExchangeApiConfig {
        private String exchangeCode;
        private String restUrl;
        private String accessKey;
        private String secretKey;

        public String getExchangeCode() {
            return exchangeCode;
        }

        public void setExchangeCode(String exchangeCode) {
            this.exchangeCode = exchangeCode;
        }

        public String getAccessKey() {
            return accessKey;
        }

        public void setAccessKey(String accessKey) {
            this.accessKey = accessKey;
        }

        public String getSecretKey() {
            return secretKey;
        }

        public void setSecretKey(String secretKey) {
            this.secretKey = secretKey;
        }

        public String getRestUrl() {
            return restUrl;
        }

        public void setRestUrl(String restUrl) {
            this.restUrl = restUrl;
        }
    }


}
