package aacoin.trade.exchange.rest;


import aacoin.trade.exchange.rest.dto.OrderDetailDTO;
import aacoin.trade.exchange.rest.dto.OrderTradeDTO;
import vip.xiaonuo.core.enums.KLineType;
import vip.xiaonuo.core.pojo.dto.KLineDepthsDTO;
import vip.xiaonuo.core.pojo.dto.TradeDTO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface ExchangeRestApi {

    String getExchangeCode();

    List<List<BigDecimal>> getKLines(String currencyCode, String baseCurrencyCode, KLineType kLineType, Integer limit);

    List<TradeDTO> getRecentTrades(String currencyCode, String baseCurrencyCode, Integer limit);

    KLineDepthsDTO getOrderDepthData(String currencyCode, String baseCurrencyCode);

    String placeOrder(String currencyCode, String baseCurrencyCode, BigDecimal price, BigDecimal quantity, OrderSide orderSide);

    void cancelOrder(String currencyCode, String baseCurrencyCode, String orderId);

    List<OrderTradeDTO> getOrderTradeDetail(String orderId);

    List<OrderTradeDTO> getMarketTradeDetail(String currencyCode, String baseCurrencyCode, Date startDate, Date endDate);

    OrderDetailDTO getOrderDetail(String currencyCode, String baseCurrencyCode, String orderId);
}
