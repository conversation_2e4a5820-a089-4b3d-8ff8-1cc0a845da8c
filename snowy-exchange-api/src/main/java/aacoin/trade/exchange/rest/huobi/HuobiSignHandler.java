package aacoin.trade.exchange.rest.huobi;

import aacoin.trade.exchange.rest.PrivateRequestSignHandler;
import aacoin.trade.exchange.util.Hmac;
import aacoin.trade.exchange.util.SignatureUtil;
import com.alibaba.fastjson.JSON;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HuobiSignHandler implements PrivateRequestSignHandler {

    private String accessKey;
    private String secretKey;

    public HuobiSignHandler(String accessKey, String secretKey) {
        this.accessKey = StringUtils.defaultString(accessKey, "");
        this.secretKey = StringUtils.defaultString(secretKey, "");
    }

    public Request processSign(Request oldRequest) {
        Request newRequest;
        String oldUrl = oldRequest.url().toString();

        Map<String, String> parameters = new HashMap<>();


        StringBuilder sb = new StringBuilder();
        String path;
        String baseUrl;
        try {
            URL url = new URL(oldUrl);
            path = url.getPath();
            baseUrl = url.getHost();
        } catch (MalformedURLException e) {
            path = "";
            baseUrl = "";
        }
        String utcTimeStr = SignatureUtil.encodeUTF8(SignatureUtil.getUTCTimeStr());
        sb.append(oldRequest.method().toUpperCase())
                .append('\n')
                .append(baseUrl)
                .append('\n')
                .append(path)
                .append('\n');

        parameters.put("Timestamp", utcTimeStr);
        parameters.put("AccessKeyId", accessKey);
        parameters.put("SignatureMethod", "HmacSHA256");
        parameters.put("SignatureVersion", String.valueOf(2));

        if ("get".equalsIgnoreCase(oldRequest.method())) {
            Map<String, String> stringListMap = SignatureUtil.sortMap(parameters);
            String signContent = stringListMap.entrySet()
                    .stream()
                    .map(item -> {
                        if ("Timestamp".equals(item.getKey())) {
                            return item.getKey() + "=" + item.getValue() + "&";
                        } else {
                            return item.getKey() + "=" + SignatureUtil.encodeUTF8(item.getValue()) + "&";
                        }
                    })
                    .reduce((o1, o2) -> o1 + o2)
                    .get();
            signContent = signContent.substring(0, signContent.length() - 1);
            byte[] data = Hmac.encodeHmacSHA256((sb.toString() + signContent).getBytes(SignatureUtil.CHAR_SET_UTF8), secretKey.getBytes(SignatureUtil.CHAR_SET_UTF8));
            String sign = Base64.getEncoder().encodeToString(data);
            sign = SignatureUtil.encodeUTF8(sign);
            String newUrl = addUrlParameter(oldUrl, "Timestamp", utcTimeStr);
            sb.delete(0, sb.length());
            sb.append(newUrl)
                    .append("&AccessKeyId=").append(accessKey)
                    .append("&SignatureMethod=").append("HmacSHA256")
                    .append("&SignatureVersion=").append(2)
                    .append("&Signature=").append(sign);
            parameters.put("Timestamp", SignatureUtil.decodeUTF8(parameters.get("Timestamp")));

            HttpUrl parse = HttpUrl.parse(sb.toString());
            Request.Builder builder = oldRequest.newBuilder();
            addCommonMessage(oldRequest, builder);
            newRequest = builder.url(parse).build();
            return newRequest;
        } else {

            Map<String, String> stringStringMap = SignatureUtil.sortMap(parameters);
            String signContent = stringStringMap.entrySet()
                    .stream()
                    .map(item -> {
                        if ("Timestamp".equals(item.getKey())) {
                            return item.getKey() + "=" + item.getValue() + "&";
                        } else {
                            return item.getKey() + "=" + SignatureUtil.encodeUTF8(item.getValue()) + "&";
                        }
                    })
                    .reduce((o1, o2) -> o1 + o2)
                    .get();
            signContent = signContent.substring(0, signContent.length() - 1);
            System.out.println("post :" + sb.toString() + signContent);
            byte[] data = Hmac.encodeHmacSHA256((sb.toString() + signContent).getBytes(SignatureUtil.CHAR_SET_UTF8), secretKey.getBytes(SignatureUtil.CHAR_SET_UTF8));
            String sign = Base64.getEncoder().encodeToString(data);
            parameters.put("Signature", sign);
            parameters.put("Timestamp", SignatureUtil.decodeUTF8(parameters.get("Timestamp")));
            int size = oldRequest.url().querySize();
            HttpUrl.Builder newUrlBuild = oldRequest.url().newBuilder();

            for (int i = 0; i < size; i++) {
                newUrlBuild.removeAllQueryParameters(oldRequest.url().queryParameterName(i));
            }

            parameters.forEach(newUrlBuild::addQueryParameter);

            Request.Builder requestBuilder = oldRequest.newBuilder();
            addCommonMessage(oldRequest, requestBuilder);


            if (size > 0) {
                MultiValueMap<String, String> maps = new LinkedMultiValueMap<>();
                HttpUrl url = oldRequest.url();
                for (int i = 0; i < size; i++) {
                    String key = url.queryParameterName(i);
                    maps.add(key, url.queryParameterValue(i));
                }

                Map<String, Object> maps2 = new HashMap<>();

                for (Map.Entry<String, List<String>> en : maps.entrySet()) {
                    maps2.put(en.getKey(), en.getValue().size() > 1 ? en.getValue() : en.getValue().get(0));
                }
                String json = JSON.toJSONString(maps2);
                requestBuilder.method(oldRequest.method(), RequestBody.create(oldRequest.body().contentType(), json));
            }

            newRequest = requestBuilder
                    .url(newUrlBuild.build())
                    .build();
            return newRequest;
        }
    }

    private void addCommonMessage(Request request, Request.Builder builder) {
        if (request.method().equalsIgnoreCase("get")) {
            builder.addHeader("Content-Type", "application/x-www-form-urlencoded");
        } else if (request.method().equalsIgnoreCase("post")) {
            builder.addHeader("Content-Type", "application/json");
        }
    }

    private String addUrlParameter(String url, String key, String value) {
        StringBuilder sb = new StringBuilder(url.length() + 10);
        sb.append(url);
        if (!url.contains("?")) {
            sb.append("?");
        } else {
            sb.append("&");
        }
        sb.append(key)
                .append("=")
                .append(value);
        return sb.toString();
    }

}
