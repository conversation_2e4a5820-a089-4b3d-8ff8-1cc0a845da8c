package aacoin.trade.exchange.rest;

import com.binance.api.client.domain.market.CandlestickInterval;
import vip.xiaonuo.core.enums.KLineType;

import java.util.*;

public class KLinePeriodMapping {

    private static LinkedHashMap<CandlestickInterval, KLineType> binancePeriodMapping = new LinkedHashMap<>();
    private static LinkedHashMap<String, KLineType> huobiPeriodMapping = new LinkedHashMap<>();

    static {
        binancePeriodMapping.put(CandlestickInterval.ONE_MINUTE, KLineType.M1);
        binancePeriodMapping.put(CandlestickInterval.FIVE_MINUTES, KLineType.M5);
        binancePeriodMapping.put(CandlestickInterval.FIFTEEN_MINUTES, KLineType.M15);
        binancePeriodMapping.put(CandlestickInterval.HALF_HOURLY, KLineType.M30);

        binancePeriodMapping.put(CandlestickInterval.HOURLY, KLineType.H1);
        binancePeriodMapping.put(CandlestickInterval.TWO_HOURLY, KLineType.H2);
        binancePeriodMapping.put(CandlestickInterval.FOUR_HOURLY, KLineType.H4);
        binancePeriodMapping.put(CandlestickInterval.SIX_HOURLY, KLineType.H6);
        binancePeriodMapping.put(CandlestickInterval.TWELVE_HOURLY, KLineType.H12);
        binancePeriodMapping.put(CandlestickInterval.DAILY, KLineType.D1);
        binancePeriodMapping.put(CandlestickInterval.WEEKLY, KLineType.W1);

        huobiPeriodMapping.put("1min", KLineType.M1);
        huobiPeriodMapping.put("5min", KLineType.M5);
        huobiPeriodMapping.put("15min", KLineType.M15);
        huobiPeriodMapping.put("30min", KLineType.M30);

        huobiPeriodMapping.put("60min", KLineType.H1);
        huobiPeriodMapping.put("4hour", KLineType.H4);
        huobiPeriodMapping.put("1day", KLineType.D1);
        huobiPeriodMapping.put("1week", KLineType.W1);
    }

    public static Map<CandlestickInterval, KLineType> getBinancePeriodMapping() {
        return Collections.unmodifiableMap(binancePeriodMapping);
    }

    public static Map<String, KLineType> getHuobiPeriodMapping() {
        return Collections.unmodifiableMap(huobiPeriodMapping);
    }

    public static KLineType getLocalKLinePeriod(String huobiPeriod) {
        return huobiPeriodMapping.get(huobiPeriod);
    }

    public static String getHuobiPeriod(KLineType kLineType) {
        Map.Entry<String, KLineType> entry = huobiPeriodMapping.entrySet().stream()
                .filter(e -> kLineType == e.getValue()).findFirst().orElse(null);
        return entry == null ? null : entry.getKey();
    }

    public static KLineType getLocalKLinePeriod(CandlestickInterval binancePeriod) {
        return binancePeriodMapping.get(binancePeriod);
    }

    public static CandlestickInterval getBinancePeriod(KLineType kLineType) {
        Map.Entry<CandlestickInterval, KLineType> entry = binancePeriodMapping.entrySet().stream()
                .filter(e -> kLineType == e.getValue()).findFirst().orElse(null);
        return entry == null ? null : entry.getKey();
    }

    public static List<KLineType> getSupportedKLinePeriods(String exchangeCode) {
        if (Const.EXCHANGE_CODE_HUOBI.equalsIgnoreCase(exchangeCode)) {
            return new ArrayList<>(huobiPeriodMapping.values());
        } else if (Const.EXCHANGE_CODE_BINANCE.equalsIgnoreCase(exchangeCode)) {
            return new ArrayList<>(binancePeriodMapping.values());
        }
        return new ArrayList<>();
    }

}
