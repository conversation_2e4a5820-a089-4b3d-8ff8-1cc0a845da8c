package aacoin.trade.exchange.rest.huobi;

import aacoin.trade.exchange.rest.Const;
import aacoin.trade.exchange.rest.huobi.response.HuobiBaseResponse;
import aacoin.trade.exchange.rest.huobi.response.HuobiOrderDetail;
import aacoin.trade.exchange.rest.huobi.response.HuobiOrderTradeDetail;
import aacoin.trade.exchange.rest.huobi.response.HuobiTotalAccount;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.List;

public interface HuobiPrivateExchangeApi {

    @POST("order/orders/{order-id}/submitcancel")
    @Headers(Const.API_REQUIRE_SIGN)
    Call<HuobiBaseResponse<String>> cancelOrder(@Path("order-id") String orderId);

    @GET("account/accounts")
    @Headers(Const.API_REQUIRE_SIGN)
    Call<HuobiBaseResponse<List<HuobiTotalAccount>>> getTotalAccount();

    @POST("order/orders/place")
    @Headers(Const.API_REQUIRE_SIGN)
    Call<HuobiBaseResponse<String>> doOrder(@Query("account-id") String id, @Query("amount") String amount, @Query("price") String price, @Query("symbol") String symbol, @Query("type") String type);

    @GET("order/orders/{order-id}/matchresults")
    @Headers(Const.API_REQUIRE_SIGN)
    Call<HuobiBaseResponse<List<HuobiOrderTradeDetail>>> queryOrderTradeDetail(@Path("order-id") String orderId);

    @GET("order/matchresults")
    @Headers(Const.API_REQUIRE_SIGN)
    Call<HuobiBaseResponse<List<HuobiOrderTradeDetail>>> queryMarketTradeDetail();

    @GET("order/orders/{order-id}")
    @Headers(Const.API_REQUIRE_SIGN)
    Call<HuobiBaseResponse<HuobiOrderDetail>> queryOrderDetail(@Path("order-id") String orderId);

}
