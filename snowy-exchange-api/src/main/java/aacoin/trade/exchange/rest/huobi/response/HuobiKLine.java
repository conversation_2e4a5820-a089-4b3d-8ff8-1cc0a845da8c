package aacoin.trade.exchange.rest.huobi.response;

public class HuobiKLine {

    /**
     * id : 1499184000
     * amount : 37593.0266
     * count : 0
     * open : 1935.2
     * close : 1879.0
     * low : 1856.0
     * high : 1940.0
     * vol : 7.1031537978665E7
     */

    private Long id;
    private String amount;
    private Long count;
    private String open;
    private String close;
    private String low;
    private String high;
    private String vol;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public String getOpen() {
        return open;
    }

    public void setOpen(String open) {
        this.open = open;
    }

    public String getClose() {
        return close;
    }

    public void setClose(String close) {
        this.close = close;
    }

    public String getLow() {
        return low;
    }

    public void setLow(String low) {
        this.low = low;
    }

    public String getHigh() {
        return high;
    }

    public void setHigh(String high) {
        this.high = high;
    }

    public String getVol() {
        return vol;
    }

    public void setVol(String vol) {
        this.vol = vol;
    }
}
