package aacoin.trade.exchange.rest.huobi.response;

import com.google.gson.annotations.SerializedName;

public class HuobiOrderDetail {


    /**
     * id : 59378
     * symbol : ethusdt
     * account-id : 100009
     * amount : 10.**********
     * price : 100.**********
     * created-at : *************
     * type : buy-limit
     * field-amount : 10.**********
     * field-cash-amount : 1011.**********
     * field-fees : 0.**********
     * finished-at : *************
     * user-id : 1000
     * source : api
     * state : filled
     * canceled-at : 0
     * exchange : huobi
     * batch :
     */

    private long id;
    private String symbol;
    @SerializedName("account-id")
    private long accountid;
    private String amount;
    private String price;
    @SerializedName("created-at")
    private long createdat;
    private String type;
    @SerializedName("field-amount")
    private String fieldamount;
    @SerializedName("field-cash-amount")
    private String fieldcashamount;
    @SerializedName("field-fees")
    private String fieldfees;
    @SerializedName("finished-at")
    private long finishedat;
    @SerializedName("user-id")
    private long userid;
    private String source;
    private String state;
    @SerializedName("canceled-at")
    private long canceledat;
    private String exchange;
    private String batch;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public long getAccountid() {
        return accountid;
    }

    public void setAccountid(long accountid) {
        this.accountid = accountid;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public long getCreatedat() {
        return createdat;
    }

    public void setCreatedat(long createdat) {
        this.createdat = createdat;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFieldamount() {
        return fieldamount;
    }

    public void setFieldamount(String fieldamount) {
        this.fieldamount = fieldamount;
    }

    public String getFieldcashamount() {
        return fieldcashamount;
    }

    public void setFieldcashamount(String fieldcashamount) {
        this.fieldcashamount = fieldcashamount;
    }

    public String getFieldfees() {
        return fieldfees;
    }

    public void setFieldfees(String fieldfees) {
        this.fieldfees = fieldfees;
    }

    public long getFinishedat() {
        return finishedat;
    }

    public void setFinishedat(long finishedat) {
        this.finishedat = finishedat;
    }

    public long getUserid() {
        return userid;
    }

    public void setUserid(long userid) {
        this.userid = userid;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public long getCanceledat() {
        return canceledat;
    }

    public void setCanceledat(long canceledat) {
        this.canceledat = canceledat;
    }

    public String getExchange() {
        return exchange;
    }

    public void setExchange(String exchange) {
        this.exchange = exchange;
    }

    public String getBatch() {
        return batch;
    }

    public void setBatch(String batch) {
        this.batch = batch;
    }
}
