package aacoin.trade.exchange.rest;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;

public class RequestInterceptor implements Interceptor {

    private final PrivateRequestSignHandler privateRequestSignHandler;
//    private RateLimitChecker rateLimitChecker;

    public RequestInterceptor(PrivateRequestSignHandler privateRequestSignHandler/*, RateLimitChecker rateLimitChecker*/) {
        this.privateRequestSignHandler = privateRequestSignHandler;
//        this.rateLimitChecker = rateLimitChecker;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Request newRequest;
//        rateLimitChecker.limit(RateLimitChecker.LimitPath.HUOBI);
        if (Const.API_REQUIRE_SIGN_VALUE.equals(request.header(Const.API_REQUIRE_SIGN_NAME))) {
            newRequest = privateRequestSignHandler.processSign(request);
        } else {
            newRequest = request;
        }
        return chain.proceed(newRequest.newBuilder().removeHeader(Const.API_REQUIRE_SIGN_NAME).build());
    }
}
