package aacoin.trade.exchange.rest.huobi;

import aacoin.trade.exchange.rest.huobi.response.HuobiBaseResponse;
import aacoin.trade.exchange.rest.huobi.response.HuobiDepthResponse;
import aacoin.trade.exchange.rest.huobi.response.HuobiKLine;
import aacoin.trade.exchange.rest.huobi.response.HuobiTrade;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

import java.util.List;

public interface HuobiPublicApiService {

    @GET("history/kline")
    Call<HuobiBaseResponse<List<HuobiKLine>>> kline(@Query("symbol") String symbol, @Query("period") String period, @Query("size") int size);

    @GET("depth")
    Call<HuobiDepthResponse> queryDepth(@Query("symbol") String symbol, @Query("type") String type);

    @GET("history/trade")
    Call<HuobiBaseResponse<List<HuobiTrade>>> trade(@Query("symbol") String symbol, @Query("size") int size);


}
