package aacoin.trade.exchange.rest;

import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.net.Proxy;

public class OkHttpClientFactory {

    public static OkHttpClient.Builder builder(boolean isSetProxy) {
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor(new HttpRequestLogger());
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.NONE);
        OkHttpClient.Builder builder = new OkHttpClient.Builder().addNetworkInterceptor(httpLoggingInterceptor);
        if (!isSetProxy) {
            return builder;
        }

        return builder
                .proxy(new Proxy(Proxy.Type.HTTP, InetSocketAddress.createUnresolved("localhost", 7890)));
    }

    private static class HttpRequestLogger implements HttpLoggingInterceptor.Logger {
        private static final Logger logger = LoggerFactory.getLogger(HttpRequestLogger.class);

        @Override
        public void log(String s) {
            logger.info(s);
        }
    }
}
