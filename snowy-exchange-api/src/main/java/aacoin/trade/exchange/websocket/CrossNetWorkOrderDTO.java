package aacoin.trade.exchange.websocket;

import com.alibaba.fastjson.annotation.JSONField;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

/**
 * 跨网订单
 */
public class CrossNetWorkOrderDTO {
    public static final String TYPE_CANCEL = "cancel";
    public static final String TYPE_EXCHANGE = "exchange";

    private Integer messageId;

    private String currency;
    private String baseCurrency;

    private String messageType;

    /**
     * 外部推送过来的消息id
     */
    private String externalMessageId;
    //订单ID
    private String externalOrderId;
    //订单数量
    private String orderQuantity;
    private String orderPrice;
    //成交数量
    private String lastFilledQuantity;
    //成交价格
    private String lastFilledPrice;
    //未成交数量
    private String remainQuantity;

    private String orderType;

    private String transactionSide;

    private String source;

    @JSONField(serialize = false)
    private String status;
    @JSONField(serialize = false)
    private Date createdTime;
    @JSONField(serialize = false)
    private Date updatedTime;
    @JSONField(serialize = false)
    private String createdBy;

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getTransactionSide() {
        return transactionSide;
    }

    public void setTransactionSide(String transactionSide) {
        this.transactionSide = transactionSide;
    }

    public String getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(String orderPrice) {
        this.orderPrice = orderPrice;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public String getExternalMessageId() {
        return externalMessageId;
    }

    public void setExternalMessageId(String externalMessageId) {
        this.externalMessageId = externalMessageId;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getBaseCurrency() {
        return baseCurrency;
    }

    public void setBaseCurrency(String baseCurrency) {
        this.baseCurrency = baseCurrency;
    }

    public String getExternalOrderId() {
        return externalOrderId;
    }

    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    public String getOrderQuantity() {
        return orderQuantity;
    }

    public void setOrderQuantity(String orderQuantity) {
        this.orderQuantity = orderQuantity;
    }

    public String getLastFilledQuantity() {
        return lastFilledQuantity;
    }

    public void setLastFilledQuantity(String lastFilledQuantity) {
        this.lastFilledQuantity = lastFilledQuantity;
    }

    public String getLastFilledPrice() {
        return lastFilledPrice;
    }

    public void setLastFilledPrice(String lastFilledPrice) {
        this.lastFilledPrice = lastFilledPrice;
    }

    public String getRemainQuantity() {
        return remainQuantity;
    }

    public void setRemainQuantity(String remainQuantity) {
        this.remainQuantity = remainQuantity;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
