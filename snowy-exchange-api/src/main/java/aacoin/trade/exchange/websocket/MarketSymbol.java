package aacoin.trade.exchange.websocket;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2018/9/29 17:08
 */
public class MarketSymbol {

    private String currency;

    private String baseCurrency;

    public MarketSymbol(String currency, String baseCurrency) {
        this.currency = currency;
        this.baseCurrency = baseCurrency;
    }

    public String getCurrency() {
        return currency;
    }

    public String getBaseCurrency() {
        return baseCurrency;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MarketSymbol that = (MarketSymbol) o;
        return Objects.equals(currency, that.currency) &&
                Objects.equals(baseCurrency, that.baseCurrency);
    }

    @Override
    public int hashCode() {

        return Objects.hash(currency, baseCurrency);
    }

    @Override
    public String toString() {
        return currency + "_" + baseCurrency;
    }
}
