package aacoin.trade.exchange.util;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class SignatureUtil {

    public static final Charset CHAR_SET_UTF8 = Charset.forName("UTF-8");

    /**
     * Map 按 K 排序
     *
     * @param maps
     * @param <K>  需要实现Comparable接口
     * @param <V>
     * @return
     */
    public static <K extends Comparable<K>, V> Map<K, V> sortMap(Map<K, V> maps) {
        Set<Map.Entry<K, V>> entries = maps.entrySet();
        ArrayList<Map.Entry<K, V>> lists = new ArrayList<>(entries);
        lists.sort(Comparator.comparing(Map.Entry::getKey));
        Map<K, V> sortMap = new LinkedHashMap<>(lists.size());
        lists.forEach(item -> sortMap.put(item.getKey(), item.getValue()));
        return sortMap;
    }

    public static String decodeUTF8(String content) {
        try {
            return URLDecoder.decode(content, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            // ignore
            return "";
        }
    }
    public static String encodeUTF8(String content) {
        try {
            return URLEncoder.encode(content, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            // ignore
            return "";
        }
    }

    public static String getUTCTimeStr() {
        return Instant.ofEpochSecond(Instant.now().getEpochSecond()).atZone(ZONE_GMT).format(DT_FORMAT);
    }

    private static final DateTimeFormatter DT_FORMAT = DateTimeFormatter.ofPattern("uuuu-MM-dd'T'HH:mm:ss");
    private static final ZoneId ZONE_GMT = ZoneId.of("Z");
}
