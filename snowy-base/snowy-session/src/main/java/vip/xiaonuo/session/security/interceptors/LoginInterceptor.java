package vip.xiaonuo.session.security.interceptors;

import vip.xiaonuo.session.security.UserSession;
import vip.xiaonuo.core.util.MessageUtils;
import vip.xiaonuo.session.utils.PrincipalUtils;


import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class LoginInterceptor extends AbstractInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        UserSession userSession = (UserSession) request.getAttribute(PrincipalUtils.Const.USER_SESSION);
        if (userSession == null) {
            requestLogin(request, response);
            return false;
        }
        return true;
    }

    private void requestLogin(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        writeJson(response, 401, MessageUtils.get("player.login.nologin"));
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        super.afterCompletion(request, response, handler, ex);
    }


}
