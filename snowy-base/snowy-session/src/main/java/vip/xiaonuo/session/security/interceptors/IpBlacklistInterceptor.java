package vip.xiaonuo.session.security.interceptors;

import cn.hutool.extra.servlet.ServletUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public abstract class IpBlacklistInterceptor extends AbstractInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String clientIP = ServletUtil.getClientIP(request);
        return !hasBlacklist(clientIP);
    }

    public abstract boolean hasBlacklist(String clientIP);
}
