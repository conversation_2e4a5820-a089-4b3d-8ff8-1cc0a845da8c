package vip.xiaonuo.session.security.interceptors;


import com.alibaba.fastjson.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

abstract class AbstractInterceptor extends HandlerInterceptorAdapter {

    void writeJson(HttpServletResponse response, int code, String message) {
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        response.setStatus(code);
        try {
            JSONObject jo = new JSONObject();
            jo.put("code", code);
            jo.put("message", message);
            response.getWriter().print(jo.toJSONString());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
