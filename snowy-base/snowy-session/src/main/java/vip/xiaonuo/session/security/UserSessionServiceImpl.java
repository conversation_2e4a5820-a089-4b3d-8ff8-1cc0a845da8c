package vip.xiaonuo.session.security;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import vip.xiaonuo.core.util.DeviceType;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class UserSessionServiceImpl implements UserSessionService {
    private Logger logger = LoggerFactory.getLogger(getClass());
    public static final String SESSION_KEY = "W_SESSION:";
    private static final String SESSION_KEY_PREFIX = SESSION_KEY + "%s:%s";
    private static final String SESSION_USER_KEY_PREFIX = "W_SESSION_USER:%s";

    private final ValueOperations<String, String> valueOperations;
    private final StringRedisTemplate stringRedisTemplate;
    private final HashOperations<String, Object, Object> hashOperations;

    //默认86400秒  24小时
    @Value("${session.expire-time-second:86400}")
    private Integer sessionExpireTime;

    @Autowired
    public UserSessionServiceImpl(StringRedisTemplate stringRedisTemplate) {
        valueOperations = stringRedisTemplate.opsForValue();
        hashOperations = stringRedisTemplate.opsForHash();
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @Override
    public UserSession queryUserSessionByClientUniqueId(DeviceType type, String clientUniqueId) {
        String json = valueOperations.get(sessionKey(type, clientUniqueId));
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JSON.parseObject(json, UserSession.class);
    }

    public List<UserSession> getOnlineUserSessionList() {
        List<UserSession> userSessionList = new ArrayList<>();
        Set<String> sessionKeySet = stringRedisTemplate.keys(SESSION_KEY + "*");
        if (CollectionUtil.isEmpty(sessionKeySet)) {
            return userSessionList;
        }
        List<String> jsonList = valueOperations.multiGet(sessionKeySet);
        if (CollectionUtil.isEmpty(jsonList)) {
            return userSessionList;
        }
        for (String json : jsonList) {
            userSessionList.add(JSON.parseObject(json, UserSession.class));
        }
        return userSessionList;
    }

    @Override
    public void updateUserSession(DeviceType type, String clientUniqueId, UserSession userSession) {
        saveUserSession(type, clientUniqueId, userSession);
    }

    public boolean checkLogout(Long playerId) {
        boolean isLogout = true;
        String sessionUserKey = sessionUserKey(playerId);
        Map<Object, Object> values = hashOperations.entries(sessionUserKey);
        if (MapUtil.isEmpty(values)) {
            return true;
        }

        for (Map.Entry<Object, Object> entry : values.entrySet()) {
            String deviceType = (String) entry.getKey();
            String clientId = (String) entry.getValue();
            String key = sessionKey(DeviceType.valueOf(deviceType), clientId);
            if (stringRedisTemplate.hasKey(key)) {
                isLogout = false;
            } else {
                hashOperations.delete(sessionUserKey, deviceType);
            }
        }
        return isLogout;
    }

    @Override
    public void updateUserSessionByUserId(UserSession userSession) {
        Map<Object, Object> values = hashOperations.entries(sessionUserKey(userSession.getId()));

        List<String> keys = values.entrySet().stream().map(item -> {
            String deviceType = (String) item.getKey();
            String clientId = (String) item.getValue();
            return sessionKey(DeviceType.valueOf(deviceType), clientId);
        }).collect(Collectors.toList());
        Map<String, String> stringMap = new HashMap<>();
        List<String> jsonLists = valueOperations.multiGet(keys);

        for (int i = 0; i < jsonLists.size(); i++) {
            String json = jsonLists.get(i);
            String key = keys.get(i);
            UserSession session = JSON.parseObject(json, UserSession.class);
            if (session == null) {
                logger.warn("unrecognized session json: {}, key: {} user: {}", json, key, userSession.getAccount());
                continue;
            }
            session.setAccount(userSession.getAccount());
            session.setDevice(userSession.getDevice());
            session.setIp(userSession.getIp());
            session.setEmail(userSession.getEmail());
            session.setPhoneCode(userSession.getPhoneCode());
            session.setPhone(userSession.getPhone());
            stringMap.put(key, JSON.toJSONString(session));
        }
        valueOperations.multiSet(stringMap);
    }

    @Override
    public void deleteUserSessionByUserId(Long playerId) {
        List<String> values = hashOperations.entries(sessionUserKey(playerId))
                .entrySet()
                .stream()
                .map(objectObjectEntry -> sessionKey(DeviceType.valueOf(objectObjectEntry.getKey().toString()), objectObjectEntry.getValue().toString()))
                .collect(Collectors.toList());
        values.add(sessionUserKey(playerId));
        stringRedisTemplate.delete(values);
    }

    @Override
    public void deleteUserSessionByClientUniqueId(DeviceType type, String clientUniqueId, Long playerId) {
        stringRedisTemplate.delete(sessionKey(type, clientUniqueId));
        String sessionUserKey = sessionUserKey(playerId);
        if (hashOperations.keys(sessionUserKey).size() > 1) {
            hashOperations.delete(sessionUserKey, type.name());
        } else {
            stringRedisTemplate.delete(sessionUserKey);
        }
    }

    @Override
    public void saveUserSession(DeviceType type, String clientUniqueId, UserSession userSession) {
        String sessionUserKey = sessionUserKey(userSession.getId());
        String o = (String) hashOperations.get(sessionUserKey, type.name());
        if (StringUtils.isNotBlank(o)) {
            String oldKey = sessionKey(type, o);
            stringRedisTemplate.delete(oldKey);
        }
        hashOperations.put(sessionUserKey, type.name(), clientUniqueId);
        valueOperations.set(sessionKey(type, clientUniqueId), JSON.toJSONString(userSession), sessionExpireTime, TimeUnit.SECONDS);
    }

    @Override
    public void delaySessionExpireTime(DeviceType deviceType, String clientUniqueId) {
        String oldKey = sessionKey(deviceType, clientUniqueId);
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.SECOND, sessionExpireTime);
        stringRedisTemplate.expireAt(oldKey, instance.getTime());
    }

    private static String sessionKey(DeviceType type, String key) {
        return String.format(SESSION_KEY_PREFIX, type.name(), key);
    }

    private static String sessionUserKey(Long playerId) {
        return String.format(SESSION_USER_KEY_PREFIX, playerId);
    }


}
