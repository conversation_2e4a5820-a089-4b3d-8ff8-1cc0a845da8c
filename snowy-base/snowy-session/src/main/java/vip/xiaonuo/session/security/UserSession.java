package vip.xiaonuo.session.security;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class UserSession {
    /**
     * 机构代码
     */
    @JSONField(name = "oc")
    private String orgCode;
    private Long id;
    private String ip;
    @J<PERSON><PERSON>ield(name = "d")
    private String device;
    @JSONField(name = "a")
    private String account;
    @JSONField(name = "pc")
    private String phoneCode;
    @JSONField(name = "p")
    private String phone;
    @JSONField(name = "e")
    private String email;
    @JSONField(name = "s", serialize = false)
    private UserSessionStatus status = UserSessionStatus.NONE;

    private Map<String, Object> attr;

    public UserSession() {
    }

    public Object attr(String name) {
        return attr.get(name);
    }

    public void putAttr(String name, Object value) {
        if (attr == null) {
            attr = new HashMap<>();
        }
        attr.put(name, value);
    }
}
