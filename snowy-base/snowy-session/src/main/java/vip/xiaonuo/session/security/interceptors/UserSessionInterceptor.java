package vip.xiaonuo.session.security.interceptors;

import vip.xiaonuo.session.security.UserSession;
import vip.xiaonuo.session.security.UserSessionService;
import vip.xiaonuo.core.util.DeviceType;
import vip.xiaonuo.session.utils.PrincipalUtils;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class UserSessionInterceptor extends AbstractInterceptor {

    private UserSessionService userSessionService;

    public UserSessionInterceptor(UserSessionService userSessionService) {
        this.userSessionService = userSessionService;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String clientUniqueId = PrincipalUtils.getClientToken();
        if (clientUniqueId == null) {
            return true;
        }
        DeviceType deviceType = PrincipalUtils.getDeviceType();
        UserSession userSession = userSessionService.queryUserSessionByClientUniqueId(deviceType, clientUniqueId);
        request.setAttribute(PrincipalUtils.Const.USER_SESSION, userSession);
        request.setAttribute(PrincipalUtils.Const.CLIENT_TOKEN_NAME, clientUniqueId);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserSession userSession = (UserSession) request.getAttribute(PrincipalUtils.Const.USER_SESSION);
        if (userSession == null) return;
        DeviceType deviceType = PrincipalUtils.getDeviceType();
        String clientUniqueId = (String) request.getAttribute(PrincipalUtils.Const.CLIENT_TOKEN_NAME);
        Long playerId = PrincipalUtils.getPlayerId();
        switch (userSession.getStatus()) {
            case NONE:
                userSessionService.delaySessionExpireTime(deviceType, clientUniqueId);
                break;
            case SAVE:
                userSessionService.saveUserSession(deviceType, clientUniqueId, userSession);
                break;
            case UPDATE:
                userSessionService.updateUserSession(deviceType, clientUniqueId, userSession);
                break;
            case UPDATE_ALL_INSTANCE:
                userSessionService.updateUserSessionByUserId(userSession);
                break;
            case DELETE_LOGOUT_INSTANCE:
                userSessionService.deleteUserSessionByClientUniqueId(deviceType, clientUniqueId, playerId);
                break;
            case DELETE_ALL_INSTANCE:
            default:
                userSessionService.deleteUserSessionByUserId(playerId);
                break;
        }
    }
}
