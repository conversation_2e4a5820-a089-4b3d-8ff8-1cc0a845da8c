package vip.xiaonuo.session.security;

import vip.xiaonuo.core.util.DeviceType;

import java.util.List;

public interface UserSessionService {
    boolean checkLogout(Long playerId);

    UserSession queryUserSessionByClientUniqueId(DeviceType type, String clientUniqueId);

    List<UserSession> getOnlineUserSessionList();

    void updateUserSession(DeviceType type, String clientUniqueId, UserSession userSession);

    void updateUserSessionByUserId(UserSession userSession);

    void deleteUserSessionByUserId(Long playerId);

    void deleteUserSessionByClientUniqueId(DeviceType type, String clientUniqueId, Long playerId);

    void saveUserSession(DeviceType type, String clientUniqueId, UserSession userSession);

    void delaySessionExpireTime(DeviceType deviceType, String clientUniqueId);
}
