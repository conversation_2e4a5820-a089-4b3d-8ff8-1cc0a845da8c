package vip.xiaonuo.session.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import vip.xiaonuo.core.exception.AuthException;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.exception.enums.AuthExceptionEnum;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.util.DeviceType;
import vip.xiaonuo.session.security.UserSession;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

public class PrincipalUtils {

    public interface Const {
        String CLIENT_TOKEN_NAME = "coin-auth";
        String DEVICE_PLATFORM = "device-platform";
        String USER_SESSION = "user-session";
        String ORG_CODE = "org-code";
    }

    public static HttpServletRequest request() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return requestAttributes.getRequest();
    }

    public static String getClientToken() {
        HttpServletRequest request = request();
        String token = request.getHeader(Const.CLIENT_TOKEN_NAME);
        if (StringUtils.isEmpty(token)) {
            token = getToken(request);
        }
        return token;
    }

    private static String getToken(HttpServletRequest request) {
        String header = request.getHeader(Const.CLIENT_TOKEN_NAME);
        if (StringUtils.isEmpty(header)) {
            Cookie[] cookies = request.getCookies();
            if (cookies == null) return null;
            for (Cookie c : cookies) {
                if (Const.CLIENT_TOKEN_NAME.equals(c.getName())) {
                    return c.getValue();
                }
            }
        }
        return header;
    }

    public static String getDevice() {
        try {
            DeviceType deviceType = getDeviceType();
            return deviceType != null ? deviceType.name() : null;
        } catch (AuthException e) {
            return null;
        }
    }

    public static DeviceType getDeviceType() {
        HttpServletRequest request = request();
        String devicePlatform = request.getHeader(Const.DEVICE_PLATFORM);
        devicePlatform = StringUtils.isEmpty(devicePlatform) ? request.getParameter(Const.DEVICE_PLATFORM) : devicePlatform;
        if ("1".equals(devicePlatform)) {
            return DeviceType.PC;
        } else if ("2".equals(devicePlatform)) {
            return DeviceType.H5;
        } else if ("3".equals(devicePlatform)) {
            return DeviceType.ANDROID;
        } else if ("4".equals(devicePlatform)) {
            return DeviceType.IOS;
        }
        throw new AuthException(AuthExceptionEnum.NO_DEVICE_IDENTIFICATION);
    }

    public static Long getPlayerId() {
        UserSession userSession = getSessionContent();
        if (userSession == null) {
            return -1L;
        }
        return userSession.getId();
    }

    public static String getPlayerAccount() {
        UserSession userSession = getSessionContent();
        if (userSession == null) {
            return "";
        }
        return userSession.getAccount();
    }

    /**
     * 获取玩家所属机构代码
     *
     * @return
     */
    public static String getPlayerOrgCode() {
        UserSession userSession = getSessionContent();
        if (userSession == null) {
            return null;
        }
        return userSession.getOrgCode();
    }

    public static UserSession getSessionContent() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        return (UserSession) request.getAttribute(Const.USER_SESSION);
    }

    /**
     * 获取机构代码--从header里边获取
     *
     * @return
     */
    public static String getOrgCode() {
        HttpServletRequest request = request();
        String orgCode = request.getHeader(Const.ORG_CODE);
        orgCode = StringUtils.isEmpty(orgCode) ? request.getParameter(Const.ORG_CODE) : orgCode;
        if (StringUtils.isEmpty(orgCode)) {
            throw new ServiceException(ResponseData.DEFAULT_ERROR_CODE, "Header 'org-code' missing");
        }
        return orgCode;
    }
}
