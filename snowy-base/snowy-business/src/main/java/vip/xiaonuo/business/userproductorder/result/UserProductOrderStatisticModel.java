package vip.xiaonuo.business.userproductorder.result;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class UserProductOrderStatisticModel {
    /**
     *今日认购金额
     */
    private BigDecimal subscriptionDayAccount = BigDecimal.ZERO;
    /**
     *今日赎回金额
     */
    private BigDecimal redeemDayAccount = BigDecimal.ZERO;
    /**
     *认购总金额
     */
    private BigDecimal  subscriptionAccount = BigDecimal.ZERO;
    /**
     *赎回总金额
     */
    private BigDecimal redeemAccount = BigDecimal.ZERO;
    /**
     *今日违约金
     */
    private BigDecimal  unContractDayAccount = BigDecimal.ZERO;
}
