package vip.xiaonuo.business.playerwallet.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.business.playerwallet.entity.PlayerWallet;
import vip.xiaonuo.business.playerwallet.param.PlayerWalletParam;
import vip.xiaonuo.core.pojo.page.PageResult;
import java.util.List;

/**
 * 用户钱包service接口
 *
 * <AUTHOR>
 * @date 2022-04-26 10:36:18
 */
public interface PlayerWalletService extends IService<PlayerWallet> {

    /**
     * 查询用户钱包
     *
     * <AUTHOR>
     * @date 2022-04-26 10:36:18
     */
    PageResult<PlayerWallet> page(PlayerWalletParam playerWalletParam);

    /**
     * 用户钱包列表
     *
     * <AUTHOR>
     * @date 2022-04-26 10:36:18
     */
    List<PlayerWallet> list(PlayerWalletParam playerWalletParam);

    /**
     * 添加用户钱包
     *
     * <AUTHOR>
     * @date 2022-04-26 10:36:18
     */
    void add(PlayerWalletParam playerWalletParam);

    /**
     * 删除用户钱包
     *
     * <AUTHOR>
     * @date 2022-04-26 10:36:18
     */
    void delete(List<PlayerWalletParam> playerWalletParamList);

    /**
     * 编辑用户钱包
     *
     * <AUTHOR>
     * @date 2022-04-26 10:36:18
     */
    void edit(PlayerWalletParam playerWalletParam);

    /**
     * 查看用户钱包
     *
     * <AUTHOR>
     * @date 2022-04-26 10:36:18
     */
     PlayerWallet detail(PlayerWalletParam playerWalletParam);
}
