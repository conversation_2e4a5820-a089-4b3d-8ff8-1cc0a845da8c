package vip.xiaonuo.business.transaction.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.business.transaction.entity.Transaction;
import vip.xiaonuo.business.transaction.pojo.dto.FindRechargeUserForPageDto;
import vip.xiaonuo.business.transaction.pojo.dto.FindUserTransactionDto;
import vip.xiaonuo.business.transaction.pojo.dto.FindWithdrawalUserForPageDto;
import vip.xiaonuo.business.transaction.pojo.vo.FindRechargeUserForPageVo;
import vip.xiaonuo.business.transaction.pojo.vo.FindUserTransactionVo;
import vip.xiaonuo.business.transaction.result.TransactionStatisticModel;

/**
 * 用户充值，提现记录表
 *
 * <AUTHOR>
 * @date 2022-04-08 09:51:14
 */
public interface TransactionMapper extends BaseMapper<Transaction> {
    /**
     * 充值记录
     *
     * @param req
     * @return
     */
    Page<FindRechargeUserForPageVo> findRechargeUserForPage(Page<Transaction> defaultPage, @Param("ew") FindRechargeUserForPageDto req);

    /**
     * 提现记录
     *
     * @param req
     * @return
     */
    Page<FindRechargeUserForPageVo> findWithdrawalUserForPage(Page<Transaction> defaultPage, @Param("ew") FindWithdrawalUserForPageDto req);

    /**
     * 根据
     *
     * @param date
     * @return
     */
    TransactionStatisticModel findrechargeDayAccountStatistic(@Param("ew") HomeStatisticParam param);

    /**
     * 分页查询用户交易记录
     *
     * @param dto
     * @return
     */
    Page<FindUserTransactionVo> findUserTransaction(Page<FindUserTransactionVo> defaultPage, @Param("ew") FindUserTransactionDto dto);
}
