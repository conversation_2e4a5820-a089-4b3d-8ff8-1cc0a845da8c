package vip.xiaonuo.business.contractorder.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import vip.xiaonuo.core.util.MarketUtils;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ContractOrderVo {
    @ApiModelProperty("logo")
    private String logo;
    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     * 类型(1:做多;2:做空;)
     */
    @ApiModelProperty("类型(1:做多;2:做空;)")
    private Integer type;

    /**
     * 交易类型表示,如ETH_USDT
     */
    @ApiModelProperty("如ETH_USDT")
    private String symbol;

    @ApiModelProperty("交易对名称")
    public String getSymbolName() {
        return MarketUtils.getSymbolName(symbol);
    }

    /**
     * 委托类型(1:市价委托;2:现价委托;)
     */
    @ApiModelProperty("委托类型(1:市价委托;2:现价委托;)")
    private Integer entrustType;

    /**
     * 杠杆
     */
    @ApiModelProperty("杠杆")
    private Integer lever;

    @ApiModelProperty("购买时间")
    @JsonFormat(pattern = vip.xiaonuo.core.util.DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    /**
     * 下单价格
     */
    @ApiModelProperty("下单价格")
    private BigDecimal orderPrice;

    /**
     * 下单数量
     */
    @ApiModelProperty("下单数量")
    private BigDecimal quantity;

    /**
     * 平仓价格
     */
    @ApiModelProperty("平仓价格")
    private BigDecimal closePrice;
    @ApiModelProperty("平仓时间")
    @JsonFormat(pattern = vip.xiaonuo.core.util.DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date closeTime;
    /**
     * 保证金
     */
    @ApiModelProperty("保证金")
    private BigDecimal earnestMoney;
    /**
     * 手续费
     */
    @ApiModelProperty("手续费")
    private BigDecimal orderFee;
    /**
     * 盈亏
     */
    @ApiModelProperty("盈亏")
    private BigDecimal profit;

    @ApiModelProperty("0:委托,1:持仓,2:平仓,3:撤销委托,4:锁仓")
    private Integer status;
    @ApiModelProperty("止损价")
    private BigDecimal stopLoss;
    @ApiModelProperty("止盈价")
    private BigDecimal stopProfit;
}
