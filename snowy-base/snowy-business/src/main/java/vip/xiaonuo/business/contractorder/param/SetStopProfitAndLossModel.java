package vip.xiaonuo.business.contractorder.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class SetStopProfitAndLossModel {
    @ApiModelProperty("订单号")
    @NotEmpty
    private String orderNo;
    @ApiModelProperty("止损价")
    @NotNull
    private BigDecimal stopLoss;
    @ApiModelProperty("止盈价")
    @NotNull
    private BigDecimal stopProfit;
}
