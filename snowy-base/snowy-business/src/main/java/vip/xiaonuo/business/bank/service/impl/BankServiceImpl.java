/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.bank.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.bank.entity.Bank;
import vip.xiaonuo.business.bank.enums.BankExceptionEnum;
import vip.xiaonuo.business.bank.mapper.BankMapper;
import vip.xiaonuo.business.bank.param.BankParam;
import vip.xiaonuo.business.bank.service.BankService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.ArrayList;
import java.util.List;

/**
 * 银行管理service接口实现类
 *
 * <AUTHOR>
 * @date 2022-10-24 16:58:05
 */
@Service
public class BankServiceImpl extends ServiceImpl<BankMapper, Bank> implements BankService, InitializingBean {

    @Override
    public PageResult<Bank> page(BankParam bankParam) {
        QueryWrapper<Bank> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(bankParam)) {

            // 根据银行 查询
            if (ObjectUtil.isNotEmpty(bankParam.getBankName())) {
                queryWrapper.lambda().eq(Bank::getBankName, bankParam.getBankName());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<Bank> list(BankParam bankParam) {
        return this.list();
    }

    @Override
    public void add(BankParam bankParam) {
        Bank bank = new Bank();
        BeanUtil.copyProperties(bankParam, bank);
        this.save(bank);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BankParam> bankParamList) {
        bankParamList.forEach(bankParam -> {
            Bank bank = this.queryBank(bankParam);
            this.removeById(bank.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BankParam bankParam) {
        Bank bank = this.queryBank(bankParam);
        BeanUtil.copyProperties(bankParam, bank);
        this.updateById(bank);
    }

    @Override
    public Bank detail(BankParam bankParam) {
        return this.queryBank(bankParam);
    }

    /**
     * 获取银行管理
     *
     * <AUTHOR>
     * @date 2022-10-24 16:58:05
     */
    private Bank queryBank(BankParam bankParam) {
        Bank bank = this.getById(bankParam.getId());
        if (ObjectUtil.isNull(bank)) {
            throw new ServiceException(BankExceptionEnum.NOT_EXIST);
        }
        return bank;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
//        String a = "{\"code\":0,\"success\":true,\"data\":[{\"id\":\"05fa736ff0a8fd065a97885ac908531c\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng Nông nghiệp và Phát triển Nông thôn Việt Nam - Agribank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:32:15\"},{\"id\":\"2c4b732a56a5039489cecdc899e01333\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng Thương mại TNHH MTV Dầu Khí Toàn Cầu - GPBank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:32:09\"},{\"id\":\"e7d6adbaabade707201b97dca7a284c8\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng Thương mại TNHH MTV Đại Dương - Oceanbank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:32:01\"},{\"id\":\"8c471be0ef2221173c12baa044ecfe41\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng Thương mại TNHH MTV Xây dựng Việt Nam - CB Bank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:31:55\"},{\"id\":\"f12c7741b5301a96f7d59849802a54a4\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân Hàng TMCP Đầu tư và Phát triển Việt Nam    BIDV, BID\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:31:49\"},{\"id\":\"0a68ff6fd325038a4cd1a3227baccbf3\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân Hàng TMCP Công Thương Việt Nam  VietinBank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:31:43\"},{\"id\":\"6af08bf7c8197afa472f26dcff5180c5\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân Hàng TMCP Ngoại thương Việt Nam    Vietcombank, VCB\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:31:36\"},{\"id\":\"9355425272e1efefa5fb69c6fda6a830\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân Hàng TMCP Bưu điện Liên Việt    LienVietPostBank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:31:30\"},{\"id\":\"94a3dcf4ce6a1fa25137e7cb9432ca1c\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân Hàng TMCP Xuất Nhập khẩu Việt Nam  -  Eximbank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:31:22\"},{\"id\":\"6aee403e6e2ecff0dc1b52c9c835de6c\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân Hàng TMCP Xăng Dầu Petrolimex - PG Bank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:31:15\"},{\"id\":\"696abae08268bcdf61852715f6171de6\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Việt Nam Thương Tín  - VietBank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:31:09\"},{\"id\":\"89dc242894e5e29a6083b6dc97ba0bf6\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Bảo Việt   - BaoVietBank, BVB\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:31:02\"},{\"id\":\"fb00699dc1dbaebcfd5b5772643e71f9\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Việt Á -  VietABank, VAB\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:30:57\"},{\"id\":\"c2c98f7aa1f21a1229da60687a9ec7a3\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Sài Gòn Thương Tín -  Sacombank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:30:51\"},{\"id\":\"bd31eaee8d1ce78df88521bf37cce9f6\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Sài Gòn – Hà Nội -  SHBank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:30:45\"},{\"id\":\"5a235d26ad655a48bf888b774fbdff6e\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Sài Gòn Công Thương - Saigonbank,\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:30:39\"},{\"id\":\"37fa32e112ed93e6749f89b76be7ce47\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Sài Gòn -  Sài Gòn, SCB\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:30:26\"},{\"id\":\"3aef2290f63d46299bde8c443bb69898\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Quốc tế Việt Nam - VIBBank, VIB\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:30:20\"},{\"id\":\"54fb2853d3a462682d331fc797a0bcca\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Đại chúng - PVcombank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:30:15\"},{\"id\":\"9b0e93b13bbbb5d45da3f9e13bdcefcf\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Quân đội -  MB Bank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:30:10\"},{\"id\":\"a93e82fc8a63809e36334e5afb5a0eb2\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Phương Đông -  OCB\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:30:02\"},{\"id\":\"5bd95faba07e3b26318404ba2f0bfd7d\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Phát triển Thành phố Hồ Chí Minh  - HDBank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:29:55\"},{\"id\":\"e15ba378460889c7574aed14791649bb\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Việt Nam Thịnh Vượng - VPBank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:29:49\"},{\"id\":\"14dd2bb3395fe9c3d930c46f62a0cac5\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Quốc Dân - NCB\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:29:41\"},{\"id\":\"36f8792724b20cd10dc3da3601e3dabc\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Nam Á - Nam A Bank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:29:34\"},{\"id\":\"443465043cb7ced7373a6a8235763f62\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Kiên Long - KienLongBank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:29:28\"},{\"id\":\"177d430739b6833015c0db3d5c8f2f8b\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Kỹ Thương Việt Nam - TCB\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:29:22\"},{\"id\":\"2c1b82469ae89a27469565b1305531de\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Hàng hải Việt Nam -  MSB\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:29:16\"},{\"id\":\"de9375a3d4ce2cf7ff02b10bac70a034\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Bản Việt -  VietCapitalBank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:29:11\"},{\"id\":\"b4effe76f963d99b16a82df43737b56a\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Bắc Á  - BacABank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:29:05\"},{\"id\":\"a68a22b74b4a3dda695576a8fc035261\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP An Bình -  ABBANK\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:28:59\"},{\"id\":\"2d30c36bb855926eded898debc2f6e6b\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân Hàng TMCP Đông Nam Á - SeABank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:28:49\"},{\"id\":\"752c919f173f1cd927f62a9ea4127f72\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Đông Á - Đông Á Bank DAB\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:28:43\"},{\"id\":\"e0f7f21db58c817281a11330c5cc0747\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Tiên Phong - TPBank\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:28:37\"},{\"id\":\"12ad8005097ca4a9f6c0610dc63a9af3\",\"appKey\":\"bxefdn\",\"bankName\":\"Ngân hàng TMCP Á Châu -  ACB\",\"serialNo\":null,\"bankNameOther\":null,\"createTime\":\"2022-09-08 15:28:32\"}],\"message\":\"成功\"}";
//        JSONObject jsonObject = JSON.parseObject(a);
//        JSONArray jsonArray = jsonObject.getJSONArray("data");
//        List<Bank> banks = new ArrayList<>();
//        for (int i = 0; i < jsonArray.size(); i++) {
//            JSONObject obj = jsonArray.getJSONObject(i);
//            Bank bank = new Bank();
//            bank.setBankName(obj.getString("bankName"));
//            bank.setCreateUser(-1L);
//            bank.setUpdateUser(-1L);
//            banks.add(bank);
//        }
//        saveBatch(banks);
//        log.trace(
//                "---------------------------------------111111111111111111111111111111111111111"
//        );
    }
}
