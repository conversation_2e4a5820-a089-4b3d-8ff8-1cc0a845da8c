<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.business.userbankaccount.mapper.PlayerBankAccountMapper">
    <select id="getPage" parameterType="java.util.HashMap" resultType="vip.xiaonuo.business.userbankaccount.entity.PlayerBankAccount">
        SELECT
            t.*,
            tp.phone phone,
            tp.id playerId,
            tp.account
        FROM
            tt_player tp
            LEFT JOIN tt_player_bank_account t ON tp.id = t.player_id
        <where>
            <if test="param.nickName != null and param.nickName != ''">
                and tp.nick_name like concat('%',#{param.nickName},'%')
            </if>
            <if test="param.account != null and param.account != ''">
                and tp.account = #{param.account}
            </if>
            <if test="param.phone != null and param.phone != ''">
                and tp.phone =#{param.phone}
            </if>
            <if test="param.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
                and tp.agent_id in
                <foreach item="item" index="index" collection="param.agentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
                and tp.agent_id = #{param.loginUserId}
            </if>
        </where>
    </select>
</mapper>
