package vip.xiaonuo.business.userproductorder.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户赎回
 */
@ApiModel(value = "用户赎回")
@Data
public class ExecuteUserRedeemOrderDto implements Serializable {

    /**
     * 用户id
     */
    private Long playerId;
    /**
     * 赎回订单id
     */
    @ApiModelProperty(value = "赎回订单id", required = true)
    private Long userOrderId;

    /**
     * 类型(1:币币账户;2:合约账户;3:现货账户)
     */
    private Integer accountType=1;
    /**
     * 币种代码
     */
    private String currencyCode="USDT";
}
