/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.accountbill.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vip.xiaonuo.business.accountbill.enums.AccountBillBusinessTypeEnum;
import vip.xiaonuo.business.accountbill.enums.AccountBillOptTypeEnum;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;

import java.math.BigDecimal;

/**
 * 账户表
 *
 * <AUTHOR>
 * @date 2022-04-06 13:49:58
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tt_account_bill")
public class AccountBill extends BaseEntity {
    /**
     * 玩家ID
     */
    private Long playerId;

    /**
     * 类型(1:币币账户;2:合约账户;3:现货账户)
     */
    private Integer type;

    @TableField(exist = false)
    private String typeName;

    /**
     * 币种代码
     */
    private String currencyCode;
    /**
     * 账户ID
     */
    private Long accountId;
    //操作类型
    private AccountBillOptTypeEnum optType;
    @TableField(exist = false)
    private String optTypeName;
    /**
     * 变动之后余额
     */
    private BigDecimal balanceAfter;

    /**
     * 变动余额
     */
    private BigDecimal balanceChanged;

    /**
     * 业务号
     */
    private String businessNo;

    /**
     * 业务类型
     */
    private AccountBillBusinessTypeEnum businessType;
    @TableField(exist = false)
    private String businessTypeName;

    //冻结操作类型
    private AccountBillOptTypeEnum frozenOptType;
    @TableField(exist = false)
    private String frozenOptTypeName;
    /**
     * 冻结变动之后余额
     */
    private BigDecimal frozenAfter;

    /**
     * 冻结变动余额
     */
    private BigDecimal frozenChanged;

    @TableField(exist = false)
    private String account;

}
