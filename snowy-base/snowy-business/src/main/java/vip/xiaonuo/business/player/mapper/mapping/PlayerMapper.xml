<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.business.player.mapper.PlayerMapper">

    <resultMap id="BaseResultMap" type="vip.xiaonuo.business.player.entity.Player">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="org_Code" property="orgCode"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="funds_Password" property="fundsPassword"/>
        <result column="nick_Name" property="nickName"/>
        <result column="status" property="status"/>
        <result column="avatar" property="avatar"/>
        <result column="email" property="email"/>
        <result column="phone_Code" property="phoneCode"/>
        <result column="phone" property="phone"/>
        <result column="last_Login_Ip" property="lastLoginIp"/>
        <result column="last_Login_Time" property="lastLoginTime"/>
        <result column="invitation_Code" property="invitationCode"/>
        <result column="invited_By" property="invitedBy"/>
        <result column="salt" property="salt"/>
        <result column="agent_Id" property="agentId"/>
        <result column="register_Ip" property="registerIp"/>
        <result column="spot_Scale" property="spotScale"/>
        <result column="online_Flag" property="onlineFlag"/>
    </resultMap>
    <resultMap id="BaseResultExtMap" type="vip.xiaonuo.business.player.result.PlayerResult" extends="BaseResultMap">
        <result column="agentName" property="agentName"/>
        <result column="authStatus" property="authStatus"/>
        <result column="advancedStatus" property="advancedStatus"/>
        <result column="lastLoginAddress" property="lastLoginAddress"/>
    </resultMap>

    <select id="pageResult" resultMap="BaseResultExtMap">
        SELECT
            a.account as agentName,
            pi.auth_status as authStatus,
            pi.advanced_status as advancedStatus,
            concat(tip.country_code,' - ', tip.region_name,'（',tip.city,'）') as lastLoginAddress,
            p.*
        FROM
            tt_player p
            LEFT JOIN t_player_info pi ON p.id = pi.player_id
            LEFT JOIN sys_user a ON a.id = p.agent_id
            LEFT JOIN tt_ip_info tip on p.last_login_ip = tip.ip
            <where>
                <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
                    and p.agent_id in
                    <foreach item="item" index="index" collection="ew.agentIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
                    and p.agent_id = #{ew.loginUserId}
                </if>
                <if test="ew.account != null and ew.account != ''">
                    AND p.account = #{ew.account}
                </if>
                <if test="ew.phone != null and ew.phone != ''">
                    AND p.phone = #{ew.phone}
                </if>
                <if test="ew.status != null">
                    AND p.status = #{ew.status}
                </if>
                <if test="ew.onlineFlag != null">
                    AND p.online_Flag = #{ew.onlineFlag}
                </if>
                <if test="ew.agentName != null and ew.agentName != ''">
                    AND a.account like concat('%',#{ew.agentName},'%')
                </if>
                <if test="ew.lastLoginIp != null and ew.lastLoginIp != ''">
                    AND p.last_login_ip like concat('%',#{ew.lastLoginIp},'%')
                </if>
                <if test="ew.role != null and ew.role != ''">
                    AND p.role = #{ew.role}
                </if>
                <if test="ew.orgId != null and ew.orgId != ''">
                    AND p.agent_id in(select id from sys_emp where org_id = #{ew.orgId})
                </if>
            </where>
    </select>

    <select id="getAgentByInviteCode" resultType="vip.xiaonuo.business.player.result.AgentOrgInfo">
        SELECT
            a.id agentId,
            o.code orgCode
        FROM
            sys_user a left join sys_user_data_scope ud on ud.user_id = a.id left join sys_org o on o.id = ud.org_id
        WHERE
            a.invited_code = #{inviteCode} and o.code like concat(#{orgCode},'%')  and a.admin_auth_type=3
    </select>

    <select id="lockPlayer" resultType="java.lang.String">
        select id from tt_player where id in
        <foreach item="item" index="index" collection="playerIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        for update
    </select>

    <select id="queryAgentName" resultType="vip.xiaonuo.business.player.result.AgentName">
        SELECT
            a.id,a.account
        FROM
            sys_user a
        WHERE a.id in
        <foreach item="item" index="index" collection="agentIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findUserStatistic" resultType="vip.xiaonuo.business.player.result.PlayerStatisticModel">
       SELECT
            COUNT( 1 ) registerUserCount,
            count( CASE WHEN DATE ( create_time ) = #{ew.date} THEN id ELSE NULL END ) registerUserDayCount
        FROM
            tt_player p
        <where>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
                and p.org_code = #{ew.loginUserOrgCode}
            </if>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
                and p.agent_id = #{ew.loginUserId}
            </if>
        </where>
    </select>

    <select id="findUserFollower" resultType="vip.xiaonuo.business.player.pojo.vo.FindFollowerVo" parameterType="vip.xiaonuo.business.player.pojo.dto.FindFollowerDto">
        SELECT
            t1.*,
            CASE WHEN t2.id IS NOT NULL THEN 1 ELSE 0 END AS isFollowed
        FROM
            tt_follower t1
                LEFT JOIN tt_follower_relation t2 ON t1.id = t2.follower_id AND t2.player_id = #{ew.playerId}
                LEFT JOIN tt_follower_contract_spot_order t3 ON t3.follower_id = t1.id
        WHERE
            t3.order_time > NOW()
        ORDER BY
            t1.create_time DESC
    </select>
</mapper>
