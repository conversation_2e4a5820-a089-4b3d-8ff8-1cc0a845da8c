/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.currency.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.currency.entity.Currency;
import vip.xiaonuo.business.currency.enums.CurrencyExceptionEnum;
import vip.xiaonuo.business.currency.mapper.CurrencyMapper;
import vip.xiaonuo.business.currency.param.CurrencyParam;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 币种service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-06 12:56:08
 */
@Service
public class CurrencyServiceImpl extends ServiceImpl<CurrencyMapper, Currency> implements CurrencyService {

    @Override
    public PageResult<Currency> page(CurrencyParam currencyParam) {
        QueryWrapper<Currency> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(currencyParam)) {

            // 根据名称 查询
            if (ObjectUtil.isNotEmpty(currencyParam.getName())) {
                queryWrapper.lambda().eq(Currency::getName, currencyParam.getName());
            }
            // 根据logo 查询
            if (ObjectUtil.isNotEmpty(currencyParam.getLogo())) {
                queryWrapper.lambda().eq(Currency::getLogo, currencyParam.getLogo());
            }
            // 根据代码 查询
            if (ObjectUtil.isNotEmpty(currencyParam.getCode())) {
                queryWrapper.lambda().eq(Currency::getCode, currencyParam.getCode());
            }
            // 根据提现手续费 查询
            if (ObjectUtil.isNotEmpty(currencyParam.getWithdrawFee())) {
                queryWrapper.lambda().eq(Currency::getWithdrawFee, currencyParam.getWithdrawFee());
            }
            // 根据0:下线,1:上线 查询
            if (ObjectUtil.isNotEmpty(currencyParam.getStatus())) {
                queryWrapper.lambda().eq(Currency::getStatus, currencyParam.getStatus());
            }
            // 根据排序，数值越小，排序越靠前 查询
            if (ObjectUtil.isNotEmpty(currencyParam.getSort())) {
                queryWrapper.lambda().eq(Currency::getSort, currencyParam.getSort());
            }
            // 根据充值开关：0:关 1:开 查询
            if (ObjectUtil.isNotEmpty(currencyParam.getChargeSwitch())) {
                queryWrapper.lambda().eq(Currency::getChargeSwitch, currencyParam.getChargeSwitch());
            }
            // 根据提现开关：0关，1开 查询
            if (ObjectUtil.isNotEmpty(currencyParam.getWithdrawSwitch())) {
                queryWrapper.lambda().eq(Currency::getWithdrawSwitch, currencyParam.getWithdrawSwitch());
            }
            // 根据最小提币数量 查询
            if (ObjectUtil.isNotEmpty(currencyParam.getMinWithdrawAmount())) {
                queryWrapper.lambda().eq(Currency::getMinWithdrawAmount, currencyParam.getMinWithdrawAmount());
            }
            // 根据单次最大提币额 查询
            if (ObjectUtil.isNotEmpty(currencyParam.getCurrencyWithdrawLimit())) {
                queryWrapper.lambda().eq(Currency::getCurrencyWithdrawLimit, currencyParam.getCurrencyWithdrawLimit());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<Currency> list(CurrencyParam currencyParam) {
        return this.list();
    }

    @Override
    public void add(CurrencyParam currencyParam) {
        Currency currency = new Currency();
        BeanUtil.copyProperties(currencyParam, currency);
        this.save(currency);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<CurrencyParam> currencyParamList) {
        currencyParamList.forEach(currencyParam -> {
            Currency currency = this.queryCurrency(currencyParam);
            this.removeById(currency.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CurrencyParam currencyParam) {
        Currency currency = this.queryCurrency(currencyParam);
        BeanUtil.copyProperties(currencyParam, currency);
        this.updateById(currency);
    }

    @Override
    public Currency detail(CurrencyParam currencyParam) {
        return this.queryCurrency(currencyParam);
    }

    @Override
    public Currency getCurrencyByCode(String currencyCode) {
//        return lambdaQuery().eq(Currency::getCode, currencyCode).eq(Currency::getStatus, 1).one();
        return lambdaQuery().eq(Currency::getCode, currencyCode).one();
    }

    @Override
    public List<Currency> getChargeCurrencyList() {
        return lambdaQuery().eq(Currency::getChargeSwitch, 1)/*.eq(Currency::getStatus, 1)*/.list();
    }

    public List<Currency> getWithdrawCurrencyList() {
        return lambdaQuery().eq(Currency::getWithdrawSwitch, 1).eq(Currency::getStatus, 1).list();
    }

    public Currency getWithdrawCurrency(String currencyCode) {
        return lambdaQuery().eq(Currency::getWithdrawSwitch, 1).eq(Currency::getCode, currencyCode).one();
    }

    @Override
    public Map<String, String> getLogoMap(List<String> codes) {
        if (CollectionUtil.isEmpty(codes)) {
            return new HashMap<>();
        }
        return lambdaQuery().select(Currency::getCode, Currency::getLogo).in(Currency::getCode, codes).list().stream().collect(Collectors.toMap(Currency::getCode, Currency::getLogo));
    }

    @Override
    public List<Currency> getCurrencyByCode(List<String> currencyCodes) {
        return lambdaQuery().in(Currency::getCode, currencyCodes).list();
    }

    public List<Currency> getEnableList() {
        return lambdaQuery().eq(Currency::getStatus, 1).orderByAsc(Currency::getSort).list();
    }

    public List<Currency> getAllList() {
        return lambdaQuery().orderByAsc(Currency::getSort).list();
    }

    /**
     * 获取币种
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    private Currency queryCurrency(CurrencyParam currencyParam) {
        Currency currency = this.getById(currencyParam.getId());
        if (ObjectUtil.isNull(currency)) {
            throw new ServiceException(CurrencyExceptionEnum.NOT_EXIST);
        }
        return currency;
    }
}
