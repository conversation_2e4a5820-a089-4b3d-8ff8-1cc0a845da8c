package vip.xiaonuo.business.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 理财产品
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_product")
public class Product extends BaseEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 语言编码
     */
    private String languageCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 最小认购金额
     */
    private BigDecimal minPurchaseAccount;

    /**
     * 最大认购金额
     */
    private BigDecimal maxPurchaseAccount;

    /**
     * 最小收益率
     */
    private BigDecimal minProfitAccount;

    /**
     * 最大收益率
     */
    private BigDecimal maxProfitAccount;

    /**
     * 周期
     */
    private Integer cycle;

    /**
     * 提前赎回罚息费率
     */
    private BigDecimal advRedeemRate;

    /**
     * 产品介绍
     */
    private String productRemark;

    /**
     * 产品状态 1 启用 2 停用
     */
    private Integer productStatus;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 修改人
     */
    private Long updateId;

}
