/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.productintroduce.result;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;

import java.util.Date;

/**
 * 产品简介
 *
 * <AUTHOR>
 * @date 2022-04-25 15:27:42
 */
@Data
public class ProductIntroduceInfo extends BaseEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 语言编码
     */
    private String languageCode;

    /**
     * 币种id
     */
    private String currencyCode;

    /**
     * 发行时间
     */
    @Excel(name = "发行时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date releaseTime;

    /**
     * 发行总量
     */
    private String totalIssuance;

    /**
     * 流通总量
     */
    private String totalCirculation;

    /**
     * 官方网站
     */
    private String officialWebsite;

    /**
     * 白皮书
     */
    private String whitePaper;

    /**
     * 区块查询
     */
    private String blockQuery;

    /**
     * 简介
     */
    private String briefIntroduce;

    /**
     * 币种名称
     */
    private String currencyName;
}
