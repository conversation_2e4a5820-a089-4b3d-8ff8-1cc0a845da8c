package vip.xiaonuo.business.contractorder.enums;

import cn.hutool.core.lang.Pair;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum ContractOrderLeverEnum {
    LEVER5(5, "5X"),
    LEVER10(10, "10X"),
    LEVER15(15, "15X"),
    LEVER20(20, "20X"),
    LEVER100(100, "100X");

    private final Integer code;
    private final String message;

    ContractOrderLeverEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static boolean isExists(Integer code) {
        for (ContractOrderLeverEnum typeEnum : values()) {
            if (code.equals(typeEnum.getCode())) {
                return true;
            }
        }
        return false;
    }

    public static List<Pair<Integer, String>> toList() {
        return Arrays.stream(values()).map(p -> Pair.of(p.getCode(), p.getMessage())).collect(Collectors.toList());
    }
}
