/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.richtext.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import vip.xiaonuo.business.richtext.entity.RichText;
import vip.xiaonuo.business.richtext.enums.RichTextExceptionEnum;
import vip.xiaonuo.business.richtext.mapper.RichTextMapper;
import vip.xiaonuo.business.richtext.param.RichTextParam;
import vip.xiaonuo.business.richtext.service.RichTextService;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.enums.CommonStatusEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;

/**
 * 富文本信息service接口实现类
 *
 * <AUTHOR>
 * @date 2024-12-23 10:45:38
 */
@Service
public class RichTextServiceImpl extends ServiceImpl<RichTextMapper, RichText> implements RichTextService {

    @Override
    public PageResult<RichText> page(RichTextParam richTextParam) {
        QueryWrapper<RichText> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(richTextParam)) {

            // 根据语言 查询
            if (ObjectUtil.isNotEmpty(richTextParam.getLang())) {
                queryWrapper.lambda().eq(RichText::getLang, richTextParam.getLang());
            }
            // 根据类型(1:注册协议) 查询
            if (ObjectUtil.isNotEmpty(richTextParam.getType())) {
                queryWrapper.lambda().eq(RichText::getType, richTextParam.getType());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<RichText> list(RichTextParam richTextParam) {
        return this.list();
    }

    @Override
    public void add(RichTextParam richTextParam) {
        RichText richText = new RichText();
        BeanUtil.copyProperties(richTextParam, richText);
        this.save(richText);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<RichTextParam> richTextParamList) {
        richTextParamList.forEach(richTextParam -> {
        RichText richText = this.queryRichText(richTextParam);
            this.removeById(richText.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(RichTextParam richTextParam) {
        RichText richText = this.queryRichText(richTextParam);
        BeanUtil.copyProperties(richTextParam, richText);
        this.updateById(richText);
    }

    @Override
    public RichText detail(RichTextParam richTextParam) {
        return this.queryRichText(richTextParam);
    }

    /**
     * 获取富文本信息
     *
     * <AUTHOR>
     * @date 2024-12-23 10:45:38
     */
    private RichText queryRichText(RichTextParam richTextParam) {
        RichText richText = this.getById(richTextParam.getId());
        if (ObjectUtil.isNull(richText)) {
            throw new ServiceException(RichTextExceptionEnum.NOT_EXIST);
        }
        return richText;
    }
}
