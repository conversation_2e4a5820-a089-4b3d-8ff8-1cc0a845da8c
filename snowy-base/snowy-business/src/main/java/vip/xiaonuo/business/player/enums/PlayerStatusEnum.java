package vip.xiaonuo.business.player.enums;

import cn.hutool.core.lang.Pair;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum PlayerStatusEnum {
    STATUS1(1, "正常"), STATUS2(2, "冻结");
    private Integer code;
    private String name;

    PlayerStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static List<Pair<Integer, String>> toList() {
        return Arrays.stream(values()).map(p -> Pair.of(p.getCode(), p.getName())).collect(Collectors.toList());
    }
}
