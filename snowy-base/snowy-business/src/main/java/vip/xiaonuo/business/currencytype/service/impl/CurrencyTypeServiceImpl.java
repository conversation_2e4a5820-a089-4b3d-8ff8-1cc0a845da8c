/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.currencytype.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.enums.CommonStatusEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.business.currencytype.entity.CurrencyType;
import vip.xiaonuo.business.currencytype.enums.CurrencyTypeExceptionEnum;
import vip.xiaonuo.business.currencytype.mapper.CurrencyTypeMapper;
import vip.xiaonuo.business.currencytype.param.CurrencyTypeParam;
import vip.xiaonuo.business.currencytype.service.CurrencyTypeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;

/**
 * 充币类型service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-25 15:48:06
 */
@Service
public class CurrencyTypeServiceImpl extends ServiceImpl<CurrencyTypeMapper, CurrencyType> implements CurrencyTypeService {

    @Override
    public PageResult<CurrencyType> page(CurrencyTypeParam currencyTypeParam) {
        QueryWrapper<CurrencyType> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(currencyTypeParam)) {

            // 根据链名称 查询
            if (ObjectUtil.isNotEmpty(currencyTypeParam.getChainName())) {
                queryWrapper.lambda().eq(CurrencyType::getChainName, currencyTypeParam.getChainName());
            }
            // 根据币code 查询
            if (ObjectUtil.isNotEmpty(currencyTypeParam.getCurrencyCode())) {
                queryWrapper.lambda().eq(CurrencyType::getCurrencyCode, currencyTypeParam.getCurrencyCode());
            }
            // 根据充币地址 查询
            if (ObjectUtil.isNotEmpty(currencyTypeParam.getCurrencyAddress())) {
                queryWrapper.lambda().eq(CurrencyType::getCurrencyAddress, currencyTypeParam.getCurrencyAddress());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CurrencyType> list(CurrencyTypeParam currencyTypeParam) {
        return this.list();
    }

    /**
     * 充币类型列表
     *
     * @param currencyTypeParam
     * <AUTHOR>
     * @date 2022-04-25 15:48:06
     */
    @Override
    public List<CurrencyType> queryList(CurrencyTypeParam currencyTypeParam) {
        QueryWrapper<CurrencyType> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(currencyTypeParam)) {
            // 根据链名称 查询
            if (ObjectUtil.isNotEmpty(currencyTypeParam.getChainName())) {
                queryWrapper.lambda().eq(CurrencyType::getChainName, currencyTypeParam.getChainName());
            }
            // 根据币code 查询
            if (ObjectUtil.isNotEmpty(currencyTypeParam.getCurrencyCode())) {
                queryWrapper.lambda().eq(CurrencyType::getCurrencyCode, currencyTypeParam.getCurrencyCode());
            }
            // 根据充币地址 查询
            if (ObjectUtil.isNotEmpty(currencyTypeParam.getCurrencyAddress())) {
                queryWrapper.lambda().eq(CurrencyType::getCurrencyAddress, currencyTypeParam.getCurrencyAddress());
            }
        }
        return this.getBaseMapper().selectList(queryWrapper);
    }

    @Override
    public void add(CurrencyTypeParam currencyTypeParam) {
        CurrencyType currencyType = new CurrencyType();
        BeanUtil.copyProperties(currencyTypeParam, currencyType);
        this.save(currencyType);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<CurrencyTypeParam> currencyTypeParamList) {
        currencyTypeParamList.forEach(currencyTypeParam -> {
        CurrencyType currencyType = this.queryCurrencyType(currencyTypeParam);
            this.removeById(currencyType.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CurrencyTypeParam currencyTypeParam) {
        CurrencyType currencyType = this.queryCurrencyType(currencyTypeParam);
        BeanUtil.copyProperties(currencyTypeParam, currencyType);
        this.updateById(currencyType);
    }

    @Override
    public CurrencyType detail(CurrencyTypeParam currencyTypeParam) {
        return this.queryCurrencyType(currencyTypeParam);
    }

    /**
     * 获取充币类型
     *
     * <AUTHOR>
     * @date 2022-04-25 15:48:06
     */
    private CurrencyType queryCurrencyType(CurrencyTypeParam currencyTypeParam) {
        CurrencyType currencyType = this.getById(currencyTypeParam.getId());
        if (ObjectUtil.isNull(currencyType)) {
            throw new ServiceException(CurrencyTypeExceptionEnum.NOT_EXIST);
        }
        return currencyType;
    }
}
