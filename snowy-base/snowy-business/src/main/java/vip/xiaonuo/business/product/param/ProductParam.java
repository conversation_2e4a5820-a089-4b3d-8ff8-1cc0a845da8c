package vip.xiaonuo.business.product.param;

import io.swagger.annotations.ApiModel;
import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;

/**
* 理财产品参数类
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:39
*/
@ApiModel(value = "理财产品参数")
@Data
public class ProductParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 语言编码
     */
    @NotBlank(message = "语言编码不能为空，请检查languageCode参数", groups = {add.class, edit.class})
    private String languageCode;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空，请检查productName参数", groups = {add.class, edit.class})
    private String productName;

    /**
     * 最小认购金额
     */
    @NotNull(message = "最小认购金额不能为空，请检查minPurchaseAccount参数", groups = {add.class, edit.class})
    private BigDecimal minPurchaseAccount;

    /**
     * 最大认购金额
     */
    @NotNull(message = "最大认购金额不能为空，请检查maxPurchaseAccount参数", groups = {add.class, edit.class})
    private BigDecimal maxPurchaseAccount;

    /**
     * 最小收益率
     */
    @NotNull(message = "最小收益率不能为空，请检查minProfitAccount参数", groups = {add.class, edit.class})
    private BigDecimal minProfitAccount;

    /**
     * 最大收益率
     */
    @NotNull(message = "最大收益率不能为空，请检查maxProfitAccount参数", groups = {add.class, edit.class})
    private BigDecimal maxProfitAccount;

    /**
     * 周期
     */
    @NotNull(message = "周期不能为空，请检查cycle参数", groups = {add.class, edit.class})
    private Integer cycle;

    /**
     * 提前赎回罚息费率
     */
    @NotNull(message = "提前赎回罚息费率不能为空，请检查advRedeemRate参数", groups = {add.class, edit.class})
    private BigDecimal advRedeemRate;

    /**
     * 产品介绍
     */
    @NotBlank(message = "产品介绍不能为空，请检查productRemark参数", groups = {add.class, edit.class})
    private String productRemark;

    /**
     * 产品状态 1 启用 2 停用
     */
    @NotNull(message = "产品状态 1 启用 2 停用不能为空，请检查productStatus参数", groups = {add.class, edit.class})
    private Integer productStatus;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 修改人
     */
    private Long updateId;

}
