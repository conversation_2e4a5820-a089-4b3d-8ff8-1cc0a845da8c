/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.contractorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.account.enums.AccountTypeEnum;
import vip.xiaonuo.business.account.param.AccountChangeBalanceParam;
import vip.xiaonuo.business.account.service.AccountService;
import vip.xiaonuo.business.accountbill.enums.AccountBillBusinessTypeEnum;
import vip.xiaonuo.business.accountbill.enums.AccountBillOptTypeEnum;
import vip.xiaonuo.business.contractorder.entity.ContractOrder;
import vip.xiaonuo.business.contractorder.enums.*;
import vip.xiaonuo.business.contractorder.mapper.ContractOrderMapper;
import vip.xiaonuo.business.contractorder.param.ContractOrderParam;
import vip.xiaonuo.business.contractorder.param.PlaceContractOrderModel;
import vip.xiaonuo.business.contractorder.param.QueryContractOrderModel;
import vip.xiaonuo.business.contractorder.param.SetStopProfitAndLossModel;
import vip.xiaonuo.business.contractorder.result.ContractOrderStatisticModel;
import vip.xiaonuo.business.contractorder.result.ContractOrderVo;
import vip.xiaonuo.business.contractorder.service.ContractOrderCloseService;
import vip.xiaonuo.business.contractorder.service.ContractOrderHashOperations;
import vip.xiaonuo.business.contractorder.service.ContractOrderService;
import vip.xiaonuo.business.currency.entity.Currency;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.dict.service.SysDictDataService;
import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.business.market.enums.MarketExceptionEnum;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.core.consts.DictConstant;
import vip.xiaonuo.core.enums.SerialNoEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.dto.ContractOrderInfoDTO;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.util.ConvertUtils;
import vip.xiaonuo.core.util.DpDataUtils;
import vip.xiaonuo.core.util.MarketUtils;
import vip.xiaonuo.core.util.UUIDUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 合约订单service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-19 14:17:12
 */
@Service
public class ContractOrderServiceImpl extends ServiceImpl<ContractOrderMapper, ContractOrder> implements ContractOrderService, InitializingBean, DisposableBean {
    private Logger logger = LoggerFactory.getLogger(ContractOrderServiceImpl.class);
    @Resource
    private PlayerService playerService;
    @Resource
    private MarketService marketService;
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    @Lazy
    private ContractOrderCloseService contractOrderCloseService;
    @Resource
    private AccountService accountService;
    @Resource
    private CurrencyService currencyService;
    @Resource
    private ContractOrderHashOperations contractOrderHashOperations;
    private ExecutorService executorService;

    @Resource
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public PageResult<ContractOrder> page(ContractOrderParam contractOrderParam) {
        Page<ContractOrder> contractOrderPage = getBaseMapper().queryPage(PageFactory.defaultPage(), contractOrderParam);
        return new PageResult<>(contractOrderPage);
    }

    @Override
    public PageResult<ContractOrderVo> page(Long playerId, QueryContractOrderModel model) {
        QueryWrapper<ContractOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ContractOrder::getPlayerId, playerId);
        queryWrapper.lambda().eq(ContractOrder::getStatus, model.getStatus());
        queryWrapper.lambda().eq(StringUtils.isNotEmpty(model.getSymbol()), ContractOrder::getSymbol, model.getSymbol());
        queryWrapper.lambda().orderByDesc(ContractOrder::getCreateTime);
        PageResult<ContractOrder> pageResult = new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
        PageResult<ContractOrderVo> page = ConvertUtils.convert(pageResult, ContractOrderVo.class);
        List<Currency> currencies = currencyService.list();
        for (ContractOrderVo contractOrderVo : page.getData()) {
            currencies.stream().filter(p -> p.getCode().equalsIgnoreCase(MarketUtils.getCode(contractOrderVo.getSymbol()))).findAny().ifPresent(currency -> contractOrderVo.setLogo(currency.getLogo()));

            if (ContractOrderStatusEnum.STATUS0.getCode().equals(contractOrderVo.getStatus()) || ContractOrderStatusEnum.STATUS1.getCode().equals(contractOrderVo.getStatus())) {
                ContractOrderInfoDTO contractOrderInfoDTO = contractOrderHashOperations.get(contractOrderVo.getSymbol(), contractOrderVo.getOrderNo());
                if (contractOrderInfoDTO != null) {
                    contractOrderVo.setClosePrice(contractOrderInfoDTO.getClosePrice());
                    if (ContractOrderStatusEnum.STATUS0.getCode().equals(contractOrderVo.getStatus())) {
                        contractOrderVo.setProfit(BigDecimal.ZERO);
                    } else {
                        contractOrderVo.setProfit(contractOrderInfoDTO.getProfit());
                    }
                }
            }
        }
        return page;
    }

    @Override
    public List<ContractOrder> list(ContractOrderParam contractOrderParam) {
        return this.list();
    }

    @Override
    public void add(ContractOrderParam contractOrderParam) {
        ContractOrder contractOrder = new ContractOrder();
        BeanUtil.copyProperties(contractOrderParam, contractOrder);
        this.save(contractOrder);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<ContractOrderParam> contractOrderParamList) {
        contractOrderParamList.forEach(contractOrderParam -> {
            ContractOrder contractOrder = this.queryContractOrder(contractOrderParam);
            this.removeById(contractOrder.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ContractOrderParam contractOrderParam) {
        ContractOrder contractOrder = this.queryContractOrder(contractOrderParam);
        BeanUtil.copyProperties(contractOrderParam, contractOrder);
        this.updateById(contractOrder);
    }

    @Override
    public ContractOrder detail(ContractOrderParam contractOrderParam) {
        return this.queryContractOrder(contractOrderParam);
    }

    @Resource
    private SysDictDataService sysDictDataService;

    @Override
    @Transactional
    public ContractOrder placeOrder(Long playerId, PlaceContractOrderModel model) {
        if (!ContractOrderTypeEnum.isExists(model.getType())) {
            throw new ServiceException(ContractOrderExceptionEnum.TYPE_NOT_EXIST);
        }
        if (!ContractOrderEntrustTypeEnum.isExists(model.getEntrustType())) {
            throw new ServiceException(ContractOrderExceptionEnum.TYPE_NOT_EXIST);
        }
        if (!ContractOrderLeverEnum.isExists(model.getLever())) {
            throw new ServiceException(ContractOrderExceptionEnum.LEVER_NOT_EXIST);
        }
        if (BigDecimal.ZERO.compareTo(model.getQuantity()) >= 0) {
            throw new ServiceException(ContractOrderExceptionEnum.QUANTITY_ERROR);
        }
        Market market = marketService.getMarketBySymbol(model.getSymbol());
        if (market == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_EXIST);
        }
        if (market.getTransactionSwitch() != 1) {
            throw new ServiceException(MarketExceptionEnum.TRADING_CLOSED);
        }
        BigDecimal price;
        if (ContractOrderEntrustTypeEnum.TYPE2.getCode().equals(model.getEntrustType())) {
            if (BigDecimal.ZERO.compareTo(model.getPrice()) >= 0) {
                throw new ServiceException(ContractOrderExceptionEnum.PRICE_NOT_EXIST);
            }
            price = model.getPrice();
        } else {
            price = DpDataUtils.getLatestPrice(redisTemplate, market.getSymbol());
            if (price == null) {
                throw new ServiceException(MarketExceptionEnum.NOT_LAST_PRICE);
            }
        }
        BigDecimal feeRate = sysDictDataService.getCommonConfigAsDecimal(DictConstant.CONTRACT_FEE);
        if (feeRate == null) {
            throw new ServiceException(MarketExceptionEnum.FEE_RATE_NOT_EXIST);
        }
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(playerId));

        String orderNo = UUIDUtil.createOrderId(SerialNoEnum.C);
        ContractOrder contractOrder = new ContractOrder();
        contractOrder.setPlayerId(playerId);
        contractOrder.setType(model.getType());
        contractOrder.setEntrustType(model.getEntrustType());
        contractOrder.setSymbol(market.getSymbol());
        contractOrder.setOrderNo(orderNo);
        contractOrder.setQuantity(model.getQuantity());
        contractOrder.setLever(model.getLever());
        contractOrder.setOrderPrice(price);
        contractOrder.setStopLoss(model.getStopLoss());
        contractOrder.setStopProfit(model.getStopProfit());
        //验证止盈止损价
        validStopProfitLoss(contractOrder);
        //计算保证金 (价格/杠杆)*数量  保留4位小数且四舍五入
        contractOrder.setEarnestMoney(price.multiply(contractOrder.getQuantity()).divide(BigDecimal.valueOf(contractOrder.getLever()), 4, BigDecimal.ROUND_HALF_UP));
        //计算手续费 保留两位小数且四舍五入
        contractOrder.setOrderFee(contractOrder.getEarnestMoney().multiply(feeRate).setScale(2, BigDecimal.ROUND_HALF_UP));
        if (ContractOrderEntrustTypeEnum.TYPE1.getCode().equals(model.getEntrustType())) {
            //委托成功时间
            contractOrder.setOrderTime(new Date());
            contractOrder.setStatus(ContractOrderStatusEnum.STATUS1.getCode());
        } else {
            contractOrder.setStatus(ContractOrderStatusEnum.STATUS0.getCode());
        }

        //购买时间
        contractOrder.setCreateTime(new Date());
        contractOrder.setCreateUser(playerId);
        save(contractOrder);

        //变更余额
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder balanceParamBuilder = AccountChangeBalanceParam.builder()
                .playerId(contractOrder.getPlayerId())
                .type(AccountTypeEnum.TYPE1.getCode())
                .currencyCode(MarketUtils.getBaseCode(contractOrder.getSymbol()))
                .businessType(AccountBillBusinessTypeEnum.CONTRACT_ORDER)
                .optType(AccountBillOptTypeEnum.SUB)
                .businessNo(orderNo)
                //加上手续费
                .balance(contractOrder.getEarnestMoney().add(contractOrder.getOrderFee()))
                .updateUser(contractOrder.getPlayerId());
        accountService.changeBalance(balanceParamBuilder.build());

        //存储到redis
        ContractOrderInfoDTO contractOrderInfoDTO = new ContractOrderInfoDTO();
        BeanUtils.copyProperties(contractOrder, contractOrderInfoDTO);
        contractOrderInfoDTO.setClosePrice(DpDataUtils.getLatestPrice(redisTemplate, market.getSymbol()));
        contractOrderHashOperations.put(contractOrder.getSymbol(), contractOrder.getOrderNo(), contractOrderInfoDTO);
        return contractOrder;
    }

    //验证止盈止损价
    private void validStopProfitLoss(ContractOrder contractOrder) {
        if (ContractOrderTypeEnum.TYPE1.getCode().equals(contractOrder.getType())) {
            if (contractOrder.getStopLoss() != null && contractOrder.getStopProfit() != null
                    && contractOrder.getStopProfit().compareTo(contractOrder.getStopLoss()) < 0) {
                //做多时  止盈价要大于止损价
                throw new ServiceException(ContractOrderExceptionEnum.STOP_PROFIT_AND_LOSS);
            }
        } else {
            if (contractOrder.getStopLoss() != null && contractOrder.getStopProfit() != null
                    && contractOrder.getStopProfit().compareTo(contractOrder.getStopLoss()) > 0) {
                //做空时  止损价要大于止盈价
                throw new ServiceException(ContractOrderExceptionEnum.STOP_PROFIT_AND_LOSS);
            }
        }
    }

    @Override
    @Transactional
    public void revokeEntrust(Long playerId, String orderNo, Long updateBy) {
        ContractOrder contractOrder = getContractOrderByNoAsLock(orderNo);
        if (!ContractOrderStatusEnum.STATUS0.getCode().equals(contractOrder.getStatus())) {
            throw new ServiceException(ContractOrderExceptionEnum.NOT_EXIST);
        }
        if (!contractOrder.getPlayerId().equals(playerId)) {
            throw new ServiceException(ContractOrderExceptionEnum.NOT_EXIST);
        }
        contractOrder.setUpdateUser(updateBy);
        contractOrder.setUpdateTime(new Date());
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(playerId));
        //变更余额
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder balanceParamBuilder = AccountChangeBalanceParam.builder()
                .playerId(contractOrder.getPlayerId())
                .type(AccountTypeEnum.TYPE1.getCode())
                .currencyCode(MarketUtils.getBaseCode(contractOrder.getSymbol()))
                .businessType(AccountBillBusinessTypeEnum.REVOKE_ENTRUST)
                .optType(AccountBillOptTypeEnum.ADD)
                .businessNo(orderNo)
                //退还保证金
                .balance(contractOrder.getEarnestMoney())
                .updateUser(contractOrder.getUpdateUser());
        accountService.changeBalance(balanceParamBuilder.build());
        //修改状态
        lambdaUpdate()
                .set(ContractOrder::getStatus, ContractOrderStatusEnum.STATUS3.getCode())
                .set(ContractOrder::getUpdateTime, contractOrder.getUpdateTime())
                .set(ContractOrder::getUpdateUser, contractOrder.getUpdateUser())
                .eq(ContractOrder::getId, contractOrder.getId())
                .update();

        //移除redis
        contractOrderHashOperations.delete(contractOrder.getSymbol(), contractOrder.getOrderNo());
    }

    @Override
    @Transactional
    public void endContractOrder(Long playerId, String orderNo, BigDecimal price, Integer forcedFlag) {
        ContractOrder contractOrder = getContractOrderByNoAsLock(orderNo);
        if (!ContractOrderStatusEnum.STATUS1.getCode().equals(contractOrder.getStatus())) {
            if (ContractOrderStatusEnum.STATUS2.getCode().equals(contractOrder.getStatus()) || ContractOrderStatusEnum.STATUS3.getCode().equals(contractOrder.getStatus())) {
                //移除redis
                contractOrderHashOperations.delete(contractOrder.getSymbol(), contractOrder.getOrderNo());
                return;
            }
            throw new ServiceException(ContractOrderExceptionEnum.NOT_EXIST);
        }
        if (playerId != null && !contractOrder.getPlayerId().equals(playerId)) {
            throw new ServiceException(ContractOrderExceptionEnum.NOT_EXIST);
        }
        BigDecimal lastPrice = price;
        if (price == null) {
            lastPrice = DpDataUtils.getLatestPrice(redisTemplate, contractOrder.getSymbol());
            if (lastPrice == null) {
                throw new ServiceException(MarketExceptionEnum.NOT_LAST_PRICE);
            }
        }
        contractOrder.setClosePrice(lastPrice);
        contractOrder.setCloseTime(new Date());
        BigDecimal profit;
        if (forcedFlag == 1) {
            //强制平仓,全输
            profit = contractOrder.getEarnestMoney().negate();
        } else {
            //计算盈亏
            profit = calcProfit(contractOrder.getOrderPrice(), BigDecimal.valueOf(contractOrder.getLever()), contractOrder.getQuantity(), contractOrder.getType(), contractOrder.getEarnestMoney(), contractOrder.getClosePrice());
        }
        contractOrder.setProfit(profit);
        if (profit.negate().compareTo(contractOrder.getEarnestMoney()) != 0) {
            //锁玩家，在外边锁，防止死锁
            playerService.lockPlayer(Lists.newArrayList(playerId));

            BigDecimal amount = contractOrder.getEarnestMoney().add(profit);
            //变更余额
            AccountChangeBalanceParam.AccountChangeBalanceParamBuilder balanceParamBuilder = AccountChangeBalanceParam.builder()
                    .playerId(contractOrder.getPlayerId())
                    .type(AccountTypeEnum.TYPE1.getCode())
                    .currencyCode(MarketUtils.getBaseCode(contractOrder.getSymbol()))
                    .businessType(AccountBillBusinessTypeEnum.END_CONTRACT_ORDER)
                    .optType(AccountBillOptTypeEnum.ADD)
                    .businessNo(orderNo)
                    .balance(amount)
                    .updateUser(contractOrder.getPlayerId());
            accountService.changeBalance(balanceParamBuilder.build());
        }
        //强制平仓标志
        contractOrder.setForcedFlag(forcedFlag);
        contractOrder.setStatus(ContractOrderStatusEnum.STATUS2.getCode());
        updateById(contractOrder);

        //移除redis
        contractOrderHashOperations.delete(contractOrder.getSymbol(), contractOrder.getOrderNo());
    }

    private BigDecimal calcProfit(BigDecimal orderPrice, BigDecimal lever, BigDecimal quantity, Integer type, BigDecimal earnestMoney, BigDecimal closePrice) {
        //(平仓价-买入价)*倍数*手数
        BigDecimal baseAmt = closePrice.subtract(orderPrice).multiply(lever).multiply(quantity);
        if (ContractOrderTypeEnum.TYPE2.getCode().equals(type)) {
            //做空需要换个方向
            baseAmt = baseAmt.negate();
        }
        BigDecimal amount = earnestMoney.add(baseAmt);
        //为正数
        if (BigDecimal.ZERO.compareTo(amount) < 0) {
            return baseAmt;
        } else {
            //为负数，说明已经把保证金全部亏完了
            return earnestMoney.negate();
        }
    }

//    原符号       <       <=      >       >=      <>
//    对应函数    lt()     le()    gt()    ge()    ne()

    /**
     * 撮合
     *
     * @param orderNo
     * @param c
     */
    @Override
    @Transactional
    public void matchContractOrder(String orderNo, BigDecimal c) {
        ContractOrder contractOrder = getContractOrderByNoAsLock(orderNo);
        //委托成功时间
        contractOrder.setOrderPrice(c);
        contractOrder.setOrderTime(new Date());
        contractOrder.setStatus(ContractOrderStatusEnum.STATUS1.getCode());
        lambdaUpdate().eq(ContractOrder::getId, contractOrder.getId()).set(ContractOrder::getOrderPrice, contractOrder.getOrderPrice())
                .set(ContractOrder::getOrderTime, contractOrder.getOrderTime())
                .set(ContractOrder::getStatus, contractOrder.getStatus()).update();

        //更新状态到redis
        ContractOrderInfoDTO contractOrderInfoDTO = contractOrderHashOperations.get(contractOrder.getSymbol(), contractOrder.getOrderNo());
        if (contractOrderInfoDTO == null) {
            contractOrderInfoDTO = new ContractOrderInfoDTO();
            BeanUtils.copyProperties(contractOrder, contractOrderInfoDTO);
        }
        contractOrderInfoDTO.setStatus(contractOrder.getStatus());
        contractOrderHashOperations.put(contractOrder.getSymbol(), contractOrder.getOrderNo(), contractOrderInfoDTO);
    }


    /**
     * 计算合约盈亏
     *
     * @param symbol
     * @param c
     */
    public void calcContractOrderProfit(String symbol, BigDecimal c) {
        try {
            List<ContractOrderInfoDTO> contractOrderInfoDTOS = contractOrderHashOperations.values(symbol);
            for (ContractOrderInfoDTO contractOrderInfoDTO : contractOrderInfoDTOS) {
                contractOrderInfoDTO.setClosePrice(c);
                //计算盈亏
                BigDecimal profit = calcProfit(contractOrderInfoDTO.getOrderPrice(), BigDecimal.valueOf(contractOrderInfoDTO.getLever()), contractOrderInfoDTO.getQuantity(), contractOrderInfoDTO.getType(), contractOrderInfoDTO.getEarnestMoney(), contractOrderInfoDTO.getClosePrice());
                contractOrderInfoDTO.setProfit(profit);
                contractOrderInfoDTO.setClosePrice(c);
                contractOrderHashOperations.put(symbol, contractOrderInfoDTO.getOrderNo(), contractOrderInfoDTO);

                //亏的数量大于等于保证金
                if (profit.negate().compareTo(contractOrderInfoDTO.getEarnestMoney()) >= 0) {
                    //输没有了,自动平仓
                    executorService.execute(() -> {
                        endContractOrder(symbol, contractOrderInfoDTO.getOrderNo(), c, 1);
                    });
                    continue;
                }
                boolean isType1 = ContractOrderTypeEnum.TYPE1.getCode().equals(contractOrderInfoDTO.getType()); //做多
                boolean isType2 = ContractOrderTypeEnum.TYPE2.getCode().equals(contractOrderInfoDTO.getType());  //做空
                //止盈止损处理
                if (profit.compareTo(BigDecimal.ZERO) >= 0) {//盈
                    if (isType1 && contractOrderInfoDTO.getStopProfit() != null && c.compareTo(contractOrderInfoDTO.getStopProfit()) >= 0) {
                        executorService.execute(() -> {
                            endContractOrder(symbol, contractOrderInfoDTO.getOrderNo(), c, 0);
                        });
                    }
                    if (isType2 && contractOrderInfoDTO.getStopProfit() != null && c.compareTo(contractOrderInfoDTO.getStopProfit()) <= 0) {
                        executorService.execute(() -> {
                            endContractOrder(symbol, contractOrderInfoDTO.getOrderNo(), c, 0);
                        });
                    }
                    continue;
                }
                if (isType1 && contractOrderInfoDTO.getStopLoss() != null && c.compareTo(contractOrderInfoDTO.getStopLoss()) <= 0) {
                    executorService.execute(() -> {
                        endContractOrder(symbol, contractOrderInfoDTO.getOrderNo(), c, 0);
                    });
                }
                if (isType2 && contractOrderInfoDTO.getStopLoss() != null && c.compareTo(contractOrderInfoDTO.getStopLoss()) >= 0) {
                    executorService.execute(() -> {
                        endContractOrder(symbol, contractOrderInfoDTO.getOrderNo(), c, 0);
                    });
                }
            }
        } catch (Exception e) {
            logger.error("根据最新价计算合约订单异常{},{}", symbol, c, e);
        }
    }

    public void endContractOrder(String symbol, String orderNo, BigDecimal c, Integer forcedFlag) {
        try {
            contractOrderCloseService.endContractOrder(orderNo, c, forcedFlag);
        } catch (Exception ae) {
            logger.error("合约系统平仓异常{},{}", orderNo, c, ae);
            if (ae instanceof ServiceException) {
                ServiceException serviceException = (ServiceException) ae;
                if (serviceException.getExceptionEnum() == ContractOrderExceptionEnum.NOT_EXIST) {
                    //移除redis
                    logger.error("合约系统平仓没有找到mysql数据,移除" + symbol + " " + orderNo);
                    contractOrderHashOperations.delete(symbol, orderNo);
                }
            }
        }
    }

    @Override
    public ContractOrderStatisticModel findContactOrderStatistic(HomeStatisticParam param) {
        return getBaseMapper().findContactOrderStatistic(param);
    }

    public ContractOrder getContractOrder(Long playerId, String orderNo) {
        ContractOrder contractOrder = lambdaQuery().eq(ContractOrder::getPlayerId, playerId).eq(ContractOrder::getOrderNo, orderNo).one();
        if (ObjectUtil.isNull(contractOrder)) {
            throw new ServiceException(ContractOrderExceptionEnum.NOT_EXIST);
        }
        return contractOrder;
    }

    @Override
    public void setStopProfitAndLoss(Long playerId, SetStopProfitAndLossModel model) {
        ContractOrder contractOrder = getContractOrder(playerId, model.getOrderNo());
        contractOrder.setStopProfit(model.getStopProfit());
        contractOrder.setStopLoss(model.getStopLoss());
        //验证止盈止损价
        validStopProfitLoss(contractOrder);
        lambdaUpdate()
                .set(ContractOrder::getStopLoss, model.getStopLoss()).set(ContractOrder::getStopProfit, model.getStopProfit())
                .eq(ContractOrder::getId, contractOrder.getId())
                .update();
    }

    public ContractOrder getContractOrderByNoAsLock(String orderNo) {
        //行锁
        ContractOrder contractOrder = lambdaQuery().eq(ContractOrder::getOrderNo, orderNo).last(" for update").one();
        if (ObjectUtil.isNull(contractOrder)) {
            throw new ServiceException(ContractOrderExceptionEnum.NOT_EXIST);
        }
        return contractOrder;
    }

    /**
     * 获取合约订单
     *
     * <AUTHOR>
     * @date 2022-04-19 14:17:12
     */
    private ContractOrder queryContractOrder(ContractOrderParam contractOrderParam) {
        ContractOrder contractOrder = this.getById(contractOrderParam.getId());
        if (ObjectUtil.isNull(contractOrder)) {
            throw new ServiceException(ContractOrderExceptionEnum.NOT_EXIST);
        }
        return contractOrder;
    }

    @Override
    public void destroy() throws Exception {
        executorService.shutdownNow();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        executorService = Executors.newFixedThreadPool(5);
    }
}
