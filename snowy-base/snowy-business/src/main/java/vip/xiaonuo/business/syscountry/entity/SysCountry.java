package vip.xiaonuo.business.syscountry.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;

import java.math.BigDecimal;

/**
 * 国家
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_country")
public class SysCountry extends BaseEntity {

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 国家英文名
     */
    private String englishName;
    private String viName;
    private String idName;
    /**
     * 是否删除
     */
   private Boolean delFlag;


}
