package vip.xiaonuo.business.contractorder.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel("合约下单")
public class PlaceContractOrderModel {
    @ApiModelProperty("如ETH_USDT")
    @NotEmpty
    private String symbol;
    @ApiModelProperty("类型(1:做多;2:做空;)")
    @NotNull
    private Integer type;
    @ApiModelProperty(" 委托类型(1:市价委托;2:现价委托;)")
    @NotNull
    private Integer entrustType;
    @ApiModelProperty("下单数量")
    @NotNull
    private BigDecimal quantity;
    @NotNull
    @ApiModelProperty("杠杆")
    private Integer lever;
    @ApiModelProperty(name = "价格", notes = "现价委托时才需要")
    private BigDecimal price;
    @ApiModelProperty("止损价")
    private BigDecimal stopLoss;
    @ApiModelProperty("止盈价")
    private BigDecimal stopProfit;
}
