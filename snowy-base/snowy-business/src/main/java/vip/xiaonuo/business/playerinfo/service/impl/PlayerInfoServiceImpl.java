package vip.xiaonuo.business.playerinfo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.playerinfo.entity.PlayerInfo;
import vip.xiaonuo.business.playerinfo.enums.PlayerInfoExceptionEnum;
import vip.xiaonuo.business.playerinfo.mapper.PlayerInfoMapper;
import vip.xiaonuo.business.playerinfo.param.PlayerAdvancedCertificationParam;
import vip.xiaonuo.business.playerinfo.param.PlayerInfoAuditParam;
import vip.xiaonuo.business.playerinfo.param.PlayerInfoParam;
import vip.xiaonuo.business.playerinfo.param.PlayerJuniorCertificationParam;
import vip.xiaonuo.business.playerinfo.pojo.dto.FindPlayerInfoForPageDto;
import vip.xiaonuo.business.playerinfo.pojo.vo.FindPlayerInfoForPageVo;
import vip.xiaonuo.business.playerinfo.service.PlayerInfoService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.telegrambot.TelegramBot;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 认证审核service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:38
 */
@Service
public class PlayerInfoServiceImpl extends ServiceImpl<PlayerInfoMapper, PlayerInfo> implements PlayerInfoService {
    @Autowired(required = false)
    private TelegramBot telegramBot;
    @Autowired
    PlayerInfoMapper playerInfoMapper;
    @Resource
    @Lazy
    private PlayerService playerService;

    @Override
    public PageResult<PlayerInfo> page(PlayerInfoParam playerInfoParam) {
        QueryWrapper<PlayerInfo> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(playerInfoParam)) {

            // 根据用户id 查询
            if (ObjectUtil.isNotEmpty(playerInfoParam.getPlayerId())) {
                queryWrapper.lambda().eq(PlayerInfo::getPlayerId, playerInfoParam.getPlayerId());
            }
            // 根据真实姓名 查询
            if (ObjectUtil.isNotEmpty(playerInfoParam.getRealName())) {
                queryWrapper.lambda().eq(PlayerInfo::getRealName, playerInfoParam.getRealName());
            }
            // 根据国家 查询
            if (ObjectUtil.isNotEmpty(playerInfoParam.getCountry())) {
                queryWrapper.lambda().eq(PlayerInfo::getCountry, playerInfoParam.getCountry());
            }
            // 根据身份证正面 查询
            if (ObjectUtil.isNotEmpty(playerInfoParam.getIdCardJust())) {
                queryWrapper.lambda().eq(PlayerInfo::getIdCardJust, playerInfoParam.getIdCardJust());
            }
            // 根据身份证反面 查询
            if (ObjectUtil.isNotEmpty(playerInfoParam.getIdCardBack())) {
                queryWrapper.lambda().eq(PlayerInfo::getIdCardBack, playerInfoParam.getIdCardBack());
            }
            // 根据手持身份证 查询
            if (ObjectUtil.isNotEmpty(playerInfoParam.getHoldIdCard())) {
                queryWrapper.lambda().eq(PlayerInfo::getHoldIdCard, playerInfoParam.getHoldIdCard());
            }
            // 根据身份证号码 查询
            if (ObjectUtil.isNotEmpty(playerInfoParam.getIdCard())) {
                queryWrapper.lambda().eq(PlayerInfo::getIdCard, playerInfoParam.getIdCard());
            }
            // 根据居住地址 查询
            if (ObjectUtil.isNotEmpty(playerInfoParam.getLiveAddress())) {
                queryWrapper.lambda().eq(PlayerInfo::getLiveAddress, playerInfoParam.getLiveAddress());
            }
            // 根据认证状态 1 审核中 2 审核通过  3 审核不通过 4 延迟审核 查询
            if (ObjectUtil.isNotEmpty(playerInfoParam.getAuthStatus())) {
                queryWrapper.lambda().eq(PlayerInfo::getAuthStatus, playerInfoParam.getAuthStatus());
            }
            // 根据认证时间 查询
            if (ObjectUtil.isNotEmpty(playerInfoParam.getAuthTime())) {
                queryWrapper.lambda().eq(PlayerInfo::getAuthTime, playerInfoParam.getAuthTime());
            }
            // 根据审核时间 查询
            if (ObjectUtil.isNotEmpty(playerInfoParam.getCheckTime())) {
                queryWrapper.lambda().eq(PlayerInfo::getCheckTime, playerInfoParam.getCheckTime());
            }
            // 根据审核人员 查询
            if (ObjectUtil.isNotEmpty(playerInfoParam.getCheckId())) {
                queryWrapper.lambda().eq(PlayerInfo::getCheckId, playerInfoParam.getCheckId());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<PlayerInfo> list(PlayerInfoParam playerInfoParam) {
        return this.list();
    }

    @Override
    public void add(PlayerInfoParam playerInfoParam) {
        PlayerInfo playerInfo = new PlayerInfo();
        BeanUtil.copyProperties(playerInfoParam, playerInfo);
        this.save(playerInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<PlayerInfoParam> playerInfoParamList) {
        playerInfoParamList.forEach(playerInfoParam -> {
            PlayerInfo playerInfo = this.queryPlayerInfo(playerInfoParam);
            this.removeById(playerInfo.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PlayerInfoParam playerInfoParam) {
        PlayerInfo playerInfo = this.queryPlayerInfo(playerInfoParam);
        BeanUtil.copyProperties(playerInfoParam, playerInfo);
        this.updateById(playerInfo);
    }

    @Override
    public PlayerInfo detail(PlayerInfoParam playerInfoParam) {
        return this.queryPlayerInfo(playerInfoParam);
    }

    /**
     * 查询认证审核
     *
     * @param playerInfoParam
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    @Override
    public PageResult<FindPlayerInfoForPageVo> findPlayerInfoForPage(FindPlayerInfoForPageDto playerInfoParam) {
        Page<FindPlayerInfoForPageVo> playerInfoForPage = playerInfoMapper.findPlayerInfoForPage(PageFactory.defaultPage(), playerInfoParam);
        return new PageResult<>(playerInfoForPage);
    }

    /**
     * 获取认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    private PlayerInfo queryPlayerInfo(PlayerInfoParam playerInfoParam) {
        PlayerInfo playerInfo = this.getById(playerInfoParam.getId());
        if (ObjectUtil.isNull(playerInfo)) {
            throw new ServiceException(PlayerInfoExceptionEnum.NOT_EXIST);
        }
        return playerInfo;
    }

    /**
     * 根据用户id获取认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    @Override
    public PlayerInfo queryPlayerUserInfo(Long playerId) {
        QueryWrapper<PlayerInfo> queryWrapper = new QueryWrapper<>();
        // 根据用户id 查询
        queryWrapper.lambda().eq(PlayerInfo::getPlayerId, playerId);
        return this.getOne(queryWrapper);
    }

    @Override
    public void junior(Long playerId, PlayerJuniorCertificationParam playerInfoParam) {
        PlayerInfo playerInfo = queryPlayerUserInfo(playerId);
        if (playerInfo.getAuthStatus() == 2) {
            //已审核通过
            return;
        }
        Player player = playerService.getById(playerInfo.getPlayerId());
        lambdaUpdate().set(PlayerInfo::getType, playerInfoParam.getType())
                .set(PlayerInfo::getRealName, playerInfoParam.getRealName())
                .set(PlayerInfo::getIdCard, playerInfoParam.getIdCard())
                .set(PlayerInfo::getAuthStatus, 1)
                .set(PlayerInfo::getAuthTime, new Date())
                .eq(PlayerInfo::getId, playerInfo.getId()).update();
        String devMsg = "<b>[初级]有新的KYC</b>\n" +
                "<i>玩家登录名</i>：" + player.getAccount() + "\n" +
                "<i>真实姓名</i>：" + playerInfoParam.getRealName();
        if (telegramBot != null) {
            telegramBot.sendDevMessage(devMsg);
        }
    }

    @Override
    public void advanced(Long playerId, PlayerAdvancedCertificationParam playerInfoParam) {
        PlayerInfo playerInfo = queryPlayerUserInfo(playerId);
        if (playerInfo.getAuthStatus() != 2) {
            //初级没有审核通过
            return;
        }
        if (playerInfo.getAdvancedStatus() != null && playerInfo.getAdvancedStatus() == 2) {
            //已审核通过
            return;
        }
        Player player = playerService.getById(playerInfo.getPlayerId());
        lambdaUpdate().set(PlayerInfo::getAdvancedRealName, playerInfoParam.getAdvancedRealName())
                .set(PlayerInfo::getBankCardNo, playerInfoParam.getBankCardNo())
                .set(PlayerInfo::getIdCardBack, playerInfoParam.getIdCardBack())
                .set(PlayerInfo::getIdCardJust, playerInfoParam.getIdCardJust())
                .set(PlayerInfo::getHoldIdCard, playerInfoParam.getHoldIdCard())
                .set(PlayerInfo::getAdvancedStatus, 1)
                .set(PlayerInfo::getAdvancedTime, new Date())
                .eq(PlayerInfo::getId, playerInfo.getId()).update();
        String devMsg = "<b>[高级]有新的KYC</b>\n" +
                "<i>玩家登录名</i>：" + player.getAccount() + "\n" +
                "<i>真实姓名</i>：" + playerInfoParam.getAdvancedRealName();
        if (telegramBot != null) {
            telegramBot.sendDevMessage(devMsg);
        }
    }

    @Override
    @Transactional
    public void auditStatus(Long loginUserId, PlayerInfoAuditParam playerInfoParam) {
        PlayerInfo playerInfo = getById(playerInfoParam.getId());
        if (playerInfoParam.getAuthStatus() != null) {
            lambdaUpdate().set(PlayerInfo::getAuthStatus, playerInfoParam.getAuthStatus())
                    .set(PlayerInfo::getCheckId, loginUserId)
                    .set(PlayerInfo::getCheckTime, new Date())
                    .eq(PlayerInfo::getId, playerInfo.getId()).update();
        }
        if (playerInfoParam.getAdvancedStatus() != null) {
            lambdaUpdate().set(PlayerInfo::getAdvancedStatus, playerInfoParam.getAdvancedStatus())
                    .set(PlayerInfo::getAdvancedCheckId, loginUserId)
                    .set(PlayerInfo::getAdvancedCheckTime, new Date())
                    .eq(PlayerInfo::getId, playerInfo.getId()).update();
        }
    }
}
