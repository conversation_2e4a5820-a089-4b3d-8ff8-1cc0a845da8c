/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.aiaccounttransaction.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.aiaccounttransaction.entity.AiAccountTransaction;
import vip.xiaonuo.business.aiaccounttransaction.enums.AiAccountTransactionExceptionEnum;
import vip.xiaonuo.business.aiaccounttransaction.mapper.AiAccountTransactionMapper;
import vip.xiaonuo.business.aiaccounttransaction.param.AiAccountTransactionParam;
import vip.xiaonuo.business.aiaccounttransaction.service.AiAccountTransactionService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.List;

/**
 * AI银行账户流水记录表service接口实现类
 *
 * <AUTHOR>
 * @date 2025-08-04 11:32:52
 */
@Service
public class AiAccountTransactionServiceImpl extends ServiceImpl<AiAccountTransactionMapper, AiAccountTransaction> implements AiAccountTransactionService {

    @Override
    public PageResult<AiAccountTransaction> page(AiAccountTransactionParam aiAccountTransactionParam) {
        QueryWrapper<AiAccountTransaction> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(aiAccountTransactionParam)) {

            // 根据玩家id 查询
            if (ObjectUtil.isNotEmpty(aiAccountTransactionParam.getPlayerId())) {
                queryWrapper.lambda().eq(AiAccountTransaction::getPlayerId, aiAccountTransactionParam.getPlayerId());
            }
            // 根据币种 查询
            if (ObjectUtil.isNotEmpty(aiAccountTransactionParam.getSymbol())) {
                queryWrapper.lambda().eq(AiAccountTransaction::getSymbol, aiAccountTransactionParam.getSymbol());
            }
            // 根据AI账户id（tt_ai_account.id） 查询
            if (ObjectUtil.isNotEmpty(aiAccountTransactionParam.getAccountId())) {
                queryWrapper.lambda().eq(AiAccountTransaction::getAccountId, aiAccountTransactionParam.getAccountId());
            }
            // 根据记录类型（1=存入，2=收益，3=取出） 查询
            if (ObjectUtil.isNotEmpty(aiAccountTransactionParam.getType())) {
                queryWrapper.lambda().eq(AiAccountTransaction::getType, aiAccountTransactionParam.getType());
            }
            // 根据金额 查询
            if (ObjectUtil.isNotEmpty(aiAccountTransactionParam.getAmount())) {
                queryWrapper.lambda().eq(AiAccountTransaction::getAmount, aiAccountTransactionParam.getAmount());
            }
            // 根据备注 查询
            if (ObjectUtil.isNotEmpty(aiAccountTransactionParam.getRemark())) {
                queryWrapper.lambda().eq(AiAccountTransaction::getRemark, aiAccountTransactionParam.getRemark());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AiAccountTransaction> list(AiAccountTransactionParam aiAccountTransactionParam) {
        return this.list();
    }

    @Override
    public void add(AiAccountTransactionParam aiAccountTransactionParam) {
        AiAccountTransaction aiAccountTransaction = new AiAccountTransaction();
        BeanUtil.copyProperties(aiAccountTransactionParam, aiAccountTransaction);
        this.save(aiAccountTransaction);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<AiAccountTransactionParam> aiAccountTransactionParamList) {
        aiAccountTransactionParamList.forEach(aiAccountTransactionParam -> {
            AiAccountTransaction aiAccountTransaction = this.queryAiAccountTransaction(aiAccountTransactionParam);
            this.removeById(aiAccountTransaction.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AiAccountTransactionParam aiAccountTransactionParam) {
        AiAccountTransaction aiAccountTransaction = this.queryAiAccountTransaction(aiAccountTransactionParam);
        BeanUtil.copyProperties(aiAccountTransactionParam, aiAccountTransaction);
        this.updateById(aiAccountTransaction);
    }

    @Override
    public AiAccountTransaction detail(AiAccountTransactionParam aiAccountTransactionParam) {
        return this.queryAiAccountTransaction(aiAccountTransactionParam);
    }

    /**
     * 获取AI银行账户流水记录表
     *
     * <AUTHOR>
     * @date 2025-08-04 11:32:52
     */
    private AiAccountTransaction queryAiAccountTransaction(AiAccountTransactionParam aiAccountTransactionParam) {
        AiAccountTransaction aiAccountTransaction = this.getById(aiAccountTransactionParam.getId());
        if (ObjectUtil.isNull(aiAccountTransaction)) {
            throw new ServiceException(AiAccountTransactionExceptionEnum.NOT_EXIST);
        }
        return aiAccountTransaction;
    }
}
