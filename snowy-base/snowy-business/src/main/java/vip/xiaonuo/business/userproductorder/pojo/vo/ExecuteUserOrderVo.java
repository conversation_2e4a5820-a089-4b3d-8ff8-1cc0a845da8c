package vip.xiaonuo.business.userproductorder.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户理财订单统计
 */
@ApiModel(value = "用户赎回")
@Data
public class ExecuteUserOrderVo implements Serializable {
    /**
     * 托管资金
     */
    @ApiModelProperty(value = "托管资金", required = true)
    private BigDecimal depositAccount = BigDecimal.ZERO;
    /**
     * 今日预计收益
     */
    @ApiModelProperty(value = "今日预计收益", required = true)
    private BigDecimal estimatedIncomeDay =BigDecimal.ZERO;

    /**
     * 累计收益
     */
    @ApiModelProperty(value = "累计收益", required = true)
    private BigDecimal profitSum = BigDecimal.ZERO;
    /**
     * 托管中订单
     */
    @ApiModelProperty(value = "托管中订单", required = true)
    private Integer depositOrderCount=0;


}
