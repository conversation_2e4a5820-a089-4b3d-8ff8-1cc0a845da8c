package vip.xiaonuo.business.account.pojo.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 */
@Data
public class FindUserAccountListVo implements Serializable {

    /**
     * 名称
     */
    private String name;

    /**
     * logo
     */
    private String logo;

    /**
     * 代码
     */
    private String code;

    /**
     * 提现手续费
     */
    private BigDecimal withdrawFee;

    /**
     * 最小提币数量
     */
    private BigDecimal minWithdrawAmount;

    /**
     * 单次最大提币额
     */
    private BigDecimal currencyWithdrawLimit;

    /**
     * 余额
     */
    private BigDecimal balance;
}
