/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.follower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.follower.entity.Follower;
import vip.xiaonuo.business.follower.enums.FollowerExceptionEnum;
import vip.xiaonuo.business.follower.mapper.FollowerMapper;
import vip.xiaonuo.business.follower.param.FollowerParam;
import vip.xiaonuo.business.follower.service.FollowerService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.List;

/**
 * 交易员service接口实现类
 *
 * <AUTHOR>
 * @date 2025-07-20 13:22:48
 */
@Service
public class FollowerServiceImpl extends ServiceImpl<FollowerMapper, Follower> implements FollowerService {

    @Override
    public PageResult<Follower> page(FollowerParam followerParam) {
        QueryWrapper<Follower> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(followerParam)) {

            // 根据用户id 查询
            if (ObjectUtil.isNotEmpty(followerParam.getPlayerId())) {
                queryWrapper.lambda().eq(Follower::getPlayerId, followerParam.getPlayerId());
            }
            // 根据个人简介 查询
            if (ObjectUtil.isNotEmpty(followerParam.getProfile())) {
                queryWrapper.lambda().eq(Follower::getProfile, followerParam.getProfile());
            }
            // 根据标签（英文逗号分隔） 查询
            if (ObjectUtil.isNotEmpty(followerParam.getTagList())) {
                queryWrapper.lambda().eq(Follower::getTagList, followerParam.getTagList());
            }
            // 根据交易次数 查询
            if (ObjectUtil.isNotEmpty(followerParam.getTradeCount())) {
                queryWrapper.lambda().eq(Follower::getTradeCount, followerParam.getTradeCount());
            }
            // 根据风险评分（0~100） 查询
            if (ObjectUtil.isNotEmpty(followerParam.getRiskScore())) {
                queryWrapper.lambda().eq(Follower::getRiskScore, followerParam.getRiskScore());
            }
            // 根据用户评分（0~5） 查询
            if (ObjectUtil.isNotEmpty(followerParam.getUserScore())) {
                queryWrapper.lambda().eq(Follower::getUserScore, followerParam.getUserScore());
            }
            // 根据最近30天盈利率（%） 查询
            if (ObjectUtil.isNotEmpty(followerParam.getProfitRate30d())) {
                queryWrapper.lambda().eq(Follower::getProfitRate30d, followerParam.getProfitRate30d());
            }
            // 根据胜率（%） 查询
            if (ObjectUtil.isNotEmpty(followerParam.getWinRate())) {
                queryWrapper.lambda().eq(Follower::getWinRate, followerParam.getWinRate());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<Follower> list(FollowerParam followerParam) {
        return this.list();
    }

    @Override
    public void add(FollowerParam followerParam) {
        Follower follower = new Follower();
        BeanUtil.copyProperties(followerParam, follower);
        this.save(follower);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<FollowerParam> followerParamList) {
        followerParamList.forEach(followerParam -> {
            Follower follower = this.queryFollower(followerParam);
            this.removeById(follower.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(FollowerParam followerParam) {
        Follower follower = this.queryFollower(followerParam);
        BeanUtil.copyProperties(followerParam, follower);
        this.updateById(follower);
    }

    @Override
    public Follower detail(FollowerParam followerParam) {
        return this.queryFollower(followerParam);
    }

    /**
     * 获取交易员
     *
     * <AUTHOR>
     * @date 2025-07-20 13:22:48
     */
    private Follower queryFollower(FollowerParam followerParam) {
        Follower follower = this.getById(followerParam.getId());
        if (ObjectUtil.isNull(follower)) {
            throw new ServiceException(FollowerExceptionEnum.NOT_EXIST);
        }
        return follower;
    }
}
