package vip.xiaonuo.business.coinorder.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import vip.xiaonuo.business.coinorder.entity.CoinOrder;
import vip.xiaonuo.business.coinorder.enums.CoinOrderStatusEnum;
import vip.xiaonuo.business.coinorder.enums.CoinOrderTypeEnum;
import vip.xiaonuo.business.dict.service.SysDictDataService;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.consts.DictConstant;
import vip.xiaonuo.core.util.RedisLock;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Service
public class CoinOrderEndService {
    private Logger logger = LoggerFactory.getLogger(CoinOrderEndService.class);
    @Resource
    @Lazy
    private CoinOrderService coinOrderService;
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private SysDictDataService sysDictDataService;
    @Resource
    private MarketService marketService;

    @Resource
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }


    /**
     * 处理撮合
     */
    @Async
    public void handleCoinOrderTicker() {
        Boolean autoSettled = sysDictDataService.getCommonConfigAsBoolean(DictConstant.COIN_AUTOSETTLED);
        if (!autoSettled) {
            return;
        }
        String symbol = "all";
        String redisKey = "r_coin_order_match:" + symbol;
        try {
            RedisLock redisLock = new RedisLock(redisTemplate, redisKey, 0, 60 * 1000);
            try {
                if (redisLock.lock()) {
                    /*
                     * 卖：价格少于等于当前价，委托成功
                     * 买：价格大于等于当前价，委托成功
                     */
                    List<CoinOrder> coinOrders = coinOrderService.lambdaQuery().eq(CoinOrder::getStatus, CoinOrderStatusEnum.STATUS0.getCode())
                            .select(CoinOrder::getOrderNo, CoinOrder::getType, CoinOrder::getOrderPrice, CoinOrder::getSymbol).list();
                    for (CoinOrder coinOrder : coinOrders) {
                        BigDecimal price = marketService.getLatestPrice(coinOrder.getSymbol());
                        if (price == null) {
                            continue;
                        }
                        if (CoinOrderTypeEnum.TYPE1.getCode().equals(coinOrder.getType())) {
                            /*
                             * 买：price >= 当前价
                             */
                            if (coinOrder.getOrderPrice().compareTo(price) >= 0) {
                                try {
                                    coinOrderService.endCoinOrder(coinOrder.getOrderNo(), price, CommonConstant.SYS_ID);
                                } catch (Exception e) {
                                    logger.error("撮合币币委托订单异常,订单号：{}", coinOrder.getOrderNo(), e);
                                }
                            }
                        }
                        if (CoinOrderTypeEnum.TYPE2.getCode().equals(coinOrder.getType())) {
                            /*
                             * 卖：price <= 当前价
                             */
                            if (coinOrder.getOrderPrice().compareTo(price) <= 0) {
                                try {
                                    coinOrderService.endCoinOrder(coinOrder.getOrderNo(), price, CommonConstant.SYS_ID);
                                } catch (Exception e) {
                                    logger.error("撮合币币委托订单异常,订单号：{}", coinOrder.getOrderNo(), e);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("获取币币撮合锁异常", e);
            } finally {
                redisLock.unlock();
            }
        } catch (Exception e) {
            logger.error("币币撮合异常", e);
        }
    }

}
