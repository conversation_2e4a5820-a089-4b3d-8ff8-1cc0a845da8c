package vip.xiaonuo.business.coinorder.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("币币单查询")
public class QueryCoinOrderModel {
    @ApiModelProperty("0:委托,2:成交,3:撤销委托,4:锁仓")
    @NotNull
    private Integer status;
    @ApiModelProperty("如ETH_USDT")
    private String symbol;
}
