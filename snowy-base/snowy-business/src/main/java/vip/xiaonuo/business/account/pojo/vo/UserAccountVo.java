package vip.xiaonuo.business.account.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class UserAccountVo implements Serializable {

    /**
     * 币名称
     */
    @ApiModelProperty("币种")
    private String currencyCode;
    /**
     * 资产
     */
    @ApiModelProperty("可用")
    private BigDecimal balance;
    /**
     * 冻结金额
     */
    @ApiModelProperty("冻结")
    private BigDecimal frozen = BigDecimal.ZERO;

    /**
     * 币金额
     */
    @ApiModelProperty("折合(USDT)")
    private BigDecimal usdt = BigDecimal.ZERO;

    /**
     * logo
     */
    private String logo;


    /**
     * 充值开关：0:关 1:开
     */
    @ApiModelProperty("充值开关：0:关 1:开")
    private Integer chargeSwitch;

    /**
     * 提现开关：0关，1开
     */
    @ApiModelProperty("提现开关：0关，1开")
    private Integer withdrawSwitch;
    
}
