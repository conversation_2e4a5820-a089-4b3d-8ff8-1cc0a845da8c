package vip.xiaonuo.business.spotorder.service;

import cn.hutool.core.map.MapUtil;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import vip.xiaonuo.core.consts.RedisConstant;
import vip.xiaonuo.core.context.constant.ConstantContextHolder;
import vip.xiaonuo.core.exception.ServiceException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class SpotScaleHelper implements InitializingBean {
    public static final String TOTAL = "total";
    public static final String LOSE = "lose";
    public static final String WIN = "win";
    private HashOperations<String, String, String> stringHashOperations;

    @Resource
    public void setStringRedisTemplate(StringRedisTemplate stringRedisTemplate) {
        stringHashOperations = stringRedisTemplate.opsForHash();
    }

    public Map<String, String> getPlayerSpotScaleMap(Long playerId) {
        Map<String, String> spotScaleMap = stringHashOperations.entries(RedisConstant.PLAYER_SPOT_SCALE + playerId);
        if (MapUtil.isEmpty(spotScaleMap)) {
            spotScaleMap = new ConcurrentHashMap<>();
            spotScaleMap.put(TOTAL, "0");
            spotScaleMap.put(LOSE, "0");
            spotScaleMap.put(WIN, "0");
        }
        return spotScaleMap;
    }

    public Map<String, String> getSpotScaleMap(String key) {
        Map<String, String> spotScaleMap = stringHashOperations.entries(RedisConstant.SPOT_SCALE + key);
        if (MapUtil.isEmpty(spotScaleMap)) {
            spotScaleMap = new ConcurrentHashMap<>();
            spotScaleMap.put(TOTAL, "0");
            spotScaleMap.put(LOSE, "0");
            spotScaleMap.put(WIN, "0");
        }
        return spotScaleMap;
    }

    public Map.Entry<String, String> getEntryByQuantity(BigDecimal quantity) {
        Map<String, String> oldMap = stringHashOperations.entries(RedisConstant.SPOT_SCALE_SETTING);
        Set<Map.Entry<String, String>> entrySet = oldMap.entrySet();
        for (Map.Entry<String, String> entry : entrySet) {
            String[] keyArr = entry.getKey().split("-");
            BigDecimal start = new BigDecimal(keyArr[0]);
            BigDecimal end = new BigDecimal(keyArr[1]);
            if (quantity.compareTo(start) >= 0 && quantity.compareTo(end) <= 0) {
                return entry;
            }
        }
        return null;
    }

    public Integer getSpotScaleControl(Map<String, String> spotScaleMap, Integer spotScale) {
        float scale = Float.parseFloat(spotScaleMap.get(WIN)) / Float.parseFloat(spotScaleMap.get(TOTAL));
        if (scale > spotScale.floatValue()) {
            return 0;
        }
        return -1;
    }

    public void initRiskControl(String spotScale) {
        String[] arr = spotScale.split("\\|");
        Map<String, String> map = new LinkedHashMap<>();
        for (String a : arr) {
            String[] s = a.split(":");
            String key = s[0];
            if (!key.contains("-")) {
                throw new ServiceException(500, "没有包含-");
            }
            map.put(key, String.valueOf(Integer.parseInt(s[1])));
        }
        Set<Map.Entry<String, String>> entrySet = map.entrySet();
        Map.Entry<String, String> prevEntry = null;
        for (Map.Entry<String, String> entry : entrySet) {
            String[] keyArr = entry.getKey().split("-");
            int start = Integer.parseInt(keyArr[0]);
            Integer end = Integer.parseInt(keyArr[1]);
            if (prevEntry == null) {
                if (start != 1) {
                    throw new ServiceException(500, "起始必须为1");
                }
            } else {
                String[] prevKeyArr = prevEntry.getKey().split("-");
                Integer prevStart = Integer.parseInt(prevKeyArr[0]);
                int prevEnd = Integer.parseInt(prevKeyArr[1]);
                if ((prevEnd + 1) != start) {
                    throw new ServiceException(500, "起始与结尾没有对上");
                }
            }
            prevEntry = entry;
        }
//        Map<String, String> oldMap = stringHashOperations.entries(RedisConstant.SPOT_SCALE_SETTING);
        stringHashOperations.putAll(RedisConstant.SPOT_SCALE_SETTING, map);
    }

    @Override
    public void afterPropertiesSet() throws
            Exception {
        initRiskControl(ConstantContextHolder.getRiskControlSpotScale());
    }

    public void putPlayerSpotScaleNumber(Long playerId, Map<String, String> playerSpotScaleMap, boolean isWin) {
        int total = Integer.parseInt(playerSpotScaleMap.get(TOTAL));
        playerSpotScaleMap.put(TOTAL, String.valueOf(total + 1));
        if (isWin) {
            int win = Integer.parseInt(playerSpotScaleMap.get(WIN));
            playerSpotScaleMap.put(WIN, String.valueOf(win + 1));
        } else {
            int lose = Integer.parseInt(playerSpotScaleMap.get(LOSE));
            playerSpotScaleMap.put(LOSE, String.valueOf(lose + 1));
        }
        stringHashOperations.putAll(RedisConstant.PLAYER_SPOT_SCALE + playerId, playerSpotScaleMap);
    }

    public void putSpotScaleNumber(String key, Map<String, String> spotScaleMap, boolean isWin) {
        int total = Integer.parseInt(spotScaleMap.get(TOTAL));
        spotScaleMap.put(TOTAL, String.valueOf(total + 1));
        if (isWin) {
            int win = Integer.parseInt(spotScaleMap.get(WIN));
            spotScaleMap.put(WIN, String.valueOf(win + 1));
        } else {
            int lose = Integer.parseInt(spotScaleMap.get(LOSE));
            spotScaleMap.put(LOSE, String.valueOf(lose + 1));
        }
        stringHashOperations.putAll(RedisConstant.SPOT_SCALE + key, spotScaleMap);
    }
}
