package vip.xiaonuo.business.coinorder.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel("币币下单")
public class PlaceCoinOrderModel {
    @ApiModelProperty("交易类型,如ETH_USDT")
    @NotEmpty
    private String symbol;
    @ApiModelProperty("类型(1:买;2:卖;)")
    @NotNull
    private Integer type;
    @ApiModelProperty(" 委托类型(1:市价委托;2:限价委托;)")
    @NotNull
    private Integer entrustType;
    @ApiModelProperty("下单数量")
    @NotNull
    private BigDecimal quantity;
    @ApiModelProperty(name = "价格", notes = "限价委托时才需要")
    private BigDecimal price;
}
