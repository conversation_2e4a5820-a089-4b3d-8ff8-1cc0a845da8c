package vip.xiaonuo.business.exchangerate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.business.exchangerate.entity.ExchangeRate;
import vip.xiaonuo.business.exchangerate.param.ExchangeRateParam;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;

import java.math.BigDecimal;
import java.util.List;

/**
 * 汇率（同步）service接口
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:32
 */
public interface ExchangeRateService extends IService<ExchangeRate> {

    /**
     * 查询汇率（同步）
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    PageResult<ExchangeRate> page(ExchangeRateParam exchangeRateParam);

    /**
     * 汇率（同步）列表
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    List<ExchangeRate> list(ExchangeRateParam exchangeRateParam);

    /**
     * 添加汇率（同步）
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    void add(ExchangeRateParam exchangeRateParam);

    /**
     * 删除汇率（同步）
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    void delete(List<ExchangeRateParam> exchangeRateParamList);

    /**
     * 编辑汇率（同步）
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    void edit(ExchangeRateParam exchangeRateParam);

    /**
     * 查看汇率（同步）
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
     ExchangeRate detail(ExchangeRateParam exchangeRateParam);

    /**
     * 汇率同步定时任务
     * @return
     */
    ResponseData executeExchangeRateTask();

    /**
     * 根据汇率名称获取费率（如果当天不存在，获取前一天）
     * @param exchangeRateName
     * @return
     */
    ExchangeRate  findExchangeRate(String exchangeRateName);

    /**
     * 换算
     * @param currentName 当前单位
     * @param account 换算金额
     * @param conversionName 换算单位
     * @return 得到换算后数据
     */
    BigDecimal executeExchangeRateConversion(String currentName,BigDecimal account,String conversionName);

    /**
     *获取费率
     * @return
     */
    ResponseData  finExchangeRateToDay();
}
