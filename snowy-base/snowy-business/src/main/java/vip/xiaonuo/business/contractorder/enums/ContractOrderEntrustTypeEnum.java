package vip.xiaonuo.business.contractorder.enums;

import cn.hutool.core.lang.Pair;
import lombok.Getter;
import vip.xiaonuo.core.util.MessageUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum ContractOrderEntrustTypeEnum {
    TYPE1(1, "市价委托"),
    TYPE2(2, "限价委托");

    private final Integer code;

    private final String message;

    ContractOrderEntrustTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static boolean isExists(Integer code) {
        for (ContractOrderEntrustTypeEnum typeEnum : values()) {
            if (code.equals(typeEnum.getCode())) {
                return true;
            }
        }
        return false;
    }

    public static List<Pair<Integer, String>> toList() {
        return Arrays.stream(values()).map(p -> Pair.of(p.getCode(), p.getMessage())).collect(Collectors.toList());
    }

    public static List<Pair<Integer, String>> toWebList() {
        return Arrays.stream(values()).map(p -> Pair.of(p.getCode(), MessageUtils.get("contract.order.entrust.type" + p.getCode(), p.getMessage()))).collect(Collectors.toList());
    }
}
