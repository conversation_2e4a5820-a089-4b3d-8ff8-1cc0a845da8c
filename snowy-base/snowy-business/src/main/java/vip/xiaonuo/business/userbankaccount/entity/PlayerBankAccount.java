/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.userbankaccount.entity;

import com.baomidou.mybatisplus.annotation.*;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.*;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 用户账号信息
 *
 * <AUTHOR>
 * @date 2022-04-12 19:32:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tt_player_bank_account")
public class PlayerBankAccount extends BaseEntity {

    /**
     * 玩家id
     */
    private Long playerId;

    /**
     * 收款人姓名
     */
    private String userName;

    /**
     * 收款人银行卡号
     */
    private String bankCardNumber;

    /**
     * 收款人银行
     */
    private String bankName;

    /**
     * 收款人国家
     */
    private String area;

    /**
     * 收款人城市
     */
    private String city;

    /**
     * 收款人地址
     */
    private String address;

    /**
     * 收款人支行信息
     */
    private String subBranch;

    /**
     * 国际电话编码
     */
    private String internationalTelephoneNumber;

    /**
     * 清算号
     */
    private String liquidationNumber;

    /**
     * 银行地址
     */
    private String bankAddress;

    /**
     * 玩家昵称
     */
    @TableField(exist = false)
    private String account;

    /**
     * 玩家手机号
     */
    @TableField(exist = false)
    private String phone;
}
