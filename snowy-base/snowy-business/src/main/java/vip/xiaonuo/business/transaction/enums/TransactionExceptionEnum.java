package vip.xiaonuo.business.transaction.enums;

import vip.xiaonuo.core.annotion.ExpEnumType;
import vip.xiaonuo.core.consts.SysExpEnumConstant;
import vip.xiaonuo.core.exception.enums.abs.AbstractBaseExceptionEnum;
import vip.xiaonuo.core.factory.ExpEnumCodeFactory;

/**
 * 用户充值，提现记录表
 *
 * <AUTHOR>
 * @date 2022-04-08 09:51:14
 */
@ExpEnumType(module = SysExpEnumConstant.SNOWY_SYS_MODULE_EXP_CODE, kind = SysExpEnumConstant.TRANSACTION_EXCEPTION_ENUM)
public enum TransactionExceptionEnum implements AbstractBaseExceptionEnum {

    /**
     * 数据不存在
     */
    NOT_EXIST(1, "此数据不存在"),
    REAL_NAME_AUTH_FAIL(2, "实名认证未通过");

    private final Integer code;

    private final String message;

    TransactionExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return ExpEnumCodeFactory.getExpEnumCode(this.getClass(), code);
    }

    @Override
    public String getMessage() {
        return message;
    }

}
