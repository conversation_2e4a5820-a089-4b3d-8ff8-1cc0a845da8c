/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.marketadjustitemtime.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.marketadjustitemtime.entity.MarketAdjustItemTime;
import vip.xiaonuo.business.marketadjustitemtime.enums.MarketAdjustItemTimeExceptionEnum;
import vip.xiaonuo.business.marketadjustitemtime.mapper.MarketAdjustItemTimeMapper;
import vip.xiaonuo.business.marketadjustitemtime.param.MarketAdjustItemTimeParam;
import vip.xiaonuo.business.marketadjustitemtime.service.MarketAdjustItemTimeService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.List;

/**
 * 市场调整详情service接口实现类
 *
 * <AUTHOR>
 * @date 2025-07-13 15:46:14
 */
@Service
public class MarketAdjustItemTimeServiceImpl extends ServiceImpl<MarketAdjustItemTimeMapper, MarketAdjustItemTime> implements MarketAdjustItemTimeService {

    @Override
    public PageResult<MarketAdjustItemTime> page(MarketAdjustItemTimeParam marketAdjustItemTimeParam) {
        QueryWrapper<MarketAdjustItemTime> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(marketAdjustItemTimeParam)) {

            // 根据交易类型表示,如ETH_BTC 查询
            if (ObjectUtil.isNotEmpty(marketAdjustItemTimeParam.getSymbol())) {
                queryWrapper.lambda().eq(MarketAdjustItemTime::getSymbol, marketAdjustItemTimeParam.getSymbol());
            }
            // 根据调整id 查询
            if (ObjectUtil.isNotEmpty(marketAdjustItemTimeParam.getMarketAdjustItemId())) {
                queryWrapper.lambda().eq(MarketAdjustItemTime::getMarketAdjustItemId, marketAdjustItemTimeParam.getMarketAdjustItemId());
            }
            // 根据时间戳 查询
            if (ObjectUtil.isNotEmpty(marketAdjustItemTimeParam.getTs())) {
                queryWrapper.lambda().eq(MarketAdjustItemTime::getTs, marketAdjustItemTimeParam.getTs());
            }
            // 根据调整值 查询
            if (ObjectUtil.isNotEmpty(marketAdjustItemTimeParam.getValue())) {
                queryWrapper.lambda().eq(MarketAdjustItemTime::getValue, marketAdjustItemTimeParam.getValue());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<MarketAdjustItemTime> list(MarketAdjustItemTimeParam marketAdjustItemTimeParam) {
        return this.list();
    }

    @Override
    public void add(MarketAdjustItemTimeParam marketAdjustItemTimeParam) {
        MarketAdjustItemTime marketAdjustItemTime = new MarketAdjustItemTime();
        BeanUtil.copyProperties(marketAdjustItemTimeParam, marketAdjustItemTime);
        this.save(marketAdjustItemTime);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<MarketAdjustItemTimeParam> marketAdjustItemTimeParamList) {
        marketAdjustItemTimeParamList.forEach(marketAdjustItemTimeParam -> {
            MarketAdjustItemTime marketAdjustItemTime = this.queryMarketAdjustItemTime(marketAdjustItemTimeParam);
            this.removeById(marketAdjustItemTime.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(MarketAdjustItemTimeParam marketAdjustItemTimeParam) {
        MarketAdjustItemTime marketAdjustItemTime = this.queryMarketAdjustItemTime(marketAdjustItemTimeParam);
        BeanUtil.copyProperties(marketAdjustItemTimeParam, marketAdjustItemTime);
        this.updateById(marketAdjustItemTime);
    }

    @Override
    public MarketAdjustItemTime detail(MarketAdjustItemTimeParam marketAdjustItemTimeParam) {
        return this.queryMarketAdjustItemTime(marketAdjustItemTimeParam);
    }

    /**
     * 获取市场调整详情
     *
     * <AUTHOR>
     * @date 2025-07-13 15:46:14
     */
    private MarketAdjustItemTime queryMarketAdjustItemTime(MarketAdjustItemTimeParam marketAdjustItemTimeParam) {
        MarketAdjustItemTime marketAdjustItemTime = this.getById(marketAdjustItemTimeParam.getId());
        if (ObjectUtil.isNull(marketAdjustItemTime)) {
            throw new ServiceException(MarketAdjustItemTimeExceptionEnum.NOT_EXIST);
        }
        return marketAdjustItemTime;
    }
}
