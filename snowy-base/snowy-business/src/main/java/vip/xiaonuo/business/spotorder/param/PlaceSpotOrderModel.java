package vip.xiaonuo.business.spotorder.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel("现货下单")
public class PlaceSpotOrderModel {
    /**
     * 交易类型表示,如ETH_USDT
     */
    @ApiModelProperty("如ETH_USDT")
    @NotEmpty
    private String symbol;
    /**
     * 类型(1:买涨;2:买跌;)
     */
    @ApiModelProperty("类型(1:买涨;2:买跌;)")
    @NotNull
    private Integer type;
    /**
     * 秒
     */
    @ApiModelProperty("规则ID")
    @NotNull
    private String ruleId;
    /**
     * 下单数量
     */
    @ApiModelProperty("下单数量")
    @NotNull
    private BigDecimal quantity;

    /**
     * 下单数量
     */
    @ApiModelProperty("跟单员ID")
    private Long followerId;

    /**
     * 下单数量
     */
    @ApiModelProperty("佣金比例")
    private BigDecimal followerCommissionRate;

    /**
     * 下单数量
     */
    @ApiModelProperty("预设ID")
    private Long followerContractSpotOrderId;

    /**
     * 控制
     */
    @ApiModelProperty("控制")
    private Integer controlPre;

}
