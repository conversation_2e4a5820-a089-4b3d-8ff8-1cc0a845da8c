/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.spotrule.param;

import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;

/**
* 极速下单规则参数类
 *
 * <AUTHOR>
 * @date 2022-12-28 11:21:34
*/
@Data
public class SpotRuleParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;
    /**
     * 交易类型表示,如ETH_USDT
     */
    private String symbol;
    /**
     * 最小下单数量
     */
    @NotNull(message = "最小下单数量不能为空，请检查minQuantity参数", groups = {add.class, edit.class})
    private BigDecimal minQuantity;
    /**
     * 秒
     */
    @NotNull(message = "秒不能为空，请检查seconds参数", groups = {add.class, edit.class})
    private Integer seconds;

    /**
     * 最小利润率
     */
    @NotNull(message = "最小利润率不能为空，请检查minProfitRate参数", groups = {add.class, edit.class})
    private BigDecimal minProfitRate;

    /**
     * 最大利润率
     */
    @NotNull(message = "最大利润率不能为空，请检查maxProfitRate参数", groups = {add.class, edit.class})
    private BigDecimal maxProfitRate;

    /**
     * 最小亏损率
     */
    @NotNull(message = "最小亏损率不能为空，请检查minLossRate参数", groups = {add.class, edit.class})
    private BigDecimal minLossRate;

    /**
     * 最大亏损率
     */
    @NotNull(message = "最大亏损率不能为空，请检查maxLossRate参数", groups = {add.class, edit.class})
    private BigDecimal maxLossRate;

    /**
     * 最小余额
     */
    @NotNull(message = "最小余额限制不能为空，请检查minBalance参数", groups = {add.class, edit.class})
    private BigDecimal minBalance;

    /**
     * 最大余额
     */
    @NotNull(message = "最大余额限制不能为空，请检查maxBalance参数", groups = {add.class, edit.class})
    private BigDecimal maxBalance;

}
