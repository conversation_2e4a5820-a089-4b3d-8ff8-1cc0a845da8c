<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.business.transaction.mapper.TransactionMapper">

    <select id="findRechargeUserForPage" resultType="vip.xiaonuo.business.transaction.pojo.vo.FindRechargeUserForPageVo" >
        SELECT
	    t1.id id,
        t1.player_id playerId,
        t1.account_type accountType,
        t1.transaction_type transactionType,
        t1.order_num orderNum,
        t1.recharge_front_account rechargeFrontAccount,
        t1.recharge_after_account rechargeAfterAccount,
        t1.account account,
        t1.picture_url pictureUrl,
        t1.recharge_channel rechargeChannel,
        t1.operation_type operationType,
        t1.status ,
        t1.to_address as toAddress,
        t1.remark,
        t1.remarks,
        t1.json_info jsonInfo,
        t1.create_time createTime,
        t1.chenck_id chenckId,
        t1.check_time checkTime,
        t1.operation_id operationId,
        t1.operation_time operationTime,
        t1.create_user createUser,
        t1.update_time updateTime,
        t1.update_user updateUser,
        t2.account userAccount,
        t2.nick_name nickName,
        t2.email email,
        t2.phone phone,
        t3.account agentPeople,
        su.account checkName
        FROM
            t_transaction t1
            LEFT JOIN tt_player t2 ON t1.player_id = t2.id
            LEFT JOIN sys_user t3 ON t2.agent_id = t3.id
        left join sys_user su on su.id=t1.chenck_id
            <where>
                t1.operation_type = 1
                <if test="ew.status != null and ew.status != ''">
                    and t1.status = #{ew.status}
                </if>
                <if test="ew.userName != null and ew.userName != ''">
                    and t2.account like  concat('%',#{ew.userName},'%')
                </if>
                <if test="ew.phone != null and ew.phone != ''">
                    and t2.phone like concat('%',#{ew.phone},'%')
                </if>
                <if test="ew.email != null and ew.email != ''">
                    and t2.email like concat('%',#{ew.email},'%')
                </if>
                <if test="ew.transactionType != null and ew.transactionType != ''">
                    and t1.transaction_type = #{ew.transactionType}
                </if>
                <if test="ew.agentPeople != null and ew.agentPeople != ''">
                    and t3.account like concat('%',#{ew.agentPeople},'%')
                </if>
                <if test="ew.rechargeChannel != null and ew.rechargeChannel != ''">
                    and t1.recharge_Channel = #{ew.rechargeChannel}
                </if>
                <if test="ew.startDate != null and ew.startDate != ''">
                    and t1.create_time &gt;= #{ew.startDate}
                </if>
                <if test="ew.endDate != null and ew.endDate != ''">
                    and t1.create_time &lt;= #{ew.endDate}
                </if>
                <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
                    and t2.agent_id in
                    <foreach item="item" index="index" collection="ew.agentIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
                    and t2.agent_id = #{ew.loginUserId}
                </if>
            </where>
        order by t1.create_time desc
    </select>

    <select id="findWithdrawalUserForPage" resultType="vip.xiaonuo.business.transaction.pojo.vo.FindWithdrawalUserForPageVo">
        SELECT
        t1.id id,
        t1.player_id playerId,
        t1.account_type accountType,
        t1.transaction_type transactionType,
        t1.order_num orderNum,
        t1.recharge_front_account rechargeFrontAccount,
        t1.recharge_after_account rechargeAfterAccount,
        t1.account account,
        t1.picture_url pictureUrl,
        t1.recharge_channel rechargeChannel,
        t1.operation_type operationType,
        t1.status ,
        t1.json_info jsonInfo,
        t1.create_time createTime,
        t1.chenck_id chenckId,
        t1.check_time checkTime,
        t1.operation_id operationId,
        t1.operation_time operationTime,
        t1.create_user createUser,
        t1.update_time updateTime,
        t1.update_user updateUser,
        t1.remark,
        t1.charge_free chargeFree,
        t1.charge_free_rata chargeFreeRata,
        t1.arrival_account arrivalAccount,
        t1.bank_deposit bankDeposit,
        t1.bank_name bankName,
        t1.bank_card bankCard,
        t1.currency_Address as currencyAddress,
        t2.account userAccount,
        t2.nick_name nickName,
        t2.email email,
        t2.phone phone,
        t3.account agentPeople
        FROM
        t_transaction t1
        LEFT JOIN tt_player t2 ON t1.player_id = t2.id
        LEFT JOIN sys_user t3 ON t2.agent_id = t3.id
        <where>
            t1.operation_type = 2
            <if test="ew.status != null and ew.status != ''">
                and t1.status = #{ew.status}
            </if>
            <if test="ew.userName != null and ew.userName != ''">
                and t2.account like  concat('%',#{ew.userName},'%')
            </if>
            <if test="ew.phone != null and ew.phone != ''">
                and t2.phone like concat('%',#{ew.phone},'%')
            </if>
            <if test="ew.email != null and ew.email != ''">
                and t2.email like concat('%',#{ew.email},'%')
            </if>
            <if test="ew.transactionType != null and ew.transactionType != ''">
                and t1.transaction_type = #{ew.transactionType}
            </if>
            <if test="ew.agentPeople != null and ew.agentPeople != ''">
                and t3.account like concat('%',#{ew.agentPeople},'%')
            </if>
            <if test="ew.startDate != null and ew.startDate != ''">
                and t1.create_time &gt;= #{ew.startDate}
            </if>
            <if test="ew.endDate != null and ew.endDate != ''">
                and t1.create_time &lt;= #{ew.endDate}
            </if>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
                and t2.agent_id in
                <foreach item="item" index="index" collection="ew.agentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
                and t2.agent_id = #{ew.loginUserId}
            </if>
        </where>
        order by t1.create_time desc
    </select>

    <select id="findrechargeDayAccountStatistic" resultType="vip.xiaonuo.business.transaction.result.TransactionStatisticModel">
        SELECT
            SUM( CASE WHEN t.operation_type = 1 THEN IFNULL( t.usdt_Account, 0 ) ELSE 0 END ) rechargeAccount,
            SUM( CASE WHEN t.operation_type = 2 THEN IFNULL( t.usdt_Account, 0 ) ELSE 0 END ) withdrawalAccount,
            SUM( CASE WHEN t.operation_type = 1 AND DATE ( t.create_time ) = #{ew.date} THEN IFNULL( t.usdt_Account, 0 ) ELSE 0 END ) rechargeDayAccount,
            SUM( CASE WHEN t.operation_type = 2 AND DATE ( t.create_time ) = #{ew.date} THEN IFNULL( t.usdt_Account, 0 ) ELSE 0 END ) withdrawalDayAccount
        FROM
            t_transaction t left join tt_player p on p.id = t.player_id
        <where>
            and t.status = 2
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
                and p.org_code = #{ew.loginUserOrgCode}
            </if>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
                and p.agent_id = #{ew.loginUserId}
            </if>
        </where>
    </select>

    <select id="findUserTransaction" resultType="vip.xiaonuo.business.transaction.pojo.vo.FindUserTransactionVo" parameterType="vip.xiaonuo.business.transaction.pojo.dto.FindUserTransactionDto">
        SELECT
            t1.id,
            t1.account,
            t1.player_id playerId,
            t1.transaction_type transactionType,
            t1.order_num orderNum,
            t1.`status` status,
            DATE_FORMAT(t1.create_time,'%Y-%m-%d %H:%i:%S') createTime,
            t1.remark remarks,
            (SELECT t0.logo FROM tt_currency t0 WHERE t0.`code` =t1.transaction_type ) currencyUrl
        FROM
            t_transaction t1
	   	WHERE t1.player_id = #{ew.playerId} AND t1.operation_type =  #{ew.operationType}  order by t1.create_time desc
    </select>
</mapper>
