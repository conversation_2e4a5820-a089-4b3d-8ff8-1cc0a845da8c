/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.coinorder.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 币币订单
 *
 * <AUTHOR>
 * @date 2022-09-14 11:02:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tt_coin_order")
public class CoinOrder extends BaseEntity {

    /**
     * 玩家ID
     */
    private Long playerId;
    @TableField(exist = false)
    private String playerAccount;
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 类型(1:买;2:卖;)
     */
    private Integer type;

    /**
     * 委托类型(1:市价委托;2:限价委托;)
     */
    private Integer entrustType;

    /**
     * 币种编码
     */
    private String symbol;

    /**
     * 下单时间
     */
    @Excel(name = "下单时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date orderTime;

    /**
     * 下单价格
     */
    private BigDecimal orderPrice;

    /**
     * 下单数量
     */
    private BigDecimal quantity;

    /**
     * 成交价格
     */
    private BigDecimal closePrice;

    /**
     * 成交时间
     */
    @Excel(name = "成交时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date closeTime;

    /**
     * 手续费
     */
    private BigDecimal orderFee;

    /**
     * 0:委托,1:持仓,2:成交,3:撤销委托
     */
    private Integer status;
    /**
     * 成交额
     */
    private BigDecimal amount;
}
