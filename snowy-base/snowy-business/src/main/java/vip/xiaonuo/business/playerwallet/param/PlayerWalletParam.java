package vip.xiaonuo.business.playerwallet.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;

/**
* 用户钱包参数类
 *
 * <AUTHOR>
 * @date 2022-04-26 10:36:18
*/
@ApiModel(value = "用户钱包请求实体")
@Data
public class PlayerWalletParam extends BaseParam {

    /**
     * 
     */
    @NotNull(message = "不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @NotNull(message = "用户id不能为空，请检查playerId参数", groups = { edit.class})
    private Long playerId;

    /**
     * 钱包地址
     */
    @ApiModelProperty(value = "钱包地址")
    @NotBlank(message = "钱包地址不能为空，请检查walletAddress参数", groups = {add.class, edit.class})
    private String walletAddress;

    /**
     * 币种代码
     */
    @ApiModelProperty(value = "币种代码")
    @NotBlank(message = "币种代码不能为空，请检查currencyCode参数", groups = {add.class, edit.class})
    private String currencyCode;

    /**
     * 链名称
     */
    @ApiModelProperty(value = " 链名称")
    private String chainName;

    /**
     * 状态 0 正常 1 删除
     */
    @ApiModelProperty(value = "状态 0 正常 1 删除")
    private Integer status;

    /**
     *钱包图片
     */
    @ApiModelProperty(value = " 钱包图片")
    private String qrCodeUrl;
    /**
     * 备注
     */
    @ApiModelProperty(value = " 备注")
    private String remarks;

}
