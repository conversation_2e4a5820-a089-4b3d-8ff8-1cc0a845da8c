/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.ipblacklist.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.ipblacklist.entity.IpBlacklist;
import vip.xiaonuo.business.ipblacklist.enums.IpBlacklistExceptionEnum;
import vip.xiaonuo.business.ipblacklist.mapper.IpBlacklistMapper;
import vip.xiaonuo.business.ipblacklist.param.IpBlacklistParam;
import vip.xiaonuo.business.ipblacklist.service.IpBlacklistService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.redis.RedisHelper;

import javax.annotation.Resource;
import java.util.List;

/**
 * IP黑名单service接口实现类
 *
 * <AUTHOR>
 * @date 2024-03-27 18:52:06
 */
@Service
public class IpBlacklistServiceImpl extends ServiceImpl<IpBlacklistMapper, IpBlacklist> implements IpBlacklistService, InitializingBean {

    @Resource
    private RedisHelper redisHelper;

    public boolean hasBlackList(String ip) {
        if (StrUtil.isEmpty(ip)) {
            return false;
        }
        return redisHelper.hasKey("tt_ip_blacklist:" + ip);
    }

    public void handleToRedis() {
        redisHelper.deleteByPattern("tt_ip_blacklist:*");
        List<IpBlacklist> list = list(Wrappers.<IpBlacklist>lambdaQuery().eq(IpBlacklist::getStatus, 1));
        for (IpBlacklist ipBlacklist : list) {
            String ip = ipBlacklist.getIp();
            if (StrUtil.isEmpty(ip)) {
                continue;
            }
            redisHelper.set("tt_ip_blacklist:" + ip, ipBlacklist.getStatus());
        }
    }

    @Override
    public PageResult<IpBlacklist> page(IpBlacklistParam ipBlacklistParam) {
        QueryWrapper<IpBlacklist> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(ipBlacklistParam)) {

            // 根据IP 查询
            if (ObjectUtil.isNotEmpty(ipBlacklistParam.getIp())) {
                queryWrapper.lambda().eq(IpBlacklist::getIp, ipBlacklistParam.getIp());
            }
            // 根据0:禁用,1:启用 查询
            if (ObjectUtil.isNotEmpty(ipBlacklistParam.getStatus())) {
                queryWrapper.lambda().eq(IpBlacklist::getStatus, ipBlacklistParam.getStatus());
            }
            // 根据IP 查询
            if (ObjectUtil.isNotEmpty(ipBlacklistParam.getRemark())) {
                queryWrapper.lambda().eq(IpBlacklist::getRemark, ipBlacklistParam.getRemark());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<IpBlacklist> list(IpBlacklistParam ipBlacklistParam) {
        return this.list();
    }

    @Override
    public void add(IpBlacklistParam ipBlacklistParam) {
        IpBlacklist ipBlacklist = new IpBlacklist();
        BeanUtil.copyProperties(ipBlacklistParam, ipBlacklist);
        this.save(ipBlacklist);
        handleToRedis();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<IpBlacklistParam> ipBlacklistParamList) {
        ipBlacklistParamList.forEach(ipBlacklistParam -> {
            IpBlacklist ipBlacklist = this.queryIpBlacklist(ipBlacklistParam);
            this.removeById(ipBlacklist.getId());
        });
        handleToRedis();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(IpBlacklistParam ipBlacklistParam) {
        IpBlacklist ipBlacklist = this.queryIpBlacklist(ipBlacklistParam);
        BeanUtil.copyProperties(ipBlacklistParam, ipBlacklist);
        this.updateById(ipBlacklist);
        handleToRedis();
    }

    @Override
    public IpBlacklist detail(IpBlacklistParam ipBlacklistParam) {
        return this.queryIpBlacklist(ipBlacklistParam);
    }

    /**
     * 获取IP黑名单
     *
     * <AUTHOR>
     * @date 2024-03-27 18:52:06
     */
    private IpBlacklist queryIpBlacklist(IpBlacklistParam ipBlacklistParam) {
        IpBlacklist ipBlacklist = this.getById(ipBlacklistParam.getId());
        if (ObjectUtil.isNull(ipBlacklist)) {
            throw new ServiceException(IpBlacklistExceptionEnum.NOT_EXIST);
        }
        return ipBlacklist;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        handleToRedis();
    }
}
