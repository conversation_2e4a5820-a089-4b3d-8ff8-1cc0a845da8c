package vip.xiaonuo.business.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.business.account.entity.Account;
import vip.xiaonuo.business.account.param.AccountChangeBalanceParam;
import vip.xiaonuo.business.account.param.AccountParam;
import vip.xiaonuo.business.account.pojo.dto.ExecuteUserCutTurnDto;
import vip.xiaonuo.business.account.pojo.dto.ExecuteUserExchangeDto;
import vip.xiaonuo.business.account.pojo.vo.FindUserAccountVo;
import vip.xiaonuo.business.account.pojo.vo.UserAccountVo;
import vip.xiaonuo.business.account.result.AccountChangeBalanceResult;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;

import java.util.List;

/**
 * 账户表service接口
 *
 * <AUTHOR>
 * @date 2022-04-06 13:49:40
 */
public interface AccountService extends IService<Account> {

    /**
     * 查询账户表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    PageResult<Account> page(AccountParam accountParam);

    /**
     * 账户表列表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    List<Account> list(AccountParam accountParam);

    /**
     * 添加账户表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    void add(AccountParam accountParam);

    /**
     * 删除账户表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    void delete(List<AccountParam> accountParamList);

    /**
     * 编辑账户表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    void edit(AccountParam accountParam);

    /**
     * 查看账户表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    Account detail(AccountParam accountParam);

    /**
     * 余额变动
     *
     * @param accountChangeBalanceParam
     */
    AccountChangeBalanceResult changeBalance(AccountChangeBalanceParam accountChangeBalanceParam);

    /**
     * 割转
     *
     * @param dto
     * @return
     */
    ResponseData executeUserCutTurn(ExecuteUserCutTurnDto dto);

    /**
     * 兑换
     *
     * @param dto
     * @return
     */
    ResponseData executeUserExchange(ExecuteUserExchangeDto dto);

    /**
     * 资产
     *
     * @return
     */
    ResponseData<FindUserAccountVo> findUserAccount(Long playerId);

    /**
     * 根据用户，币种，账户类型查询余额
     *
     * @return
     */
    ResponseData findAccountByTypeAndCurrencyCode(Long playerId, String currencyCode, Integer type);

    List<UserAccountVo> exchangeList(Long playerId);

}
