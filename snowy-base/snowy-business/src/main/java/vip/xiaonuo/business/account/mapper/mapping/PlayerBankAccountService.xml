<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.business.account.mapper.AccountMapper">

    <select id="queryBalanceForLock" resultType="vip.xiaonuo.business.account.entity.Account">
        SELECT
            id,
            player_id playerId,
            `type`,
            currency_code currencyCode,
            balance,
            frozen_balance frozenBalance
        FROM
            tt_account
        WHERE
            player_id = #{playerId} and `type` = #{type} and currency_code = #{currencyCode}
        for update
    </select>
</mapper>
