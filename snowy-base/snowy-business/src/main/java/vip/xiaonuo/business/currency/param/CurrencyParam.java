/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.currency.param;

import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;

/**
* 币种参数类
 *
 * <AUTHOR>
 * @date 2022-04-06 12:56:08
*/
@Data
public class CurrencyParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空，请检查name参数", groups = {add.class, edit.class})
    private String name;

    /**
     * logo
     */
    @NotBlank(message = "logo不能为空，请检查logo参数", groups = {add.class, edit.class})
    private String logo;

    /**
     * 代码
     */
    @NotBlank(message = "代码不能为空，请检查code参数", groups = {add.class, edit.class})
    private String code;

    /**
     * 提现手续费
     */
    @NotNull(message = "提现手续费不能为空，请检查withdrawFee参数", groups = {add.class, edit.class})
    private BigDecimal withdrawFee;

    /**
     * 0:下线,1:上线
     */
    @NotNull(message = "0:下线,1:上线不能为空，请检查status参数", groups = {add.class, edit.class})
    private Integer status;

    /**
     * 排序，数值越小，排序越靠前
     */
    @NotNull(message = "排序，数值越小，排序越靠前不能为空，请检查sort参数", groups = {add.class, edit.class})
    private Integer sort;

    /**
     * 充值开关：0:关 1:开
     */
    @NotNull(message = "充值开关：0:关 1:开不能为空，请检查chargeSwitch参数", groups = {add.class, edit.class})
    private Integer chargeSwitch;

    /**
     * 提现开关：0关，1开
     */
    @NotNull(message = "提现开关：0关，1开不能为空，请检查withdrawSwitch参数", groups = {add.class, edit.class})
    private Integer withdrawSwitch;

    /**
     * 最小提币数量
     */
    @NotNull(message = "最小提币数量不能为空，请检查minWithdrawAmount参数", groups = {add.class, edit.class})
    private BigDecimal minWithdrawAmount;

    /**
     * 单次最大提币额
     */
    @NotNull(message = "单次最大提币额不能为空，请检查currencyWithdrawLimit参数", groups = {add.class, edit.class})
    private BigDecimal currencyWithdrawLimit;

}
