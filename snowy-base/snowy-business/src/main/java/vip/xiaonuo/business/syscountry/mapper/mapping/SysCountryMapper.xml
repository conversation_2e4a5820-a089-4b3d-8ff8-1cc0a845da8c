<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.business.syscountry.mapper.SysCountryMapper">
    <select id="findAllCountry" resultType="vip.xiaonuo.business.syscountry.entity.SysCountry">
        SELECT
	id,
	country_name countryName,
	english_name englishName
        FROM
	sys_country
	WHERE del_flag = 0
    </select>
</mapper>
