/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.aiaccount.param;

import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;

/**
* AI银行账户表参数类
 *
 * <AUTHOR>
 * @date 2025-08-04 11:32:42
*/
@Data
public class AiAccountParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 玩家id
     */
    @NotNull(message = "玩家id不能为空，请检查playerId参数", groups = {add.class, edit.class})
    private Long playerId;

    /**
     * 币种，如USDT
     */
    @NotBlank(message = "币种，如USDT不能为空，请检查symbol参数", groups = {add.class, edit.class})
    private String symbol;

    /**
     * 当前余额
     */
    @NotNull(message = "当前余额不能为空，请检查balance参数", groups = {add.class, edit.class})
    private BigDecimal balance;

    /**
     * 累计收益
     */
    @NotNull(message = "累计收益不能为空，请检查totalIncome参数", groups = {add.class, edit.class})
    private BigDecimal totalIncome;

    /**
     * 状态（1=启用，0=禁用）
     */
    @NotNull(message = "状态（1=启用，0=禁用）不能为空，请检查status参数", groups = {add.class, edit.class})
    private Integer status;

}
