package vip.xiaonuo.business.transaction.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户充值提现记录
 */
@Data
public class FindUserTransactionVo implements Serializable {

    private Long id;
    /**
     * 用户id
     */
    private Long playerId;
    /**
     * 操作类型 1 充值 2 提现
     */
    @ApiModelProperty("1 充值 2 提现")
    private Integer operationType;

    /**
     * 充值类型
     */
    @ApiModelProperty("币种代码")
    private String transactionType;
    /**
     * 订单编号
     */
    private String orderNum;
    /**
     * 状态
     * 0 待付款
     * 1 待审核
     *  2 成功
     * 3 失败
     *  4 处理中
     * 5 交易超时
     * 6 交易撤销
     */
    @ApiModelProperty("     * 状态\n" +
            "     * 0 待付款\n" +
            "     * 1 待审核\n" +
            "     *  2 成功\n" +
            "     * 3 失败\n" +
            "     *  4 处理中\n" +
            "     * 5 交易超时\n" +
            "     * 6 交易撤销")
    private Integer status;
    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal account;
    /**
     * 时间
     */
    @ApiModelProperty("时间")
    private String createTime;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;
    /**
     * 币种图标
     */
    private String currencyUrl;
}
