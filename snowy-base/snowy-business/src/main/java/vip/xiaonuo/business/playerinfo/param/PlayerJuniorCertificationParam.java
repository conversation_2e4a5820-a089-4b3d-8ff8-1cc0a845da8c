package vip.xiaonuo.business.playerinfo.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class PlayerJuniorCertificationParam {
    //类型(1:身份证,2:护照,3:驾驶证)
    @ApiModelProperty(value = "类型(1:身份证,2:护照,3:驾驶证)")
    @NotNull
    private Integer type;
    @ApiModelProperty(value = "真实姓名")
    @NotEmpty
    private String realName;
    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    @NotEmpty
    private String idCard;
}
