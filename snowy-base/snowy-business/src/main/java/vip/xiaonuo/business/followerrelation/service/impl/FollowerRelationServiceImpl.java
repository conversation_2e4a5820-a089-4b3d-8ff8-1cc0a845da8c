/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.followerrelation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.followerrelation.entity.FollowerRelation;
import vip.xiaonuo.business.followerrelation.enums.FollowerRelationExceptionEnum;
import vip.xiaonuo.business.followerrelation.mapper.FollowerRelationMapper;
import vip.xiaonuo.business.followerrelation.param.FollowerRelationParam;
import vip.xiaonuo.business.followerrelation.service.FollowerRelationService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.List;

/**
 * 玩家跟单service接口实现类
 *
 * <AUTHOR>
 * @date 2025-07-18 14:22:02
 */
@Service
public class FollowerRelationServiceImpl extends ServiceImpl<FollowerRelationMapper, FollowerRelation> implements FollowerRelationService {

    @Override
    public PageResult<FollowerRelation> page(FollowerRelationParam followerRelationParam) {
        QueryWrapper<FollowerRelation> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(followerRelationParam)) {

            // 根据用户id 查询
            if (ObjectUtil.isNotEmpty(followerRelationParam.getPlayerId())) {
                queryWrapper.lambda().eq(FollowerRelation::getPlayerId, followerRelationParam.getPlayerId());
            }
            // 根据跟单员id 查询
            if (ObjectUtil.isNotEmpty(followerRelationParam.getFollowerId())) {
                queryWrapper.lambda().eq(FollowerRelation::getFollowerId, followerRelationParam.getFollowerId());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<FollowerRelation> list(FollowerRelationParam followerRelationParam) {
        return this.list();
    }

    @Override
    public void add(FollowerRelationParam followerRelationParam) {
        FollowerRelation followerRelation = new FollowerRelation();
        BeanUtil.copyProperties(followerRelationParam, followerRelation);
        this.save(followerRelation);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<FollowerRelationParam> followerRelationParamList) {
        followerRelationParamList.forEach(followerRelationParam -> {
            FollowerRelation followerRelation = this.queryFollowerRelation(followerRelationParam);
            this.removeById(followerRelation.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(FollowerRelationParam followerRelationParam) {
        FollowerRelation followerRelation = this.queryFollowerRelation(followerRelationParam);
        BeanUtil.copyProperties(followerRelationParam, followerRelation);
        this.updateById(followerRelation);
    }

    @Override
    public FollowerRelation detail(FollowerRelationParam followerRelationParam) {
        return this.queryFollowerRelation(followerRelationParam);
    }

    /**
     * 获取玩家跟单
     *
     * <AUTHOR>
     * @date 2025-07-18 14:22:02
     */
    private FollowerRelation queryFollowerRelation(FollowerRelationParam followerRelationParam) {
        FollowerRelation followerRelation = this.getById(followerRelationParam.getId());
        if (ObjectUtil.isNull(followerRelation)) {
            throw new ServiceException(FollowerRelationExceptionEnum.NOT_EXIST);
        }
        return followerRelation;
    }
}
