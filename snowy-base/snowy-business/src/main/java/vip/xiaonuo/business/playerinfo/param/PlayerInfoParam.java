package vip.xiaonuo.business.playerinfo.param;

import io.swagger.annotations.ApiModelProperty;
import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;

/**
* 认证审核参数类
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:38
*/
@Data
public class PlayerInfoParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    @ApiModelProperty(value = "主键", required = true)
    private Long id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long playerId;

    /**
     * 真实姓名
     */
    @ApiModelProperty(value = "真实姓名")
    private String realName;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * 身份证正面
     */
    @ApiModelProperty(value = "身份证正面")
    private String idCardJust;

    /**
     * 身份证反面
     */
    @ApiModelProperty(value = "身份证反面")
    private String idCardBack;

    /**
     * 手持身份证
     */
    @ApiModelProperty(value = "手持身份证")
    private String holdIdCard;

    /**
     * 身份证号码
     */
    @ApiModelProperty(value = "身份证号码")
    private String idCard;

    /**
     * 居住地址
     */
    @ApiModelProperty(value = "居住地址")
    private String liveAddress;

    /**
     * 认证状态 0 待提交 1 审核中 2 审核通过  3 审核不通过 4 延迟审核
     */
    private Integer authStatus;

    /**
     * 认证时间
     */
    private Date authTime;

    /**
     * 审核时间
     */
    private Date checkTime;

    /**
     * 审核人员
     */
    private Long checkId;

}
