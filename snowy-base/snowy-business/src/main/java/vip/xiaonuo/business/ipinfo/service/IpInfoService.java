package vip.xiaonuo.business.ipinfo.service;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import vip.xiaonuo.business.ipinfo.entity.IpInfo;
import vip.xiaonuo.business.ipinfo.mapper.IpInfoMapper;
import vip.xiaonuo.business.ipinfo.param.IpInfoCallback;
import vip.xiaonuo.core.consts.SymbolConstant;
import vip.xiaonuo.core.util.IpAddressUtil;

import java.util.List;

@Service
public class IpInfoService extends ServiceImpl<IpInfoMapper, IpInfo> {
    private Logger logger = LoggerFactory.getLogger(getClass());
    private static final String IP_URL = "http://ip-api.com/json/";

    @Async
    public void saveIpInfo(String ip) {
        saveIpInfo(ip, null);
    }

    @Async
    public void saveIpInfo(String ip, IpInfoCallback callback) {
        IpInfo ipInfo = new IpInfo();
        ipInfo.setIp(ip);
        if (StringUtils.isEmpty(ip)
                || SymbolConstant.DASH.equals(ip)
                || IpAddressUtil.LOCAL_IP.equals(ip)
                || IpAddressUtil.LOCAL_REMOTE_HOST.equals(ip)) {
            if (callback != null) {
                callback.call(ipInfo);
            }
            return;
        }
        try {
            List<IpInfo> list = lambdaQuery().eq(IpInfo::getIp, ip).list();
            if (list.size() >= 1) {
                ipInfo = list.get(0);
            } else {
                ipInfo.setStatus(0);
                save(ipInfo);
                //去网络上获取位置信息
                handleIpInfo(ipInfo);
            }
        } catch (Exception e) {
            logger.error("保存IP信息失败{}", ip, e);
        }
        if (callback != null) {
            callback.call(ipInfo);
        }
    }

    private void handleIpInfo(IpInfo ipInfo) {
        String url = IP_URL + ipInfo.getIp();
        try {
            //获取信息
            String body = HttpUtil.get(url);
            if (StringUtils.isEmpty(body)) {
                return;
            }
            JSONObject jsonObject = JSON.parseObject(body);
            if ("success".equals(jsonObject.getString("status"))) {
                ipInfo.setStatus(1);
                ipInfo.setCountryCode(jsonObject.getString("countryCode"));
                ipInfo.setRegion(jsonObject.getString("region"));
                ipInfo.setRegionName(jsonObject.getString("regionName"));
                ipInfo.setCity(jsonObject.getString("city"));
                ipInfo.setOrg(jsonObject.getString("org"));
                updateById(ipInfo);
            } else {
                fail(ipInfo);
                logger.error("获取ip信息错误{}", body);
            }
        } catch (Exception e) {
            logger.error("获取ip信息异常{}", url, e);
            fail(ipInfo);
        }
    }

    private void fail(IpInfo ipInfo) {
        ipInfo.setStatus(2);
        updateById(ipInfo);
    }
}
