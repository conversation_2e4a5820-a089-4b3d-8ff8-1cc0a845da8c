/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.transaction.param;

import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;

/**
* 用户充值，提现记录表参数类
 *
 * <AUTHOR>
 * @date 2022-04-08 10:08:56
*/
@Data
public class TransactionParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空，请检查playerId参数", groups = {add.class, edit.class})
    private Long playerId;

    /**
     * 订单编号
     */
    @NotBlank(message = "订单编号不能为空，请检查orderNum参数", groups = { edit.class})
    private String orderNum;

    /**
     * 充值前余额
     */
    private BigDecimal rechargeFrontAccount;

    /**
     * 充值后余额
     */
    private BigDecimal rechargeAfterAccount;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空，请检查account参数", groups = {add.class, edit.class})
    private BigDecimal account;

    /**
     * 图片地址
     */
    private String pictureUrl;

    /**
     * 渠道
     */
    @NotBlank(message = "渠道不能为空，请检查rechargeChannel参数", groups = {add.class, edit.class})
    private String rechargeChannel;

    /**
     * 操作类型 1 充值 2 提现 
     */
    @NotNull(message = "操作类型 1 充值 2 提现 不能为空，请检查operationType参数", groups = {add.class, edit.class})
    private Integer operationType;

    /**
     * 状态 
        0 待付款
        1 待审核
         2 成功
        3 失败
         4 处理中
        5 交易超时
        6 交易撤销
     */
    private Integer status;

    /**
     * 交易扩展信息
     */
    private String jsonInfo;

    /**
     * 审核人员
     */
    private Long chenckId;

    /**
     * 审核时间
     */
    private String checkTime;

    /**
     * 操作用户
     */
    private Long operationId;

    /**
     * 操作时间
     */
    private String operationTime;

    @NotBlank(message = "交易类型（币种代码）不能为空，如  BTC，请检查transactionType参数", groups = {BaseParam.add.class, BaseParam.edit.class})
    private String transactionType;

    /**
     * 类型(1:币币账户;2:合约账户;3:现货账户)
     */
    private Integer accountType;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 手续费
     */
    private BigDecimal chargeFree;
    /**
     * 手续费率
     */
    private BigDecimal  chargeFreeRata;
    /**
     * 到账金额
     */
    private  BigDecimal arrivalAccount;
    /**
     * 开户行
     */
    @NotBlank(message = "开户行不能为空，请检查bankDeposit参数", groups = {BaseParam.add.class, BaseParam.edit.class})
    private String bankDeposit;
    /**
     * 提现银行名称
     */
    @NotBlank(message = "提现银行名称不能为空，请检查bankName参数", groups = {BaseParam.add.class, BaseParam.edit.class})
    private String bankName;
    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号，请检查bankCarde参数", groups = {BaseParam.add.class, BaseParam.edit.class})
    private String bankCard;

}
