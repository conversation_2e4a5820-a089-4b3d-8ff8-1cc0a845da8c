package vip.xiaonuo.business.statistic.pojo.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 首页统计
 */
@Data
public class HomeStatisticVo implements Serializable {
    /**
     * 注册用户
     */
    private Integer registerUserCount = 0;
    /**
     * 单日注册用户
     */
    private Integer registerUserDayCount = 0;

    /**
     * 总代理
     */
    private BigDecimal generalAgentCount = BigDecimal.ZERO;

    /**
     * 今日充值
     */
    private BigDecimal rechargeDayAccount = BigDecimal.ZERO;
    /**
     * 今日提现
     */
    private BigDecimal withdrawalDayAccount = BigDecimal.ZERO;
    /**
     * 充值总额
     */
    private BigDecimal rechargeAccount = BigDecimal.ZERO;
    /**
     * 提现总额
     */
    private BigDecimal withdrawalAccount = BigDecimal.ZERO;
    /**
     * 用户总余额
     */
    private BigDecimal userBalanceAccount = BigDecimal.ZERO;


    /**
     * 今日认购金额
     */
    private BigDecimal subscriptionDayAccount = BigDecimal.ZERO;
    /**
     * 今日赎回金额
     */
    private BigDecimal redeemDayAccount = BigDecimal.ZERO;
    /**
     * 认购总金额
     */
    private BigDecimal subscriptionAccount = BigDecimal.ZERO;
    /**
     * 赎回总金额
     */
    private BigDecimal redeemAccount = BigDecimal.ZERO;
    /**
     * 今日违约金
     */
    private BigDecimal unContractDayAccount = BigDecimal.ZERO;


    /**
     * 现货今日交易金额
     */
    private BigDecimal spotOrderDayAmount = BigDecimal.ZERO;
    /**
     * 现货总交易金额
     */
    private BigDecimal spotOrderTotalAmount = BigDecimal.ZERO;

    /**
     * 合约今日交易金额
     */
    private BigDecimal contactOrderDayAmount = BigDecimal.ZERO;
    /**
     * 合约总交易金额
     */
    private BigDecimal contactOrderTotalAmount = BigDecimal.ZERO;


    /**
     * 币币今日交易金额
     */
    private BigDecimal coinOrderDayAmount = BigDecimal.ZERO;
    /**
     * 币币总交易金额
     */
    private BigDecimal coinOrderTotalAmount = BigDecimal.ZERO;
}