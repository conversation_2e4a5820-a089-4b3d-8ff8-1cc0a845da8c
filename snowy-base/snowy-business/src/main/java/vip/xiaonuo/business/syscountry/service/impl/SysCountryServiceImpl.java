package vip.xiaonuo.business.syscountry.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vip.xiaonuo.business.syscountry.entity.SysCountry;
import vip.xiaonuo.business.syscountry.mapper.SysCountryMapper;
import vip.xiaonuo.business.syscountry.service.SysCountryService;
import vip.xiaonuo.core.util.translate.GoogleTranslateNew;
import vip.xiaonuo.core.util.translate.Translate;

import java.util.List;

@Service
public class SysCountryServiceImpl extends ServiceImpl<SysCountryMapper, SysCountry> implements SysCountryService, InitializingBean {

    @Autowired
    SysCountryMapper sysCountryMapper;

    @Override
    public List<SysCountry> list() {
        return sysCountryMapper.findAllCountry();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
//        List<SysCountry> list = list();
//        for (SysCountry sysCountry : list) {
//            Translate.Word word = new GoogleTranslateNew().getContent(GoogleTranslateNew.url, sysCountry.getEnglishName().trim(), "id");
//            sysCountry.setIdName(word.getTargetText());
//            updateById(sysCountry);
//            Thread.sleep(500L);
//            System.out.println(sysCountry.getEnglishName() + "》》VI》》》" + sysCountry.getIdName());
//        }
    }
}
