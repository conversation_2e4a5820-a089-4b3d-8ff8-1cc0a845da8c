/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.followerapplication.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.followerapplication.entity.FollowerApplication;
import vip.xiaonuo.business.followerapplication.enums.FollowerApplicationExceptionEnum;
import vip.xiaonuo.business.followerapplication.mapper.FollowerApplicationMapper;
import vip.xiaonuo.business.followerapplication.param.FollowerApplicationParam;
import vip.xiaonuo.business.followerapplication.service.FollowerApplicationService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.List;

/**
 * 交易员申请service接口实现类
 *
 * <AUTHOR>
 * @date 2025-07-18 15:55:41
 */
@Service
public class FollowerApplicationServiceImpl extends ServiceImpl<FollowerApplicationMapper, FollowerApplication> implements FollowerApplicationService {

    @Override
    public PageResult<FollowerApplication> page(FollowerApplicationParam followerApplicationParam) {
        QueryWrapper<FollowerApplication> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(followerApplicationParam)) {

            // 根据用户id 查询
            if (ObjectUtil.isNotEmpty(followerApplicationParam.getPlayerId())) {
                queryWrapper.lambda().eq(FollowerApplication::getPlayerId, followerApplicationParam.getPlayerId());
            }
            // 根据个人简介 查询
            if (ObjectUtil.isNotEmpty(followerApplicationParam.getProfile())) {
                queryWrapper.lambda().eq(FollowerApplication::getProfile, followerApplicationParam.getProfile());
            }
            // 根据标签（英文逗号分隔） 查询
            if (ObjectUtil.isNotEmpty(followerApplicationParam.getTagList())) {
                queryWrapper.lambda().eq(FollowerApplication::getTagList, followerApplicationParam.getTagList());
            }
            // 根据交易经验 查询
            if (ObjectUtil.isNotEmpty(followerApplicationParam.getExperience())) {
                queryWrapper.lambda().eq(FollowerApplication::getExperience, followerApplicationParam.getExperience());
            }
            // 根据预期年化收益率 (%) 查询
            if (ObjectUtil.isNotEmpty(followerApplicationParam.getExpectedAnnualYield())) {
                queryWrapper.lambda().eq(FollowerApplication::getExpectedAnnualYield, followerApplicationParam.getExpectedAnnualYield());
            }
            // 根据风险偏好 查询
            if (ObjectUtil.isNotEmpty(followerApplicationParam.getRiskPreference())) {
                queryWrapper.lambda().eq(FollowerApplication::getRiskPreference, followerApplicationParam.getRiskPreference());
            }
            // 根据交易截图URL 查询
            if (ObjectUtil.isNotEmpty(followerApplicationParam.getTradeUrl())) {
                queryWrapper.lambda().eq(FollowerApplication::getTradeUrl, followerApplicationParam.getTradeUrl());
            }
            // 根据联系方式 查询
            if (ObjectUtil.isNotEmpty(followerApplicationParam.getContactInfo())) {
                queryWrapper.lambda().eq(FollowerApplication::getContactInfo, followerApplicationParam.getContactInfo());
            }
            // 根据审核状态（0待审核，1通过，2拒绝） 查询
            if (ObjectUtil.isNotEmpty(followerApplicationParam.getAuditStatus())) {
                queryWrapper.lambda().eq(FollowerApplication::getAuditStatus, followerApplicationParam.getAuditStatus());
            }
            // 根据审核备注 查询
            if (ObjectUtil.isNotEmpty(followerApplicationParam.getAuditRemark())) {
                queryWrapper.lambda().eq(FollowerApplication::getAuditRemark, followerApplicationParam.getAuditRemark());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<FollowerApplication> list(FollowerApplicationParam followerApplicationParam) {
        return this.list();
    }

    @Override
    public void add(FollowerApplicationParam followerApplicationParam) {
        FollowerApplication followerApplication = new FollowerApplication();
        BeanUtil.copyProperties(followerApplicationParam, followerApplication);
        followerApplication.setAuditStatus(0);
        followerApplication.setAuditRemark(null);
        this.save(followerApplication);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<FollowerApplicationParam> followerApplicationParamList) {
        followerApplicationParamList.forEach(followerApplicationParam -> {
            FollowerApplication followerApplication = this.queryFollowerApplication(followerApplicationParam);
            this.removeById(followerApplication.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(FollowerApplicationParam followerApplicationParam) {
        FollowerApplication followerApplication = this.queryFollowerApplication(followerApplicationParam);
        BeanUtil.copyProperties(followerApplicationParam, followerApplication);
        this.updateById(followerApplication);
    }

    @Override
    public FollowerApplication detail(FollowerApplicationParam followerApplicationParam) {
        return this.queryFollowerApplication(followerApplicationParam);
    }

    /**
     * 获取交易员申请
     *
     * <AUTHOR>
     * @date 2025-07-18 15:55:41
     */
    private FollowerApplication queryFollowerApplication(FollowerApplicationParam followerApplicationParam) {
        FollowerApplication followerApplication = this.getById(followerApplicationParam.getId());
        if (ObjectUtil.isNull(followerApplication)) {
            throw new ServiceException(FollowerApplicationExceptionEnum.NOT_EXIST);
        }
        return followerApplication;
    }
}
