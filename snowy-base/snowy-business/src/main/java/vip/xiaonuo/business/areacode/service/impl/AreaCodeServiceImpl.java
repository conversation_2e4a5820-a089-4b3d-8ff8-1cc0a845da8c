/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.areacode.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.areacode.entity.AreaCode;
import vip.xiaonuo.business.areacode.enums.AreaCodeExceptionEnum;
import vip.xiaonuo.business.areacode.mapper.AreaCodeMapper;
import vip.xiaonuo.business.areacode.param.AreaCodeParam;
import vip.xiaonuo.business.areacode.service.AreaCodeService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 国际区号service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-07 15:37:38
 */
@Service
public class AreaCodeServiceImpl extends ServiceImpl<AreaCodeMapper, AreaCode> implements AreaCodeService, InitializingBean {

    @Override
    public PageResult<AreaCode> page(AreaCodeParam areaCodeParam) {
        QueryWrapper<AreaCode> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(areaCodeParam)) {

            // 根据区号 查询
            if (ObjectUtil.isNotEmpty(areaCodeParam.getCode())) {
                queryWrapper.lambda().eq(AreaCode::getCode, areaCodeParam.getCode());
            }
            // 根据中文名 查询
            if (ObjectUtil.isNotEmpty(areaCodeParam.getCnName())) {
                queryWrapper.lambda().eq(AreaCode::getCnName, areaCodeParam.getCnName());
            }
            // 根据英文名 查询
            if (ObjectUtil.isNotEmpty(areaCodeParam.getEnName())) {
                queryWrapper.lambda().eq(AreaCode::getEnName, areaCodeParam.getEnName());
            }
            // 根据状态(0:禁用,1:启用) 查询
            if (ObjectUtil.isNotEmpty(areaCodeParam.getStatus())) {
                queryWrapper.lambda().eq(AreaCode::getStatus, areaCodeParam.getStatus());
            }
        }

        /*获取所有数据*/
        //Page<AreaCode> areaCodeList = baseMapper.queryPage(PageFactory.defaultAll(), queryWrapper);
        //areaCodeList.getRecords();

        /*获取分页数据*/
        //Page<AreaCode> areaCodePage = baseMapper.queryPage(PageFactory.defaultPage(), queryWrapper);

        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AreaCode> list(AreaCodeParam areaCodeParam) {
        return this.list(Wrappers.<AreaCode>lambdaQuery().eq(AreaCode::getStatus, 1));
    }

    @Override
    public void add(AreaCodeParam areaCodeParam) {
        AreaCode areaCode = new AreaCode();
        BeanUtil.copyProperties(areaCodeParam, areaCode);
        this.save(areaCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<AreaCodeParam> areaCodeParamList) {
        areaCodeParamList.forEach(areaCodeParam -> {
            AreaCode areaCode = this.queryAreaCode(areaCodeParam);
            this.removeById(areaCode.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AreaCodeParam areaCodeParam) {
        AreaCode areaCode = this.queryAreaCode(areaCodeParam);
        BeanUtil.copyProperties(areaCodeParam, areaCode);
        this.updateById(areaCode);
    }

    @Override
    public AreaCode detail(AreaCodeParam areaCodeParam) {
        return this.queryAreaCode(areaCodeParam);
    }

    @Override
    public boolean checkPhone(String orgCode, String areaCode, String phone) {
        AreaCode areaCodeDTO = lambdaQuery().eq(AreaCode::getOrgCode, orgCode).eq(AreaCode::getCode, areaCode).eq(AreaCode::getStatus, 1).one();
        if (areaCodeDTO == null) {
            return false;
        }
        if (StringUtils.isEmpty(areaCodeDTO.getPhoneRegex())) {
            return false;
        }
        Pattern phonePattern = Pattern.compile(areaCodeDTO.getPhoneRegex());
        Matcher matcher = phonePattern.matcher(phone);
        return matcher.matches();
    }

    //中国大陆
    private static boolean isMobile(String mobile) {
        String regex = "^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(16[5,6])|(17[0-8])|(18[0-9])|(19[1、5、8、9]))\\d{8}$";
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(mobile);
        return m.matches();
    }


    /**
     * 获取国际区号
     *
     * <AUTHOR>
     * @date 2022-04-07 15:37:38
     */
    private AreaCode queryAreaCode(AreaCodeParam areaCodeParam) {
        AreaCode areaCode = this.getById(areaCodeParam.getId());
        if (ObjectUtil.isNull(areaCode)) {
            throw new ServiceException(AreaCodeExceptionEnum.NOT_EXIST);
        }
        return areaCode;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
//        page(new AreaCodeParam(){{
//            setStatus(1);
//        }});
    }
}
