/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.coinorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.account.enums.AccountTypeEnum;
import vip.xiaonuo.business.account.param.AccountChangeBalanceParam;
import vip.xiaonuo.business.account.service.AccountService;
import vip.xiaonuo.business.accountbill.enums.AccountBillBusinessTypeEnum;
import vip.xiaonuo.business.accountbill.enums.AccountBillOptTypeEnum;
import vip.xiaonuo.business.coinorder.entity.CoinOrder;
import vip.xiaonuo.business.coinorder.enums.CoinOrderEntrustTypeEnum;
import vip.xiaonuo.business.coinorder.enums.CoinOrderExceptionEnum;
import vip.xiaonuo.business.coinorder.enums.CoinOrderStatusEnum;
import vip.xiaonuo.business.coinorder.enums.CoinOrderTypeEnum;
import vip.xiaonuo.business.coinorder.mapper.CoinOrderMapper;
import vip.xiaonuo.business.coinorder.param.CoinOrderParam;
import vip.xiaonuo.business.coinorder.param.PlaceCoinOrderModel;
import vip.xiaonuo.business.coinorder.param.QueryCoinOrderModel;
import vip.xiaonuo.business.coinorder.result.CoinOrderStatisticModel;
import vip.xiaonuo.business.coinorder.result.CoinOrderVo;
import vip.xiaonuo.business.coinorder.service.CoinOrderEndService;
import vip.xiaonuo.business.coinorder.service.CoinOrderService;
import vip.xiaonuo.business.contractorder.enums.ContractOrderEntrustTypeEnum;
import vip.xiaonuo.business.contractorder.enums.ContractOrderExceptionEnum;
import vip.xiaonuo.business.dict.service.SysDictDataService;
import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.business.market.enums.MarketExceptionEnum;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.core.consts.DictConstant;
import vip.xiaonuo.core.enums.SerialNoEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.util.ConvertUtils;
import vip.xiaonuo.core.util.DpDataUtils;
import vip.xiaonuo.core.util.MarketUtils;
import vip.xiaonuo.core.util.UUIDUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * 币币订单service接口实现类
 *
 * <AUTHOR>
 * @date 2022-09-14 11:02:20
 */
@Service
public class CoinOrderServiceImpl extends ServiceImpl<CoinOrderMapper, CoinOrder> implements CoinOrderService {
    private Logger logger = LoggerFactory.getLogger(CoinOrderService.class);
    @Resource
    private PlayerService playerService;
    @Resource
    private MarketService marketService;
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private AccountService accountService;
    @Resource
    @Lazy
    private CoinOrderEndService coinOrderEndService;

    @Resource
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public PageResult<CoinOrder> page(CoinOrderParam coinOrderParam) {
        Page<CoinOrder> coinOrderPage = getBaseMapper().queryPage(PageFactory.defaultPage(), coinOrderParam);
        return new PageResult<>(coinOrderPage);
    }

    @Override
    public PageResult<CoinOrderVo> page(Long playerId, QueryCoinOrderModel model) {
        QueryWrapper<CoinOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CoinOrder::getPlayerId, playerId);
        queryWrapper.lambda().eq(CoinOrder::getStatus, model.getStatus());
        queryWrapper.lambda().eq(StringUtils.isNotEmpty(model.getSymbol()), CoinOrder::getSymbol, model.getSymbol());
        queryWrapper.lambda().orderByDesc(CoinOrder::getCreateTime);
        PageResult<CoinOrder> pageResult = new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
        return ConvertUtils.convert(pageResult, CoinOrderVo.class);
    }

    @Override
    public List<CoinOrder> list(CoinOrderParam coinOrderParam) {
        return this.list();
    }

    @Override
    public void add(CoinOrderParam coinOrderParam) {
        CoinOrder coinOrder = new CoinOrder();
        BeanUtil.copyProperties(coinOrderParam, coinOrder);
        this.save(coinOrder);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<CoinOrderParam> coinOrderParamList) {
        coinOrderParamList.forEach(coinOrderParam -> {
            CoinOrder coinOrder = this.queryCoinOrder(coinOrderParam);
            this.removeById(coinOrder.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CoinOrderParam coinOrderParam) {
        CoinOrder coinOrder = this.queryCoinOrder(coinOrderParam);
        BeanUtil.copyProperties(coinOrderParam, coinOrder);
        this.updateById(coinOrder);
    }

    @Override
    public CoinOrder detail(CoinOrderParam coinOrderParam) {
        return this.queryCoinOrder(coinOrderParam);
    }

    @Resource
    private SysDictDataService sysDictDataService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CoinOrder placeOrder(Long playerId, PlaceCoinOrderModel model) {
        if (!CoinOrderTypeEnum.isExists(model.getType())) {
            throw new ServiceException(CoinOrderExceptionEnum.TYPE_NOT_EXIST);
        }
        if (!CoinOrderEntrustTypeEnum.isExists(model.getEntrustType())) {
            throw new ServiceException(CoinOrderExceptionEnum.TYPE_NOT_EXIST);
        }
        if (BigDecimal.ZERO.compareTo(model.getQuantity()) >= 0) {
            throw new ServiceException(CoinOrderExceptionEnum.QUANTITY_ERROR);
        }
        Market market = marketService.getMarketBySymbol(model.getSymbol());
        if (market == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_EXIST);
        }
        if (market.getTransactionSwitch() != 1) {
            throw new ServiceException(MarketExceptionEnum.TRADING_CLOSED);
        }
        BigDecimal price;
        if (ContractOrderEntrustTypeEnum.TYPE2.getCode().equals(model.getEntrustType())) {
            if (BigDecimal.ZERO.compareTo(model.getPrice()) >= 0) {
                throw new ServiceException(ContractOrderExceptionEnum.PRICE_NOT_EXIST);
            }
            price = model.getPrice();
        } else {
            price = DpDataUtils.getLatestPrice(redisTemplate, market.getSymbol());
            if (price == null) {
                throw new ServiceException(MarketExceptionEnum.NOT_LAST_PRICE);
            }
        }
        BigDecimal feeRate = sysDictDataService.getCommonConfigAsDecimal(DictConstant.COIN_FEE);
        if (feeRate == null) {
            throw new ServiceException(MarketExceptionEnum.FEE_RATE_NOT_EXIST);
        }
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(playerId));

        String orderNo = UUIDUtil.createOrderId(SerialNoEnum.B);
        CoinOrder coinOrder = new CoinOrder();
        coinOrder.setPlayerId(playerId);
        coinOrder.setType(model.getType());
        coinOrder.setEntrustType(model.getEntrustType());
        coinOrder.setSymbol(model.getSymbol());
        coinOrder.setOrderNo(orderNo);
        coinOrder.setQuantity(model.getQuantity());
        coinOrder.setOrderPrice(price);
        coinOrder.setOrderTime(new Date());
        coinOrder.setStatus(CoinOrderStatusEnum.STATUS0.getCode());
        //购买时间
        coinOrder.setCreateTime(new Date());
        coinOrder.setCreateUser(playerId);
        save(coinOrder);

        String accountCurrencyCode = null;
        AccountTypeEnum accountTypeEnum = null;
        BigDecimal frozenBalance = null;
        if (CoinOrderTypeEnum.TYPE1.getCode().equals(coinOrder.getType())) {
            accountCurrencyCode = MarketUtils.getBaseCode(coinOrder.getSymbol());
            accountTypeEnum = AccountTypeEnum.TYPE1;
//            frozenBalance = coinOrder.getQuantity().multiply(coinOrder.getOrderPrice()).setScale(market.getPriceDecimal(), RoundingMode.HALF_DOWN);
            frozenBalance = coinOrder.getQuantity();
        }
        if (CoinOrderTypeEnum.TYPE2.getCode().equals(coinOrder.getType())) {
            accountCurrencyCode = MarketUtils.getCode(coinOrder.getSymbol());
            accountTypeEnum = AccountTypeEnum.TYPE2;
            frozenBalance = coinOrder.getQuantity();
        }
        //变更余额
        assert accountTypeEnum != null;
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder balanceParamBuilder = AccountChangeBalanceParam.builder()
                .playerId(coinOrder.getPlayerId())
                .type(accountTypeEnum.getCode())
                .currencyCode(accountCurrencyCode)
                .businessType(AccountBillBusinessTypeEnum.COIN_ORDER)
                .businessNo(orderNo)
                .balance(frozenBalance)
                .optType(AccountBillOptTypeEnum.SUB)
                //操作冻结
                .frozen(frozenBalance)
                .frozenOptType(AccountBillOptTypeEnum.ADD)
                .updateUser(coinOrder.getPlayerId());
        accountService.changeBalance(balanceParamBuilder.build());
        return coinOrder;
    }

    @Transactional
    public void revokeEntrust(Long playerId, String orderNo, Long updateBy) {
        CoinOrder coinOrder = getCoinOrderByNoAsLock(orderNo);
        if (!CoinOrderStatusEnum.STATUS0.getCode().equals(coinOrder.getStatus())) {
            throw new ServiceException(CoinOrderExceptionEnum.NOT_EXIST);
        }
        if (!coinOrder.getPlayerId().equals(playerId)) {
            throw new ServiceException(CoinOrderExceptionEnum.NOT_EXIST);
        }
        Market market = marketService.getMarketBySymbol(coinOrder.getSymbol());
        if (market == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_EXIST);
        }
        coinOrder.setUpdateUser(updateBy);
        coinOrder.setUpdateTime(new Date());
        String accountCurrencyCode = null;
        AccountTypeEnum accountTypeEnum = null;
        BigDecimal frozenBalance = null;
        if (CoinOrderTypeEnum.TYPE1.getCode().equals(coinOrder.getType())) {
            accountCurrencyCode = MarketUtils.getBaseCode(coinOrder.getSymbol());
            accountTypeEnum = AccountTypeEnum.TYPE1;
            frozenBalance = coinOrder.getQuantity().multiply(coinOrder.getOrderPrice()).setScale(market.getPriceDecimal(), RoundingMode.HALF_DOWN);
        }
        if (CoinOrderTypeEnum.TYPE2.getCode().equals(coinOrder.getType())) {
            accountCurrencyCode = MarketUtils.getCode(coinOrder.getSymbol());
            accountTypeEnum = AccountTypeEnum.TYPE2;
            frozenBalance = coinOrder.getQuantity();
        }
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(playerId));
        //变更余额
        assert accountTypeEnum != null;
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder balanceParamBuilder = AccountChangeBalanceParam.builder()
                .playerId(coinOrder.getPlayerId())
                .type(accountTypeEnum.getCode())
                .currencyCode(accountCurrencyCode)
                .businessType(AccountBillBusinessTypeEnum.REVOKE_COIN)
                .businessNo(orderNo)
                //退还
                .balance(frozenBalance)
                .optType(AccountBillOptTypeEnum.ADD)
                //操作冻结
                .frozen(frozenBalance)
                .frozenOptType(AccountBillOptTypeEnum.SUB)
                .updateUser(coinOrder.getUpdateUser());
        accountService.changeBalance(balanceParamBuilder.build());
        //修改状态
        lambdaUpdate().set(CoinOrder::getStatus, CoinOrderStatusEnum.STATUS3.getCode()).set(CoinOrder::getUpdateTime, new Date())
                .set(CoinOrder::getUpdateTime, coinOrder.getUpdateTime())
                .set(CoinOrder::getUpdateUser, coinOrder.getUpdateUser())
                .eq(CoinOrder::getId, coinOrder.getId())
                .update();

    }


    public CoinOrder getCoinOrderByNoAsLock(String orderNo) {
        //行锁
        CoinOrder coinOrder = lambdaQuery().eq(CoinOrder::getOrderNo, orderNo).last(" for update").one();
        if (ObjectUtil.isNull(coinOrder)) {
            throw new ServiceException(CoinOrderExceptionEnum.NOT_EXIST);
        }
        return coinOrder;
    }

    /**
     * 获取币币订单
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    private CoinOrder queryCoinOrder(CoinOrderParam coinOrderParam) {
        CoinOrder coinOrder = this.getById(coinOrderParam.getId());
        if (ObjectUtil.isNull(coinOrder)) {
            throw new ServiceException(CoinOrderExceptionEnum.NOT_EXIST);
        }
        return coinOrder;
    }

    @Override
    @Transactional
    public void endCoinOrder(String coinOrderNo, BigDecimal c, Long updateBy) {
        CoinOrder coinOrder = getCoinOrderByNoAsLock(coinOrderNo);
        if (!CoinOrderStatusEnum.STATUS0.getCode().equals(coinOrder.getStatus())) {
            //已处理，直接跳过
            return;
        }
        coinOrder.setUpdateUser(updateBy);
        coinOrder.setUpdateTime(new Date());
        BigDecimal feeRate = sysDictDataService.getCommonConfigAsDecimal(DictConstant.COIN_FEE);
        if (feeRate == null) {
            throw new ServiceException(MarketExceptionEnum.FEE_RATE_NOT_EXIST);
        }
        Market market = marketService.getMarketBySymbol(coinOrder.getSymbol());
        if (market == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_EXIST);
        }

        Long playerId = coinOrder.getPlayerId();
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(playerId));

        BigDecimal amount = BigDecimal.ZERO;
        String accountCurrencyCode = null;
        AccountTypeEnum accountTypeEnum = null;
        String accountBaseCurrencyCode = null;
        AccountTypeEnum accountBaseTypeEnum = null;
        BigDecimal frozenBalance = null;
        if (CoinOrderTypeEnum.TYPE1.getCode().equals(coinOrder.getType())) {
            accountCurrencyCode = MarketUtils.getBaseCode(coinOrder.getSymbol());
            accountTypeEnum = AccountTypeEnum.TYPE1;
            accountBaseCurrencyCode = MarketUtils.getCode(coinOrder.getSymbol());
            accountBaseTypeEnum = AccountTypeEnum.TYPE2;
//            amount = coinOrder.getQuantity();
            amount = coinOrder.getQuantity().divide(c, market.getQuantityDecimal(), RoundingMode.HALF_DOWN);
            //计算手续费 保留两位小数且四舍五入
            coinOrder.setOrderFee(amount.multiply(feeRate).setScale(market.getQuantityDecimal(), BigDecimal.ROUND_HALF_UP));

            //买入时：价格为下单价
            c = coinOrder.getOrderPrice();
            frozenBalance = coinOrder.getQuantity();
//            frozenBalance = coinOrder.getQuantity().multiply(c).setScale(market.getPriceDecimal(), RoundingMode.HALF_DOWN);
        }
        if (CoinOrderTypeEnum.TYPE2.getCode().equals(coinOrder.getType())) {
            accountCurrencyCode = MarketUtils.getCode(coinOrder.getSymbol());
            accountTypeEnum = AccountTypeEnum.TYPE2;
            accountBaseCurrencyCode = MarketUtils.getBaseCode(coinOrder.getSymbol());
            accountBaseTypeEnum = AccountTypeEnum.TYPE1;
            amount = coinOrder.getQuantity().multiply(c).setScale(market.getPriceDecimal(), RoundingMode.HALF_DOWN);
            //计算手续费 保留两位小数且四舍五入
            coinOrder.setOrderFee(amount.multiply(feeRate).setScale(market.getPriceDecimal(), BigDecimal.ROUND_HALF_UP));

            frozenBalance = coinOrder.getQuantity();
        }

        coinOrder.setClosePrice(c);
        coinOrder.setCloseTime(new Date());
        coinOrder.setAmount(amount);
        coinOrder.setStatus(CoinOrderStatusEnum.STATUS2.getCode());
        //采用增量更新，不能使用全量更新
        lambdaUpdate().set(CoinOrder::getOrderFee, coinOrder.getOrderFee())
                .set(CoinOrder::getClosePrice, coinOrder.getClosePrice())
                .set(CoinOrder::getCloseTime, coinOrder.getCloseTime())
                .set(CoinOrder::getAmount, coinOrder.getAmount())
                .set(CoinOrder::getStatus, coinOrder.getStatus())
                .set(CoinOrder::getUpdateTime, coinOrder.getUpdateTime())
                .set(CoinOrder::getUpdateUser, coinOrder.getUpdateUser())
                .eq(CoinOrder::getId, coinOrder.getId()).update();


        //减少冻结
        assert accountTypeEnum != null;
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder balanceParamBuilder = AccountChangeBalanceParam.builder()
                .playerId(coinOrder.getPlayerId())
                .type(accountTypeEnum.getCode())
                .currencyCode(accountCurrencyCode)
                .businessType(AccountBillBusinessTypeEnum.END_COIN_ORDER)
                .businessNo(coinOrder.getOrderNo())
                //操作冻结
                .frozen(frozenBalance)
                .frozenOptType(AccountBillOptTypeEnum.SUB)
                .updateUser(coinOrder.getUpdateUser());
        accountService.changeBalance(balanceParamBuilder.build());

        //添加
        balanceParamBuilder = AccountChangeBalanceParam.builder()
                .playerId(coinOrder.getPlayerId())
                .type(accountBaseTypeEnum.getCode())
                .currencyCode(accountBaseCurrencyCode)
                .businessType(AccountBillBusinessTypeEnum.END_COIN_ORDER)
                .businessNo(coinOrder.getOrderNo())
                //减掉手续费
                .balance(amount.subtract(coinOrder.getOrderFee()))
                .optType(AccountBillOptTypeEnum.ADD)
                .updateUser(coinOrder.getUpdateUser());
        accountService.changeBalance(balanceParamBuilder.build());
    }

    @Override
    public CoinOrderStatisticModel findCoinOrderStatistic(HomeStatisticParam param) {
        return getBaseMapper().findCoinOrderStatistic(param);
    }
}
