package vip.xiaonuo.business.timer.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.cron.CronUtil;
import cn.hutool.cron.task.Task;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.log.Log;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import vip.xiaonuo.business.timer.enums.SysTimersExceptionEnum;
import vip.xiaonuo.business.timer.service.TimerExeService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.timer.TimerTaskRunner;

import java.lang.reflect.Method;

/**
 * hutool方式的定时任务执行
 *
 * <AUTHOR>
 * @date 2020/7/1 13:48
 */
@Service
public class HutoolTimerExeServiceImpl implements TimerExeService {

    private static final Log log = Log.get();

    @Override
    public void startTimer(String taskId, String cron, String className) {

        if (ObjectUtil.hasEmpty(taskId, cron, className)) {
            throw new ServiceException(SysTimersExceptionEnum.EXE_EMPTY_PARAM);
        }

        // 预加载类看是否存在此定时任务类
        try {
            Class.forName(className);
        } catch (ClassNotFoundException e) {
            log.error(e);
            throw new ServiceException(SysTimersExceptionEnum.TIMER_NOT_EXISTED);
        }

        // 定义hutool的任务
        Task task = () -> {
            try {
                TimerTaskRunner timerTaskRunner = (TimerTaskRunner) SpringUtil.getBean(Class.forName(className));
                timerTaskRunner.action();
            } catch (ClassNotFoundException e) {
                log.error(">>> 任务执行异常：{}", e.getMessage());
            }
        };

        // 开始执行任务
        CronUtil.schedule(taskId, cron, task);
    }

    @Override
    public void stopTimer(String taskId) {
        CronUtil.remove(taskId);
    }

    /**
     * 执行一个task
     *
     * @param className
     */
    @SneakyThrows
    @Override
    public void executeTimer(String className) {

        Class clazz = Class.forName(className);
        // 获取public方法getScore，参数为String:
        // 创建对象
        TimerTaskRunner timerTaskRunner = (TimerTaskRunner) SpringUtil.getBean(clazz);
        timerTaskRunner.action();
       /* Object instance = clazz.getConstructor().newInstance();
        // 调用方法
        for(Method method :clazz.getMethods()) {
            if("action".equals(method.getName())) {
                method.invoke(instance);//
            }
        }*/
    }




}
