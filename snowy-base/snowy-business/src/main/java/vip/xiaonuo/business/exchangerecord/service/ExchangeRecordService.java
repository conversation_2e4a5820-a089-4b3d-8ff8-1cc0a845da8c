package vip.xiaonuo.business.exchangerecord.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import vip.xiaonuo.business.exchangerecord.entity.ExchangeRecord;
import vip.xiaonuo.business.exchangerecord.mapper.ExchangeRecordMapper;
import vip.xiaonuo.business.exchangerecord.param.ExchangeRecordParam;
import vip.xiaonuo.business.exchangerecord.result.ExchangeRecordResult;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

@Service
public class ExchangeRecordService extends ServiceImpl<ExchangeRecordMapper, ExchangeRecord> {

    public PageResult<ExchangeRecordResult> page(ExchangeRecordParam param) {
        return new PageResult<>(baseMapper.pageResult(PageFactory.defaultPage(), param));
    }
}
