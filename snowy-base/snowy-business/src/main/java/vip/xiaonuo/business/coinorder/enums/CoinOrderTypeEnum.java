package vip.xiaonuo.business.coinorder.enums;

import lombok.Getter;

@Getter
public enum CoinOrderTypeEnum {
    TYPE1(1, "买"),
    TYPE2(2, "卖");

    private final Integer code;

    private final String message;

    CoinOrderTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static boolean isExists(Integer code) {
        for (CoinOrderTypeEnum typeEnum : values()) {
            if (code.equals(typeEnum.getCode())) {
                return true;
            }
        }
        return false;
    }

}
