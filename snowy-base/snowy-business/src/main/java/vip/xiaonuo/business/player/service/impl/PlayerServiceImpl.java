/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.player.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import vip.xiaonuo.business.context.login.AdminLoginContext;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.enums.PlayerExceptionEnum;
import vip.xiaonuo.business.player.enums.PlayerStatusEnum;
import vip.xiaonuo.business.player.mapper.PlayerMapper;
import vip.xiaonuo.business.player.param.PlayerParam;
import vip.xiaonuo.business.player.pojo.dto.FindFollowerDto;
import vip.xiaonuo.business.player.pojo.vo.FindFollowerVo;
import vip.xiaonuo.business.player.result.AgentName;
import vip.xiaonuo.business.player.result.AgentOrgInfo;
import vip.xiaonuo.business.player.result.PlayerResult;
import vip.xiaonuo.business.player.result.PlayerStatisticModel;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.playerinfo.entity.PlayerInfo;
import vip.xiaonuo.business.playerinfo.service.PlayerInfoService;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.business.transaction.pojo.dto.FindUserTransactionDto;
import vip.xiaonuo.business.transaction.pojo.vo.FindUserTransactionVo;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.context.login.LoginContext;
import vip.xiaonuo.core.context.login.LoginContextHolder;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.util.CommonUtils;
import vip.xiaonuo.core.util.Hmac;
import vip.xiaonuo.core.util.InviteCodeUtil;
import vip.xiaonuo.core.util.MessageUtils;
import vip.xiaonuo.session.security.UserSessionService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 玩家service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-04 14:31:45
 */
@Service
public class PlayerServiceImpl extends ServiceImpl<PlayerMapper, Player> implements PlayerService {
    private Logger logger = LoggerFactory.getLogger(PlayerServiceImpl.class);
    @Resource
    private PlayerInfoService playerInfoService;
    @Resource
    private UserSessionService userSessionService;

    private String toSortColumn(String c) {
        if ("createTime".equalsIgnoreCase(c)) {
            return "create_Time";
        }
        if ("lastLoginTime".equalsIgnoreCase(c)) {
            return "last_Login_Time";
        }
        return c;
    }

    @Override
    public PageResult<PlayerResult> pageResult(PlayerParam playerParam) {
        Page<PlayerResult> page = PageFactory.defaultPage();
        if (ObjectUtil.isAllNotEmpty(playerParam.getSortBy(), playerParam.getOrderBy())) {
            page.addOrder(new OrderItem(toSortColumn(playerParam.getSortBy()), playerParam.getOrderBy().equals(CommonConstant.ASC)));
        } else {
            page.addOrder(new OrderItem("create_Time", false));
        }
        Page<PlayerResult> playerResultPage = getBaseMapper().pageResult(page, playerParam);
        playerResultPage.getRecords().forEach(p -> {
            p.setPassword("");
            p.setFundsPassword("");
        });
        return new PageResult<>(playerResultPage);
    }

    public Map<Long, String> queryAgentName(List<Long> agentIds) {
        if (CollectionUtil.isEmpty(agentIds)) {
            return new HashMap<>();
        }
        return getBaseMapper().queryAgentName(agentIds).stream().collect(Collectors.toMap(AgentName::getId, AgentName::getAccount));
    }

    public Map<Long, String> queryPlayerName(List<Long> playerIds) {
        if (CollectionUtil.isEmpty(playerIds)) {
            return new HashMap<>();
        }
        return lambdaQuery().in(Player::getId, playerIds).select(Player::getId, Player::getAccount).list().stream().collect(Collectors.toMap(Player::getId, Player::getAccount));
    }

    @Override
    public void editSpotScale(Long id, Integer spotScale) {
        Player player = getById(id);
        if (player == null) {
            throw new ServiceException(PlayerExceptionEnum.NOT_EXIST);
        }
        if (spotScale >= -1 && spotScale <= 100) {
            player.setSpotScale(spotScale);
            player.setUpdateTime(new Date());
            player.setUpdateUser(LoginContextHolder.me(AdminLoginContext.class).getLoginUserId());
            updateById(player);
            return;
        }
        throw new ServiceException(500, "比例必须为-1至100中间的值");
    }

    @Override
    public void isForbidTrade(Long id) {
        Player player = getById(id);
        if(ObjectUtils.isEmpty(player) || ObjectUtils.isEmpty(player.getIsForbidTrade())){
            return;
        }
        if(Integer.valueOf(1).equals(player.getIsForbidTrade())){
            throw new ServiceException(MessageUtils.get("account.is.forbid.trade"));
        }
    }

    public List<Player> list(PlayerParam playerParam) {
        return this.list();
    }

    public void add(PlayerParam playerParam) {
        Player player = new Player();
        BeanUtil.copyProperties(playerParam, player);
//        player.setAccount(playerParam.getPhone());
        player.setInvitationCode(genInviteCode());
        player.setSalt(InviteCodeUtil.getRandom(5));
        player.setCreateTime(new Date());
        String pwd = Hmac.md5EncryptSalt(playerParam.getPassword(), player.getSalt());
        player.setPassword(pwd);
        //注册时，资金密码保持与登录密码一致
        player.setFundsPassword(pwd);
        //昵称
        if (StrUtil.isEmpty(player.getNickName())) {
            player.setNickName(CommonUtils.star(player.getAccount()));
        }
        try {
            save(player);
        } catch (DuplicateKeyException e){
            throw new ServiceException("account.is.exist");
        }

        //注册直接创建用户认证信息
        PlayerInfo playerInfo = new PlayerInfo();
        playerInfo.setPlayerId(player.getId());
        playerInfo.setAuthStatus(0);
        playerInfo.setAdvancedStatus(0);
        playerInfoService.save(playerInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(List<PlayerParam> playerParamList) {
        playerParamList.forEach(playerParam -> {
            Player player = this.queryPlayer(playerParam);
            this.removeById(player.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void edit(PlayerParam playerParam) {
        LambdaUpdateChainWrapper<Player> updateChainWrapper = lambdaUpdate().set(Player::getNickName, playerParam.getNickName()).set(Player::getRemark, playerParam.getRemark()).set(Player::getStatus, playerParam.getStatus()).eq(Player::getId, playerParam.getId());
        //验证账号是否被冻结
        if (PlayerStatusEnum.STATUS2.getCode().equals(playerParam.getStatus())) {
            //强制下线
            userSessionService.deleteUserSessionByUserId(playerParam.getId());
            updateChainWrapper.set(Player::getOnlineFlag, 0);
        }
        if(StringUtils.isNotEmpty(playerParam.getPhone())){
            updateChainWrapper.set(Player::getPhone, playerParam.getPhone());
        }
        updateChainWrapper.update();
    }

    public Player detail(PlayerParam playerParam) {
        return this.queryPlayer(playerParam);
    }

    @Override
    public AgentOrgInfo getAgentByInviteCode(String orgCode, String inviteCode) {
        try {
            return getBaseMapper().getAgentByInviteCode(orgCode, inviteCode);
        } catch (Exception e) {
            logger.error("根据邀请码{}查询代理信息异常", inviteCode, e);
            throw new ServiceException(PlayerExceptionEnum.INVITE_CODE_NOT_EXIST);
        }
    }

    /**
     * 获取玩家
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    private Player queryPlayer(PlayerParam playerParam) {
        Player player = this.getById(playerParam.getId());
        if (ObjectUtil.isNull(player)) {
            throw new ServiceException(PlayerExceptionEnum.NOT_EXIST);
        }
        return player;
    }

    public String genInviteCode() {
        String finalInviteCode = "";
        int index = 0;
        while (true) {
            index++;
            String inviteCode = InviteCodeUtil.getRandom(4);
            LambdaQueryWrapper<Player> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(Player::getInvitationCode, inviteCode);
            if (this.count(lambdaQueryWrapper) <= 0) {
                finalInviteCode = inviteCode;
                break;
            }
            if (index > 20) {
                break;
            }
        }
        if (ObjectUtil.isEmpty(finalInviteCode)) {
            throw new ServiceException(PlayerExceptionEnum.INVITE_CODE_GEN_ERROR);
        }
        return finalInviteCode;
    }

    @Override
    public void lockPlayer(List<Long> playerIds) {
        getBaseMapper().lockPlayer(playerIds);
    }

    /**
     * 根据账号获取用户
     *
     * @param account
     * @return
     */
    @Override
    public Player findPlayerByAccount(String account) {
        QueryWrapper<Player> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Player::getAccount, account);
        return this.getOne(queryWrapper);
    }

    /**
     * 查询注册用户（时间为null 查询总注册数）
     *
     * @return
     */
    @Override
    public PlayerStatisticModel findUserStatistic(HomeStatisticParam param) {
        return getBaseMapper().findUserStatistic(param);
    }

    /**
     * 分页查询用户充值提现交易记录
     *
     * @param dto
     * @return
     */
    @Override
    public PageResult<FindFollowerVo> findUserFollower(FindFollowerDto dto) {
        Page<FindFollowerVo> listAll = getBaseMapper().findUserFollower(PageFactory.defaultPage(), dto);
        return new PageResult<>(listAll);
    }
}
