/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.notice.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.BooleanUtils;
import vip.xiaonuo.business.notice.entity.PlayerNotice;
import vip.xiaonuo.business.notice.enums.PlayerNoticeExceptionEnum;
import vip.xiaonuo.business.notice.enums.PlayerNoticeStatusEnum;
import vip.xiaonuo.business.notice.mapper.PlayerNoticeMapper;
import vip.xiaonuo.business.notice.param.PlayerNoticeParam;
import vip.xiaonuo.business.notice.service.PlayerNoticeService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 玩家公告表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-07 17:14:13
 */
@Service
public class PlayerNoticeServiceImpl extends ServiceImpl<PlayerNoticeMapper, PlayerNotice> implements PlayerNoticeService {

    @Override
    public PageResult<PlayerNotice> page(PlayerNoticeParam playerNoticeParam) {
        QueryWrapper<PlayerNotice> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(playerNoticeParam)) {

            // 根据标题 查询
            if (ObjectUtil.isNotEmpty(playerNoticeParam.getTitle())) {
                queryWrapper.lambda().like(PlayerNotice::getTitle, playerNoticeParam.getTitle());
            }
            // 根据状态（字典 0草稿 1发布 2撤回 3删除） 查询
            if (ObjectUtil.isNotEmpty(playerNoticeParam.getStatus())) {
                queryWrapper.lambda().eq(PlayerNotice::getStatus, playerNoticeParam.getStatus());
            }
            //是否置顶
            if (ObjectUtil.isNotEmpty(playerNoticeParam.getIsTop())) {
                queryWrapper.lambda().eq(PlayerNotice::getIsTop, playerNoticeParam.getIsTop());
            }
            if (ObjectUtil.isNotEmpty(playerNoticeParam.getType())) {
                queryWrapper.lambda().eq(PlayerNotice::getType, playerNoticeParam.getType());
            }
            if (ObjectUtil.isNotEmpty(playerNoticeParam.getLanguageCode())) {
                queryWrapper.lambda().eq(PlayerNotice::getLanguageCode, playerNoticeParam.getLanguageCode());
            }
            if (ObjectUtil.isNotEmpty(playerNoticeParam.getOrgCode())) {
                queryWrapper.lambda().eq(PlayerNotice::getOrgCode, playerNoticeParam.getOrgCode());
            }
            queryWrapper.orderByDesc("TOP_TIME").orderByDesc("CREATE_TIME");
        }
        return new PageResult<>(this.page(new Page<>(playerNoticeParam.getPage(), playerNoticeParam.getLimit()), queryWrapper));
    }

    @Override
    public List<PlayerNotice> list(PlayerNoticeParam playerNoticeParam) {
        QueryWrapper<PlayerNotice> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(playerNoticeParam)) {
            // 根据状态（字典 0草稿 1发布 2撤回 3删除） 查询
            if (ObjectUtil.isNotEmpty(playerNoticeParam.getStatus())) {
                queryWrapper.lambda().eq(PlayerNotice::getStatus, playerNoticeParam.getStatus());
            }
            if (ObjectUtil.isNotEmpty(playerNoticeParam.getType())) {
                queryWrapper.lambda().eq(PlayerNotice::getType, playerNoticeParam.getType());
            }
            if (ObjectUtil.isNotEmpty(playerNoticeParam.getOrgCode())) {
                queryWrapper.lambda().eq(PlayerNotice::getOrgCode, playerNoticeParam.getOrgCode());
            }
        }
        queryWrapper.orderByDesc("TOP_TIME").orderByAsc("CREATE_TIME");
        return this.list(queryWrapper);
    }

    @Override
    public void add(PlayerNoticeParam playerNoticeParam) {
        PlayerNotice playerNotice = new PlayerNotice();
        BeanUtil.copyProperties(playerNoticeParam, playerNotice);
        playerNotice.setUpdateTime(new Date());
        this.save(playerNotice);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<PlayerNoticeParam> playerNoticeParamList) {
        playerNoticeParamList.forEach(playerNoticeParam -> {
        PlayerNotice playerNotice = this.queryPlayerNotice(playerNoticeParam);
            this.removeById(playerNotice.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PlayerNoticeParam playerNoticeParam) {
        PlayerNotice playerNotice = this.queryPlayerNotice(playerNoticeParam);
        BeanUtil.copyProperties(playerNoticeParam, playerNotice);
        playerNotice.setUpdateTime(new Date());
        this.updateById(playerNotice);
    }

    @Override
    public PlayerNotice detail(PlayerNoticeParam playerNoticeParam) {
        return this.queryPlayerNotice(playerNoticeParam);
    }

    @Override
    public void changeStatus(PlayerNoticeParam playerNoticeParam) {
        PlayerNotice playerNotice = this.queryPlayerNotice(playerNoticeParam);
        //校验参数，检查状态是否正确
        checkParam(playerNoticeParam, false);
        playerNotice.setStatus(playerNoticeParam.getStatus());
        playerNotice.setUpdateTime(new Date());
        this.updateById(playerNotice);
    }

    @Override
    public void changeTop(PlayerNoticeParam playerNoticeParam) {
        PlayerNotice playerNotice = this.queryPlayerNotice(playerNoticeParam);
        playerNotice.setIsTop(playerNoticeParam.getIsTop());
        playerNotice.setTopTime(BooleanUtils.toBoolean(playerNoticeParam.getIsTop()) ? new Date() : null);
        playerNotice.setUpdateTime(new Date());
        this.updateById(playerNotice);
    }

    /**
     * 获取玩家公告表
     *
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    private PlayerNotice queryPlayerNotice(PlayerNoticeParam playerNoticeParam) {
        PlayerNotice playerNotice = this.getById(playerNoticeParam.getId());
        if (ObjectUtil.isNull(playerNotice)) {
            throw new ServiceException(PlayerNoticeExceptionEnum.NOT_EXIST);
        }
        return playerNotice;
    }

    /**
     * 校验参数，判断状态是否正确
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    private void checkParam(PlayerNoticeParam playerNoticeParam, boolean isAddOrEdit) {
        //保存或编辑时，传递的状态参数应为草稿，或发布
        Integer status = playerNoticeParam.getStatus();
        if (isAddOrEdit) {
            if (!PlayerNoticeStatusEnum.DRAFT.getCode().equals(status) &&
                    !PlayerNoticeStatusEnum.PUBLIC.getCode().equals(status)) {
                throw new ServiceException(PlayerNoticeExceptionEnum.NOTICE_STATUS_ERROR);
            }
        } else {
            //修改状态时，传递的状态参数应为撤回或删除或发布
            if (!PlayerNoticeStatusEnum.CANCEL.getCode().equals(status) &&
                    !PlayerNoticeStatusEnum.DELETED.getCode().equals(status) &&
                    !PlayerNoticeStatusEnum.PUBLIC.getCode().equals(status)) {
                throw new ServiceException(PlayerNoticeExceptionEnum.NOTICE_STATUS_ERROR);
            }
        }
    }
}
