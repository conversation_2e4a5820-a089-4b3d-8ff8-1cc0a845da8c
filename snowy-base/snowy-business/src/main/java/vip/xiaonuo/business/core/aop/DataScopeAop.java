
package vip.xiaonuo.business.core.aop;

import cn.hutool.core.collection.CollectionUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import vip.xiaonuo.business.context.login.AdminLoginContext;
import vip.xiaonuo.business.user.service.SysUserService;
import vip.xiaonuo.core.consts.AopSortConstant;
import vip.xiaonuo.core.context.login.LoginContextHolder;
import vip.xiaonuo.core.enums.AdminRoleTypeEnum;
import vip.xiaonuo.core.pojo.base.param.BaseParam;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据权限切面
 *
 * <AUTHOR>
 * @date 2020/3/28 17:16
 */
@Aspect
@Order(AopSortConstant.DATA_SCOPE_AOP)
public class DataScopeAop {
    @Resource
    private SysUserService sysUserService;

    /**
     * 数据范围切入点
     *
     * <AUTHOR>
     * @date 2020/4/6 13:32
     */
    @Pointcut("@annotation(vip.xiaonuo.core.annotion.DataScope)")
    private void getDataScopePointCut() {
    }

    /**
     * 执行数据范围过滤
     *
     * <AUTHOR>
     * @date 2020/4/6 13:32
     */
    @Before("getDataScopePointCut()")
    public void doDataScope(JoinPoint joinPoint) {

        //不是超级管理员时进行数据权限过滤
        if (!LoginContextHolder.me(AdminLoginContext.class).isSuperAdmin()) {
            Object[] args = joinPoint.getArgs();

            //数据范围就是组织机构id集合
            List<Long> loginUserDataScopeIdList = LoginContextHolder.me(AdminLoginContext.class).getLoginUserDataScopeIdList();
            AdminRoleTypeEnum adminRoleTypeEnum = LoginContextHolder.me(AdminLoginContext.class).getLoginUserRoleType();
            String orgCode = LoginContextHolder.me(AdminLoginContext.class).getLoginUserOrgCode();
            Long loginUserId = LoginContextHolder.me(AdminLoginContext.class).getLoginUserId();
            BaseParam baseParam;
            for (Object object : args) {
                if (object instanceof BaseParam) {
                    baseParam = (BaseParam) object;
                    baseParam.setDataScope(loginUserDataScopeIdList);
                    baseParam.setAdminRoleType(adminRoleTypeEnum);
                    baseParam.setLoginUserOrgCode(orgCode);
                    baseParam.setLoginUserId(loginUserId);

                    if (adminRoleTypeEnum == AdminRoleTypeEnum.AGENT_LINE) {
                        //如果为代理线，找出所有代理人
                        baseParam.setAgentIds(sysUserService.getUserIdsBySuperiorId(loginUserId));
                        if (CollectionUtil.isEmpty(baseParam.getAgentIds())) {
                            baseParam.setAgentIds(new ArrayList<Long>() {{
                                add(-99999L);//给一个不存在的
                            }});
                        }
                    }
                }
            }
        }
    }
}
