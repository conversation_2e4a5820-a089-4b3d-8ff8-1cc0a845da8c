package vip.xiaonuo.business.contractorder.enums;

import lombok.Getter;

@Getter
public enum ContractOrderStatusEnum {
//0:委托,1:持仓,2:平仓

    STATUS0(0, "委托"),
    STATUS1(1, "持仓"),
    STATUS2(2, "平仓"),
    STATUS3(3, "撤销委托");

    private final Integer code;

    private final String message;

    ContractOrderStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
