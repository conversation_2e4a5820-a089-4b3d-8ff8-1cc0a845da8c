/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.player.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 玩家
 *
 * <AUTHOR>
 * @date 2022-04-04 14:31:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tt_player")
public class Player extends BaseEntity {
    //角色(c:真实玩家,p:代理商)
    private String role;
    /**
     * 机构代码
     */
    private String orgCode;
    /**
     * 账号
     */
    private String account;

    /**
     * 密码
     */
    private String password;
    //资金密码
    private String fundsPassword;
    /**
     * 昵称
     */
    private String nickName;

    /**
     * 状态:(1:正常,2:冻结)
     */
    private Integer status;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 邮箱
     */
    private String email;
    //国际区号
    private String phoneCode;
    /**
     * 手机
     */
    private String phone;

    /**
     * 最后登陆IP
     */
    private String lastLoginIp;

    /**
     * 最后登陆时间
     */
    @Excel(name = "最后登陆时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date lastLoginTime;

    /**
     * 邀请码
     */
    private String invitationCode;

    /**
     * 邀请人
     */
    private Long invitedBy;
    /**
     * 盐加密
     */
    private String salt;
    //代理人ID
    private Long agentId;
    /**
     * 注册IP
     */
    private String registerIp;
    //现货比例(-1:不控制,0:亏,100:赢)
    private Integer spotScale;
    //在线标志(0:不在线,1:在线)
    private Integer onlineFlag;
    //是否禁止交易(1-是；0-不是)
    private Integer isForbidTrade;
    private String remark;

    private Integer isFollower;

    private String inviteSeq;
}
