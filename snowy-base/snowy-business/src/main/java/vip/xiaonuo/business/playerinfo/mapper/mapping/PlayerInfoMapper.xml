<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.business.playerinfo.mapper.PlayerInfoMapper">
    <select id="findPlayerInfoForPage" resultType="vip.xiaonuo.business.playerinfo.pojo.vo.FindPlayerInfoForPageVo">
        SELECT
        t1.id,
        t1.player_id playerId,
        t1.real_name realName,
        t1.country country,
        t1.id_card_just idCardJust,
        t1.id_card_back idCardBack,
        t1.hold_id_card holdIdCard,
        t1.id_card idCard,
        t1.live_address liveAddress,
        t1.auth_status authStatus,
        t1.create_time createTime,
        t1.auth_time authTime,
        t1.check_time checkTime,
        t1.check_id checkId,
        t2.nick_name userName,
        t2.account userAccount,
        t3.account superiorName
        FROM
        t_player_info t1
        LEFT JOIN tt_player t2 ON t1.player_id = t2.id
        LEFT JOIN sys_user t3 ON t2.agent_id = t3.id
	<where>
        <if test="ew.playerId != null and ew.playerId != ''">
            and t1.player_id = #{ew.playerId}
        </if>
        <if test="ew.userAccount != null and ew.userAccount != ''">
            and t2.account = #{ew.userAccount}
        </if>
        <if test="ew.authStatus != null and ew.authStatus != ''">
            and t1.auth_status = #{ew.authStatus}
        </if>
        <if test="ew.userName != null and ew.userName != ''">
            and t2.nick_name like  concat('%',#{ew.userName},'%')
        </if>
        <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
            and t2.agent_id in
            <foreach item="item" index="index" collection="ew.agentIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
            and t2.agent_id = #{ew.loginUserId}
        </if>
    </where>
    </select>
</mapper>
