package vip.xiaonuo.business.userproductorder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import vip.xiaonuo.business.playerinfo.entity.PlayerInfo;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.business.userproductorder.entity.UserProductOrder;
import vip.xiaonuo.business.userproductorder.param.UserProductOrderParam;
import vip.xiaonuo.business.userproductorder.pojo.vo.FindOrderForPageVo;
import vip.xiaonuo.business.userproductorder.result.UserProductOrderStatisticModel;

import java.math.BigDecimal;
import java.util.List;

/**
 * 理财订单
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:41
 */
public interface UserProductOrderMapper extends BaseMapper<UserProductOrder> {
    /**
     * 分页查询订单信息
     * @param userProductOrderParam
     * @return
     */
    Page<FindOrderForPageVo> findOrderForPage(Page<UserProductOrder> defaultPage, @Param("ew") UserProductOrderParam userProductOrderParam);

    /**
     * 获取当前需要计算收益的订单
     * @return
     */
    List<UserProductOrder> findUserFinancialIncome();

    /**
     * 根据用户获取累计收益
     * @param playerId
     * @return
     */
    BigDecimal findprofitSum(@Param("playerId") Long playerId);

    /**
     * 根据时间与类型统计数据
     * @param type
     * @param date
     * @return
     */
//   BigDecimal findUserOrderStatistic(@Param("type")Integer type,@Param("date") String date);

    /**
     * 根据时间查询今日赎回和赎回金额
     * @param date
     * @return
     */
//    BigDecimal findUserOrderStatistic(@Param("date") String date);

    /**
     * 根据时间查询今日赎回和赎回金额
     * @param date
     * @return
     */
//    BigDecimal findUserOrderRedeemStatistic(@Param("date") String date);
    /**
     * 今日违约金
     * @param date
     * @return
     */
//    BigDecimal findUserOrderViolateStatistic(@Param("date") String date);

    UserProductOrderStatisticModel findUserProductOrderStatistic(@Param("ew") HomeStatisticParam param);


}
