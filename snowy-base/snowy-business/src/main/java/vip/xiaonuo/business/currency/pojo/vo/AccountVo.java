package vip.xiaonuo.business.currency.pojo.vo;

import lombok.Data;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;

import java.math.BigDecimal;

@Data
public class AccountVo extends BaseEntity {

    /**
     * 玩家ID
     */
    private Long playerId;

    /**
     * 类型(1:币币账户;2:合约账户;3:现货账户)
     */
    private Integer type;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 冻结余额
     */
    private BigDecimal frozenBalance;

    /**
     * logo
     */
    private String logo;
}
