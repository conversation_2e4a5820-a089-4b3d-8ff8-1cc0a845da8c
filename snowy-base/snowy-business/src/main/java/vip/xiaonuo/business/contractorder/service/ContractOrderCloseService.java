package vip.xiaonuo.business.contractorder.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.contractorder.entity.ContractOrder;
import vip.xiaonuo.business.contractorder.enums.ContractOrderStatusEnum;
import vip.xiaonuo.business.contractorder.enums.ContractOrderTypeEnum;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.core.pojo.dto.ContractOrderInfoDTO;
import vip.xiaonuo.core.util.RedisLock;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Service
public class ContractOrderCloseService {
    private Logger logger = LoggerFactory.getLogger(ContractOrderCloseService.class);
    private RedisTemplate<String, Object> redisTemplate;
    private HashOperations<String, String, ContractOrderInfoDTO> orderHashOperations;
    @Resource
    private ContractOrderService contractOrderService;
    @Resource
    private MarketService marketService;

    @Resource
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
        orderHashOperations = redisTemplate.opsForHash();
    }

    /**
     * 处理撮合
     */
    @Async
    public void matchContractOrder() {
        String symbol = "all";
        String redisKey = "r_contract_order_match:" + symbol;
        try {
            RedisLock redisLock = new RedisLock(redisTemplate, redisKey, 0, 2 * 60 * 1000);
            try {
                if (redisLock.lock()) {
                    /* 撮合
                     * 做空：价格少于等于当前价，委托成功
                     * 做多：价格大于等于当前价，委托成功
                     */
                    List<ContractOrder> contractOrders = contractOrderService.lambdaQuery().eq(ContractOrder::getStatus, ContractOrderStatusEnum.STATUS0.getCode()).list();
                    for (ContractOrder contractOrder : contractOrders) {
                        BigDecimal price = marketService.getLatestPrice(contractOrder.getSymbol());
                        if (price == null) {
                            continue;
                        }
                        if (ContractOrderTypeEnum.TYPE2.getCode().equals(contractOrder.getType())) {
                            if (contractOrder.getOrderPrice().compareTo(price) <= 0) {
                                try {
                                    contractOrderService.matchContractOrder(contractOrder.getOrderNo(), price);
                                } catch (Exception e) {
                                    logger.error("撮合委托订单异常,订单号：{}", contractOrder.getOrderNo(), e);
                                }
                            }
                        }
                        if (ContractOrderTypeEnum.TYPE1.getCode().equals(contractOrder.getType())) {
                            if (contractOrder.getOrderPrice().compareTo(price) >= 0) {
                                try {
                                    contractOrderService.matchContractOrder(contractOrder.getOrderNo(), price);
                                } catch (Exception e) {
                                    logger.error("撮合委托订单异常,订单号：{}", contractOrder.getOrderNo(), e);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("获取合约撮合锁异常", e);
            } finally {
                redisLock.unlock();
            }
        } catch (Exception e) {
            logger.error("处理合约撮合异常", e);
        }
    }

    /**
     * 计算合约盈亏
     */
    @Async
    public void calcContractOrderProfit(String symbol, BigDecimal c) {
        String redisKey = "r_contract_order_calc:" + symbol;
        try {
            RedisLock redisLock = new RedisLock(redisTemplate, redisKey, 0, 2 * 60 * 1000);
            try {
                if (redisLock.lock()) {
                    contractOrderService.calcContractOrderProfit(symbol, c);
                }
            } catch (Exception e) {
                logger.error("计算合约盈亏锁异常", e);
            } finally {
                redisLock.unlock();
            }
        } catch (Exception e) {
            logger.error("计算合约盈亏异常", e);
        }
    }

    /**
     * 为了能开启事务
     */
    @Transactional
    public void endContractOrder(String orderNo, BigDecimal c, Integer forcedFlag) {
        contractOrderService.endContractOrder(null, orderNo, c, forcedFlag);
    }
}
