package vip.xiaonuo.business.exchangerecord.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("tt_exchange_record")
public class ExchangeRecord extends BaseEntity {
    private Long playerId;
    private String orderNo;
    private String fromCurrencyCode;
    @ApiModelProperty("兑换数量")
    private BigDecimal fromQuantity;
    @ApiModelProperty("兑换币种")
    private String currencyCode;
    @ApiModelProperty("到账数量")
    private BigDecimal quantity;
    private BigDecimal price;
    @ApiModelProperty("手续费")
    private BigDecimal orderFee;
    private Date orderTime;
    @ApiModelProperty("兑换时间")
    private Date closeTime;
    //0:委托,1:成交
    @ApiModelProperty("0:委托,1:成交")
    private Integer status;
}
