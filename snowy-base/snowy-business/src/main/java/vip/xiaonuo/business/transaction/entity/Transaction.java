package vip.xiaonuo.business.transaction.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户充值，提现记录表
 *
 * <AUTHOR>
 * @date 2022-04-08 10:08:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_transaction")
public class Transaction extends BaseEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    private Long playerId;

    /**
     * 订单编号
     */
    private String orderNum;

    /**
     * 充值前余额
     */
    private BigDecimal rechargeFrontAccount;

    /**
     * 充值后余额
     */
    private BigDecimal rechargeAfterAccount;

    /**
     * 金额
     */
    private BigDecimal account;

    /**
     * 图片地址
     */
    private String pictureUrl;

    /**
     * 渠道
     */
    private String rechargeChannel;

    /**
     * 操作类型 1 充值 2 提现
     */
    private Integer operationType;

    /**
     * 状态
     * 0 待付款
     * 1 待审核
     * 2 成功
     * 3 失败
     * 4 处理中
     * 5 交易超时
     * 6 交易撤销
     */
    private Integer status;

    /**
     * 交易扩展信息
     */
    private String jsonInfo;

    /**
     * 审核人员
     */
    private Long chenckId;

    /**
     * 审核时间
     */
    @Excel(name = "审核时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /**
     * 操作用户
     */
    private Long operationId;

    /**
     * 操作时间
     */
    @Excel(name = "操作时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 类型(1:币币账户;2:合约账户;3:现货账户)
     */
    private Integer accountType;
    /**
     * 订单审核备注
     */
    private String remark;
    /**
     * 手续费
     */
    private BigDecimal chargeFree;
    /**
     * 手续费率
     */
    private BigDecimal chargeFreeRata;
    /**
     * 到账金额
     */
    private BigDecimal arrivalAccount;
    /**
     * 开户行
     */
    private String bankDeposit;
    /**
     * 提现银行名称
     */
    private String bankName;
    /**
     * 银行卡号
     */
    private String bankCard;
    /**
     * usdt金额
     */
    private BigDecimal usdtAccount;
    /**
     * 链名称
     */
    private String chainName;
    /**
     * 地址
     */
    private String currencyAddress;
    /**
     * 订单备注
     */
    private String remarks;
    //到账地址
    private String toAddress;
}
