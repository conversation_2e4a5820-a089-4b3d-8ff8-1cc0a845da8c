/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.contractorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.business.contractorder.entity.ContractOrder;
import vip.xiaonuo.business.contractorder.param.ContractOrderParam;
import vip.xiaonuo.business.contractorder.param.PlaceContractOrderModel;
import vip.xiaonuo.business.contractorder.param.QueryContractOrderModel;
import vip.xiaonuo.business.contractorder.param.SetStopProfitAndLossModel;
import vip.xiaonuo.business.contractorder.result.ContractOrderStatisticModel;
import vip.xiaonuo.business.contractorder.result.ContractOrderVo;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * 合约订单service接口
 *
 * <AUTHOR>
 * @date 2022-04-19 14:17:12
 */
public interface ContractOrderService extends IService<ContractOrder> {

    /**
     * 查询合约订单
     *
     * <AUTHOR>
     * @date 2022-04-19 14:17:12
     */
    PageResult<ContractOrder> page(ContractOrderParam contractOrderParam);

    PageResult<ContractOrderVo> page(Long playerId, QueryContractOrderModel model);

    /**
     * 合约订单列表
     *
     * <AUTHOR>
     * @date 2022-04-19 14:17:12
     */
    List<ContractOrder> list(ContractOrderParam contractOrderParam);

    /**
     * 添加合约订单
     *
     * <AUTHOR>
     * @date 2022-04-19 14:17:12
     */
    void add(ContractOrderParam contractOrderParam);

    /**
     * 删除合约订单
     *
     * <AUTHOR>
     * @date 2022-04-19 14:17:12
     */
    void delete(List<ContractOrderParam> contractOrderParamList);

    /**
     * 编辑合约订单
     *
     * <AUTHOR>
     * @date 2022-04-19 14:17:12
     */
    void edit(ContractOrderParam contractOrderParam);

    /**
     * 查看合约订单
     *
     * <AUTHOR>
     * @date 2022-04-19 14:17:12
     */
    ContractOrder detail(ContractOrderParam contractOrderParam);

    ContractOrder getContractOrderByNoAsLock(String orderNo);

    ContractOrder placeOrder(Long playerId, PlaceContractOrderModel model);

    void revokeEntrust(Long playerId, String orderNo, Long updateBy);

    void endContractOrder(Long playerId, String orderNo, BigDecimal price, Integer forcedFlag);

    /**
     * 撮合订单
     *
     * @param orderNo
     * @param c
     */
    void matchContractOrder(String orderNo, BigDecimal c);

    /**
     * 计算合约盈亏
     *
     * @param symbol
     * @param c
     */
    void calcContractOrderProfit(String symbol, BigDecimal c);

    ContractOrderStatisticModel findContactOrderStatistic(HomeStatisticParam param);

    void setStopProfitAndLoss(Long playerId, SetStopProfitAndLossModel model);

    ContractOrder getContractOrder(Long playerId, String orderNo);
}
