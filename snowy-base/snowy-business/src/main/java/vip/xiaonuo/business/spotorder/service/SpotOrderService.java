/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.spotorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.business.spotorder.entity.SpotOrder;
import vip.xiaonuo.business.spotorder.param.PlaceSpotOrderModel;
import vip.xiaonuo.business.spotorder.param.QuerySpotOrderModel;
import vip.xiaonuo.business.spotorder.param.SpotOrderParam;
import vip.xiaonuo.business.spotorder.result.EndSpotOrderResult;
import vip.xiaonuo.business.spotorder.result.SpotOrderStatisticModel;
import vip.xiaonuo.business.spotorder.result.SpotOrderVo;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.List;

/**
 * 现货订单service接口
 *
 * <AUTHOR>
 * @date 2022-04-14 18:27:46
 */
public interface SpotOrderService extends IService<SpotOrder> {

    /**
     * 查询现货订单
     *
     * <AUTHOR>
     * @date 2022-04-14 18:27:46
     */
    PageResult<SpotOrder> page(SpotOrderParam spotOrderParam);

    PageResult<SpotOrderVo> page(Long playerId, QuerySpotOrderModel model);

    /**
     * 现货订单列表
     *
     * <AUTHOR>
     * @date 2022-04-14 18:27:46
     */
    List<SpotOrder> list(SpotOrderParam spotOrderParam);

    /**
     * 添加现货订单
     *
     * <AUTHOR>
     * @date 2022-04-14 18:27:46
     */
    void add(SpotOrderParam spotOrderParam);

    /**
     * 删除现货订单
     *
     * <AUTHOR>
     * @date 2022-04-14 18:27:46
     */
    void delete(List<SpotOrderParam> spotOrderParamList);

    /**
     * 编辑现货订单
     *
     * <AUTHOR>
     * @date 2022-04-14 18:27:46
     */
    void edit(SpotOrderParam spotOrderParam);

    /**
     * 查看现货订单
     *
     * <AUTHOR>
     * @date 2022-04-14 18:27:46
     */
    SpotOrder detail(SpotOrderParam spotOrderParam);

    /**
     * 下单
     *
     * @param playerId
     * @param model
     */
    SpotOrder placeOrder(Long playerId, PlaceSpotOrderModel model);

    /**
     * 完成
     *
     * @param orderNo
     */
    SpotOrder finishOrder(String orderNo,SpotOrder spotOrder);

    SpotOrderStatisticModel findSpotOrderStatistic(HomeStatisticParam param);

    void editControl(Long id, Integer control);

    EndSpotOrderResult endSpotOrder(Long playerId, String orderNo);

    List<SpotOrder> getOverdueOrder();

    SpotOrder getSpotOrderForLock(String orderNo);
}
