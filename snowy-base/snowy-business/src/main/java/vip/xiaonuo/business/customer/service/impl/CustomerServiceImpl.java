/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import vip.xiaonuo.business.customer.entity.Customer;
import vip.xiaonuo.business.customer.enums.CustomerExceptionEnum;
import vip.xiaonuo.business.customer.enums.CustomerStatusEnum;
import vip.xiaonuo.business.customer.mapper.CustomerMapper;
import vip.xiaonuo.business.customer.param.CustomerParam;
import vip.xiaonuo.business.customer.service.CustomerService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;

/**
 * 客服管理service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-12 13:32:27
 */
@Service
public class CustomerServiceImpl extends ServiceImpl<CustomerMapper, Customer> implements CustomerService {

    @Override
    public PageResult<Customer> page(CustomerParam customerParam) {
        QueryWrapper<Customer> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(customerParam)) {

            // 根据标题 查询
            if (ObjectUtil.isNotEmpty(customerParam.getTitle())) {
                queryWrapper.lambda().like(Customer::getTitle, customerParam.getTitle());
            }
            // 根据状态（1-启用；禁用） 查询
            if (ObjectUtil.isNotEmpty(customerParam.getStatus())) {
                queryWrapper.lambda().eq(Customer::getStatus, customerParam.getStatus());
            }
            if (ObjectUtil.isNotEmpty(customerParam.getLanguageCode())) {
                queryWrapper.lambda().eq(Customer::getLanguageCode, customerParam.getLanguageCode());
            }
            if (ObjectUtil.isNotEmpty(customerParam.getOrgCode())) {
                queryWrapper.lambda().eq(Customer::getOrgCode, customerParam.getOrgCode());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<Customer> list(CustomerParam customerParam) {
        QueryWrapper<Customer> queryWrapper = new QueryWrapper<>();
        // 根据状态（1-启用；禁用） 查询
        if (ObjectUtil.isNotEmpty(customerParam.getStatus())) {
            queryWrapper.lambda().eq(Customer::getStatus, customerParam.getStatus());
        }
        if (ObjectUtil.isNotEmpty(customerParam.getOrgCode())) {
            queryWrapper.lambda().eq(Customer::getOrgCode, customerParam.getOrgCode());
        }
        if (ObjectUtil.isNotEmpty(customerParam.getLanguageCode())) {
            queryWrapper.lambda().eq(Customer::getLanguageCode, customerParam.getLanguageCode());
        }
        queryWrapper.last("limit 1");
        return this.list(queryWrapper);
    }

    @Override
    public void add(CustomerParam customerParam) {
        Customer customer = new Customer();
        BeanUtil.copyProperties(customerParam, customer);
        this.save(customer);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<CustomerParam> customerParamList) {
        customerParamList.forEach(customerParam -> {
        Customer customer = this.queryCustomer(customerParam);
            this.removeById(customer.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CustomerParam customerParam) {
        Customer customer = this.queryCustomer(customerParam);
        BeanUtil.copyProperties(customerParam, customer);
        this.updateById(customer);
    }

    @Override
    public Customer detail(CustomerParam customerParam) {
        return this.queryCustomer(customerParam);
    }

    @Override
    public void changeStatus(CustomerParam customerParam) {
        List<Customer> customerList = this.list();
        if(CollectionUtils.isEmpty(customerList)){
            return;
        }
        Customer cus = this.queryCustomer(customerParam);
        BeanUtil.copyProperties(customerParam, cus);
        this.updateById(cus);
    }

    /**
     * 获取客服管理
     *
     * <AUTHOR>
     * @date 2022-04-12 13:32:27
     */
    private Customer queryCustomer(CustomerParam customerParam) {
        Customer customer = this.getById(customerParam.getId());
        if (ObjectUtil.isNull(customer)) {
            throw new ServiceException(CustomerExceptionEnum.NOT_EXIST);
        }
        return customer;
    }
}
