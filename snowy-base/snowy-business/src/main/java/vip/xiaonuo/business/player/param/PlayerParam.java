/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.player.param;

import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;

/**
* 玩家参数类
 *
 * <AUTHOR>
 * @date 2022-04-04 14:31:45
*/
@Data
public class PlayerParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 账号
     */
//    @NotBlank(message = "账号不能为空，请检查account参数", groups = {add.class, edit.class})
    private String account;
    //代理人ID
    private Long agentId;
    private String orgCode;
    private String orgId;
    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空，请检查password参数", groups = {add.class})
    private String password;
    //资金密码
    private String fundsPassword;
    /**
     * 昵称
     */
//    @NotBlank(message = "昵称不能为空，请检查nickName参数", groups = {add.class, edit.class})
    private String nickName;

    /**
     * 状态:(1:正常,2:冻结)
     */
    @NotNull(message = "状态:(1:正常,2:冻结)不能为空，请检查status参数", groups = {add.class, edit.class})
    private Integer status;

    /**
     * 头像
     */
//    @NotNull(message = "头像不能为空，请检查avatar参数", groups = {add.class, edit.class})
    private Long avatar;

    /**
     * 邮箱
     */
//    @NotBlank(message = "邮箱不能为空，请检查email参数", groups = {add.class, edit.class})
    private String email;

    private String phoneCode;
    /**
     * 手机
     */
//    @NotBlank(message = "手机不能为空，请检查phone参数", groups = {add.class})
    private String phone;

    /**
     * 最后登陆IP
     */
//    @NotBlank(message = "最后登陆IP不能为空，请检查lastLoginIp参数", groups = {add.class, edit.class})
    private String lastLoginIp;

    /**
     * 最后登陆时间
     */
//    @NotNull(message = "最后登陆时间不能为空，请检查lastLoginTime参数", groups = {add.class, edit.class})
    private String lastLoginTime;

    /**
     * 邀请码
     */
//    @NotBlank(message = "邀请码不能为空，请检查invitationCode参数", groups = {add.class, edit.class})
    private String invitationCode;

    /**
     * 邀请人
     */
//    @NotNull(message = "邀请人不能为空，请检查invitedBy参数", groups = {add.class, edit.class})
    private Long invitedBy;
    /**
     * 注册IP
     */
    private String registerIp;
    //在线标志(0:不在线,1:在线)
    private Integer onlineFlag;
    private String role;

    //是否禁止交易(1-是；0-不是)
    private Integer isForbidTrade;

    private String agentName;

    private String remark;
}
