
package vip.xiaonuo.business.role.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import vip.xiaonuo.business.context.login.AdminLoginContext;
import vip.xiaonuo.business.org.service.SysOrgService;
import vip.xiaonuo.business.role.entity.SysRole;
import vip.xiaonuo.business.role.entity.SysRoleDataScope;
import vip.xiaonuo.business.role.enums.SysRoleDataScopeTypeEnum;
import vip.xiaonuo.business.role.enums.SysRoleExceptionEnum;
import vip.xiaonuo.business.role.mapper.SysRoleMapper;
import vip.xiaonuo.business.role.param.SysRoleParam;
import vip.xiaonuo.business.role.service.SysRoleDataScopeService;
import vip.xiaonuo.business.role.service.SysRoleMenuService;
import vip.xiaonuo.business.role.service.SysRoleService;
import vip.xiaonuo.business.user.entity.SysUser;
import vip.xiaonuo.business.user.service.SysUserRoleService;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.consts.SymbolConstant;
import vip.xiaonuo.core.context.login.LoginContextHolder;
import vip.xiaonuo.core.enums.AdminRoleTypeEnum;
import vip.xiaonuo.core.enums.CommonStatusEnum;
import vip.xiaonuo.core.exception.PermissionException;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.exception.enums.PermissionExceptionEnum;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 系统角色service接口实现类
 *
 * <AUTHOR>
 * @date 2020/3/13 15:55
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    @Resource
    private SysUserRoleService sysUserRoleService;

    @Resource
    private SysRoleMenuService sysRoleMenuService;

    @Resource
    private SysRoleDataScopeService sysRoleDataScopeService;

    @Resource
    private SysOrgService sysOrgService;

    @Override
    public List<Dict> getLoginRoles(Long userId) {
        List<Dict> dictList = CollectionUtil.newArrayList();
        //获取用户角色id集合
        List<Long> roleIdList = sysUserRoleService.getUserRoleIdList(userId);
        if (ObjectUtil.isNotEmpty(roleIdList)) {
            LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SysRole::getId, roleIdList).eq(SysRole::getStatus, CommonStatusEnum.ENABLE.getCode());
            //根据角色id集合查询并返回结果
            dictList = this.list(queryWrapper).stream().map(sysRole -> {
                Dict dict = Dict.create();
                dict.put(CommonConstant.ID, sysRole.getId());
                dict.put(CommonConstant.CODE, sysRole.getCode());
                dict.put(CommonConstant.NAME, sysRole.getName());
                return dict;
            }).collect(Collectors.toList());
        }
        return dictList;
    }

    @Override
    public PageResult<SysRole> page(SysRoleParam sysRoleParam) {
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(sysRoleParam)) {
            //根据名称模糊查询
            if (ObjectUtil.isNotEmpty(sysRoleParam.getName())) {
                queryWrapper.lambda().like(SysRole::getName, sysRoleParam.getName());
            }
            //根据编码模糊查询
            if (ObjectUtil.isNotEmpty(sysRoleParam.getCode())) {
                queryWrapper.lambda().like(SysRole::getCode, sysRoleParam.getCode());
            }
            //排序
            if (ObjectUtil.isAllNotEmpty(sysRoleParam.getSortBy(), sysRoleParam.getOrderBy())) {
                queryWrapper.orderBy(true, sysRoleParam.getOrderBy().equals(CommonConstant.ASC), StrUtil.toUnderlineCase(sysRoleParam.getSortBy()));
            } else {
                //根据排序升序排列，序号越小越在前
                queryWrapper.lambda().orderByAsc(SysRole::getSort);
            }

            boolean superAdmin = LoginContextHolder.me(AdminLoginContext.class).isSuperAdmin();
            AdminRoleTypeEnum roleTypeEnum = LoginContextHolder.me(AdminLoginContext.class).getLoginUserRoleType();
            if (!superAdmin && roleTypeEnum != AdminRoleTypeEnum.ADMIN) {
                Long loginUserOrgId = LoginContextHolder.me(AdminLoginContext.class).getSysLoginUserOrgId();
                SysRoleParam roleParam = new SysRoleParam();
                roleParam.setGrantOrgIdList(Arrays.asList(loginUserOrgId));
                List<Long> roleIdList = sysRoleDataScopeService.getDataScopeRole(roleParam);
                if (!CollectionUtils.isEmpty(roleIdList)) {
                    queryWrapper.lambda().in(SysRole::getId, roleIdList);
                }
            }
        }
        queryWrapper.lambda().eq(SysRole::getStatus, CommonStatusEnum.ENABLE.getCode());
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<Dict> list(SysRoleParam sysRoleParam) {
        List<Dict> dictList = CollectionUtil.newArrayList();
        LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotNull(sysRoleParam)) {
            //根据角色名称或编码模糊查询
            if (ObjectUtil.isNotEmpty(sysRoleParam.getName())) {
                queryWrapper.and(i -> i.like(SysRole::getName, sysRoleParam.getName())
                        .or().like(SysRole::getCode, sysRoleParam.getName()));
            }
        }
        //只查询正常状态
        queryWrapper.eq(SysRole::getStatus, CommonStatusEnum.ENABLE.getCode());
        //根据排序升序排列，序号越小越在前
        queryWrapper.orderByAsc(SysRole::getSort);
        this.list(queryWrapper).forEach(sysRole -> {
            Dict dict = Dict.create();
            dict.put(CommonConstant.ID, sysRole.getId());
            dict.put(CommonConstant.NAME, sysRole.getName() + SymbolConstant.LEFT_SQUARE_BRACKETS
                    + sysRole.getCode() + SymbolConstant.RIGHT_SQUARE_BRACKETS);
            dictList.add(dict);
        });
        return dictList;
    }

    @Override
    public List<Dict> dropDown(SysUser loginSysUser) {
        List<Dict> dictList = CollectionUtil.newArrayList();
        LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
        //如果当前登录用户不是超级管理员，则查询自己拥有的
        boolean skip = !loginSysUser.getAdminAuthType().equals(1) && !LoginContextHolder.me(AdminLoginContext.class).isSuperAdmin();
        if (skip) {
            //查询自己拥有的
            List<String> loginUserRoleIds = LoginContextHolder.me(AdminLoginContext.class).getLoginUserRoleIds();
            if (ObjectUtil.isEmpty(loginUserRoleIds)) {
                return dictList;
            }
            queryWrapper.in(SysRole::getId, loginUserRoleIds);
        }
        //只查询正常状态
        queryWrapper.eq(SysRole::getStatus, CommonStatusEnum.ENABLE.getCode());
        this.list(queryWrapper)
                .forEach(sysRole -> {
                    Dict dict = Dict.create();
                    dict.put(CommonConstant.ID, sysRole.getId());
                    dict.put(CommonConstant.CODE, sysRole.getCode());
                    dict.put(CommonConstant.NAME, sysRole.getName());
                    dictList.add(dict);
                });
        return dictList;
    }

    @Override
    @Transactional
    public void add(SysRoleParam sysRoleParam) {
        //校验参数，检查是否存在相同的名称和编码
        checkParam(sysRoleParam, false);
        SysRole sysRole = new SysRole();
        BeanUtil.copyProperties(sysRoleParam, sysRole);
        sysRole.setStatus(CommonStatusEnum.ENABLE.getCode());
        this.save(sysRole);

        if (!LoginContextHolder.me(AdminLoginContext.class).isSuperAdmin()) {
            sysRole.setDataScopeType(SysRoleDataScopeTypeEnum.DEFINE.getCode());
            SysRoleDataScope sysRoleData = new SysRoleDataScope();
            sysRoleData.setRoleId(sysRole.getId());
            sysRoleData.setOrgId(LoginContextHolder.me(AdminLoginContext.class).getSysLoginUserOrgId());
            sysRoleDataScopeService.save(sysRoleData);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<SysRoleParam> sysRoleParamList) {
        sysRoleParamList.forEach(sysRoleParam -> {
            SysRole sysRole = this.querySysRole(sysRoleParam);
            sysRole.setStatus(CommonStatusEnum.DELETED.getCode());
            this.updateById(sysRole);
            Long id = sysRole.getId();
            //级联删除该角色对应的角色-数据范围关联信息
            sysRoleDataScopeService.deleteRoleDataScopeListByRoleId(id);

            //级联删除该角色对应的用户-角色表关联信息
            sysUserRoleService.deleteUserRoleListByRoleId(id);

            //级联删除该角色对应的角色-菜单表关联信息
            sysRoleMenuService.deleteRoleMenuListByRoleId(id);
        });
    }

    @Override
    public void edit(SysRoleParam sysRoleParam) {
        SysRole sysRole = this.querySysRole(sysRoleParam);
        //校验参数，检查是否存在相同的名称和编码
        checkParam(sysRoleParam, true);
        BeanUtil.copyProperties(sysRoleParam, sysRole);
        //不能修改状态，用修改状态接口修改状态
        sysRole.setStatus(null);
        this.updateById(sysRole);
    }

    @Override
    public SysRole detail(SysRoleParam sysRoleParam) {
        return this.querySysRole(sysRoleParam);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void grantMenu(SysRoleParam sysRoleParam) {
        this.querySysRole(sysRoleParam);
        sysRoleMenuService.grantMenu(sysRoleParam);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void grantData(SysRoleParam sysRoleParam) {
        SysRole sysRole = this.querySysRole(sysRoleParam);
        boolean superAdmin = LoginContextHolder.me(AdminLoginContext.class).isSuperAdmin();
        //如果登录用户不是超级管理员，则进行数据权限校验
        if (!superAdmin) {
            Integer dataScopeType = sysRoleParam.getDataScopeType();
            //如果授权的角色的数据范围类型为全部，则没权限，只有超级管理员有
            if (SysRoleDataScopeTypeEnum.ALL.getCode().equals(dataScopeType)) {
                throw new PermissionException(PermissionExceptionEnum.NO_PERMISSION_OPERATE);
            }
            //如果授权的角色数据范围类型为自定义，则要判断授权的数据范围是否在自己的数据范围内
            if (SysRoleDataScopeTypeEnum.DEFINE.getCode().equals(dataScopeType)) {
                List<Long> dataScope = sysRoleParam.getDataScope();
                //要授权的数据范围列表
                List<Long> grantOrgIdList = sysRoleParam.getGrantOrgIdList();
                if (ObjectUtil.isNotEmpty(grantOrgIdList)) {
                    //数据范围为空
                    if (ObjectUtil.isEmpty(dataScope)) {
                        throw new PermissionException(PermissionExceptionEnum.NO_PERMISSION_OPERATE);
                    } else if (!dataScope.containsAll(grantOrgIdList)) {
                        //所要授权的数据不在自己的数据范围内
                        throw new PermissionException(PermissionExceptionEnum.NO_PERMISSION_OPERATE);
                    }
                }
            }
        }
        sysRole.setDataScopeType(sysRoleParam.getDataScopeType());
        this.updateById(sysRole);
        sysRoleDataScopeService.grantDataScope(sysRoleParam);
    }

    @Override
    public List<Long> getUserDataScopeIdList(List<Long> roleIdList, Long orgId) {
        Set<Long> resultList = CollectionUtil.newHashSet();

        //定义角色中最大数据范围的类型，目前系统按最大范围策略来，如果你同时拥有ALL和SELF的权限，系统最后按ALL返回
        Integer strongerDataScopeType = SysRoleDataScopeTypeEnum.SELF.getCode();

        //获取用户自定义数据范围的角色集合
        List<Long> customDataScopeRoleIdList = CollectionUtil.newArrayList();
        if (ObjectUtil.isNotEmpty(roleIdList)) {
            List<SysRole> sysRoleList = this.listByIds(roleIdList);
            for (SysRole sysRole : sysRoleList) {
                if (SysRoleDataScopeTypeEnum.DEFINE.getCode().equals(sysRole.getDataScopeType())) {
                    customDataScopeRoleIdList.add(sysRole.getId());
                } else {
                    if (sysRole.getDataScopeType() <= strongerDataScopeType) {
                        strongerDataScopeType = sysRole.getDataScopeType();
                    }
                }
            }
        }

        //自定义数据范围的角色对应的数据范围
        List<Long> roleDataScopeIdList = sysRoleDataScopeService.getRoleDataScopeIdList(customDataScopeRoleIdList);

        //角色中拥有最大数据范围类型的数据范围
        List<Long> dataScopeIdList = sysOrgService.getDataScopeListByDataScopeType(strongerDataScopeType, orgId);

        resultList.addAll(dataScopeIdList);
        resultList.addAll(roleDataScopeIdList);
        return CollectionUtil.newArrayList(resultList);
    }

    @Override
    public String getNameByRoleId(Long roleId) {
        SysRole sysRole = this.getById(roleId);
        if (ObjectUtil.isEmpty(sysRole)) {
            throw new ServiceException(SysRoleExceptionEnum.ROLE_NOT_EXIST);
        }
        return sysRole.getName();
    }

    @Override
    public List<Long> ownMenu(SysRoleParam sysRoleParam) {
        SysRole sysRole = this.querySysRole(sysRoleParam);
        return sysRoleMenuService.getRoleMenuIdList(CollectionUtil.newArrayList(sysRole.getId()));
    }

    @Override
    public List<Long> ownData(SysRoleParam sysRoleParam) {
        SysRole sysRole = this.querySysRole(sysRoleParam);
        return sysRoleDataScopeService.getRoleDataScopeIdList(CollectionUtil.newArrayList(sysRole.getId()));
    }

    /**
     * 校验参数，检查是否存在相同的名称和编码
     *
     * <AUTHOR>
     * @date 2020/3/28 14:59
     */
    private void checkParam(SysRoleParam sysRoleParam, boolean isExcludeSelf) {
        Long id = sysRoleParam.getId();
        String name = sysRoleParam.getName();
        String code = sysRoleParam.getCode();

        LambdaQueryWrapper<SysRole> queryWrapperByName = new LambdaQueryWrapper<>();
        queryWrapperByName.eq(SysRole::getName, name)
                .ne(SysRole::getStatus, CommonStatusEnum.DELETED.getCode());

        LambdaQueryWrapper<SysRole> queryWrapperByCode = new LambdaQueryWrapper<>();
        queryWrapperByCode.eq(SysRole::getCode, code)
                .ne(SysRole::getStatus, CommonStatusEnum.DELETED.getCode());

        //是否排除自己，如果排除自己则不查询自己的id
        if (isExcludeSelf) {
            queryWrapperByName.ne(SysRole::getId, id);
            queryWrapperByCode.ne(SysRole::getId, id);
        }
        int countByName = this.count(queryWrapperByName);
        int countByCode = this.count(queryWrapperByCode);

        if (countByName >= 1) {
            throw new ServiceException(SysRoleExceptionEnum.ROLE_NAME_REPEAT);
        }
        if (countByCode >= 1) {
            throw new ServiceException(SysRoleExceptionEnum.ROLE_CODE_REPEAT);
        }
    }

    /**
     * 获取系统角色
     *
     * <AUTHOR>
     * @date 2020/3/28 14:59
     */
    private SysRole querySysRole(SysRoleParam sysRoleParam) {
        SysRole sysRole = this.getById(sysRoleParam.getId());
        if (ObjectUtil.isNull(sysRole)) {
            throw new ServiceException(SysRoleExceptionEnum.ROLE_NOT_EXIST);
        }
        return sysRole;
    }
}
