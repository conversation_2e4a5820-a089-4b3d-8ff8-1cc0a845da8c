/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.aiminer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.log.Log;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.aiminer.entity.AiMiner;
import vip.xiaonuo.business.aiminer.enums.AiMinerExceptionEnum;
import vip.xiaonuo.business.aiminer.mapper.AiMinerMapper;
import vip.xiaonuo.business.aiminer.param.AiMinerParam;
import vip.xiaonuo.business.aiminer.service.AiMinerService;
import vip.xiaonuo.business.aiminingtransaction.service.AiMiningTransactionService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * AI矿机基础表service接口实现类
 *
 * <AUTHOR>
 * @date 2025-08-04 11:32:53
 */
@Service
public class AiMinerServiceImpl extends ServiceImpl<AiMinerMapper, AiMiner> implements AiMinerService {

    private static final Log log = Log.get();

    @Resource
    private AiMiningTransactionService aiMiningTransactionService;

    @Override
    public PageResult<AiMiner> page(AiMinerParam aiMinerParam) {
        QueryWrapper<AiMiner> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(aiMinerParam)) {

            // 根据玩家id 查询
            if (ObjectUtil.isNotEmpty(aiMinerParam.getPlayerId())) {
                queryWrapper.lambda().eq(AiMiner::getPlayerId, aiMinerParam.getPlayerId());
            }
            // 根据状态(1=待激活,2=运行中,3=已停止) 查询
            if (ObjectUtil.isNotEmpty(aiMinerParam.getStatus())) {
                queryWrapper.lambda().eq(AiMiner::getStatus, aiMinerParam.getStatus());
            }
            // 根据最近启动时间 查询
            if (ObjectUtil.isNotEmpty(aiMinerParam.getLastStartTime())) {
                queryWrapper.lambda().eq(AiMiner::getLastStartTime, aiMinerParam.getLastStartTime());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AiMiner> list(AiMinerParam aiMinerParam) {
        return this.list();
    }

    @Override
    public void add(AiMinerParam aiMinerParam) {
        AiMiner aiMiner = new AiMiner();
        BeanUtil.copyProperties(aiMinerParam, aiMiner);
        this.save(aiMiner);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<AiMinerParam> aiMinerParamList) {
        aiMinerParamList.forEach(aiMinerParam -> {
            AiMiner aiMiner = this.queryAiMiner(aiMinerParam);
            this.removeById(aiMiner.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AiMinerParam aiMinerParam) {
        AiMiner aiMiner = this.queryAiMiner(aiMinerParam);
        BeanUtil.copyProperties(aiMinerParam, aiMiner);
        this.updateById(aiMiner);
    }

    @Override
    public AiMiner detail(AiMinerParam aiMinerParam) {
        return this.queryAiMiner(aiMinerParam);
    }

    @Override
    public void initAiMinerForNewPlayer(Long playerId) {
        // 检查是否已存在AI挖矿账户
        AiMiner existingMiner = this.lambdaQuery()
                .eq(AiMiner::getPlayerId, playerId)
                .one();

        if (existingMiner != null) {
            return;
        }

        // 创建AI挖矿账户
        AiMiner aiMiner = new AiMiner();
        aiMiner.setPlayerId(playerId);
        aiMiner.setStatus(1); // 1=待激活
        aiMiner.setLastStartTime(null); // 初始化时未启动
        aiMiner.setCreateTime(new Date());

        this.save(aiMiner);
    }

    /**
     * 获取AI矿机基础表
     *
     * <AUTHOR>
     * @date 2025-08-04 11:32:53
     */
    private AiMiner queryAiMiner(AiMinerParam aiMinerParam) {
        AiMiner aiMiner = this.getById(aiMinerParam.getId());
        if (ObjectUtil.isNull(aiMiner)) {
            throw new ServiceException(AiMinerExceptionEnum.NOT_EXIST);
        }
        return aiMiner;
    }
}
