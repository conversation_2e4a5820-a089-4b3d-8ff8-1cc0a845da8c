<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.business.user.mapper.SysUserMapper">

    <resultMap id="sysUserResult" type="vip.xiaonuo.business.user.result.SysUserResult">
        <id column="id" property="id" />
        <result column="account" property="account" />
        <result column="nick_name" property="nickName" />
        <result column="name" property="name" />
        <result column="avatar" property="avatar" />
        <result column="birthday" property="birthday" />
        <result column="sex" property="sex" />
        <result column="email" property="email" />
        <result column="phone" property="phone" />
        <result column="tel" property="tel" />
        <result column="status" property="status" />
        <result column="invited_code" property="invitedCode" />
        <result column="promotion_link" property="promotionLink" />
        <result column="promotion_link_h5" property="promotionLinkH5" />
        <result column="superior_id" property="superiorId" />
        <result column="admin_auth_type" property="adminAuthType" />
        <association property="sysEmpInfo" javaType="vip.xiaonuo.business.emp.result.SysEmpInfo">
            <result column="job_num" property="jobNum" />
            <result column="org_id" property="orgId" />
            <result column="org_name" property="orgName" />
            <result column="org_code" property="orgCode" />
        </association>
    </resultMap>

    <!--获取用户分页列表-->
    <select id="page" resultMap="sysUserResult">
        select sys_user.*,
               sys_emp.job_num,
               sys_emp.org_id,
               sys_emp.org_name,
               sys_org.code as org_code
        from sys_user left join sys_emp on sys_user.id = sys_emp.id left join sys_org on sys_emp.org_id = sys_org.id
        ${ew.customSqlSegment}
    </select>

    <select id="findAgentStatistic" resultType="java.math.BigDecimal">
        SELECT COUNT(1) FROM sys_user WHERE admin_auth_type= 3
        <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
            and superior_id =  #{ew.loginUserId}
        </if>
    </select>
</mapper>
