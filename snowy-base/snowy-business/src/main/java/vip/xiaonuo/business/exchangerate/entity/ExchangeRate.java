package vip.xiaonuo.business.exchangerate.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.*;
import java.math.BigDecimal;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 汇率（同步）
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_exchange_rate")
public class ExchangeRate extends BaseEntity {

    /**
     * 汇率名称
     */
    private String exchangeRateName;

    /**
     * 汇率
     */
    private BigDecimal exchangeRate;

    /**
     * 同步时间
     */
    @Excel(name = "同步时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exchangeRateSyncTime;

}
