<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.business.exchangerate.mapper.ExchangeRateMapper">

    <delete id="delectExchangeRateToDay">
        DELETE FROM t_exchange_rate WHERE DATE(#{date}) = DATE(create_time)
    </delete>

    <select id="findExchangeRateToDay" resultType="vip.xiaonuo.business.exchangerate.entity.ExchangeRate">
          select
            id,
            exchange_rate_name exchangeRateName,
            exchange_rate exchangeRate,
            exchange_rate_sync_time exchangeRateSyncTime
           FROM t_exchange_rate WHERE exchange_rate_name = #{exchangeRateName} order by exchange_rate_sync_time desc limit 1
    </select>

    <select id="findExchangeRateToDayList" resultType="vip.xiaonuo.business.exchangerate.entity.ExchangeRate">
          select
            id,
            exchange_rate_name exchangeRateName,
            exchange_rate exchangeRate,
            exchange_rate_sync_time exchangeRateSyncTime
           FROM t_exchange_rate WHERE  DATE(#{date}) = DATE(create_time) and exchange_rate_name in
           <foreach collection="list" index="index" item="value" open="(" close=")" separator=",">
           #{value}
           </foreach>
    </select>

    <select id="findExchangeRateToBeforeDayList" resultType="vip.xiaonuo.business.exchangerate.entity.ExchangeRate">
          select
            id,
            exchange_rate_name exchangeRateName,
            exchange_rate exchangeRate,
            exchange_rate_sync_time exchangeRateSyncTime
           FROM t_exchange_rate WHERE DATE(create_time) = date_sub(curdate(),interval 1 day) and exchange_rate_name in
            <foreach collection="list" index="index" item="value" open="(" close=")" separator=",">
                #{value}
            </foreach>
    </select>
</mapper>
