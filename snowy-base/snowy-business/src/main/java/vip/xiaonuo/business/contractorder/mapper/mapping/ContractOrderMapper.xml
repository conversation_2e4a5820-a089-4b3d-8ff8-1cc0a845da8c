<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.business.contractorder.mapper.ContractOrderMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="vip.xiaonuo.business.contractorder.entity.ContractOrder">
        <id column="id" property="id" />
        <result column="player_id" property="playerId" />
        <result column="order_no" property="orderNo" />
        <result column="type" property="type" />
        <result column="symbol" property="symbol" />
        <result column="entrust_type" property="entrustType" />
        <result column="lever" property="lever" />
        <result column="order_time" property="orderTime" />
        <result column="order_price" property="orderPrice" />
        <result column="quantity" property="quantity" />
        <result column="earnest_money" property="earnestMoney" />
        <result column="close_price" property="closePrice" />
        <result column="close_time" property="closeTime" />
        <result column="order_fee" property="orderFee" />
        <result column="profit" property="profit" />
        <result column="forced_flag" property="forcedFlag" />
        <result column="status" property="status" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="playerAccount" property="playerAccount" />
    </resultMap>

    <select id="queryPage" resultMap="BaseResultMap">
        SELECT
        so.*,
        p.account playerAccount
        FROM
        tt_contract_order so
        LEFT JOIN tt_player p ON p.id = so.player_id
        <where>
            <if test="ew.playerAccount != null and ew.playerAccount != ''">
                and p.account = #{ew.playerAccount}
            </if>
            <if test="ew.orderNo != null and ew.orderNo != ''">
                and so.order_no = #{ew.orderNo}
            </if>
            <if test="ew.type != null">
                and so.type = #{ew.type}
            </if>
            <if test="ew.status != null">
                and so.status = #{ew.status}
            </if>
            <if test="ew.entrustType != null">
                and so.entrust_type = #{ew.entrustType}
            </if>
            <if test="ew.forcedFlag != null">
                and so.forced_flag = #{ew.forcedFlag}
            </if>
            <if test="ew.symbol != null and ew.symbol != ''">
                and so.symbol = #{ew.symbol}
            </if>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
                and p.agent_id in
                <foreach item="item" index="index" collection="ew.agentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
                and p.agent_id = #{ew.loginUserId}
            </if>
        </where>
        order by so.create_time desc
    </select>

    <select id="findContactOrderStatistic" resultType="vip.xiaonuo.business.contractorder.result.ContractOrderStatisticModel">
        SELECT
            SUM(IFNULL(so.earnest_money, 0 )) contactOrderTotalAmount,
            SUM( CASE WHEN DATE ( so.create_time ) = #{ew.date} THEN IFNULL(so.earnest_money, 0 ) ELSE 0 END ) contactOrderDayAmount
        FROM
            tt_contract_order so
            LEFT JOIN tt_player p ON p.id = so.player_id
        <where>
            so.status = 2
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
                and p.org_code = #{ew.loginUserOrgCode}
            </if>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
                and p.agent_id = #{ew.loginUserId}
            </if>
        </where>
    </select>
</mapper>
