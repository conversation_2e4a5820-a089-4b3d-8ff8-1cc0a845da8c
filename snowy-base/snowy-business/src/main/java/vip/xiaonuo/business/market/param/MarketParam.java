/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.market.param;

import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.math.BigDecimal;

/**
* 市场参数类
 *
 * <AUTHOR>
 * @date 2022-04-11 10:55:42
*/
@Data
public class MarketParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 交易币种编码
     */
    @NotBlank(message = "交易币种编码不能为空，请检查currencyCode参数", groups = {add.class, edit.class})
    private String currencyCode;

    /**
     * 基础币种编码
     */
    @NotBlank(message = "基础币种编码不能为空，请检查baseCurrencyCode参数", groups = {add.class, edit.class})
    private String baseCurrencyCode;

    /**
     * 交易类型表示,如ETH_BTC
     */
    @NotBlank(message = "交易类型表示,如ETH_BTC不能为空，请检查symbol参数", groups = {add.class, edit.class})
    private String symbol;

    /**
     * 币种展现名称,如ETH/BTC
     */
    @NotBlank(message = "币种展现名称,如ETH/BTC不能为空，请检查name参数", groups = {add.class, edit.class})
    private String name;

    /**
     * 0:下线,1:上线
     */
    @NotNull(message = "0:下线,1:上线不能为空，请检查status参数", groups = {add.class, edit.class})
    private Integer status;

    /**
     * 热门市场排序，越小排的越靠前
     */
    @NotNull(message = "热门市场排序，越小排的越靠前不能为空，请检查sort参数", groups = {add.class, edit.class})
    private Integer sort;

    /**
     * 发行价
     */
    @NotNull(message = "发行价不能为空，请检查issuePrice参数", groups = {add.class, edit.class})
    private BigDecimal issuePrice;

    /**
     * 价格的小数位
     */
    @NotNull(message = "价格的小数位不能为空，请检查priceDecimal参数", groups = {add.class, edit.class})
    private Integer priceDecimal;

    /**
     * 数量的小数位
     */
    @NotNull(message = "数量的小数位不能为空，请检查quantityDecimal参数", groups = {add.class, edit.class})
    private Integer quantityDecimal;

    /**
     * 深度小数位
     */
    @NotNull(message = "深度小数位不能为空，请检查minPriceGroupDecimal参数", groups = {add.class, edit.class})
    private Integer minPriceGroupDecimal;
    //交易开关：0:关 1:开
    private Integer transactionSwitch;
}
