package vip.xiaonuo.business.upay.enums;

public enum MoneyTypeEnums {
    USD("1","美元"),
    CNY("2","人民币"),
    INR("3","印度卢比"),
    JPY("4","日元"),
    KRW("5","韩元"),
    PHP("6","菲律宾比索"),
    EUR("7","欧元"),
    GBP("8","英镑"),
    CHF("9","瑞士法郎"),
    TWD("10","新台币"),
    HKD("11","港币"),
    MOP("12","澳门元"),
    SGD("13","新加坡币"),
    NZD("14","新西兰元"),
    THB("15","泰铢"),
    CAD("16","加拿大元");
    private final String code;
    private final String remark;
    MoneyTypeEnums(String code,String remark){
        this.code = code;
        this.remark = remark;
    }
    public String getCode(){
        return this.code;
    }
}
