/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.aiaccount.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.aiaccount.entity.AiAccount;
import vip.xiaonuo.business.aiaccount.enums.AiAccountExceptionEnum;
import vip.xiaonuo.business.aiaccount.mapper.AiAccountMapper;
import vip.xiaonuo.business.aiaccount.param.AiAccountParam;
import vip.xiaonuo.business.aiaccount.service.AiAccountService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.List;

/**
 * AI银行账户表service接口实现类
 *
 * <AUTHOR>
 * @date 2025-08-04 11:32:42
 */
@Service
public class AiAccountServiceImpl extends ServiceImpl<AiAccountMapper, AiAccount> implements AiAccountService {

    @Override
    public PageResult<AiAccount> page(AiAccountParam aiAccountParam) {
        QueryWrapper<AiAccount> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(aiAccountParam)) {

            // 根据玩家id 查询
            if (ObjectUtil.isNotEmpty(aiAccountParam.getPlayerId())) {
                queryWrapper.lambda().eq(AiAccount::getPlayerId, aiAccountParam.getPlayerId());
            }
            // 根据币种，如USDT 查询
            if (ObjectUtil.isNotEmpty(aiAccountParam.getSymbol())) {
                queryWrapper.lambda().eq(AiAccount::getSymbol, aiAccountParam.getSymbol());
            }
            // 根据当前余额 查询
            if (ObjectUtil.isNotEmpty(aiAccountParam.getBalance())) {
                queryWrapper.lambda().eq(AiAccount::getBalance, aiAccountParam.getBalance());
            }
            // 根据累计收益 查询
            if (ObjectUtil.isNotEmpty(aiAccountParam.getTotalIncome())) {
                queryWrapper.lambda().eq(AiAccount::getTotalIncome, aiAccountParam.getTotalIncome());
            }
            // 根据状态（1=启用，0=禁用） 查询
            if (ObjectUtil.isNotEmpty(aiAccountParam.getStatus())) {
                queryWrapper.lambda().eq(AiAccount::getStatus, aiAccountParam.getStatus());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AiAccount> list(AiAccountParam aiAccountParam) {
        return this.list();
    }

    @Override
    public void add(AiAccountParam aiAccountParam) {
        AiAccount aiAccount = new AiAccount();
        BeanUtil.copyProperties(aiAccountParam, aiAccount);
        this.save(aiAccount);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<AiAccountParam> aiAccountParamList) {
        aiAccountParamList.forEach(aiAccountParam -> {
            AiAccount aiAccount = this.queryAiAccount(aiAccountParam);
            this.removeById(aiAccount.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AiAccountParam aiAccountParam) {
        AiAccount aiAccount = this.queryAiAccount(aiAccountParam);
        BeanUtil.copyProperties(aiAccountParam, aiAccount);
        this.updateById(aiAccount);
    }

    @Override
    public AiAccount detail(AiAccountParam aiAccountParam) {
        return this.queryAiAccount(aiAccountParam);
    }

    /**
     * 获取AI银行账户表
     *
     * <AUTHOR>
     * @date 2025-08-04 11:32:42
     */
    private AiAccount queryAiAccount(AiAccountParam aiAccountParam) {
        AiAccount aiAccount = this.getById(aiAccountParam.getId());
        if (ObjectUtil.isNull(aiAccount)) {
            throw new ServiceException(AiAccountExceptionEnum.NOT_EXIST);
        }
        return aiAccount;
    }
}
