/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.player.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.param.PlayerParam;
import vip.xiaonuo.business.player.pojo.dto.FindFollowerDto;
import vip.xiaonuo.business.player.pojo.vo.FindFollowerVo;
import vip.xiaonuo.business.player.result.AgentName;
import vip.xiaonuo.business.player.result.AgentOrgInfo;
import vip.xiaonuo.business.player.result.PlayerResult;
import vip.xiaonuo.business.player.result.PlayerStatisticModel;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.business.transaction.pojo.dto.FindUserTransactionDto;
import vip.xiaonuo.business.transaction.pojo.vo.FindUserTransactionVo;

import java.util.List;

/**
 * 玩家
 *
 * <AUTHOR>
 * @date 2022-04-04 14:31:45
 */
public interface PlayerMapper extends BaseMapper<Player> {

    Page<PlayerResult> pageResult(IPage<PlayerResult> page, @Param(Constants.WRAPPER) PlayerParam playerParam);

    AgentOrgInfo getAgentByInviteCode(@Param("orgCode") String orgCode, @Param("inviteCode") String inviteCode);

    List<String> lockPlayer(@Param("playerIds") List<Long> playerIds);

    List<AgentName> queryAgentName(@Param("agentIds") List<Long> agentIds);

    /**
     * 查询注册用户（时间为null 查询总注册数）
     *
     * @param date
     * @return
     */
    PlayerStatisticModel findUserStatistic(@Param("ew") HomeStatisticParam param);


    /**
     * 分页查询用户交易记录
     *
     * @param dto
     * @return
     */
    Page<FindFollowerVo> findUserFollower(Page<FindFollowerVo> defaultPage, @Param("ew") FindFollowerDto dto);
}
