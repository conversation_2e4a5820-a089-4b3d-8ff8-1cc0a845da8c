/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.ipinfo.param;

import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;

/**
* IP信息参数类
 *
 * <AUTHOR>
 * @date 2022-04-07 18:58:26
*/
@Data
public class IpInfoParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * IP
     */
    @NotBlank(message = "IP不能为空，请检查ip参数", groups = {add.class, edit.class})
    private String ip;

    /**
     * 国家代码
     */
    @NotBlank(message = "国家代码不能为空，请检查countryCode参数", groups = {add.class, edit.class})
    private String countryCode;

    /**
     * 区域代码
     */
    @NotBlank(message = "区域代码不能为空，请检查region参数", groups = {add.class, edit.class})
    private String region;

    /**
     * 区域名称
     */
    @NotBlank(message = "区域名称不能为空，请检查regionName参数", groups = {add.class, edit.class})
    private String regionName;

    /**
     * 城市
     */
    @NotBlank(message = "城市不能为空，请检查city参数", groups = {add.class, edit.class})
    private String city;

    /**
     * 机构
     */
    @NotBlank(message = "机构不能为空，请检查org参数", groups = {add.class, edit.class})
    private String org;

    /**
     * 状态(0:待更新,1:已更新,2:更新失败)
     */
    @NotNull(message = "状态(0:待更新,1:已更新,2:更新失败)不能为空，请检查status参数", groups = {add.class, edit.class})
    private Integer status;

}
