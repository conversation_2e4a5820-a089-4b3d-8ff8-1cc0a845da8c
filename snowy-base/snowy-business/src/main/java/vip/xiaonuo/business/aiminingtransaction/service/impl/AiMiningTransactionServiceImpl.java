/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.aiminingtransaction.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.enums.CommonStatusEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.business.aiminingtransaction.entity.AiMiningTransaction;
import vip.xiaonuo.business.aiminingtransaction.enums.AiMiningTransactionExceptionEnum;
import vip.xiaonuo.business.aiminingtransaction.mapper.AiMiningTransactionMapper;
import vip.xiaonuo.business.aiminingtransaction.param.AiMiningTransactionParam;
import vip.xiaonuo.business.aiminingtransaction.service.AiMiningTransactionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;

/**
 * AI矿机操作记录表service接口实现类
 *
 * <AUTHOR>
 * @date 2025-08-04 11:32:53
 */
@Service
public class AiMiningTransactionServiceImpl extends ServiceImpl<AiMiningTransactionMapper, AiMiningTransaction> implements AiMiningTransactionService {

    @Override
    public PageResult<AiMiningTransaction> page(AiMiningTransactionParam aiMiningTransactionParam) {
        QueryWrapper<AiMiningTransaction> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(aiMiningTransactionParam)) {

            // 根据玩家id 查询
            if (ObjectUtil.isNotEmpty(aiMiningTransactionParam.getPlayerId())) {
                queryWrapper.lambda().eq(AiMiningTransaction::getPlayerId, aiMiningTransactionParam.getPlayerId());
            }
            // 根据币种 查询
            if (ObjectUtil.isNotEmpty(aiMiningTransactionParam.getSymbol())) {
                queryWrapper.lambda().eq(AiMiningTransaction::getSymbol, aiMiningTransactionParam.getSymbol());
            }
            // 根据矿机ID 查询
            if (ObjectUtil.isNotEmpty(aiMiningTransactionParam.getMinerId())) {
                queryWrapper.lambda().eq(AiMiningTransaction::getMinerId, aiMiningTransactionParam.getMinerId());
            }
            // 根据操作类型(1=开始挖矿,2=停止挖矿,3=挖矿收益) 查询
            if (ObjectUtil.isNotEmpty(aiMiningTransactionParam.getType())) {
                queryWrapper.lambda().eq(AiMiningTransaction::getType, aiMiningTransactionParam.getType());
            }
            // 根据收益金额(仅type=3时有效) 查询
            if (ObjectUtil.isNotEmpty(aiMiningTransactionParam.getAmount())) {
                queryWrapper.lambda().eq(AiMiningTransaction::getAmount, aiMiningTransactionParam.getAmount());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AiMiningTransaction> list(AiMiningTransactionParam aiMiningTransactionParam) {
        return this.list();
    }

    @Override
    public void add(AiMiningTransactionParam aiMiningTransactionParam) {
        AiMiningTransaction aiMiningTransaction = new AiMiningTransaction();
        BeanUtil.copyProperties(aiMiningTransactionParam, aiMiningTransaction);
        this.save(aiMiningTransaction);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<AiMiningTransactionParam> aiMiningTransactionParamList) {
        aiMiningTransactionParamList.forEach(aiMiningTransactionParam -> {
        AiMiningTransaction aiMiningTransaction = this.queryAiMiningTransaction(aiMiningTransactionParam);
            this.removeById(aiMiningTransaction.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AiMiningTransactionParam aiMiningTransactionParam) {
        AiMiningTransaction aiMiningTransaction = this.queryAiMiningTransaction(aiMiningTransactionParam);
        BeanUtil.copyProperties(aiMiningTransactionParam, aiMiningTransaction);
        this.updateById(aiMiningTransaction);
    }

    @Override
    public AiMiningTransaction detail(AiMiningTransactionParam aiMiningTransactionParam) {
        return this.queryAiMiningTransaction(aiMiningTransactionParam);
    }

    /**
     * 获取AI矿机操作记录表
     *
     * <AUTHOR>
     * @date 2025-08-04 11:32:53
     */
    private AiMiningTransaction queryAiMiningTransaction(AiMiningTransactionParam aiMiningTransactionParam) {
        AiMiningTransaction aiMiningTransaction = this.getById(aiMiningTransactionParam.getId());
        if (ObjectUtil.isNull(aiMiningTransaction)) {
            throw new ServiceException(AiMiningTransactionExceptionEnum.NOT_EXIST);
        }
        return aiMiningTransaction;
    }
}
