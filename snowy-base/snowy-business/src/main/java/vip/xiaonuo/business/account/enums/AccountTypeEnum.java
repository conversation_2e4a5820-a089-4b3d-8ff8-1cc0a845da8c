package vip.xiaonuo.business.account.enums;

import lombok.Getter;
import org.springframework.util.ObjectUtils;

@Getter
public enum AccountTypeEnum {

    TYPE1(1, "账户资产"),
    TYPE2(2, "币币账户");

    private final Integer code;

    private final String message;

    AccountTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static boolean isExists(Integer code) {
        for (AccountTypeEnum typeEnum : values()) {
            if (code.equals(typeEnum.getCode())) {
                return true;
            }
        }
        return false;
    }

    public static String getMessageByCode(Integer code){
        if(ObjectUtils.isEmpty(code)){
            return "";
        }
        for(AccountTypeEnum typeEnum : AccountTypeEnum.values()){
            if(code == typeEnum.getCode()){
                return typeEnum.getMessage();
            }
        }
        return "";
    }

}
