package vip.xiaonuo.business.exchangerate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.dict.service.SysDictDataService;
import vip.xiaonuo.business.exchangerate.entity.ExchangeRate;
import vip.xiaonuo.business.exchangerate.enums.ExchangeRateExceptionEnum;
import vip.xiaonuo.business.exchangerate.mapper.ExchangeRateMapper;
import vip.xiaonuo.business.exchangerate.param.ExchangeRateParam;
import vip.xiaonuo.business.exchangerate.service.ExchangeRateService;
import vip.xiaonuo.core.consts.DpConstants;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.exechangeRate.QueryExchangeRateVo;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.third.ExchangeRateUtil;
import vip.xiaonuo.core.util.MessageUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 汇率（同步）service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:32
 */
@Service
public class ExchangeRateServiceImpl extends ServiceImpl<ExchangeRateMapper, ExchangeRate> implements ExchangeRateService, InitializingBean {
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    ExchangeRateMapper exchangeRateMapper;
    @Autowired
    SysDictDataService sysDictDataService;

    @Override
    public PageResult<ExchangeRate> page(ExchangeRateParam exchangeRateParam) {
        QueryWrapper<ExchangeRate> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(exchangeRateParam)) {

            // 根据汇率名称 查询
            if (ObjectUtil.isNotEmpty(exchangeRateParam.getExchangeRateName())) {
                queryWrapper.lambda().eq(ExchangeRate::getExchangeRateName, exchangeRateParam.getExchangeRateName());
            }
            // 根据汇率 查询
            if (ObjectUtil.isNotEmpty(exchangeRateParam.getExchangeRate())) {
                queryWrapper.lambda().eq(ExchangeRate::getExchangeRate, exchangeRateParam.getExchangeRate());
            }
            // 根据同步时间 查询
            if (ObjectUtil.isNotEmpty(exchangeRateParam.getExchangeRateSyncTime())) {
                queryWrapper.lambda().eq(ExchangeRate::getExchangeRateSyncTime, exchangeRateParam.getExchangeRateSyncTime());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<ExchangeRate> list(ExchangeRateParam exchangeRateParam) {
        return this.list();
    }

    @Override
    public void add(ExchangeRateParam exchangeRateParam) {
        ExchangeRate exchangeRate = new ExchangeRate();
        BeanUtil.copyProperties(exchangeRateParam, exchangeRate);
        this.save(exchangeRate);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<ExchangeRateParam> exchangeRateParamList) {
        exchangeRateParamList.forEach(exchangeRateParam -> {
            ExchangeRate exchangeRate = this.queryExchangeRate(exchangeRateParam);
            this.removeById(exchangeRate.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ExchangeRateParam exchangeRateParam) {
        ExchangeRate exchangeRate = this.queryExchangeRate(exchangeRateParam);
        BeanUtil.copyProperties(exchangeRateParam, exchangeRate);
        this.updateById(exchangeRate);
    }

    @Override
    public ExchangeRate detail(ExchangeRateParam exchangeRateParam) {
        return this.queryExchangeRate(exchangeRateParam);
    }

    /**
     * 汇率同步定时任务
     *
     * @return
     */
    @Transactional
    @Override
    @Async
    public ResponseData executeExchangeRateTask() {
        //获取同步数据
        List<QueryExchangeRateVo> list = ExchangeRateUtil.queryExchangeRate();
        if (ObjectUtils.isEmpty(list)) {
            return ResponseData.error("未获取到汇率数据，暂不更新");
        }
        //删除当日同步舒服
        exchangeRateMapper.delectExchangeRateToDay(new Date());
        for (QueryExchangeRateVo vo : list) {
            ExchangeRate rate = new ExchangeRate();
            rate.setExchangeRateName(vo.getExchangeRateName());
            rate.setExchangeRate(vo.getExchangeRate());
            rate.setExchangeRateSyncTime(new Date());
            this.save(rate);

            if (marketRateCodeList.contains(rate.getExchangeRateName())) {
                setMarketRatePrice(rate.getExchangeRateName(), rate.getExchangeRate());
            }
        }
        return ResponseData.success();
    }

    private void setMarketRatePrice(String rateName, BigDecimal exchangeRate) {
        String symbol = (rateName + "_USDT").toUpperCase();
        BigDecimal price = BigDecimal.ONE.divide(exchangeRate, 8, BigDecimal.ROUND_DOWN);
        redisTemplate.opsForValue().set(String.format(DpConstants.MARKET_LATEST_PRICE, symbol), price);
    }

    /**
     * 根据汇率名称获取费率（如果当天不存在，获取前一天）
     *
     * @param exchangeRateName
     * @return
     */
    @Override
    public ExchangeRate findExchangeRate(String exchangeRateName) {
        return exchangeRateMapper.findExchangeRateToDay(exchangeRateName);
    }

    /**
     * 换算
     *
     * @param currentName    当前单位
     * @param account        换算金额
     * @param conversionName 换算单位
     * @return 得到换算后数据
     */
    @Override
    public BigDecimal executeExchangeRateConversion(String currentName, BigDecimal account, String conversionName) {
        if (ObjectUtils.isEmpty(currentName)) {
            throw new RuntimeException(MessageUtils.get("rate.company.is.null.error"));
        }
        if (ObjectUtils.isEmpty(conversionName)) {
            throw new RuntimeException(MessageUtils.get("rate.company.is.null.error"));
        }
        if (ObjectUtils.isEmpty(account) && account.compareTo(BigDecimal.ZERO) < 1) {
            throw new RuntimeException(MessageUtils.get("rate.account.is.null.error"));
        }
        //获取换算当前单位到usd费率
        ExchangeRate exchangeRate = findExchangeRate(currentName);
        if (ObjectUtils.isEmpty(exchangeRate)) {
            throw new RuntimeException(MessageUtils.get("rate.fee.is.null.error"));
        }
        //获取换算单位到usd费率
        ExchangeRate convRate = findExchangeRate(conversionName);
        if (ObjectUtils.isEmpty(convRate)) {
            throw new RuntimeException(MessageUtils.get("rate.fee.is.null.error"));
        }
        //换算
        BigDecimal usdAccount = account.divide(exchangeRate.getExchangeRate(), 2, BigDecimal.ROUND_DOWN);
        //换算后如果小于1返回0
        if (usdAccount.compareTo(BigDecimal.ZERO) < 1) {
            return BigDecimal.ZERO;
        }
        BigDecimal convAccount = usdAccount.multiply(convRate.getExchangeRate()).setScale(2, BigDecimal.ROUND_DOWN);
        return convAccount;
    }

    /**
     * 获取费率
     *
     * @return
     */
    @Override
    public ResponseData finExchangeRateToDay() {
        List<String> list = sysDictDataService.getDictvalueByDictTypeCode("default_current");
        if (ObjectUtils.isEmpty(list)) {
            /*list = new ArrayList<>();
            list.add("SGD");*/
            return ResponseData.success();
        }
        List<ExchangeRate> exchangeRate = exchangeRateMapper.findExchangeRateToDayList(new Date(), list);
        if (ObjectUtils.isEmpty(exchangeRate)) {
            exchangeRate = exchangeRateMapper.findExchangeRateToBeforeDayList(list);
        }
        return ResponseData.success(exchangeRate);
    }

    /**
     * 获取汇率（同步）
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    private ExchangeRate queryExchangeRate(ExchangeRateParam exchangeRateParam) {
        ExchangeRate exchangeRate = this.getById(exchangeRateParam.getId());
        if (ObjectUtil.isNull(exchangeRate)) {
            throw new ServiceException(ExchangeRateExceptionEnum.NOT_EXIST);
        }
        return exchangeRate;
    }

    private List<String> marketRateCodeList = new ArrayList<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        marketRateCodeList.add("VND");

        for (String code : marketRateCodeList) {
            ExchangeRate exchangeRate = findExchangeRate(code);
            if (exchangeRate != null) {
                setMarketRatePrice(exchangeRate.getExchangeRateName(), exchangeRate.getExchangeRate());
            }
        }
    }
}
