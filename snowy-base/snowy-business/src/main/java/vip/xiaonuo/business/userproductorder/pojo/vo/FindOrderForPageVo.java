package vip.xiaonuo.business.userproductorder.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import vip.xiaonuo.business.userproductorder.entity.UserProductOrder;

import java.io.Serializable;

/**
 * 后端查询订单
 */
@ApiModel(value = "订单")
@Data
public class FindOrderForPageVo extends UserProductOrder implements Serializable {
    /**
     * 用户账户
     */
    @ApiModelProperty(value = "用户账户", required = true)
    private String userAccount;
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", required = true)
    private String userNickName;
    /**
     * 代理人
     */
    @ApiModelProperty(value = "代理人", required = true)
    private String systemNickName;
    /**
     * 账户类型
     */
    @ApiModelProperty(value = "账户类型", required = true)
    private String accountType="普通";
}
