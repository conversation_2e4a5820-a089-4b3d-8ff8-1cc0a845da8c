/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.spotorder.param;

import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;

/**
* 现货订单参数类
 *
 * <AUTHOR>
 * @date 2022-04-14 18:27:46
*/
@Data
public class SpotOrderParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 玩家ID
     */
    @NotNull(message = "玩家ID不能为空，请检查playerId参数", groups = {add.class, edit.class})
    private Long playerId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空，请检查orderNo参数", groups = {add.class, edit.class})
    private String orderNo;

    /**
     * 类型(1:买涨;2:买跌;)
     */
    @NotNull(message = "类型(1:买涨;2:买跌;)不能为空，请检查type参数", groups = {add.class, edit.class})
    private Integer type;

    /**
     * 交易类型表示,如ETH_USDT
     */
    @NotBlank(message = "交易类型表示,如ETH_USDT不能为空，请检查symbol参数", groups = {add.class, edit.class})
    private String symbol;

    /**
     * 秒
     */
    @NotNull(message = "秒不能为空，请检查seconds参数", groups = {add.class, edit.class})
    private Integer seconds;

    /**
     * 下单时间
     */
    @NotNull(message = "下单时间不能为空，请检查orderTime参数", groups = {add.class, edit.class})
    private String orderTime;

    /**
     * 下单价格
     */
    @NotNull(message = "下单价格不能为空，请检查orderPrice参数", groups = {add.class, edit.class})
    private BigDecimal orderPrice;

    /**
     * 下单数量
     */
    @NotNull(message = "下单数量不能为空，请检查quantity参数", groups = {add.class, edit.class})
    private BigDecimal quantity;

    /**
     * 平仓价格
     */
    @NotNull(message = "平仓价格不能为空，请检查closePrice参数", groups = {add.class, edit.class})
    private BigDecimal closePrice;

    /**
     * 平仓时间
     */
    @NotNull(message = "平仓时间不能为空，请检查closeTime参数", groups = {add.class, edit.class})
    private String closeTime;

    /**
     * 盈利率
     */
    @NotNull(message = "盈利率不能为空，请检查profitability参数", groups = {add.class, edit.class})
    private Integer profitability;

    /**
     * 手续费
     */
    @NotNull(message = "手续费不能为空，请检查orderFee参数", groups = {add.class, edit.class})
    private BigDecimal orderFee;

    /**
     * 盈亏
     */
    @NotNull(message = "盈亏不能为空，请检查profit参数", groups = {add.class, edit.class})
    private BigDecimal profit;

    /**
     * 0:持仓,1:平仓
     */
    @NotNull(message = "0:持仓,1:平仓不能为空，请检查status参数", groups = {add.class, edit.class})
    private Integer status;

    private String playerAccount;
    private Integer control;

    private String orgId;
}
