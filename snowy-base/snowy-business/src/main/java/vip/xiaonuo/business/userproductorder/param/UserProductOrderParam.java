package vip.xiaonuo.business.userproductorder.param;

import io.swagger.annotations.ApiModel;
import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;

/**
* 理财订单参数类
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:41
*/
@ApiModel(value = "理财订单列表")
@Data
public class UserProductOrderParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空，请检查playerId参数", groups = {add.class, edit.class})
    private Long playerId;

    /**
     * 产品id
     */
    @NotNull(message = "产品id不能为空，请检查productId参数", groups = {add.class, edit.class})
    private Long productId;

    /**
     * 购买金额
     */
    @NotNull(message = "购买金额不能为空，请检查purchaseAccount参数", groups = {add.class, edit.class})
    private BigDecimal purchaseAccount;

    /**
     * 周期
     */
    @NotNull(message = "周期不能为空，请检查cycle参数", groups = {add.class, edit.class})
    private Integer cycle;

    /**
     * 购买时间
     */
    @NotNull(message = "购买时间不能为空，请检查purchaseTime参数", groups = {add.class, edit.class})
    private String purchaseTime;

    /**
     * 计息开始时间
     */
    @NotNull(message = "计息开始时间不能为空，请检查startInterestData参数", groups = {add.class, edit.class})
    private String startInterestData;

    /**
     * 截止计息时间
     */
    @NotNull(message = "截止计息时间不能为空，请检查endInterestData参数", groups = {add.class, edit.class})
    private String endInterestData;

    /**
     * 下一计息时间
     */
    @NotNull(message = "下一计息时间不能为空，请检查nextInterestData参数", groups = {add.class, edit.class})
    private String nextInterestData;

    /**
     * 计息利率
     */
    @NotNull(message = "计息利率不能为空，请检查profitRate参数", groups = {add.class, edit.class})
    private BigDecimal profitRate;

    /**
     * 认购金额区间
     */
    @NotBlank(message = "认购金额区间不能为空，请检查productAccountInfo参数", groups = {add.class, edit.class})
    private String productAccountInfo;

    /**
     * 认购利息区间
     */
    @NotBlank(message = "认购利息区间不能为空，请检查profitRateInfo参数", groups = {add.class, edit.class})
    private String profitRateInfo;

    /**
     * 剩余天数
     */
    @NotNull(message = "剩余天数不能为空，请检查surplusDay参数", groups = {add.class, edit.class})
    private Integer surplusDay;

    /**
     * 收益金额
     */
    @NotNull(message = "收益金额不能为空，请检查profitAccount参数", groups = {add.class, edit.class})
    private BigDecimal profitAccount;

    /**
     * 违约金比例
     */
    @NotNull(message = "违约金比例不能为空，请检查violationRate参数", groups = {add.class, edit.class})
    private BigDecimal violationRate;

    /**
     * 违约金额
     */
    @NotNull(message = "违约金额不能为空，请检查violationAccount参数", groups = {add.class, edit.class})
    private BigDecimal violationAccount;

    /**
     * 退还本金
     */
    @NotNull(message = "退还本金不能为空，请检查returnPrincipal参数", groups = {add.class, edit.class})
    private BigDecimal returnPrincipal;

    /**
     * 订单状态 1 进行中 2 结束 3 赎回
     */
    @NotNull(message = "订单状态 1 进行中 2 结束 3 赎回不能为空，请检查productOrderStatus参数", groups = {add.class, edit.class})
    private Integer productOrderStatus;

    /**
     * 产品信息
     */
    @NotBlank(message = "产品信息不能为空，请检查productInfo参数", groups = {add.class, edit.class})
    private String productInfo;


    /**
     * 用户名
     */
    private String userNickName;
    /**
     * 代理人
     */
    private String systemNickName;

    /**
     * 订单编号
     */
    private String orderNum;

}
