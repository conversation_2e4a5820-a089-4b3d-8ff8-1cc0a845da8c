package vip.xiaonuo.business.contractorder.enums;

import lombok.Getter;

@Getter
public enum ContractOrderTypeEnum {
    TYPE1(1, "做多"),
    TYPE2(2, "做空");

    private final Integer code;

    private final String message;

    ContractOrderTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static boolean isExists(Integer code) {
        for (ContractOrderTypeEnum typeEnum : values()) {
            if (code.equals(typeEnum.getCode())) {
                return true;
            }
        }
        return false;
    }

}
