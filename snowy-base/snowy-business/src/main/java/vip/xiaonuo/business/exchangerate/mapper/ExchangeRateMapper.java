package vip.xiaonuo.business.exchangerate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import vip.xiaonuo.business.exchangerate.entity.ExchangeRate;

import java.util.Date;
import java.util.List;

/**
 * 汇率（同步）
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:32
 */
public interface ExchangeRateMapper extends BaseMapper<ExchangeRate> {
    /**
     * 删除当日汇率同步数据
     *
     * @return
     */
    int delectExchangeRateToDay(@Param("date") Date date);

    /**
     * 根据汇率名称获取当天汇率转换为usd汇率
     *
     * @param exchangeRateName
     * @return
     */
    ExchangeRate findExchangeRateToDay(@Param("exchangeRateName") String exchangeRateName);

    List<ExchangeRate> findExchangeRateToDayList(@Param("date") Date date, @Param("list") List<String> list);


    List<ExchangeRate> findExchangeRateToBeforeDayList(List<String> list);
}
