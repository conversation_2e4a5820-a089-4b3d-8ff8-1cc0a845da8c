package vip.xiaonuo.business.userproductorder.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import vip.xiaonuo.core.pojo.base.param.BaseParam;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户购买产品
 */
@ApiModel(value = "用户购买产品")
@Data
public class ExecuteUserPurchaseOrderDto implements Serializable {
    /**
     * 用户id
     */
    private Long playerId;
    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id", required = true)
    private Long productId;

    /**
     * 类型(1:币币账户;2:合约账户;3:现货账户)
     */
    @ApiModelProperty(value = "类型(1:币币账户;2:合约账户;3:现货账户)", required = true)
    private Integer accountType=1;
    /**
     * 币种代码
     */
    private String currencyCode="USDT";
    /**
     * 金额
     */
    @ApiModelProperty(value = "金额", required = true)
    private BigDecimal account = BigDecimal.ZERO;
    /**
     * 购买渠道
     */
    @ApiModelProperty(value = "购买渠道", required = true)
    private String purchaseChannel;

    @ApiModelProperty(value = "订单编号", required = true)
    private String orderNum;


}
