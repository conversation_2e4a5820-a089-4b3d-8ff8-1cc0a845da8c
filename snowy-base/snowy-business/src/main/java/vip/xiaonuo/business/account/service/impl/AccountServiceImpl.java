/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.account.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.account.entity.Account;
import vip.xiaonuo.business.account.enums.AccountExceptionEnum;
import vip.xiaonuo.business.account.enums.AccountTypeEnum;
import vip.xiaonuo.business.account.mapper.AccountMapper;
import vip.xiaonuo.business.account.param.AccountChangeBalanceParam;
import vip.xiaonuo.business.account.param.AccountParam;
import vip.xiaonuo.business.account.pojo.dto.ExecuteUserCutTurnDto;
import vip.xiaonuo.business.account.pojo.dto.ExecuteUserExchangeDto;
import vip.xiaonuo.business.account.pojo.vo.FindUserAccountVo;
import vip.xiaonuo.business.account.pojo.vo.UserAccountVo;
import vip.xiaonuo.business.account.result.AccountChangeBalanceResult;
import vip.xiaonuo.business.account.service.AccountService;
import vip.xiaonuo.business.accountbill.entity.AccountBill;
import vip.xiaonuo.business.accountbill.enums.AccountBillBusinessTypeEnum;
import vip.xiaonuo.business.accountbill.enums.AccountBillOptTypeEnum;
import vip.xiaonuo.business.accountbill.service.AccountBillService;
import vip.xiaonuo.business.currency.entity.Currency;
import vip.xiaonuo.business.currency.enums.CurrencyExceptionEnum;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.dict.service.SysDictDataService;
import vip.xiaonuo.business.exchangerecord.entity.ExchangeRecord;
import vip.xiaonuo.business.exchangerecord.service.ExchangeRecordService;
import vip.xiaonuo.business.market.enums.MarketExceptionEnum;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.spotcontractrule.entity.SpotContractRule;
import vip.xiaonuo.business.spotcontractrule.service.SpotContractRuleService;
import vip.xiaonuo.business.spotrule.entity.SpotRule;
import vip.xiaonuo.business.spotrule.service.SpotRuleService;
import vip.xiaonuo.core.consts.DictConstant;
import vip.xiaonuo.core.enums.SerialNoEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.core.util.MessageUtils;
import vip.xiaonuo.core.util.UUIDUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 账户表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-06 13:49:40
 */
@Service
public class AccountServiceImpl extends ServiceImpl<AccountMapper, Account> implements AccountService {
    private Logger logger = LoggerFactory.getLogger(AccountServiceImpl.class);
    @Resource
    private CurrencyService currencyService;
    @Resource
    private PlayerService playerService;
    @Resource
    private AccountBillService accountBillService;
    @Autowired
    AccountService accountService;
    @Resource
    private ExchangeRecordService exchangeRecordService;
    @Autowired
    MarketService marketService;
    @Autowired
    SysDictDataService sysDictDataService;
    @Resource
    SpotRuleService spotRuleService;
    @Resource
    SpotContractRuleService spotContractRuleService;

    @Override
    public PageResult<Account> page(AccountParam accountParam) {
        QueryWrapper<Account> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(accountParam)) {

            // 根据玩家ID 查询
            if (ObjectUtil.isNotEmpty(accountParam.getPlayerId())) {
                queryWrapper.lambda().eq(Account::getPlayerId, accountParam.getPlayerId());
            }
            // 根据类型(1:币币账户;2:合约账户;3:现货账户) 查询
            if (ObjectUtil.isNotEmpty(accountParam.getType())) {
                queryWrapper.lambda().eq(Account::getType, accountParam.getType());
            }
            // 根据币种代码 查询
            if (ObjectUtil.isNotEmpty(accountParam.getCurrencyCode())) {
                queryWrapper.lambda().eq(Account::getCurrencyCode, accountParam.getCurrencyCode());
            }
            // 根据余额 查询
            if (ObjectUtil.isNotEmpty(accountParam.getBalance())) {
                queryWrapper.lambda().eq(Account::getBalance, accountParam.getBalance());
            }
            // 根据冻结余额 查询
            if (ObjectUtil.isNotEmpty(accountParam.getFrozenBalance())) {
                queryWrapper.lambda().eq(Account::getFrozenBalance, accountParam.getFrozenBalance());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<Account> list(AccountParam accountParam) {
        LambdaQueryWrapper<Account> queryWrapper = Wrappers.<Account>lambdaQuery().eq(accountParam.getType() != null, Account::getType, accountParam.getType());
        queryWrapper.eq(accountParam.getPlayerId() != null, Account::getPlayerId, accountParam.getPlayerId());
        return this.list(queryWrapper);
    }

    @Override
    public void add(AccountParam accountParam) {
        Account account = new Account();
        BeanUtil.copyProperties(accountParam, account);
        this.save(account);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<AccountParam> accountParamList) {
        accountParamList.forEach(accountParam -> {
            Account account = this.queryAccount(accountParam);
            this.removeById(account.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AccountParam accountParam) {
        Account account = this.queryAccount(accountParam);
        BeanUtil.copyProperties(accountParam, account);
        this.updateById(account);
    }

    @Override
    public Account detail(AccountParam accountParam) {
        return this.queryAccount(accountParam);
    }

    /**
     * 获取账户表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    private Account queryAccount(AccountParam accountParam) {
        Account account = this.getById(accountParam.getId());
        if (ObjectUtil.isNull(account)) {
            throw new ServiceException(AccountExceptionEnum.NOT_EXIST);
        }
        return account;
    }

    /**
     * 变更账户余额
     *
     * @param accountChangeBalanceParam
     */
//    AccountChangeBalanceParam.builder()
//            .accountId(1L)//账户ID
//            .businessType(AccountBillBusinessTypeEnum.DEPOSIT)//业务类型
//            .businessNo("001")//业务流水号（ID）
//            .balance(BigDecimal.ONE)//变动金额（扣减为负数，添加为正数）
//            .updateUser(1L)//变动人
//            .remarks("备注（可不填）").build();
    @Transactional
    @Override
    public AccountChangeBalanceResult changeBalance(AccountChangeBalanceParam accountChangeBalanceParam) {
        //验证参数
        accountChangeBalanceParam.valid();
        /*
         * 查询余额
         * 使用了数据的行锁（悲观锁）
         */
        Account account = baseMapper.queryBalanceForLock(accountChangeBalanceParam.getPlayerId(), accountChangeBalanceParam.getType(), accountChangeBalanceParam.getCurrencyCode());
        if (account == null) {
            account = getAccount(accountChangeBalanceParam.getPlayerId(), accountChangeBalanceParam.getType(), accountChangeBalanceParam.getCurrencyCode());
        }
        if(AccountBillBusinessTypeEnum.SPOT_ORDER.name().equals(accountChangeBalanceParam.getBusinessType().name())){
            SpotRule spotRule = spotRuleService.getById(accountChangeBalanceParam.getRuleId());
            BigDecimal balance = account.getBalance();
            if(ObjectUtils.isNotEmpty(spotRule) && ObjectUtils.isNotEmpty(spotRule.getMinBalance()) && (balance.compareTo(spotRule.getMinBalance()) < 0)){
                throw new ServiceException(MessageUtils.get("balance.min.amount")+spotRule.getMinBalance().setScale(0));
            }
        }
        if(AccountBillBusinessTypeEnum.SPOT_CONTRACT_ORDER.name().equals(accountChangeBalanceParam.getBusinessType().name())){
            SpotContractRule contractRule = spotContractRuleService.getById(accountChangeBalanceParam.getRuleId());
            BigDecimal balance = account.getBalance();
            if(ObjectUtils.isNotEmpty(contractRule) && ObjectUtils.isNotEmpty(contractRule.getMinBalance()) && (balance.compareTo(contractRule.getMinBalance()) < 0)){
                throw new ServiceException(MessageUtils.get("balance.min.amount")+contractRule.getMinBalance().setScale(0));
            }
        }

        AccountChangeBalanceResult result = new AccountChangeBalanceResult();
        LambdaUpdateChainWrapper<Account> accountUpdateWrapper = lambdaUpdate().eq(Account::getId, account.getId());
        if (!BigDecimal.ZERO.equals(accountChangeBalanceParam.getBalance()) || !BigDecimal.ZERO.equals(accountChangeBalanceParam.getFrozen())) {
            //组装余额记录DTO
            AccountBill accountBill = accountChangeBalanceParam.toAccountBill(account);
            logger.info("之前余额：" + account.getBalance() + ";之前冻结：" + account.getFrozenBalance() + "，余额变动" + JSON.toJSONString(accountBill));
            //唯一性验证
            accountBillService.unique(accountBill);
            //保存余额记录
            accountBillService.save(accountBill);
            //更新余额
            if (accountBill.getOptType() != AccountBillOptTypeEnum.N) {
                accountUpdateWrapper
                        .set(Account::getBalance, accountBill.getBalanceAfter());
            }
//            if (accountBill.getBalanceAfter() != null) {
//                accountUpdateWrapper
//                        .set(Account::getBalance, accountBill.getBalanceAfter());
//            }
            if (accountBill.getFrozenOptType() != AccountBillOptTypeEnum.N) {
                accountUpdateWrapper
                        .set(Account::getFrozenBalance, accountBill.getFrozenAfter());
            }
//            if (accountBill.getFrozenAfter() != null) {
//                accountUpdateWrapper
//                        .set(Account::getFrozenBalance, accountBill.getFrozenAfter());
//            }
            accountUpdateWrapper.update();
            result.setAccountBill(accountBill);
        }
        result.setAccount(account);
        return result;
    }

    public Account getAccount(Long playerId, Integer type, String currencyCode) {
        //锁玩家
        playerService.lockPlayer(Lists.newArrayList(playerId));
        Account account = baseMapper.queryBalanceForLock(playerId, type, currencyCode);
        if (account == null) {
            Currency currency = currencyService.getCurrencyByCode(currencyCode);
            if (currency == null) {
                throw new ServiceException(CurrencyExceptionEnum.NOT_EXIST);
            }
            account = new Account();
            account.setPlayerId(playerId);
            account.setType(type);
            account.setCurrencyCode(currencyCode);
            account.setBalance(BigDecimal.ZERO);
            account.setFrozenBalance(BigDecimal.ZERO);
            account.setCreateTime(new Date());
            account.setCreateUser(-1L);
            save(account);
        }
        return account;
    }

    /**
     * 割转
     *
     * @param dto
     * @return
     */
    @Transactional
    @Override
    public ResponseData executeUserCutTurn(ExecuteUserCutTurnDto dto) {
        if (ObjectUtils.isEmpty(dto)) {
            return ResponseData.error(MessageUtils.get("error"));
        }
        if (ObjectUtils.isEmpty(dto.getPlayerId())) {
            return ResponseData.error(MessageUtils.get("user.is.null.error"));
        }
        if (ObjectUtils.isEmpty(dto.getTransferInType())) {
            return ResponseData.error(MessageUtils.get("transaction.recharge..in.error"));
        }
        if (ObjectUtils.isEmpty(dto.getTransferOutType())) {
            return ResponseData.error(MessageUtils.get("transaction.recharge..out.error"));
        }
        if (ObjectUtils.isEmpty(dto.getTransferOutCode())) {
            return ResponseData.error(MessageUtils.get("error"));
        }
        if (ObjectUtils.isEmpty(dto.getTransferOutAccount())) {
            return ResponseData.error(MessageUtils.get("rate.account.is.null.error"));
        }
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(dto.getPlayerId()));

        String busNo = UUIDUtil.createOrderId(SerialNoEnum.Z);
        //扣减转出币种金额
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder frozen = AccountChangeBalanceParam.builder()
                .playerId(dto.getPlayerId())
                .type(Integer.parseInt(dto.getTransferOutType()))
                .currencyCode(dto.getTransferOutCode())
                .businessType(AccountBillBusinessTypeEnum.TRANSFER)
                .optType(AccountBillOptTypeEnum.SUB)
                .businessNo(busNo).balance(dto.getTransferOutAccount()).updateUser(dto.getPlayerId());
        accountService.changeBalance(frozen.build());

        //增加金额
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder infrozen = AccountChangeBalanceParam.builder()
                .playerId(dto.getPlayerId())
                .type(Integer.parseInt(dto.getTransferInType()))
                .currencyCode(dto.getTransferOutCode())
                .businessType(AccountBillBusinessTypeEnum.TRANSFER)
                .optType(AccountBillOptTypeEnum.ADD)
                .businessNo(busNo)
                .balance(dto.getTransferOutAccount()).updateUser(dto.getPlayerId());
        accountService.changeBalance(infrozen.build());
        return ResponseData.success();
    }

    /**
     * 兑换
     *
     * @param dto
     * @return
     */
    @Transactional
    @Override
    public ResponseData executeUserExchange(ExecuteUserExchangeDto dto) {
        BigDecimal marketRate = marketService.getMarketRate(dto.getTransferOutCode(), dto.getChangeIntoCode());
        if (ObjectUtils.isEmpty(marketRate)) {
            return ResponseData.error(MessageUtils.get("rate.fee.is.null.error"));
        }
        BigDecimal feeRate = sysDictDataService.getCommonConfigAsDecimal(DictConstant.EXCHANGE_FEE);
        if (feeRate == null) {
            throw new ServiceException(MarketExceptionEnum.FEE_RATE_NOT_EXIST);
        }
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(dto.getPlayerId()));

        ExchangeRecord exchangeRecord = new ExchangeRecord();
        exchangeRecord.setPrice(marketRate);
        exchangeRecord.setPlayerId(dto.getPlayerId());
        exchangeRecord.setFromCurrencyCode(dto.getTransferOutCode());
        exchangeRecord.setFromQuantity(dto.getTransferOutAccount());
        String busNo = UUIDUtil.createOrderId(SerialNoEnum.H);
        exchangeRecord.setOrderNo(busNo);
        exchangeRecord.setCurrencyCode(dto.getChangeIntoCode());
        //计算转入
        BigDecimal inAccount = dto.getTransferOutAccount().multiply(marketRate).setScale(4, BigDecimal.ROUND_HALF_DOWN);
        BigDecimal orderFee = inAccount.multiply(feeRate).setScale(4, BigDecimal.ROUND_HALF_UP);
        exchangeRecord.setQuantity(inAccount.subtract(orderFee));
        //计算手续费 保留两位小数且四舍五入
        exchangeRecord.setOrderFee(orderFee);
        exchangeRecord.setStatus(1);
        exchangeRecord.setOrderTime(new Date());
        exchangeRecord.setCloseTime(new Date());
        exchangeRecordService.save(exchangeRecord);

        //兑出
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder frozen = AccountChangeBalanceParam.builder()
                .playerId(dto.getPlayerId())
                .type(AccountTypeEnum.TYPE1.getCode())
                .currencyCode(dto.getTransferOutCode())
                .businessType(AccountBillBusinessTypeEnum.CONVERT)
                .optType(AccountBillOptTypeEnum.SUB)
                .businessNo(busNo).balance(dto.getTransferOutAccount()).updateUser(dto.getPlayerId());
        accountService.changeBalance(frozen.build());


        //兑入
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder infrozen = AccountChangeBalanceParam.builder()
                .playerId(dto.getPlayerId())
                .type(AccountTypeEnum.TYPE1.getCode())
                .currencyCode(dto.getChangeIntoCode())
                .businessType(AccountBillBusinessTypeEnum.CONVERT)
                .optType(AccountBillOptTypeEnum.ADD)
                .businessNo(busNo).balance(exchangeRecord.getQuantity()).updateUser(dto.getPlayerId());
        accountService.changeBalance(infrozen.build());


        return ResponseData.success();
    }

    /**
     * 资产
     *
     * @param playerId
     * @return
     */
    @Override
    public ResponseData<FindUserAccountVo> findUserAccount(Long playerId) {
        if (ObjectUtils.isEmpty(playerId)) {
            return new SuccessResponseData<>();
        }
        QueryWrapper<Account> accountWrapper = new QueryWrapper<>();
        accountWrapper.lambda().eq(Account::getPlayerId, playerId);
        List<Account> list = accountService.list(accountWrapper);
        List<Currency> currencyList = currencyService.getAllList();
        Map<String, Currency> collect = currencyList.stream().collect(Collectors.toMap(Currency::getCode, account -> account));
        FindUserAccountVo accountVo = new FindUserAccountVo();
        //得到合约账户数据
        for (Account account : list) {
            Currency currency = collect.get(account.getCurrencyCode());
            //获取币种费率
            BigDecimal usdtRate = marketService.getMarketRate(account.getCurrencyCode(), "USDT");
            if (ObjectUtils.isEmpty(usdtRate)) {
                usdtRate = BigDecimal.ZERO;
//                return ResponseData.error(ResponseData.DEFAULT_ERROR_CODE, MessageUtils.get("rate.fee.is.null.error"), null);
            }
            BigDecimal acc = account.getBalance().multiply(usdtRate).setScale(4, BigDecimal.ROUND_HALF_UP);
            accountVo.setTotal(accountVo.getTotal().add(acc));
            UserAccountVo vo = new UserAccountVo();
            vo.setBalance(account.getBalance());
            vo.setFrozen(account.getFrozenBalance());
            vo.setCurrencyCode(account.getCurrencyCode());
            vo.setUsdt(acc);
            vo.setLogo(currency.getLogo());
            vo.setChargeSwitch(currency.getChargeSwitch());
            vo.setWithdrawSwitch(currency.getWithdrawSwitch());
            if (account.getType().equals(AccountTypeEnum.TYPE1.getCode())) {
                accountVo.getList().add(vo);
            } else if (account.getType().equals(AccountTypeEnum.TYPE2.getCode())) {
                accountVo.getCoinList().add(vo);
            }
        }
        Map<String, UserAccountVo> map = accountVo.getList().stream().collect(Collectors.toMap(UserAccountVo::getCurrencyCode, account -> account));
        Map<String, UserAccountVo> map2 = accountVo.getCoinList().stream().collect(Collectors.toMap(UserAccountVo::getCurrencyCode, account -> account));
        List<UserAccountVo> userCurrencyAccountVos = new ArrayList<>();
        List<UserAccountVo> userCurrencyAccountVos2 = new ArrayList<>();
        //找出有效的
        List<Currency> list2 = currencyList.stream().filter(p -> Integer.valueOf(1).equals(p.getStatus())).collect(Collectors.toList());
        for (Currency currency : list2) {
            if (map.get(currency.getCode()) == null) {
                UserAccountVo vo = new UserAccountVo();
                vo.setBalance(BigDecimal.ZERO);
                vo.setCurrencyCode(currency.getCode());
                vo.setUsdt(BigDecimal.ZERO);
                vo.setLogo(currency.getLogo());
                userCurrencyAccountVos.add(vo);
            } else {
                UserAccountVo vo = map.get(currency.getCode());
                userCurrencyAccountVos.add(vo);
            }

            if (map2.get(currency.getCode()) == null) {
                UserAccountVo vo = new UserAccountVo();
                vo.setBalance(BigDecimal.ZERO);
                vo.setCurrencyCode(currency.getCode());
                vo.setUsdt(BigDecimal.ZERO);
                vo.setLogo(currency.getLogo());
                userCurrencyAccountVos2.add(vo);
            } else {
                UserAccountVo vo = map.get(currency.getCode());
                userCurrencyAccountVos2.add(vo);
            }
        }
        accountVo.setList(userCurrencyAccountVos);
        accountVo.setCoinList(userCurrencyAccountVos2);
        return ResponseData.success(accountVo);
    }

    /**
     * 根据用户，币种，账户类型查询余额
     *
     * @param playerId
     * @param currencyCode
     * @param type
     * @return
     */
    @Override
    public ResponseData findAccountByTypeAndCurrencyCode(Long playerId, String currencyCode, Integer type) {
        BigDecimal userAccount = BigDecimal.ZERO;
        if (ObjectUtils.isEmpty(playerId) || ObjectUtils.isEmpty(currencyCode) || ObjectUtils.isEmpty(type)) {
            return ResponseData.success();
        }
        QueryWrapper<Account> accountWrapper = new QueryWrapper<>();
        accountWrapper.lambda().eq(Account::getPlayerId, playerId);
        accountWrapper.lambda().eq(Account::getType, type);
        accountWrapper.lambda().eq(Account::getCurrencyCode, currencyCode);
        List<Account> list = accountService.list(accountWrapper);
        if (ObjectUtils.isEmpty(list)) {
            return ResponseData.success(userAccount);
        }
        for (Account account : list) {
            userAccount = userAccount.add(account.getBalance());
        }
        return ResponseData.success(userAccount);
    }

    @Override
    public List<UserAccountVo> exchangeList(Long playerId) {
        List<UserAccountVo> result = new ArrayList<>();
        String exchange_currency_code = sysDictDataService.getCommonConfigAsString(DictConstant.EXCHANGE_CURRENCY_CODE);
        if (StrUtil.isEmpty(exchange_currency_code)) {
            return result;
        }
        List<String> currencyCodes = ListUtil.toList(exchange_currency_code.toUpperCase().split(","));

        QueryWrapper<Account> accountWrapper = new QueryWrapper<>();
        accountWrapper.lambda().eq(Account::getPlayerId, playerId);
        accountWrapper.lambda().eq(Account::getType, AccountTypeEnum.TYPE1.getCode());
        accountWrapper.lambda().in(Account::getCurrencyCode, currencyCodes);
        List<Account> list = accountService.list(accountWrapper);
        Map<String, Account> collect = list.stream().collect(Collectors.toMap(Account::getCurrencyCode, account -> account));
        List<Currency> currencies = currencyService.getCurrencyByCode(currencyCodes);
        for (Currency currency : currencies) {
            Account account = collect.get(currency.getCode());
            if (account == null) {
                account = new Account();
                account.setBalance(BigDecimal.ZERO);
                account.setFrozenBalance(BigDecimal.ZERO);
            }
            UserAccountVo vo = new UserAccountVo();
            vo.setBalance(account.getBalance());
            vo.setFrozen(account.getFrozenBalance());
            vo.setCurrencyCode(currency.getCode());
            vo.setLogo(currency.getLogo());
            result.add(vo);
        }
        return result;
    }
}
