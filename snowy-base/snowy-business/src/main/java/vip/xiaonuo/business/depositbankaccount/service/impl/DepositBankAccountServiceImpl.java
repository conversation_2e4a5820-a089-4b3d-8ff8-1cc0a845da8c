/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.depositbankaccount.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.enums.CommonStatusEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.business.depositbankaccount.entity.DepositBankAccount;
import vip.xiaonuo.business.depositbankaccount.enums.DepositBankAccountExceptionEnum;
import vip.xiaonuo.business.depositbankaccount.mapper.DepositBankAccountMapper;
import vip.xiaonuo.business.depositbankaccount.param.DepositBankAccountParam;
import vip.xiaonuo.business.depositbankaccount.service.DepositBankAccountService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;

/**
 * 存款银行账号service接口实现类
 *
 * <AUTHOR>
 * @date 2022-10-24 15:49:10
 */
@Service
public class DepositBankAccountServiceImpl extends ServiceImpl<DepositBankAccountMapper, DepositBankAccount> implements DepositBankAccountService {

    @Override
    public PageResult<DepositBankAccount> page(DepositBankAccountParam depositBankAccountParam) {
        QueryWrapper<DepositBankAccount> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(depositBankAccountParam)) {

            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(depositBankAccountParam.getUserName())) {
                queryWrapper.lambda().eq(DepositBankAccount::getUserName, depositBankAccountParam.getUserName());
            }
            // 根据银行卡号 查询
            if (ObjectUtil.isNotEmpty(depositBankAccountParam.getBankCardNumber())) {
                queryWrapper.lambda().eq(DepositBankAccount::getBankCardNumber, depositBankAccountParam.getBankCardNumber());
            }
            // 根据银行 查询
            if (ObjectUtil.isNotEmpty(depositBankAccountParam.getBankName())) {
                queryWrapper.lambda().eq(DepositBankAccount::getBankName, depositBankAccountParam.getBankName());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<DepositBankAccount> list(DepositBankAccountParam depositBankAccountParam) {
        return this.list();
    }

    @Override
    public void add(DepositBankAccountParam depositBankAccountParam) {
        DepositBankAccount depositBankAccount = new DepositBankAccount();
        BeanUtil.copyProperties(depositBankAccountParam, depositBankAccount);
        this.save(depositBankAccount);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<DepositBankAccountParam> depositBankAccountParamList) {
        depositBankAccountParamList.forEach(depositBankAccountParam -> {
        DepositBankAccount depositBankAccount = this.queryDepositBankAccount(depositBankAccountParam);
            this.removeById(depositBankAccount.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(DepositBankAccountParam depositBankAccountParam) {
        DepositBankAccount depositBankAccount = this.queryDepositBankAccount(depositBankAccountParam);
        BeanUtil.copyProperties(depositBankAccountParam, depositBankAccount);
        this.updateById(depositBankAccount);
    }

    @Override
    public DepositBankAccount detail(DepositBankAccountParam depositBankAccountParam) {
        return this.queryDepositBankAccount(depositBankAccountParam);
    }

    /**
     * 获取存款银行账号
     *
     * <AUTHOR>
     * @date 2022-10-24 15:49:10
     */
    private DepositBankAccount queryDepositBankAccount(DepositBankAccountParam depositBankAccountParam) {
        DepositBankAccount depositBankAccount = this.getById(depositBankAccountParam.getId());
        if (ObjectUtil.isNull(depositBankAccount)) {
            throw new ServiceException(DepositBankAccountExceptionEnum.NOT_EXIST);
        }
        return depositBankAccount;
    }
}
