package vip.xiaonuo.business.transaction.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import vip.xiaonuo.core.pojo.base.param.BaseParam;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户充值
 */
@ApiModel(value = "用户充值请求实体")
@Data
public class ExecuteRechargeUserDto implements Serializable {

    /**
     * 用户id
     */
    private Long playerId;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额", required = true)
    @NotNull(message = "金额不能为空，请检查account参数", groups = {BaseParam.add.class, BaseParam.edit.class})
    private BigDecimal account;

    /**
     * 图片地址
     */
    @ApiModelProperty(value = "图片地址", required = true)
    @NotBlank(message = "图片地址不能为空，请检查pictureUrl参数", groups = {BaseParam.add.class, BaseParam.edit.class})
    private String pictureUrl;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道", required = true)
    @NotBlank(message = "渠道不能为空，请检查rechargeChannel参数", groups = {BaseParam.add.class, BaseParam.edit.class})
    private String rechargeChannel;

    /**
     * 操作类型 1 充值 2 提现
     */
    @ApiModelProperty(value = "操作类型 1 充值 2 提现", required = true)
    private Integer operationType;

    @ApiModelProperty(value = "交易类型（币种代码）", required = true)
    @NotBlank(message = "交易类型（币种代码）不能为空，如 1 BTC，请检查transactionType参数", groups = {BaseParam.add.class, BaseParam.edit.class})
    private String transactionType;

    /**
     * 类型(1:币币账户;2:合约账户;3:现货账户)
     */
    @NotBlank(message = "账户交易类型不能为空，1:币币账户;2:合约账户;3:现货账户，请检查accountType参数", groups = {BaseParam.add.class, BaseParam.edit.class})
    private Integer accountType;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 充值前余额
     */
    private BigDecimal rechargeFrontAccount;
    /**
     * 充值后余额
     */
    private BigDecimal rechargeAfterAccount;

    /**
     * 审核人员
     */
    private Long chenckId;
    /**
     * 链名称
     */
    @ApiModelProperty(value = "链名称")
    private String chainName;
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String currencyAddress;

    /**
     * 状态
     */
    private Integer status;
    @ApiModelProperty(value = "到账地址")
    private String toAddress;
    private String remarks;

    /**
     * 开户行
     */
    private String bankDeposit;
    /**
     * 银行卡号
     */
    private String bankCard;

    private String chainType;
}
