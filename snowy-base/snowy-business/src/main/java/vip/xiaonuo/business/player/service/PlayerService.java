/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.player.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.param.PlayerParam;
import vip.xiaonuo.business.player.pojo.dto.FindFollowerDto;
import vip.xiaonuo.business.player.pojo.vo.FindFollowerVo;
import vip.xiaonuo.business.player.result.AgentOrgInfo;
import vip.xiaonuo.business.player.result.PlayerResult;
import vip.xiaonuo.business.player.result.PlayerStatisticModel;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.List;
import java.util.Map;

/**
 * 玩家service接口
 *
 * <AUTHOR>
 * @date 2022-04-04 14:31:45
 */
public interface PlayerService extends IService<Player> {

    /**
     * 查询玩家
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    PageResult<PlayerResult> pageResult(PlayerParam playerParam);

    /**
     * 玩家列表
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    List<Player> list(PlayerParam playerParam);

    /**
     * 添加玩家
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    void add(PlayerParam playerParam);

    /**
     * 删除玩家
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    void delete(List<PlayerParam> playerParamList);

    /**
     * 编辑玩家
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    void edit(PlayerParam playerParam);

    /**
     * 查看玩家
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    Player detail(PlayerParam playerParam);

    /**
     * 去sys_user表查询代理人邀请码
     *
     * @param inviteCode
     * @return
     */
    AgentOrgInfo getAgentByInviteCode(String orgCode, String inviteCode);

    /**
     * 获取一个邀请码
     *
     * @return
     */
    String genInviteCode();

    void lockPlayer(List<Long> playerIds);

    /**
     * 根据账号获取用户
     *
     * @param account
     * @return
     */
    Player findPlayerByAccount(String account);

    /**
     * 查询注册用户（时间为null 查询总注册数）
     *
     * @param date
     * @return
     */
    PlayerStatisticModel findUserStatistic(HomeStatisticParam param);

    Map<Long, String> queryAgentName(List<Long> agentIds);

    Map<Long, String> queryPlayerName(List<Long> playerIds);

    void editSpotScale(Long id, Integer spotScale);

    /**
     * 是否禁止交易
     *
     * @param id
     * @return
     */
    void isForbidTrade(Long id);

    /**
     * 分页查询用户充值提现交易记录
     *
     * @param dto
     * @return
     */
    PageResult<FindFollowerVo> findUserFollower(FindFollowerDto dto);
}
