package vip.xiaonuo.business.transaction.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value = "充值")
@Data
public class BankRechargeParam implements Serializable {

    @ApiModelProperty(value = "交易类型（币种代码）", required = true)
    @NotBlank
    private String currencyCode;
    @ApiModelProperty(value = "充值数量", required = true)
    @NotNull
    private BigDecimal quantity;
    @ApiModelProperty(value = "汇款名")
    @NotBlank(message = "汇款名")
    private String username;
    @ApiModelProperty(value = "汇款卡号")
    @NotBlank(message = "汇款卡号")
    private String bankCardNo;
    @ApiModelProperty(value = "图片地址", required = true)
    @NotBlank(message = "图片地址不能为空，请检查pictureUrl参数")
    private String pictureUrl;

    @ApiModelProperty(value = "到账卡号")
    @NotBlank(message = "到账卡号")
    private String toAddress;
}
