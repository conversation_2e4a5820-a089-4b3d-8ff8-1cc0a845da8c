/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.contractspotorder.controller.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import vip.xiaonuo.business.account.enums.AccountTypeEnum;
import vip.xiaonuo.business.account.param.AccountChangeBalanceParam;
import vip.xiaonuo.business.account.service.AccountService;
import vip.xiaonuo.business.accountbill.enums.AccountBillBusinessTypeEnum;
import vip.xiaonuo.business.accountbill.enums.AccountBillOptTypeEnum;
import vip.xiaonuo.business.contractRuleLoophole.entity.ContractRuleLoophole;
import vip.xiaonuo.business.contractRuleLoophole.service.ContractRuleLoopholeService;
import vip.xiaonuo.business.contractorder.result.ContractOrderStatisticModel;
import vip.xiaonuo.business.contractspotorder.controller.entity.ContractSpotOrder;
import vip.xiaonuo.business.contractspotorder.controller.enums.ContractSpotOrderExceptionEnum;
import vip.xiaonuo.business.contractspotorder.controller.mapper.ContractSpotOrderMapper;
import vip.xiaonuo.business.contractspotorder.controller.param.ContractSpotOrderParam;
import vip.xiaonuo.business.contractspotorder.controller.service.ContractSpotOrderService;
import vip.xiaonuo.business.currency.entity.Currency;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.dict.service.SysDictDataService;
import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.business.market.enums.MarketExceptionEnum;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.spotcontractrule.entity.SpotContractRule;
import vip.xiaonuo.business.spotcontractrule.service.SpotContractRuleService;
import vip.xiaonuo.business.spotorder.entity.SpotOrder;
import vip.xiaonuo.business.spotorder.enums.SpotOrderExceptionEnum;
import vip.xiaonuo.business.spotorder.enums.SpotOrderStatusEnum;
import vip.xiaonuo.business.spotorder.enums.SpotOrderTypeEnum;
import vip.xiaonuo.business.spotorder.param.PlaceSpotOrderModel;
import vip.xiaonuo.business.spotorder.param.QuerySpotOrderModel;
import vip.xiaonuo.business.spotorder.param.SpotOrderParam;
import vip.xiaonuo.business.spotorder.result.EndSpotOrderResult;
import vip.xiaonuo.business.spotorder.result.SpotOrderStatisticModel;
import vip.xiaonuo.business.spotorder.result.SpotOrderVo;
import vip.xiaonuo.business.spotorder.service.SpotScaleHelper;
import vip.xiaonuo.business.spotrule.entity.SpotRule;
import vip.xiaonuo.business.spotrule.service.SpotRuleService;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.consts.DictConstant;
import vip.xiaonuo.core.enums.CommonStatusEnum;
import vip.xiaonuo.core.enums.SerialNoEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.RedisDelayMessage;
import vip.xiaonuo.core.pojo.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.core.redis.DelayMessageType;
import vip.xiaonuo.core.redis.RedisHelper;
import vip.xiaonuo.core.redis.RedissonDelayQueueClient;
import vip.xiaonuo.core.util.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 合约极速订单表service接口实现类
 *
 * <AUTHOR>
 * @date 2024-04-08 20:24:21
 */
@Service
public class ContractSpotOrderServiceImpl extends ServiceImpl<ContractSpotOrderMapper, ContractSpotOrder> implements ContractSpotOrderService {
    @Resource
    private MarketService marketService;
    @Resource
    private SpotContractRuleService spotContractRuleService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private RedisHelper redisHelper;
    @Resource
    private AccountService accountService;
    @Resource
    private CurrencyService currencyService;
    @Resource
    private PlayerService playerService;
    @Resource
    private SpotScaleHelper spotScaleHelper;
    @Autowired(required = false)
    private RedissonDelayQueueClient redissonDelayQueueClient;
    @Resource
    private SysDictDataService sysDictDataService;
    @Resource
    private ContractRuleLoopholeService contractRuleLoopholeService;

    @Override
    public PageResult<ContractSpotOrder> page(ContractSpotOrderParam contractSpotOrderParam) {
        Page<ContractSpotOrder> spotOrderIPage = getBaseMapper().queryPage(PageFactory.defaultPage(), contractSpotOrderParam);
        return new PageResult<>(spotOrderIPage);
    }

    @Override
    public PageResult<SpotOrderVo> page(Long playerId, QuerySpotOrderModel model) {
        QueryWrapper<ContractSpotOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ContractSpotOrder::getPlayerId, playerId);
        if (ObjectUtil.isNotNull(model)) {
            // 根据0:持仓,1:平仓 查询
            if (ObjectUtil.isNotEmpty(model.getStatus())) {
                queryWrapper.lambda().eq(ContractSpotOrder::getStatus, model.getStatus());
            }
        }
        queryWrapper.lambda().orderByDesc(ContractSpotOrder::getCreateTime);
        PageResult<ContractSpotOrder> pageResult = new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
        PageResult<SpotOrderVo> page = ConvertUtils.convert(pageResult, SpotOrderVo.class);
        List<Currency> currencies = currencyService.list();
        for (SpotOrderVo spotOrderVo : page.getData()) {
            currencies.stream().filter(p -> p.getCode().equalsIgnoreCase(MarketUtils.getCode(spotOrderVo.getSymbol()))).findAny().ifPresent(currency -> spotOrderVo.setLogo(currency.getLogo()));
            if (SpotOrderStatusEnum.STATUS0.getCode().equals(spotOrderVo.getStatus()) && spotOrderVo.getOrderTime() != null) {
                spotOrderVo.setSurplusSeconds(spotOrderVo.getSeconds() - DateUtil.differentSeconds(spotOrderVo.getOrderTime(), new Date()));
            }
        }
        return page;
    }

    @Override
    public List<ContractSpotOrder> list(ContractSpotOrderParam contractSpotOrderParam) {
        return this.list();
    }

    @Override
    public void add(ContractSpotOrderParam contractSpotOrderParam) {
        ContractSpotOrder contractSpotOrder = new ContractSpotOrder();
        BeanUtil.copyProperties(contractSpotOrderParam, contractSpotOrder);
        this.save(contractSpotOrder);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<ContractSpotOrderParam> contractSpotOrderParamList) {
        contractSpotOrderParamList.forEach(contractSpotOrderParam -> {
        ContractSpotOrder contractSpotOrder = this.queryContractSpotOrder(contractSpotOrderParam);
            this.removeById(contractSpotOrder.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ContractSpotOrderParam contractSpotOrderParam) {
        ContractSpotOrder contractSpotOrder = this.queryContractSpotOrder(contractSpotOrderParam);
        BeanUtil.copyProperties(contractSpotOrderParam, contractSpotOrder);
        this.updateById(contractSpotOrder);
    }

    @Override
    public ContractSpotOrder detail(ContractSpotOrderParam contractSpotOrderParam) {
        return this.queryContractSpotOrder(contractSpotOrderParam);
    }

    /**
     * 获取合约极速订单表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    private ContractSpotOrder queryContractSpotOrder(ContractSpotOrderParam contractSpotOrderParam) {
        ContractSpotOrder contractSpotOrder = this.getById(contractSpotOrderParam.getId());
        if (ObjectUtil.isNull(contractSpotOrder)) {
            throw new ServiceException(ContractSpotOrderExceptionEnum.NOT_EXIST);
        }
        return contractSpotOrder;
    }

    @Override
    @Transactional
    public ContractSpotOrder placeOrder(Long playerId, PlaceSpotOrderModel model) {
        SpotContractRule spotRule = spotContractRuleService.getById(model.getRuleId());
        if (spotRule == null) {
            throw new ServiceException(SpotOrderExceptionEnum.NOT_EXIST);
        }
        Market market = marketService.getMarketBySymbol(model.getSymbol());
        if (market == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_EXIST);
        }
        if (market.getTransactionSwitch() != 1) {
            throw new ServiceException(MarketExceptionEnum.TRADING_CLOSED);
        }
        BigDecimal lastPrice = DpDataUtils.getLatestPrice(redisTemplate, market.getSymbol());
        if (lastPrice == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_LAST_PRICE);
        }
        if (BigDecimal.ZERO.compareTo(model.getQuantity()) >= 0) {
            throw new ServiceException(SpotOrderExceptionEnum.QUANTITY_ERROR);
        }
        if(spotRule.getMinQuantity().compareTo(model.getQuantity()) > 0){
            throw new ServiceException(MessageUtils.get("order.num.lt.min"));
        }
        if(spotRule.getMaxQuantity().compareTo(model.getQuantity()) < 0){
            throw new ServiceException(MessageUtils.get("order.num.gt.max"));
        }
        if (!SpotOrderTypeEnum.isExists(model.getType())) {
            throw new ServiceException(SpotOrderExceptionEnum.TYPE_NOT_EXIST);
        }
        BigDecimal feeRate = sysDictDataService.getCommonConfigAsDecimal(DictConstant.SPOT_FEE);
        if (feeRate == null) {
            throw new ServiceException(MarketExceptionEnum.FEE_RATE_NOT_EXIST);
        }
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(playerId));

        Integer playerSpotScale = playerService.lambdaQuery().select(Player::getSpotScale).eq(Player::getId, playerId).one().getSpotScale();
        String orderNo = UUIDUtil.createOrderId(SerialNoEnum.S);
        ContractSpotOrder spotOrder = new ContractSpotOrder();
        spotOrder.setPlayerId(playerId);
        spotOrder.setType(model.getType());
        spotOrder.setSymbol(market.getSymbol());
        spotOrder.setRuleId(spotRule.getId());
        spotOrder.setSeconds(spotRule.getSeconds());
        spotOrder.setOrderNo(orderNo);
        spotOrder.setOrderPrice(lastPrice);
        spotOrder.setOrderTime(new Date());
        spotOrder.setQuantity(model.getQuantity());
        spotOrder.setStatus(SpotOrderStatusEnum.STATUS0.getCode());
        spotOrder.setFollowerId(model.getFollowerId());
        spotOrder.setFollowerCommissionRate(model.getFollowerCommissionRate());
        spotOrder.setFollowerContractSpotOrderId(model.getFollowerContractSpotOrderId());
        //计算手续费        //保留两位小数且四舍五入
        spotOrder.setOrderFee(spotOrder.getQuantity().multiply(feeRate).setScale(2, BigDecimal.ROUND_HALF_UP));
        //控制订单输赢（如果用户设置了输赢比例，则订单默认输赢改为默认）
        if (ObjectUtil.isNull(model.getControlPre())) {
            ContractRuleLoophole contractRuleLoophole = contractRuleLoopholeService.getEffectiveLoophole(spotRule.getId());
            if(ObjectUtils.isNotEmpty(contractRuleLoophole)){
                spotOrder.setControl(1);
            }else{
                if(playerSpotScale >= 0){
                    spotOrder.setControl(playerSpotScale == 0 ? 0 : (playerSpotScale == 100 ? 1 : -1));
                }
            }
        } else {
            spotOrder.setControl(model.getControlPre());
        }
        save(spotOrder);
        //加上手续费
        BigDecimal balance = spotOrder.getQuantity().add(spotOrder.getOrderFee());
        //变更余额
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder balanceParamBuilder = AccountChangeBalanceParam.builder()
                .playerId(spotOrder.getPlayerId())
                .type(AccountTypeEnum.TYPE1.getCode())
                .currencyCode(MarketUtils.getBaseCode(spotOrder.getSymbol()))
                .businessType(AccountBillBusinessTypeEnum.SPOT_CONTRACT_ORDER)
                .optType(AccountBillOptTypeEnum.SUB)
                .businessNo(orderNo)
                .balance(balance)
                //增加冻结金额
                .frozenOptType(AccountBillOptTypeEnum.ADD)
                .frozen(spotOrder.getQuantity())
                .updateUser(spotOrder.getPlayerId())
                .ruleId(model.getRuleId());
        accountService.changeBalance(balanceParamBuilder.build());

        //存入redis，等待过期；提前1秒处理
//        redisHelper.set(RedisConstant.SPOT_ORDER + spotOrder.getOrderNo(), spotOrder.getId(), spotRule.getSeconds() - 1);
        redissonDelayQueueClient.addDelay(new RedisDelayMessage<>(DelayMessageType.SPOT_ORDER, spotOrder.getOrderNo(), spotRule.getSeconds().longValue(), TimeUnit.SECONDS));
        return spotOrder;
    }

    private ContractSpotOrder getSpotOrderForLock(String orderNo) {
        return lambdaQuery().eq(ContractSpotOrder::getOrderNo, orderNo).last(" for update").one();
    }

    private ContractSpotOrder getSpotOrderForLock(Long id) {
        return lambdaQuery().eq(ContractSpotOrder::getId, id).last(" for update").one();
    }

    private boolean calcWin(ContractSpotOrder spotOrder, BigDecimal lastPrice) {
        boolean isWin = false;
        BigDecimal diff = spotOrder.getOrderPrice().subtract(lastPrice);
        if (spotOrder.getType().equals(SpotOrderTypeEnum.TYPE1.getCode())) {
            if (diff.compareTo(BigDecimal.ZERO) < 0) {
                isWin = true;
            }
        }
        if (spotOrder.getType().equals(SpotOrderTypeEnum.TYPE2.getCode())) {
            isWin = diff.compareTo(BigDecimal.ZERO) > 0;
        }
        return isWin;
    }

    @Override
    @Transactional
    public void finishOrder(String orderNo,ContractSpotOrder contractspotOrder) {
        ContractSpotOrder spotOrder = getSpotOrderForLock(orderNo);
        if (spotOrder == null || !SpotOrderStatusEnum.STATUS0.getCode().equals(spotOrder.getStatus())) {
            throw new ServiceException(SpotOrderExceptionEnum.NOT_EXIST);
        }
        Market market = marketService.getMarketBySymbol(spotOrder.getSymbol());
        if (market == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_EXIST);
        }
        BigDecimal lastPrice = DpDataUtils.getLatestPrice(redisTemplate, spotOrder.getSymbol());
        if (lastPrice == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_LAST_PRICE);
        }
        SpotContractRule spotRule = spotContractRuleService.getById(spotOrder.getRuleId());
        if (spotRule == null) {
            //兼容老订单
            if (spotOrder.getProfitability() == null) {
                throw new ServiceException(SpotOrderExceptionEnum.NOT_EXIST);
            }
        }
        lastPrice = MarketUtils.getLastPrice(spotOrder.getType(), spotOrder.getOrderPrice(), lastPrice, market.getPriceDecimal());
        boolean isWin = calcWin(spotOrder, lastPrice);
        spotOrder.setClosePrice(lastPrice);
        spotOrder.setCloseTime(new Date());
        spotOrder.setStatus(SpotOrderStatusEnum.STATUS1.getCode());
        //风控处理
        isWin = riskControl(spotOrder, isWin, market.getPriceDecimal(),spotRule);
        BigDecimal profitability = getProfitability(spotOrder, spotRule, isWin);
        spotOrder.setProfitability(profitability);
        BigDecimal profit = spotOrder.getQuantity().multiply(spotOrder.getProfitability().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_DOWN));
        spotOrder.setProfit(profit);
        //加上本金
        BigDecimal balanceChange = profit.add(spotOrder.getQuantity());
        AccountBillOptTypeEnum optType = AccountBillOptTypeEnum.ADD;
        if (BigDecimal.ZERO.compareTo(balanceChange) >= 0) {
            optType = AccountBillOptTypeEnum.N;
        }
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(spotOrder.getPlayerId()));
        //变更余额
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder balanceParamBuilder = AccountChangeBalanceParam.builder()
                .playerId(spotOrder.getPlayerId())
                .type(AccountTypeEnum.TYPE1.getCode())
                .currencyCode(MarketUtils.getBaseCode(spotOrder.getSymbol()))
                .businessType(AccountBillBusinessTypeEnum.SPOT_WIN)
                .businessNo(orderNo)
                .balance(balanceChange)
                .optType(optType)
                .updateUser(spotOrder.getPlayerId())
                //变更冻结金额
                .frozen(spotOrder.getQuantity())
                .frozenOptType(AccountBillOptTypeEnum.SUB);
        accountService.changeBalance(balanceParamBuilder.build());

        spotOrder.setUpdateUser((long) -1);
        spotOrder.setUpdateTime(new Date());
//        updateById(spotOrder);
        lambdaUpdate().set(ContractSpotOrder::getUpdateTime, spotOrder.getUpdateTime())
                .set(ContractSpotOrder::getUpdateUser, spotOrder.getUpdateUser())
                .set(ContractSpotOrder::getClosePrice, spotOrder.getClosePrice())
                .set(ContractSpotOrder::getCloseTime, spotOrder.getCloseTime())
                .set(ContractSpotOrder::getStatus, spotOrder.getStatus())
                .set(ContractSpotOrder::getProfit, spotOrder.getProfit())
                .set(ContractSpotOrder::getProfitability, spotOrder.getProfitability())
                .eq(ContractSpotOrder::getId, spotOrder.getId())
                .update();
    }

    private BigDecimal getProfitability(ContractSpotOrder spotOrder, SpotContractRule spotRule, boolean isWin) {
        if (isWin) {
            if (spotRule == null) {
                return spotOrder.getProfitability();
            }
            if (spotRule.getMaxProfitRate().compareTo(spotRule.getMinProfitRate()) == 0) {
                return spotRule.getMinProfitRate();
            }
            return RandomUtil.randomBigDecimal(spotRule.getMinProfitRate(), spotRule.getMaxProfitRate());
        } else {
            if (spotRule == null) {
                return new BigDecimal("100").negate();
            }
            if (spotRule.getMaxLossRate().compareTo(spotRule.getMinLossRate()) == 0) {
                return spotRule.getMinLossRate().negate();
            }
            return RandomUtil.randomBigDecimal(spotRule.getMinLossRate(), spotRule.getMaxLossRate()).negate();
        }
    }

    /**
     * 风控处理
     *
     * @param spotOrder
     * @param isWin
     */
    private boolean riskControl(ContractSpotOrder spotOrder, boolean isWin, Integer priceDecimal,SpotContractRule spotRule) {
        ContractRuleLoophole contractRuleLoophole = contractRuleLoopholeService.getEffectiveLoophole(spotRule.getId());
        if(ObjectUtils.isNotEmpty(contractRuleLoophole) && ObjectUtils.isNotEmpty(contractRuleLoophole.getRate()) && contractRuleLoophole.getRate().compareTo(BigDecimal.valueOf(100)) == 0){
            return true;
        }
        boolean control = false;
        Integer orderControl = spotOrder.getControl();
        if (orderControl != -1) {
            control = true;
            //订单上控制
            isWin = orderControl(spotOrder, isWin, priceDecimal, orderControl);
        }

        Map<String, String> playerSpotScaleMap = spotScaleHelper.getPlayerSpotScaleMap(spotOrder.getPlayerId());
        Integer playerSpotScale = playerService.lambdaQuery().select(Player::getSpotScale).eq(Player::getId, spotOrder.getPlayerId()).one().getSpotScale();
        if (playerSpotScale != -1 && !control) {
            /*如果玩家上设置了风控*/
            control = true;
            orderControl = spotScaleHelper.getSpotScaleControl(playerSpotScaleMap, playerSpotScale);
            isWin = orderControl(spotOrder, isWin, priceDecimal, orderControl);
        }
        Map.Entry<String, String> entry = spotScaleHelper.getEntryByQuantity(spotOrder.getQuantity());
        Map<String, String> spotScaleMap = null;
        if (entry != null) {
            spotScaleMap = spotScaleHelper.getSpotScaleMap(entry.getKey());
        }
        if (!control && spotScaleMap != null) {
            control = true;
            orderControl = spotScaleHelper.getSpotScaleControl(spotScaleMap, Integer.parseInt(entry.getValue()));
            isWin = orderControl(spotOrder, isWin, priceDecimal, orderControl);
        }
        spotScaleHelper.putPlayerSpotScaleNumber(spotOrder.getPlayerId(), playerSpotScaleMap, isWin);
        if (spotScaleMap != null) {
            spotScaleHelper.putSpotScaleNumber(entry.getKey(), spotScaleMap, isWin);
        }
        return isWin;
    }

    private boolean orderControl(ContractSpotOrder spotOrder, boolean isWin, Integer priceDecimal, Integer orderControl) {
        if (orderControl == 0 && isWin) {
            //设置成了必输，结果为赢
            BigDecimal lastPrice = MarketUtils.getLastPrice(spotOrder.getType(), spotOrder.getOrderPrice(), spotOrder.getOrderPrice(), priceDecimal);
            spotOrder.setClosePrice(lastPrice);
            isWin = calcWin(spotOrder, lastPrice);
        }
        if (orderControl == 1 && !isWin) {
            BigDecimal lastPrice = MarketUtils.getLastPriceAdd(spotOrder.getType(), spotOrder.getOrderPrice(), priceDecimal);
            spotOrder.setClosePrice(lastPrice);
            isWin = calcWin(spotOrder, lastPrice);
        }
        return isWin;
    }

    @Override
    public ContractOrderStatisticModel findSpotOrderStatistic(HomeStatisticParam param) {
        return getBaseMapper().findSpotOrderStatistic(param);
    }

    @Override
    public void editControl(Long id, Integer control) {
        ContractSpotOrder spotOrder = getSpotOrderForLock(id);
        if (spotOrder == null || SpotOrderStatusEnum.STATUS1.getCode().equals(spotOrder.getStatus())) {
            throw new ServiceException(SpotOrderExceptionEnum.NOT_EXIST);
        }
        lambdaUpdate().set(ContractSpotOrder::getControl, control).eq(ContractSpotOrder::getId, id).update();
    }

    @Override
    public EndSpotOrderResult endSpotOrder(Long playerId, String orderNo) {
        ContractSpotOrder spotOrder = lambdaQuery().eq(ContractSpotOrder::getOrderNo, orderNo).eq(ContractSpotOrder::getPlayerId, playerId).one();
        if (spotOrder == null) {
            throw new ServiceException(SpotOrderExceptionEnum.NOT_EXIST);
        }
        EndSpotOrderResult endSpotOrderResult = new EndSpotOrderResult();
        BeanUtil.copyProperties(spotOrder, endSpotOrderResult);
        if (SpotOrderStatusEnum.STATUS0.getCode().equals(spotOrder.getStatus()) && spotOrder.getOrderTime() != null) {
            BigDecimal lastPrice = DpDataUtils.getLatestPrice(redisTemplate, spotOrder.getSymbol());
            endSpotOrderResult.setClosePrice(lastPrice);
        }
        return endSpotOrderResult;
    }

    @Override
    public List<ContractSpotOrder> getOverdueOrder() {
        return baseMapper.getOverdueOrder(new Date());
    }

    /**
     * 获取现货订单
     *
     * <AUTHOR>
     * @date 2022-04-14 18:27:46
     */
    private ContractSpotOrder querySpotOrder(SpotOrderParam spotOrderParam) {
        ContractSpotOrder spotOrder = this.getById(spotOrderParam.getId());
        if (ObjectUtil.isNull(spotOrder)) {
            throw new ServiceException(SpotOrderExceptionEnum.NOT_EXIST);
        }
        return spotOrder;
    }

    @Override
    public ContractSpotOrder getContractSpotOrderForLock(String orderNo) {
        return lambdaQuery().eq(ContractSpotOrder::getOrderNo, orderNo).last(" for update").one();
    }
}
