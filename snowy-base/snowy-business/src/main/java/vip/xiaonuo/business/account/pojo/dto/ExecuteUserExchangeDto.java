package vip.xiaonuo.business.account.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 兑换
 */
@ApiModel(value = "兑换")
@Data
public class ExecuteUserExchangeDto implements Serializable {

    /**
     * 用户id
     */
    private Long playerId;
    /**
     * 转出code 如 BTC
     */
    @ApiModelProperty(value = " 转出code 如 BTC", required = true)
    private String transferOutCode;
    /**
     * 转出金额
     */
    @ApiModelProperty(value = " 转出金额", required = true)
    private BigDecimal transferOutAccount;

    /**
     * 转入code 如 BTC
     */
    @ApiModelProperty(value = " 转入code 如 BTC", required = true)
    private String changeIntoCode;
}
