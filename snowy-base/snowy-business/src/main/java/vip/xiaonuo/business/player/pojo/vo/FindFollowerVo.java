package vip.xiaonuo.business.player.pojo.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户充值提现记录
 */
@Data
public class FindFollowerVo implements Serializable {

    private Long id;
    /**
     * 用户id
     */
    private Long playerId;

    /**
     * 个人简介
     */
    private String profile;

    /**
     * 标签（英文逗号分隔）
     */
    private String tagList;

    /**
     * 交易次数
     */
    private Integer tradeCount;

    /**
     * 风险评分（0~100）
     */
    private BigDecimal riskScore;

    /**
     * 用户评分（0~5）
     */
    private BigDecimal userScore;

    /**
     * 最近30天盈利率（%）
     */
    private BigDecimal profitRate30d;

    /**
     * 胜率（%）
     */
    private BigDecimal winRate;

    /**
     * 胜率（%）
     */
    private String avatar;

    /**
     * 胜率（%）
     */
    private String nickname;

    /**
     * 是否已跟单（0-否，1-是）
     */
    private Integer isFollowed;
}
