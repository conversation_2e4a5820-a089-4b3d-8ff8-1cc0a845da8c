package vip.xiaonuo.business.playerinfo.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class PlayerAdvancedCertificationParam {
    @ApiModelProperty(value = "姓名")
    private String advancedRealName;
    //银行卡号
    @ApiModelProperty(value = "银行卡号")
    private String bankCardNo;
    /**
     * 身份证正面
     */
    @ApiModelProperty(value = "身份证正面")
    @NotEmpty
    private String idCardJust;

    /**
     * 身份证反面
     */
    @ApiModelProperty(value = "身份证反面")
    @NotEmpty
    private String idCardBack;

    /**
     * 手持身份证
     */
    @ApiModelProperty(value = "手持身份证")
    @NotEmpty
    private String holdIdCard;
}
