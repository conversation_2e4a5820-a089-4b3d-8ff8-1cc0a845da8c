/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.contractRuleLoophole.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import vip.xiaonuo.business.contractRuleLoophole.entity.ContractRuleLoophole;
import vip.xiaonuo.business.contractRuleLoophole.enums.ContractRuleLoopholeExceptionEnum;
import vip.xiaonuo.business.contractRuleLoophole.mapper.ContractRuleLoopholeMapper;
import vip.xiaonuo.business.contractRuleLoophole.param.ContractRuleLoopholeParam;
import vip.xiaonuo.business.contractRuleLoophole.service.ContractRuleLoopholeService;
import vip.xiaonuo.business.spotcontractrule.entity.SpotContractRule;
import vip.xiaonuo.business.spotcontractrule.service.SpotContractRuleService;
import vip.xiaonuo.core.consts.RedisConstant;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.core.redis.RedisHelper;
import vip.xiaonuo.core.util.DateFormaterUtils;
import vip.xiaonuo.core.util.HardwareUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合约漏洞表service接口实现类
 *
 * <AUTHOR>
 * @date 2024-04-08 20:04:41
 */
@Service
@Slf4j
public class ContractRuleLoopholeServiceImpl extends ServiceImpl<ContractRuleLoopholeMapper, ContractRuleLoophole> implements ContractRuleLoopholeService {

    @Resource
    private SpotContractRuleService spotContractRuleService;
    @Resource
    private RedisHelper redisHelper;

    private List<ContractRuleLoophole> cacheList = null;

    @Override
    public PageResult<ContractRuleLoophole> page(ContractRuleLoopholeParam contractRuleLoopholeParam) {
        QueryWrapper<ContractRuleLoophole> queryWrapper = new QueryWrapper<>();
        PageResult<ContractRuleLoophole> page = new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
        if(CollectionUtils.isNotEmpty(page.getData())){
            List<SpotContractRule> ruleList = spotContractRuleService.list();
            Map<Long,Integer> ruleMap = ruleList.stream().collect(Collectors.toMap(SpotContractRule::getId,SpotContractRule::getSeconds));
            page.getData().stream().forEach(loophole ->{
                loophole.setContractRule(ruleMap.get(loophole.getContractRuleId()));
            });
        }
        return page;
    }

    @Override
    public List<ContractRuleLoophole> list(ContractRuleLoopholeParam contractRuleLoopholeParam) {
        return this.list();
    }

    @Override
    public void add(ContractRuleLoopholeParam contractRuleLoopholeParam) {
        ContractRuleLoophole contractRuleLoophole = new ContractRuleLoophole();
        BeanUtil.copyProperties(contractRuleLoopholeParam, contractRuleLoophole);
        this.save(contractRuleLoophole);

        //将缓存清理
        redisHelper.deleteByPattern(RedisConstant.REDIS_CONTRACT_RULE_LOOPHOLE_CACHE + "*");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<ContractRuleLoopholeParam> contractRuleLoopholeParamList) {
        contractRuleLoopholeParamList.forEach(contractRuleLoopholeParam -> {
        ContractRuleLoophole contractRuleLoophole = this.queryContractRuleLoophole(contractRuleLoopholeParam);
            this.removeById(contractRuleLoophole.getId());
        });

        //将缓存清理
        redisHelper.deleteByPattern(RedisConstant.REDIS_CONTRACT_RULE_LOOPHOLE_CACHE + "*");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ContractRuleLoopholeParam contractRuleLoopholeParam) {
        ContractRuleLoophole contractRuleLoophole = this.queryContractRuleLoophole(contractRuleLoopholeParam);
        BeanUtil.copyProperties(contractRuleLoopholeParam, contractRuleLoophole);
        this.updateById(contractRuleLoophole);

        //将缓存清理
        redisHelper.deleteByPattern(RedisConstant.REDIS_CONTRACT_RULE_LOOPHOLE_CACHE + "*");
    }

    @Override
    public ContractRuleLoophole detail(ContractRuleLoopholeParam contractRuleLoopholeParam) {
        return this.queryContractRuleLoophole(contractRuleLoopholeParam);
    }

    /**
     * 获取合约漏洞表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:04:41
     */
    private ContractRuleLoophole queryContractRuleLoophole(ContractRuleLoopholeParam contractRuleLoopholeParam) {
        ContractRuleLoophole contractRuleLoophole = this.getById(contractRuleLoopholeParam.getId());
        if (ObjectUtil.isNull(contractRuleLoophole)) {
            throw new ServiceException(ContractRuleLoopholeExceptionEnum.NOT_EXIST);
        }
        return contractRuleLoophole;
    }

    @Override
    public ContractRuleLoophole getEffectiveLoophole(Long contractRuleId) {
        List<ContractRuleLoophole> playLoopholes = getCacheList();
        List<ContractRuleLoophole> fList = playLoopholes.stream().filter(p -> contractRuleId.equals(p.getContractRuleId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(fList)) {
            return null;
        }
        Date cDate = new Date();
        for (ContractRuleLoophole playLoophole : fList) {
            // 2022-01-06 13:00:00   2022-01-06 14:00:00
            String[] startTimeArr = playLoophole.getStartDate().split(":");
            DateTime startDate = new DateTime();
            startDate.setField(DateField.HOUR_OF_DAY, Integer.parseInt(startTimeArr[0]));
            startDate.setField(DateField.MINUTE, Integer.parseInt(startTimeArr[1]));
            startDate.setField(DateField.SECOND, Integer.parseInt(startTimeArr[2]));

            String[] endTimeArr = playLoophole.getEndDate().split(":");
            DateTime endDate = new DateTime();
            endDate.setField(DateField.HOUR_OF_DAY, Integer.parseInt(endTimeArr[0]));
            endDate.setField(DateField.MINUTE, Integer.parseInt(endTimeArr[1]));
            endDate.setField(DateField.SECOND, Integer.parseInt(endTimeArr[2]));

            if (cDate.compareTo(startDate) >= 0 && cDate.compareTo(endDate) <= 0) {
                return playLoophole;
            }
        }
        return null;
    }

    public synchronized List<ContractRuleLoophole> getCacheList() {
        if (CollectionUtil.isEmpty(cacheList)) {
            cacheList = list();
        } else {
            String macAddr = HardwareUtils.getMacAddr(false);
            String redisKey = RedisConstant.REDIS_CONTRACT_RULE_LOOPHOLE_CACHE + ":" + macAddr + ":" + SpringUtil.getProperty("server.port");
            String flag = redisHelper.get(redisKey);
            if (StringUtils.isEmpty(flag)) {
                cacheList = list();
                redisHelper.set(redisKey, DateFormaterUtils.datetimeFormat.format(new Date()));
            }
        }
        return cacheList;
    }
}
