package vip.xiaonuo.business.transaction.pojo.dto;

import lombok.Data;
import vip.xiaonuo.core.pojo.base.param.BaseParam;

import java.io.Serializable;

@Data
public class FindRechargeUserForPageDto extends BaseParam implements Serializable {

    /**
     * 用户id
     */
    private Long playerId;
    /**
     * 用户名
     */
    private String userName;
    private String transactionType;
    /**
     * 状态
     0 待付款
     1 待审核
     2 成功
     3 失败
     4 处理中
     5 交易超时
     6 交易撤销
     */
    private Integer status;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 代理人
     */
    private String agentPeople;
    /**
     * 开始时间
     */
    private String startDate;
    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 操作类型 1 充值 2 提现
     */
    private Integer operationType;

    /**
     * 订单备注
     */
    private String remark;
    private String rechargeChannel;
}
