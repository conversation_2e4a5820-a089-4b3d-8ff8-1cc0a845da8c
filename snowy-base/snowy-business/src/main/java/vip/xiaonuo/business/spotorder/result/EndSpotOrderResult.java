package vip.xiaonuo.business.spotorder.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("现货下单")
public class EndSpotOrderResult {
    /**
     * 平仓价格
     */
    @ApiModelProperty("平仓价格")
    private BigDecimal closePrice;

    /**
     * 盈亏
     */
    @ApiModelProperty("盈亏")
    private BigDecimal profit;

    /**
     * 类型(1:买涨;2:买跌;)
     */
    @ApiModelProperty("类型(1:买涨;2:买跌;)")
    private Integer type;

    /**
     * 交易类型表示,如ETH_USDT
     */
    @ApiModelProperty("如ETH_USDT")
    private String symbol;


    /**
     * 0:持仓,1:平仓
     */
    @ApiModelProperty("0:持仓,1:平仓")
    private Integer status;

    /**
     * 秒
     */
    @ApiModelProperty("秒")
    private Integer seconds;

    /**
     * 下单时间
     */
    @ApiModelProperty("下单时间")
    private Date orderTime;

    /**
     * 下单价格
     */
    @ApiModelProperty("下单价格")
    private BigDecimal orderPrice;

    /**
     * 下单数量
     */
    @ApiModelProperty("下单数量")
    private BigDecimal quantity;

    /**
     * 手续费
     */
    @ApiModelProperty("手续费")
    private BigDecimal orderFee;
}
