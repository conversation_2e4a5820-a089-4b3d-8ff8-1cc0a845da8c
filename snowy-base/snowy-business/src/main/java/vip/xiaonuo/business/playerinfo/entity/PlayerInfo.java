package vip.xiaonuo.business.playerinfo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;

import java.util.Date;

/**
 * 认证审核
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_player_info")
public class PlayerInfo extends BaseEntity {

    /**
     * 用户id
     */
    private Long playerId;
    //类型(1:身份证,2:护照,3:驾驶证)
    private Integer type;
    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 国家
     */
    private String country;

    /**
     * 身份证正面
     */
    private String idCardJust;

    /**
     * 身份证反面
     */
    private String idCardBack;

    /**
     * 手持身份证
     */
    private String holdIdCard;

    /**
     * 证件号码
     */
    private String idCard;
    //银行卡号
    private String bankCardNo;
    /**
     * 居住地址
     */
    private String liveAddress;

    /**
     * 认证状态 0 待提交  1审核中 2审核通过 3审核失败
     */
    private Integer authStatus;
    /**
     * 认证时间
     */
    @Excel(name = "认证时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date authTime;

    /**
     * 审核时间
     */
    @Excel(name = "审核时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date checkTime;

    /**
     * 审核人员
     */
    private Long checkId;

    //高级验证状态 0 待提交  1审核中 2审核通过 3审核失败
    private Integer advancedStatus;
    private Date advancedTime;
    private Date advancedCheckTime;
    private Long advancedCheckId;
    private String advancedRealName;

}
