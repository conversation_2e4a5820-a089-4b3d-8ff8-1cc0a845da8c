/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.playerwallet.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import vip.xiaonuo.business.playerwallet.entity.PlayerWallet;
import vip.xiaonuo.business.playerwallet.enums.PlayerWalletExceptionEnum;
import vip.xiaonuo.business.playerwallet.mapper.PlayerWalletMapper;
import vip.xiaonuo.business.playerwallet.param.PlayerWalletParam;
import vip.xiaonuo.business.playerwallet.service.PlayerWalletService;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.enums.CommonStatusEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;

/**
 * 用户钱包service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-26 10:36:18
 */
@Service
public class PlayerWalletServiceImpl extends ServiceImpl<PlayerWalletMapper, PlayerWallet> implements PlayerWalletService {

    @Override
    public PageResult<PlayerWallet> page(PlayerWalletParam playerWalletParam) {
        QueryWrapper<PlayerWallet> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(playerWalletParam)) {

            // 根据用户id 查询
            if (ObjectUtil.isNotEmpty(playerWalletParam.getPlayerId())) {
                queryWrapper.lambda().eq(PlayerWallet::getPlayerId, playerWalletParam.getPlayerId());
            }
            // 根据钱包地址 查询
            if (ObjectUtil.isNotEmpty(playerWalletParam.getWalletAddress())) {
                queryWrapper.lambda().eq(PlayerWallet::getWalletAddress, playerWalletParam.getWalletAddress());
            }
            // 根据币种代码 查询
            if (ObjectUtil.isNotEmpty(playerWalletParam.getCurrencyCode())) {
                queryWrapper.lambda().eq(PlayerWallet::getCurrencyCode, playerWalletParam.getCurrencyCode());
            }
            // 根据链名称 查询
            if (ObjectUtil.isNotEmpty(playerWalletParam.getChainName())) {
                queryWrapper.lambda().eq(PlayerWallet::getChainName, playerWalletParam.getChainName());
            }
            // 根据状态 0 正常 1 删除 查询
            if (ObjectUtil.isNotEmpty(playerWalletParam.getStatus())) {
                queryWrapper.lambda().eq(PlayerWallet::getStatus, playerWalletParam.getStatus());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<PlayerWallet> list(PlayerWalletParam playerWalletParam) {
        return lambdaQuery()
                .eq(PlayerWallet::getPlayerId, playerWalletParam.getPlayerId())
                .list();
    }

    @Override
    public void add(PlayerWalletParam playerWalletParam) {
        PlayerWallet playerWallet = new PlayerWallet();
        BeanUtil.copyProperties(playerWalletParam, playerWallet);
        this.save(playerWallet);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<PlayerWalletParam> playerWalletParamList) {
        playerWalletParamList.forEach(playerWalletParam -> {
        PlayerWallet playerWallet = this.queryPlayerWallet(playerWalletParam);
            this.removeById(playerWallet.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PlayerWalletParam playerWalletParam) {
        PlayerWallet playerWallet = this.queryPlayerWallet(playerWalletParam);
        BeanUtil.copyProperties(playerWalletParam, playerWallet);
        this.updateById(playerWallet);
    }

    @Override
    public PlayerWallet detail(PlayerWalletParam playerWalletParam) {
        return this.queryPlayerWallet(playerWalletParam);
    }

    /**
     * 获取用户钱包
     *
     * <AUTHOR>
     * @date 2022-04-26 10:36:18
     */
    private PlayerWallet queryPlayerWallet(PlayerWalletParam playerWalletParam) {
        PlayerWallet playerWallet = this.getById(playerWalletParam.getId());
        if (ObjectUtil.isNull(playerWallet)) {
            throw new ServiceException(PlayerWalletExceptionEnum.NOT_EXIST);
        }
        return playerWallet;
    }
}
