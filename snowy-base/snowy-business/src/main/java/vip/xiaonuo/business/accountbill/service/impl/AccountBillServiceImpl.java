/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.accountbill.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.account.enums.AccountTypeEnum;
import vip.xiaonuo.business.account.service.AccountService;
import vip.xiaonuo.business.accountbill.entity.AccountBill;
import vip.xiaonuo.business.accountbill.enums.AccountBillExceptionEnum;
import vip.xiaonuo.business.accountbill.mapper.AccountBillMapper;
import vip.xiaonuo.business.accountbill.param.AccountBillParam;
import vip.xiaonuo.business.accountbill.service.AccountBillService;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.util.MessageUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 账户表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-06 13:49:58
 */
@Service
public class AccountBillServiceImpl extends ServiceImpl<AccountBillMapper, AccountBill> implements AccountBillService {

    @Resource
    private PlayerService playerService;

    @Override
    public PageResult<AccountBill> page(AccountBillParam accountBillParam) {
        QueryWrapper<AccountBill> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(accountBillParam)) {
            if (ObjectUtil.isNotEmpty(accountBillParam.getAccount())) {
                Player Player = playerService.findPlayerByAccount(accountBillParam.getAccount());
                queryWrapper.lambda().eq(AccountBill::getPlayerId, Player.getId());
            }
            if (ObjectUtil.isNotEmpty(accountBillParam.getOptType())) {
                queryWrapper.lambda().eq(AccountBill::getOptType, accountBillParam.getOptType());
            }
            if (ObjectUtil.isNotEmpty(accountBillParam.getPlayerId())) {
                queryWrapper.lambda().eq(AccountBill::getPlayerId, accountBillParam.getPlayerId());
            }
        }
        queryWrapper.lambda().orderByDesc(AccountBill::getCreateTime);
        Page<AccountBill> page = this.page(PageFactory.defaultPage(), queryWrapper);

        if(CollectionUtils.isEmpty(page.getRecords())){
            return new PageResult<>(page);
        }

        List<Long> playerIdList = page.getRecords().stream().map(AccountBill::getPlayerId).collect(Collectors.toList());
        Map<Long,String> playerMap = playerService.queryPlayerName(playerIdList);

        page.getRecords().forEach(p -> {
            p.setBusinessTypeName(MessageUtils.get("account.bill.business.type." + p.getBusinessType().name(), p.getBusinessType().name()));
            p.setTypeName(AccountTypeEnum.getMessageByCode(p.getType()));
            p.setAccount(playerMap.get(p.getPlayerId()));
        });
        return new PageResult<>(page);
    }

    @Override
    public List<AccountBill> list(AccountBillParam accountBillParam) {
        return this.list();
    }

    @Override
    public void add(AccountBillParam accountBillParam) {
        AccountBill accountBill = new AccountBill();
        BeanUtil.copyProperties(accountBillParam, accountBill);
        this.save(accountBill);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<AccountBillParam> accountBillParamList) {
        accountBillParamList.forEach(accountBillParam -> {
            AccountBill accountBill = this.queryAccountBill(accountBillParam);
            this.removeById(accountBill.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AccountBillParam accountBillParam) {
        AccountBill accountBill = this.queryAccountBill(accountBillParam);
        BeanUtil.copyProperties(accountBillParam, accountBill);
        this.updateById(accountBill);
    }

    @Override
    public AccountBill detail(AccountBillParam accountBillParam) {
        return this.queryAccountBill(accountBillParam);
    }

    @Override
    public void unique(AccountBill accountBill) {
        if (lambdaQuery()
                .eq(AccountBill::getAccountId, accountBill.getAccountId())
                .eq(AccountBill::getBusinessType, accountBill.getBusinessType())
                .eq(AccountBill::getBusinessNo, accountBill.getBusinessNo())
                .eq(AccountBill::getOptType, accountBill.getOptType())
                .eq(AccountBill::getFrozenOptType, accountBill.getFrozenOptType())
                .count() > 0) {
            throw new RuntimeException("Balance change bill already exists");
        }
    }

    /**
     * 获取账户表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:58
     */
    private AccountBill queryAccountBill(AccountBillParam accountBillParam) {
        AccountBill accountBill = this.getById(accountBillParam.getId());
        if (ObjectUtil.isNull(accountBill)) {
            throw new ServiceException(AccountBillExceptionEnum.NOT_EXIST);
        }
        return accountBill;
    }
}
