/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.currency.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.business.currency.entity.Currency;
import vip.xiaonuo.business.currency.param.CurrencyParam;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.List;
import java.util.Map;

/**
 * 币种service接口
 *
 * <AUTHOR>
 * @date 2022-04-06 12:56:08
 */
public interface CurrencyService extends IService<Currency> {

    /**
     * 查询币种
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    PageResult<Currency> page(CurrencyParam currencyParam);

    /**
     * 币种列表
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    List<Currency> list(CurrencyParam currencyParam);

    /**
     * 添加币种
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    void add(CurrencyParam currencyParam);

    /**
     * 删除币种
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    void delete(List<CurrencyParam> currencyParamList);

    /**
     * 编辑币种
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    void edit(CurrencyParam currencyParam);

    /**
     * 查看币种
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    Currency detail(CurrencyParam currencyParam);

    Currency getCurrencyByCode(String currencyCode);

    /**
     * 获取充值币种
     *
     * @return
     */
    List<Currency> getChargeCurrencyList();

    /**
     * 获取提现币种
     *
     * @return
     */
    List<Currency> getWithdrawCurrencyList();

    List<Currency> getEnableList();

    List<Currency> getAllList();

    Currency getWithdrawCurrency(String currencyCode);

    Map<String, String> getLogoMap(List<String> codes);

    List<Currency> getCurrencyByCode(List<String> currencyCodes);
}
