package vip.xiaonuo.business.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import vip.xiaonuo.business.product.entity.Product;
import vip.xiaonuo.business.product.enums.ProductExceptionEnum;
import vip.xiaonuo.business.product.mapper.ProductMapper;
import vip.xiaonuo.business.product.param.ProductParam;
import vip.xiaonuo.business.product.service.ProductService;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.enums.CommonStatusEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;

/**
 * 理财产品service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:39
 */
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Override
    public PageResult<Product> page(ProductParam productParam) {
        QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(productParam)) {

            // 根据产品名称 查询
            if (ObjectUtil.isNotEmpty(productParam.getProductName())) {
                queryWrapper.lambda().eq(Product::getProductName, productParam.getProductName());
            }
            // 根据最小认购金额 查询
            if (ObjectUtil.isNotEmpty(productParam.getMinPurchaseAccount())) {
                queryWrapper.lambda().eq(Product::getMinPurchaseAccount, productParam.getMinPurchaseAccount());
            }
            // 根据最大认购金额 查询
            if (ObjectUtil.isNotEmpty(productParam.getMaxPurchaseAccount())) {
                queryWrapper.lambda().eq(Product::getMaxPurchaseAccount, productParam.getMaxPurchaseAccount());
            }
            // 根据最小收益率 查询
            if (ObjectUtil.isNotEmpty(productParam.getMinProfitAccount())) {
                queryWrapper.lambda().eq(Product::getMinProfitAccount, productParam.getMinProfitAccount());
            }
            // 根据最大收益率 查询
            if (ObjectUtil.isNotEmpty(productParam.getMaxProfitAccount())) {
                queryWrapper.lambda().eq(Product::getMaxProfitAccount, productParam.getMaxProfitAccount());
            }
            // 根据周期 查询
            if (ObjectUtil.isNotEmpty(productParam.getCycle())) {
                queryWrapper.lambda().eq(Product::getCycle, productParam.getCycle());
            }
            // 根据提前赎回罚息费率 查询
            if (ObjectUtil.isNotEmpty(productParam.getAdvRedeemRate())) {
                queryWrapper.lambda().eq(Product::getAdvRedeemRate, productParam.getAdvRedeemRate());
            }
            // 根据产品介绍 查询
            if (ObjectUtil.isNotEmpty(productParam.getProductRemark())) {
                queryWrapper.lambda().eq(Product::getProductRemark, productParam.getProductRemark());
            }
            // 根据产品状态 1 启用 2 停用 查询
            if (ObjectUtil.isNotEmpty(productParam.getProductStatus())) {
                queryWrapper.lambda().eq(Product::getProductStatus, productParam.getProductStatus());
            }
            // 根据创建人 查询
            if (ObjectUtil.isNotEmpty(productParam.getCreateId())) {
                queryWrapper.lambda().eq(Product::getCreateId, productParam.getCreateId());
            }
            // 根据修改人 查询
            if (ObjectUtil.isNotEmpty(productParam.getUpdateId())) {
                queryWrapper.lambda().eq(Product::getUpdateId, productParam.getUpdateId());
            }
            if (ObjectUtil.isNotEmpty(productParam.getOrgCode())) {
                queryWrapper.lambda().eq(Product::getOrgCode, productParam.getOrgCode());
            }
            if (ObjectUtil.isNotEmpty(productParam.getLanguageCode())) {
                queryWrapper.lambda().eq(Product::getLanguageCode, productParam.getLanguageCode());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<Product> list(ProductParam productParam) {
        return this.list();
    }

    @Override
    public void add(ProductParam productParam) {
        Product product = new Product();
        BeanUtil.copyProperties(productParam, product);
        this.save(product);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<ProductParam> productParamList) {
        productParamList.forEach(productParam -> {
        Product product = this.queryProduct(productParam);
            this.removeById(product.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ProductParam productParam) {
        Product product = this.queryProduct(productParam);
        BeanUtil.copyProperties(productParam, product);
        this.updateById(product);
    }

    @Override
    public Product detail(ProductParam productParam) {
        return this.queryProduct(productParam);
    }

    /**
     * 获取理财产品
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    private Product queryProduct(ProductParam productParam) {
        Product product = this.getById(productParam.getId());
        if (ObjectUtil.isNull(product)) {
            throw new ServiceException(ProductExceptionEnum.NOT_EXIST);
        }
        return product;
    }
}
