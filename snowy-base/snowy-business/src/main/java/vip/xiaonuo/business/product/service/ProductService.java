package vip.xiaonuo.business.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.business.product.entity.Product;
import vip.xiaonuo.business.product.param.ProductParam;
import vip.xiaonuo.core.pojo.page.PageResult;
import java.util.List;

/**
 * 理财产品service接口
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:39
 */
public interface ProductService extends IService<Product> {

    /**
     * 查询理财产品
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    PageResult<Product> page(ProductParam productParam);

    /**
     * 理财产品列表
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    List<Product> list(ProductParam productParam);

    /**
     * 添加理财产品
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    void add(ProductParam productParam);

    /**
     * 删除理财产品
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    void delete(List<ProductParam> productParamList);

    /**
     * 编辑理财产品
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    void edit(ProductParam productParam);

    /**
     * 查看理财产品
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
     Product detail(ProductParam productParam);
}
