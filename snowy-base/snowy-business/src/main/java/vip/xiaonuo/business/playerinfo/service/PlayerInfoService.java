package vip.xiaonuo.business.playerinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.business.playerinfo.entity.PlayerInfo;
import vip.xiaonuo.business.playerinfo.param.PlayerAdvancedCertificationParam;
import vip.xiaonuo.business.playerinfo.param.PlayerInfoAuditParam;
import vip.xiaonuo.business.playerinfo.param.PlayerInfoParam;
import vip.xiaonuo.business.playerinfo.param.PlayerJuniorCertificationParam;
import vip.xiaonuo.business.playerinfo.pojo.dto.FindPlayerInfoForPageDto;
import vip.xiaonuo.business.playerinfo.pojo.vo.FindPlayerInfoForPageVo;
import vip.xiaonuo.core.pojo.page.PageResult;
import java.util.List;

/**
 * 认证审核service接口
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:38
 */
public interface PlayerInfoService extends IService<PlayerInfo> {

    /**
     * 查询认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    PageResult<PlayerInfo> page(PlayerInfoParam playerInfoParam);

    /**
     * 认证审核列表
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    List<PlayerInfo> list(PlayerInfoParam playerInfoParam);

    /**
     * 添加认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    void add(PlayerInfoParam playerInfoParam);

    /**
     * 删除认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    void delete(List<PlayerInfoParam> playerInfoParamList);

    /**
     * 编辑认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    void edit(PlayerInfoParam playerInfoParam);

    /**
     * 查看认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
     PlayerInfo detail(PlayerInfoParam playerInfoParam);


    /**
     * 查询认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    PageResult<FindPlayerInfoForPageVo> findPlayerInfoForPage(FindPlayerInfoForPageDto playerInfoParam);

    /**
     * 根据用户id获取用户信息
     * @param playerId
     * @return
     */
    PlayerInfo queryPlayerUserInfo(Long playerId);

    void junior(Long playerId, PlayerJuniorCertificationParam playerInfoParam);

    void advanced(Long playerId, PlayerAdvancedCertificationParam playerInfoParam);

    void auditStatus(Long loginUserId, PlayerInfoAuditParam playerInfoParam);
}
