/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.userbankaccount.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.userbankaccount.entity.PlayerBankAccount;
import vip.xiaonuo.business.userbankaccount.enums.PlayerBankAccountExceptionEnum;
import vip.xiaonuo.business.userbankaccount.mapper.PlayerBankAccountMapper;
import vip.xiaonuo.business.userbankaccount.param.PlayerBankAccountParam;
import vip.xiaonuo.business.userbankaccount.service.PlayerBankAccountService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户账号信息service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-12 19:32:16
 */
@Service
public class PlayerBankAccountServiceImpl extends ServiceImpl<PlayerBankAccountMapper, PlayerBankAccount> implements PlayerBankAccountService {

    @Resource
    private PlayerBankAccountMapper playerBankAccountMapper;

    @Override
    public PageResult<PlayerBankAccount> page(PlayerBankAccountParam playerBankAccountParam) {
//        Map<String,Object> param = new HashMap<String,Object>();
//        param.put("nickName",playerBankAccountParam.getNickName());
//        param.put("phone",playerBankAccountParam.getPhone());
        Page<PlayerBankAccount> page = playerBankAccountMapper.getPage(PageFactory.defaultPage(), playerBankAccountParam);
        return new PageResult<>(page);
    }

    @Override
    public List<PlayerBankAccount> list(PlayerBankAccountParam playerBankAccountParam) {
        return this.list();
    }

    @Override
    public void add(PlayerBankAccountParam playerBankAccountParam) {
        PlayerBankAccount playerBankAccount = new PlayerBankAccount();
        BeanUtil.copyProperties(playerBankAccountParam, playerBankAccount);
        this.save(playerBankAccount);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<PlayerBankAccountParam> playerBankAccountParamList) {
        playerBankAccountParamList.forEach(playerBankAccountParam -> {
            PlayerBankAccount playerBankAccount = this.queryUserBankAccount(playerBankAccountParam);
            this.removeById(playerBankAccount.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PlayerBankAccountParam playerBankAccountParam) {
        PlayerBankAccount playerBankAccount = this.queryUserBankAccount(playerBankAccountParam);
        BeanUtil.copyProperties(playerBankAccountParam, playerBankAccount);
        this.updateById(playerBankAccount);
    }

    @Override
    public PlayerBankAccount detail(PlayerBankAccountParam playerBankAccountParam) {
        return this.queryUserBankAccount(playerBankAccountParam);
    }

    @Override
    public PlayerBankAccount getByPlayerId(Long playerId) {
        List<PlayerBankAccount> playerBankAccountList = lambdaQuery().eq(PlayerBankAccount::getPlayerId, playerId).list();
        if (CollectionUtil.isNotEmpty(playerBankAccountList)) {
            return playerBankAccountList.get(0);
        }
        return null;
    }

    @Override
    public void delete(PlayerBankAccountParam playerBankAccountParam) {
        PlayerBankAccount playerBankAccount = this.queryUserBankAccount(playerBankAccountParam);
        this.removeById(playerBankAccount.getId());
    }

    /**
     * 获取用户账号信息
     *
     * <AUTHOR>
     * @date 2022-04-12 19:32:16
     */
    private PlayerBankAccount queryUserBankAccount(PlayerBankAccountParam playerBankAccountParam) {
        PlayerBankAccount playerBankAccount = this.getById(playerBankAccountParam.getId());
        if (ObjectUtil.isNull(playerBankAccount)) {
            throw new ServiceException(PlayerBankAccountExceptionEnum.NOT_EXIST);
        }
        return playerBankAccount;
    }
}
