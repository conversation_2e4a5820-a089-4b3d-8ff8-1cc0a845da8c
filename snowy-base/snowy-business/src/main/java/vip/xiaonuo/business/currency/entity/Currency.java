/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.currency.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;

import java.math.BigDecimal;

/**
 * 币种
 *
 * <AUTHOR>
 * @date 2022-04-06 12:56:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tt_currency")
public class Currency extends BaseEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * logo
     */
    private String logo;

    /**
     * 代码
     */
    private String code;
    //类型(0:虚拟货币,1:法币)
    private Integer type;
    /**
     * 提现手续费
     */
    private BigDecimal withdrawFee;

    /**
     * 0:下线,1:上线
     */
    private Integer status;

    /**
     * 排序，数值越小，排序越靠前
     */
    private Integer sort;

    /**
     * 充值开关：0:关 1:开
     */
    private Integer chargeSwitch;

    /**
     * 提现开关：0关，1开
     */
    private Integer withdrawSwitch;

    /**
     * 最小提币数量
     */
    private BigDecimal minWithdrawAmount;

    /**
     * 单次最大提币额
     */
    private BigDecimal currencyWithdrawLimit;

}
