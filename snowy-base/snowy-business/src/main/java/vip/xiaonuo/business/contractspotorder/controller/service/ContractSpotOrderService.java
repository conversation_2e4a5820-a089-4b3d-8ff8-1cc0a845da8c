/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.contractspotorder.controller.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.business.contractorder.result.ContractOrderStatisticModel;
import vip.xiaonuo.business.contractspotorder.controller.entity.ContractSpotOrder;
import vip.xiaonuo.business.contractspotorder.controller.param.ContractSpotOrderParam;
import vip.xiaonuo.business.spotcontractrule.entity.SpotContractRule;
import vip.xiaonuo.business.spotorder.entity.SpotOrder;
import vip.xiaonuo.business.spotorder.param.PlaceSpotOrderModel;
import vip.xiaonuo.business.spotorder.param.QuerySpotOrderModel;
import vip.xiaonuo.business.spotorder.result.EndSpotOrderResult;
import vip.xiaonuo.business.spotorder.result.SpotOrderStatisticModel;
import vip.xiaonuo.business.spotorder.result.SpotOrderVo;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.core.pojo.page.PageResult;
import java.util.List;

/**
 * 合约极速订单表service接口
 *
 * <AUTHOR>
 * @date 2024-04-08 20:24:21
 */
public interface ContractSpotOrderService extends IService<ContractSpotOrder> {

    /**
     * 查询合约极速订单表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    PageResult<ContractSpotOrder> page(ContractSpotOrderParam contractSpotOrderParam);

    PageResult<SpotOrderVo> page(Long playerId, QuerySpotOrderModel model);

    /**
     * 合约极速订单表列表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    List<ContractSpotOrder> list(ContractSpotOrderParam contractSpotOrderParam);

    /**
     * 添加合约极速订单表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    void add(ContractSpotOrderParam contractSpotOrderParam);

    /**
     * 删除合约极速订单表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    void delete(List<ContractSpotOrderParam> contractSpotOrderParamList);

    /**
     * 编辑合约极速订单表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    void edit(ContractSpotOrderParam contractSpotOrderParam);

    /**
     * 查看合约极速订单表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
     ContractSpotOrder detail(ContractSpotOrderParam contractSpotOrderParam);

    /**
     * 下单
     *
     * @param playerId
     * @param model
     */
    ContractSpotOrder placeOrder(Long playerId, PlaceSpotOrderModel model);


    /**
     * 完成
     *
     * @param orderNo
     */
    void finishOrder(String orderNo,ContractSpotOrder contractspotOrder);

    ContractOrderStatisticModel findSpotOrderStatistic(HomeStatisticParam param);

    void editControl(Long id, Integer control);

    EndSpotOrderResult endSpotOrder(Long playerId, String orderNo);

    List<ContractSpotOrder> getOverdueOrder();

    public ContractSpotOrder getContractSpotOrderForLock(String orderNo);
}
