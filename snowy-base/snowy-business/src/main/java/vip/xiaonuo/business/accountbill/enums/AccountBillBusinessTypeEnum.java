package vip.xiaonuo.business.accountbill.enums;

public enum AccountBillBusinessTypeEnum {
    //存款
    DEPOSIT,
    //取款
    WITHDRAW,
    //取消取款
    WITHDRAW_CANCEL,
    //划转
    TRANSFER,
    //兑换
    CONVERT,
    //币池认购
    COIN_POOL_ORDER,
    //币池赎回本金
    REDEMPTION,
    //币池收益
    COIN_POOL_PROFIT,
    //现货订单-下单
    SPOT_ORDER,
    //现货订单-赢
    SPOT_WIN,
    //合约订单
    CONTRACT_ORDER,
    //合约撤销委托
    REVOKE_ENTRUST,
    //合约平仓
    END_CONTRACT_ORDER,
    //币币订单
    COIN_ORDER,
    //币币撤销委托
    REVOKE_COIN,
    //币币成交
    END_COIN_ORDER,
    //后续根据实际业务增加
    SPOT_CONTRACT_ORDER,
    //极速合约单
    REFERRAL_REWARD
    // 推广奖励
}
