<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.business.userproductorder.mapper.UserProductOrderMapper">

    <select id="findOrderForPage" resultType="vip.xiaonuo.business.userproductorder.pojo.vo.FindOrderForPageVo">
        SELECT
            t1.id ,
        t1.player_id playerId,
        t1.order_num orderNum,
        t1.product_id productId,
        t1.purchase_account purchaseAccount,
        t1.cycle cycle,
        t1.purchase_time purchaseTime,
        t1.start_interest_data startInterestData,
        t1.end_interest_data endInterestData,
        t1.next_interest_data nextInterestData,
        t1.profit_rate profitRate,
        t1.product_account_info productAccountInfo,
        t1.profit_rate_info profitRateInfo,
        t1.surplus_day surplusDay,
        t1.profit_account profitAccount,
        t1.violation_rate violationRate,
        t1.violation_account violationAccount,
        t1.return_principal returnPrincipal,
        t1.create_time createTime,
        t1.product_order_status productOrderStatus,
        t1.product_info productInfo,
        t1.create_user createUser,
        t1.update_time updateTime,
        t1.update_user updateUser,
        t2.account userAccount,
        t2.nick_name  userNickName,
        t3.nick_name systemNickName
        FROM
        t_user_product_order t1
        LEFT JOIN tt_player t2 ON t1.player_id = t2.id
        LEFT JOIN sys_user t3 ON t2.agent_id = t3.id
            <where>
                <if test="ew.userNickName != null and ew.userNickName != ''">
                    and  t2.nick_nam like  concat('%',#{ew.userNickName},'%')
                </if>
                <if test="ew.orderNum != null and ew.orderNum != ''">
                    and  t1.order_num like  concat('%',#{ew.orderNum},'%')
                </if>
                <if test="ew.playerId != null and ew.playerId != ''">
                    and  t1.player_id = #{ew.playerId}
                </if>
                <if test="ew.systemNickName != null and ew.systemNickName != ''">
                    and  t3.nick_name like  concat('%',#{ew.systemNickName},'%')
                </if>
                <if test="ew.productOrderStatus != null and ew.productOrderStatus != ''">
                    and  t1.product_order_status = #{ew.productOrderStatus}
                </if>
                <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
                    and t2.org_code = #{ew.loginUserOrgCode}
                </if>
                <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
                    and t2.agent_id = #{ew.loginUserId}
                </if>
            </where>
            order by t1.create_time desc
    </select>

    <select id="findUserFinancialIncome" resultType="vip.xiaonuo.business.userproductorder.entity.UserProductOrder">
        SELECT
            t1.id ,
        t1.player_id playerId,
        t1.order_num orderNum,
        t1.product_id productId,
        t1.purchase_account purchaseAccount,
        t1.cycle cycle,
        t1.purchase_time purchaseTime,
        t1.start_interest_data startInterestData,
        t1.end_interest_data endInterestData,
        t1.next_interest_data nextInterestData,
        t1.profit_rate profitRate,
        t1.product_account_info productAccountInfo,
        t1.profit_rate_info profitRateInfo,
        t1.surplus_day surplusDay,
        t1.profit_account profitAccount,
        t1.violation_rate violationRate,
        t1.violation_account violationAccount,
        t1.return_principal returnPrincipal,
        t1.create_time createTime,
        t1.product_order_status productOrderStatus,
        t1.product_info productInfo,
        t1.create_user createUser,
        t1.update_time updateTime,
        t1.update_user updateUser
        FROM
        t_user_product_order t1
        <where>
            t1.product_order_status=1
            AND DATE(NOW()) = DATE(t1.next_interest_data) + 1
        </where>
        order by t1.create_time desc
    </select>

    <select id="findprofitSum" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(profit_account),0) FROM t_user_product_order WHERE player_id = #{playerId}
    </select>
<!--    &lt;!&ndash;今日认购和总认购 &ndash;&gt;
    <select id="findUserOrderStatistic" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(purchase_account),0) FROM t_user_product_order WHERE product_order_status in(1,2)
        <if test="date != null">
            and day(create_time) = #{date}
        </if>
    </select>
    &lt;!&ndash;今日赎回和总赎回 &ndash;&gt;
    <select id="findUserOrderRedeemStatistic" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(return_principal),0) FROM t_user_product_order WHERE product_order_status=3
        <if test="date != null">
            and day(create_time) = #{date}
        </if>
    </select>
    &lt;!&ndash;今日违约金&ndash;&gt;
    <select id="findUserProductOrderStatistic" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(violation_day_account),0) FROM t_user_product_order WHERE product_order_status=3
        <if test="date != null">
            and day(violation_date) = #{date}
        </if>
    </select>-->
    <select id="findUserProductOrderStatistic" resultType="vip.xiaonuo.business.userproductorder.result.UserProductOrderStatisticModel">
        SELECT
            SUM( CASE WHEN t.product_order_status IN (1,2) THEN IFNULL( t.purchase_account, 0 ) ELSE 0 END ) subscriptionAccount,
            SUM( CASE WHEN t.product_order_status IN (1,2) and DATE ( t.create_time ) = #{ew.date} THEN IFNULL( t.purchase_account, 0 ) ELSE 0 END ) subscriptionDayAccount,
            SUM( CASE WHEN t.product_order_status=3 THEN IFNULL( t.return_principal, 0 ) ELSE 0 END ) redeemAccount,
            SUM( CASE WHEN t.product_order_status=3 and DATE ( t.create_time ) = #{ew.date} THEN IFNULL( t.return_principal, 0 ) ELSE 0 END ) redeemDayAccount,
            SUM( CASE WHEN t.product_order_status=3 and DATE ( t.create_time ) = #{ew.date} THEN IFNULL( t.violation_day_account, 0 ) ELSE 0 END ) unContractDayAccount
        FROM
            t_user_product_order t
            LEFT JOIN tt_player p ON p.id = t.player_id
        <where>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
                and p.org_code = #{ew.loginUserOrgCode}
            </if>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
                and p.agent_id = #{ew.loginUserId}
            </if>
        </where>
    </select>
</mapper>
