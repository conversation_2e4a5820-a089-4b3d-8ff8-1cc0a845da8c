package vip.xiaonuo.business.transaction.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户充值
 */
@ApiModel(value = "用户充值请求实体")
@Data
public class RechargeParam implements Serializable {

    @ApiModelProperty(value = "交易类型（币种代码）", required = true)
    @NotBlank
    private String currencyCode;

    @ApiModelProperty(value = "充值数量", required = true)
    @NotNull
    private BigDecimal quantity;

    @ApiModelProperty(value = "汇款地址")
    private String currencyAddress;

    @ApiModelProperty(value = "图片地址", required = true)
    private String pictureUrl;

    @ApiModelProperty(value = "到账地址")
    private String toAddress;

    @ApiModelProperty(value = "充值方式")
    private String chainType;
}
