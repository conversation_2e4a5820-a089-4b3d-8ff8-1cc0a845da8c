/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.productintroduce.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import vip.xiaonuo.business.productintroduce.entity.ProductIntroduce;
import vip.xiaonuo.business.productintroduce.enums.ProductIntroduceExceptionEnum;
import vip.xiaonuo.business.productintroduce.mapper.ProductIntroduceMapper;
import vip.xiaonuo.business.productintroduce.param.ProductIntroduceParam;
import vip.xiaonuo.business.productintroduce.service.ProductIntroduceService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.core.util.Language;

import java.util.List;

/**
 * 产品简介service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-25 15:27:42
 */
@Service
public class ProductIntroduceServiceImpl extends ServiceImpl<ProductIntroduceMapper, ProductIntroduce> implements ProductIntroduceService {

    @Override
    public PageResult<ProductIntroduce> page(ProductIntroduceParam productIntroduceParam) {
        QueryWrapper<ProductIntroduce> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(productIntroduceParam)) {

            // 根据语言编码 查询
            if (ObjectUtil.isNotEmpty(productIntroduceParam.getLanguageCode())) {
                queryWrapper.lambda().eq(ProductIntroduce::getLanguageCode, productIntroduceParam.getLanguageCode());
            }
            // 根据币种id 查询
            if (ObjectUtil.isNotEmpty(productIntroduceParam.getCurrencyCode())) {
                queryWrapper.lambda().eq(ProductIntroduce::getCurrencyCode, productIntroduceParam.getCurrencyCode());
            }
            // 根据发行时间 查询
            if (ObjectUtil.isNotEmpty(productIntroduceParam.getReleaseTime())) {
                queryWrapper.lambda().eq(ProductIntroduce::getReleaseTime, productIntroduceParam.getReleaseTime());
            }
            // 根据发行总量 查询
            if (ObjectUtil.isNotEmpty(productIntroduceParam.getTotalIssuance())) {
                queryWrapper.lambda().eq(ProductIntroduce::getTotalIssuance, productIntroduceParam.getTotalIssuance());
            }
            // 根据官方网站 查询
            if (ObjectUtil.isNotEmpty(productIntroduceParam.getOfficialWebsite())) {
                queryWrapper.lambda().eq(ProductIntroduce::getOfficialWebsite, productIntroduceParam.getOfficialWebsite());
            }
            // 根据白皮书 查询
            if (ObjectUtil.isNotEmpty(productIntroduceParam.getWhitePaper())) {
                queryWrapper.lambda().eq(ProductIntroduce::getWhitePaper, productIntroduceParam.getWhitePaper());
            }
            // 根据区块查询 查询
            if (ObjectUtil.isNotEmpty(productIntroduceParam.getBlockQuery())) {
                queryWrapper.lambda().eq(ProductIntroduce::getBlockQuery, productIntroduceParam.getBlockQuery());
            }
            // 根据简介 查询
            if (ObjectUtil.isNotEmpty(productIntroduceParam.getBriefIntroduce())) {
                queryWrapper.lambda().eq(ProductIntroduce::getBriefIntroduce, productIntroduceParam.getBriefIntroduce());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<ProductIntroduce> list(ProductIntroduceParam productIntroduceParam) {
        return this.list();
    }

    @Override
    public void add(ProductIntroduceParam productIntroduceParam) {
        ProductIntroduce productIntroduce = new ProductIntroduce();
        BeanUtil.copyProperties(productIntroduceParam, productIntroduce);
        this.save(productIntroduce);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<ProductIntroduceParam> productIntroduceParamList) {
        productIntroduceParamList.forEach(productIntroduceParam -> {
        ProductIntroduce productIntroduce = this.queryProductIntroduce(productIntroduceParam);
            this.removeById(productIntroduce.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ProductIntroduceParam productIntroduceParam) {
        ProductIntroduce productIntroduce = this.queryProductIntroduce(productIntroduceParam);
        BeanUtil.copyProperties(productIntroduceParam, productIntroduce);
        this.updateById(productIntroduce);
    }

    @Override
    public ProductIntroduce detail(ProductIntroduceParam productIntroduceParam) {
        return this.queryProductIntroduce(productIntroduceParam);
    }

    @Override
    public ProductIntroduce getByCurrencyCode(String currencyCode) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("LANGUAGE_CODE",Language.getLanguage().name());
        queryWrapper.eq("CURRENCY_CODE",currencyCode);
        return this.getOne(queryWrapper);
    }

    /**
     * 获取产品简介
     *
     * <AUTHOR>
     * @date 2022-04-25 15:27:42
     */
    private ProductIntroduce queryProductIntroduce(ProductIntroduceParam productIntroduceParam) {
        ProductIntroduce productIntroduce = this.getById(productIntroduceParam.getId());
        if (ObjectUtil.isNull(productIntroduce)) {
            throw new ServiceException(ProductIntroduceExceptionEnum.NOT_EXIST);
        }
        return productIntroduce;
    }
}
