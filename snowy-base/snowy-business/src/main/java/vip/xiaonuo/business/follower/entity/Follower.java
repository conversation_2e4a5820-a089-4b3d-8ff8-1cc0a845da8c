/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.follower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;

import java.math.BigDecimal;

/**
 * 交易员
 *
 * <AUTHOR>
 * @date 2025-07-20 13:22:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tt_follower")
public class Follower extends BaseEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    private Long playerId;

    /**
     * 个人简介
     */
    private String profile;

    /**
     * 标签（英文逗号分隔）
     */
    private String tagList;

    /**
     * 交易次数
     */
    private Integer tradeCount;

    /**
     * 风险评分（0~100）
     */
    private BigDecimal riskScore;

    /**
     * 用户评分（0~5）
     */
    private BigDecimal userScore;

    /**
     * 最近30天盈利率（%）
     */
    private BigDecimal profitRate30d;

    /**
     * 胜率（%）
     */
    private BigDecimal winRate;

    /**
     * 胜率（%）
     */
    private String avatar;

    /**
     * 胜率（%）
     */
    private String nickname;

    private BigDecimal followerCommissionRate;
}
