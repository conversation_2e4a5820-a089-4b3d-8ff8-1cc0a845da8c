package vip.xiaonuo.business.contractorder.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import vip.xiaonuo.core.consts.RedisConstant;
import vip.xiaonuo.core.pojo.dto.ContractOrderInfoDTO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class ContractOrderHashOperations {
    private HashOperations<String, String, String> orderHashOperations;

    @Resource
    public void setStringRedisTemplate(StringRedisTemplate redisTemplate) {
        orderHashOperations = redisTemplate.opsForHash();
    }

    public ContractOrderInfoDTO get(String symbol, String orderNo) {
        String value = orderHashOperations.get(RedisConstant.CONTRACT_ORDER + symbol, orderNo);
        if (StrUtil.isEmpty(value)) {
            return null;
        }
        return JSON.parseObject(value, ContractOrderInfoDTO.class);
    }

    public void put(String symbol, String orderNo, ContractOrderInfoDTO contractOrderInfoDTO) {
        orderHashOperations.put(RedisConstant.CONTRACT_ORDER + symbol, orderNo, JSON.toJSONString(contractOrderInfoDTO));
    }

    public void delete(String symbol, String orderNo) {
        orderHashOperations.delete(RedisConstant.CONTRACT_ORDER + symbol, orderNo);
    }

    public List<ContractOrderInfoDTO> values(String symbol) {
        List<ContractOrderInfoDTO> contractOrderInfoDTOS = new ArrayList<>();
        List<String> values = orderHashOperations.values(RedisConstant.CONTRACT_ORDER + symbol);
        if (CollectionUtil.isEmpty(values)) {
            return contractOrderInfoDTOS;
        }
        for (String v : values) {
            contractOrderInfoDTOS.add(JSON.parseObject(v, ContractOrderInfoDTO.class));
        }
        return contractOrderInfoDTOS;
    }
}
