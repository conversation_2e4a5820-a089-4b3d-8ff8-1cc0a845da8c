/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.marketadjustitem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.marketadjustitem.entity.MarketAdjustItem;
import vip.xiaonuo.business.marketadjustitem.enums.MarketAdjustItemExceptionEnum;
import vip.xiaonuo.business.marketadjustitem.mapper.MarketAdjustItemMapper;
import vip.xiaonuo.business.marketadjustitem.param.MarketAdjustItemParam;
import vip.xiaonuo.business.marketadjustitem.service.MarketAdjustItemService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.List;

/**
 * 市场调整表service接口实现类
 *
 * <AUTHOR>
 * @date 2025-07-09 21:54:45
 */
@Service
public class MarketAdjustItemServiceImpl extends ServiceImpl<MarketAdjustItemMapper, MarketAdjustItem> implements MarketAdjustItemService {

    @Override
    public PageResult<MarketAdjustItem> page(MarketAdjustItemParam marketAdjustItemParam) {
        QueryWrapper<MarketAdjustItem> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(marketAdjustItemParam)) {

            // 根据交易类型表示,如ETH_BTC 查询
            if (ObjectUtil.isNotEmpty(marketAdjustItemParam.getSymbol())) {
                queryWrapper.lambda().eq(MarketAdjustItem::getSymbol, marketAdjustItemParam.getSymbol());
            }
            // 根据生效秒数 查询
            if (ObjectUtil.isNotEmpty(marketAdjustItemParam.getSeconds())) {
                queryWrapper.lambda().eq(MarketAdjustItem::getSeconds, marketAdjustItemParam.getSeconds());
            }
            // 根据剩余秒数 查询
            if (ObjectUtil.isNotEmpty(marketAdjustItemParam.getLeftSeconds())) {
                queryWrapper.lambda().eq(MarketAdjustItem::getLeftSeconds, marketAdjustItemParam.getLeftSeconds());
            }
            // 根据调整值 查询
            if (ObjectUtil.isNotEmpty(marketAdjustItemParam.getValue())) {
                queryWrapper.lambda().eq(MarketAdjustItem::getValue, marketAdjustItemParam.getValue());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<MarketAdjustItem> list(MarketAdjustItemParam marketAdjustItemParam) {
        return this.list();
    }

    @Override
    public void add(MarketAdjustItemParam marketAdjustItemParam) {
        MarketAdjustItem marketAdjustItem = new MarketAdjustItem();
        BeanUtil.copyProperties(marketAdjustItemParam, marketAdjustItem);
        this.save(marketAdjustItem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<MarketAdjustItemParam> marketAdjustItemParamList) {
        marketAdjustItemParamList.forEach(marketAdjustItemParam -> {
            MarketAdjustItem marketAdjustItem = this.queryMarketAdjustItem(marketAdjustItemParam);
            this.removeById(marketAdjustItem.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(MarketAdjustItemParam marketAdjustItemParam) {
        MarketAdjustItem marketAdjustItem = this.queryMarketAdjustItem(marketAdjustItemParam);
        BeanUtil.copyProperties(marketAdjustItemParam, marketAdjustItem);
        this.updateById(marketAdjustItem);
    }

    @Override
    public MarketAdjustItem detail(MarketAdjustItemParam marketAdjustItemParam) {
        return this.queryMarketAdjustItem(marketAdjustItemParam);
    }

    /**
     * 获取市场调整表
     *
     * <AUTHOR>
     * @date 2025-07-09 21:54:45
     */
    private MarketAdjustItem queryMarketAdjustItem(MarketAdjustItemParam marketAdjustItemParam) {
        MarketAdjustItem marketAdjustItem = this.getById(marketAdjustItemParam.getId());
        if (ObjectUtil.isNull(marketAdjustItem)) {
            throw new ServiceException(MarketAdjustItemExceptionEnum.NOT_EXIST);
        }
        return marketAdjustItem;
    }
}
