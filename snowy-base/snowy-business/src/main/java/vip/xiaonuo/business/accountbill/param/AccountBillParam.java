package vip.xiaonuo.business.accountbill.param;

import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;

/**
* 账户表参数类
 *
 * <AUTHOR>
 * @date 2022-04-06 13:49:58
*/
@Data
public class AccountBillParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 账户
     */
    private String account;

    /**
     * 操作类型
     */
    private String optType;

    /**
     * 玩家ID
     */
    private Long playerId;

    /**
     * 类型(1:币币账户;2:合约账户;3:现货账户)
     */
    private Integer type;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * 变动之后余额
     */
    private BigDecimal balanceAfter;

    /**
     * 变动余额
     */
    private BigDecimal balanceChanged;

    /**
     * 业务号
     */
    private String businessNo;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 冻结变动之后余额
     */
    private BigDecimal frozenAfter;

    /**
     * 冻结变动余额
     */
    private BigDecimal frozenChanged;

}
