package vip.xiaonuo.business.statistic.service.impl;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vip.xiaonuo.business.coinorder.result.CoinOrderStatisticModel;
import vip.xiaonuo.business.coinorder.service.CoinOrderService;
import vip.xiaonuo.business.contractorder.entity.ContractOrder;
import vip.xiaonuo.business.contractorder.result.ContractOrderStatisticModel;
import vip.xiaonuo.business.contractorder.service.ContractOrderService;
import vip.xiaonuo.business.contractspotorder.controller.service.ContractSpotOrderService;
import vip.xiaonuo.business.player.result.PlayerStatisticModel;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.spotorder.result.SpotOrderStatisticModel;
import vip.xiaonuo.business.spotorder.service.SpotOrderService;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.business.statistic.pojo.vo.HomeStatisticVo;
import vip.xiaonuo.business.statistic.service.StatisticService;
import vip.xiaonuo.business.transaction.result.TransactionStatisticModel;
import vip.xiaonuo.business.transaction.service.TransactionService;
import vip.xiaonuo.business.user.service.SysUserService;
import vip.xiaonuo.business.userproductorder.result.UserProductOrderStatisticModel;
import vip.xiaonuo.business.userproductorder.service.UserProductOrderService;
import vip.xiaonuo.core.util.DateUtil;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 统计
 */
@Service
public class StatisticServiceImpl implements StatisticService {
    @Autowired
    TransactionService transactionService;
    @Autowired
    PlayerService playerService;
    @Autowired
    SysUserService sysUserService;
    @Autowired
    UserProductOrderService userProductOrderService;
    @Resource
    private SpotOrderService spotOrderService;
    @Resource
    private ContractOrderService contractOrderService;
    @Resource
    private CoinOrderService coinOrderService;
    @Resource
    private ContractSpotOrderService contractSpotOrderService;

    /**
     * 首页统计
     *
     * @return
     */
    @Override
    public HomeStatisticVo homeStatistic(HomeStatisticParam param) {
        String day = DateUtil.format(new Date(), DateUtil.DATEFORMATDAY);
        param.setDate(day);

        HomeStatisticVo vo = new HomeStatisticVo();

        PlayerStatisticModel playerStatisticModel = playerService.findUserStatistic(param);
        if (playerStatisticModel != null) {
            BeanUtils.copyProperties(playerStatisticModel, vo);
        }

        vo.setGeneralAgentCount(sysUserService.findAgentStatistic(param));

        TransactionStatisticModel transactionStatisticModel = transactionService.findrechargeDayAccountStatistic(param);
        if (transactionStatisticModel != null) {
            BeanUtils.copyProperties(transactionStatisticModel, vo);
        }

        UserProductOrderStatisticModel userProductOrderStatisticModel = userProductOrderService.findUserProductOrderStatistic(param);
        if (userProductOrderStatisticModel != null) {
            BeanUtils.copyProperties(userProductOrderStatisticModel, vo);
        }

        SpotOrderStatisticModel spotOrderStatisticModel = spotOrderService.findSpotOrderStatistic(param);
        if (spotOrderStatisticModel != null) {
            BeanUtils.copyProperties(spotOrderStatisticModel, vo);
        }
        ContractOrderStatisticModel contractOrderStatisticModel = contractSpotOrderService.findSpotOrderStatistic(param);
        if (contractOrderStatisticModel != null) {
            BeanUtils.copyProperties(contractOrderStatisticModel, vo);
        }
        CoinOrderStatisticModel coinOrderStatistic = coinOrderService.findCoinOrderStatistic(param);
        if (coinOrderStatistic != null) {
            BeanUtils.copyProperties(coinOrderStatistic, vo);
        }

        return vo;
    }
}
