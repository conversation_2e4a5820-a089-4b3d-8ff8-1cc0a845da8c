package vip.xiaonuo.business.playerinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import vip.xiaonuo.business.playerinfo.entity.PlayerInfo;
import vip.xiaonuo.business.playerinfo.pojo.dto.FindPlayerInfoForPageDto;
import vip.xiaonuo.business.playerinfo.pojo.vo.FindPlayerInfoForPageVo;

/**
 * 认证审核
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:38
 */
public interface PlayerInfoMapper extends BaseMapper<PlayerInfo> {
    /**
     * 分页查询认证信息
     *
     * @param info
     * @return
     */
    Page<FindPlayerInfoForPageVo> findPlayerInfoForPage(Page<PlayerInfo> defaultPage, @Param("ew") FindPlayerInfoForPageDto info);
}
