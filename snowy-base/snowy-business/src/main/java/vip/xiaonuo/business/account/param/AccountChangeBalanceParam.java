package vip.xiaonuo.business.account.param;

import com.google.common.base.Preconditions;
import lombok.Builder;
import lombok.Data;
import org.springframework.util.Assert;
import vip.xiaonuo.business.account.entity.Account;
import vip.xiaonuo.business.account.enums.AccountExceptionEnum;
import vip.xiaonuo.business.accountbill.entity.AccountBill;
import vip.xiaonuo.business.accountbill.enums.AccountBillBusinessTypeEnum;
import vip.xiaonuo.business.accountbill.enums.AccountBillOptTypeEnum;
import vip.xiaonuo.core.exception.ServiceException;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
public class AccountChangeBalanceParam {
    /**
     * 玩家ID
     */
    private Long playerId;

    /**
     * 类型(1:币币账户;2:合约账户;3:现货账户)
     */
    private Integer type;

    /**
     * 币种代码
     */
    private String currencyCode;
    //业务类型
    private AccountBillBusinessTypeEnum businessType;
    //业务流水号（ID）
    private String businessNo;
    //操作类型
    private AccountBillOptTypeEnum optType;
    //变动金额（扣减为负数，添加为正数）
    private BigDecimal balance;
    //冻结操作类型
    private AccountBillOptTypeEnum frozenOptType;
    //变动冻结金额（扣减为负数，添加为正数）
    private BigDecimal frozen;
    //备注---可不填
    private String remarks;
    //变动人
    private Long updateUser;
    //极速规则id
    private String ruleId;


    public void valid() {
        if (balance == null) {
            balance = BigDecimal.ZERO;
            optType = AccountBillOptTypeEnum.N;
        }
        if (frozen == null) {
            frozen = BigDecimal.ZERO;
            frozenOptType = AccountBillOptTypeEnum.N;
        }

        Preconditions.checkNotNull(playerId, "Player Id should not be null");
        Preconditions.checkNotNull(type, "Type Id should not be null");
        Assert.hasLength(currencyCode, "Currency Code Id should not be null");
        Preconditions.checkNotNull(businessType, "Business Type should not be null");
        Preconditions.checkNotNull(optType, "Opt Type No should not be null");
        Assert.hasLength(businessNo, "Business No should not be null");
        Preconditions.checkNotNull(frozenOptType, "Frozen Opt Type No should not be null");

        //转成大写
        currencyCode = currencyCode.toUpperCase();
    }

    public AccountBill toAccountBill(Account account) {
        AccountBill accountBill = new AccountBill();
        accountBill.setPlayerId(account.getPlayerId());
        accountBill.setType(account.getType());
        accountBill.setCurrencyCode(account.getCurrencyCode());
        accountBill.setAccountId(account.getId());
        accountBill.setBusinessNo(businessNo);
        accountBill.setBusinessType(businessType);
        accountBill.setOptType(optType);
        accountBill.setFrozenOptType(frozenOptType);
        accountBill.setCreateUser(updateUser);
        accountBill.setUpdateUser(updateUser);
        accountBill.setCreateTime(new Date());
        accountBill.setUpdateTime(new Date());

        if (!BigDecimal.ZERO.equals(getBalance())) {
            BigDecimal afterAmount = BigDecimal.ZERO;
            if (optType == AccountBillOptTypeEnum.ADD) {
                afterAmount = account.getBalance().add(getBalance());
                accountBill.setBalanceAfter(afterAmount);
            }
            if (optType == AccountBillOptTypeEnum.SUB) {
                afterAmount = account.getBalance().subtract(getBalance());
                accountBill.setBalanceAfter(afterAmount);
            }
            if (BigDecimal.ZERO.compareTo(afterAmount) > 0) {
                throw new ServiceException(AccountExceptionEnum.INSUFFICIENT_BALANCE);
            }
            accountBill.setBalanceChanged(getBalance());
        }
        if (!BigDecimal.ZERO.equals(getFrozen())) {
            BigDecimal afterAmount = BigDecimal.ZERO;
            if (frozenOptType == AccountBillOptTypeEnum.ADD) {
                afterAmount = account.getFrozenBalance().add(getFrozen());
                accountBill.setFrozenAfter(afterAmount);
            }
            if (frozenOptType == AccountBillOptTypeEnum.SUB) {
                afterAmount = account.getFrozenBalance().subtract(getFrozen());
                accountBill.setFrozenAfter(afterAmount);
            }
            if (BigDecimal.ZERO.compareTo(afterAmount) > 0) {
                throw new ServiceException(AccountExceptionEnum.FROZEN_INSUFFICIENT_BALANCE);
            }
            accountBill.setFrozenChanged(getFrozen());
        }
        return accountBill;
    }
}
