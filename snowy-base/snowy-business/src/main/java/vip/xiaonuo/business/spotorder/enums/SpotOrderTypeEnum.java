package vip.xiaonuo.business.spotorder.enums;

import lombok.Getter;

@Getter
public enum SpotOrderTypeEnum {
//         类型(1:买涨;2:买跌;)
    /**
     * 买涨
     */
    TYPE1(1, "买涨"),

    /**
     * 买跌
     */
    TYPE2(2, "买跌");

    private final Integer code;

    private final String message;

    SpotOrderTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static boolean isExists(Integer code) {
        for (SpotOrderTypeEnum typeEnum : values()) {
            if (code.equals(typeEnum.getCode())) {
                return true;
            }
        }
        return false;
    }

}
