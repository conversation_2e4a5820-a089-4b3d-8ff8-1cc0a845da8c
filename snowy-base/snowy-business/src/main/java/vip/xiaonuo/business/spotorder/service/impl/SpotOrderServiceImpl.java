/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.spotorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.account.enums.AccountTypeEnum;
import vip.xiaonuo.business.account.param.AccountChangeBalanceParam;
import vip.xiaonuo.business.account.service.AccountService;
import vip.xiaonuo.business.accountbill.enums.AccountBillBusinessTypeEnum;
import vip.xiaonuo.business.accountbill.enums.AccountBillOptTypeEnum;
import vip.xiaonuo.business.core.cache.MappingCache;
import vip.xiaonuo.business.currency.entity.Currency;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.dict.service.SysDictDataService;
import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.business.market.enums.MarketExceptionEnum;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.spotorder.entity.SpotOrder;
import vip.xiaonuo.business.spotorder.enums.SpotOrderExceptionEnum;
import vip.xiaonuo.business.spotorder.enums.SpotOrderStatusEnum;
import vip.xiaonuo.business.spotorder.enums.SpotOrderTypeEnum;
import vip.xiaonuo.business.spotorder.mapper.SpotOrderMapper;
import vip.xiaonuo.business.spotorder.param.PlaceSpotOrderModel;
import vip.xiaonuo.business.spotorder.param.QuerySpotOrderModel;
import vip.xiaonuo.business.spotorder.param.SpotOrderParam;
import vip.xiaonuo.business.spotorder.result.EndSpotOrderResult;
import vip.xiaonuo.business.spotorder.result.SpotOrderStatisticModel;
import vip.xiaonuo.business.spotorder.result.SpotOrderVo;
import vip.xiaonuo.business.spotorder.service.SpotOrderService;
import vip.xiaonuo.business.spotorder.service.SpotScaleHelper;
import vip.xiaonuo.business.spotrule.entity.SpotRule;
import vip.xiaonuo.business.spotrule.service.SpotRuleService;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.core.consts.DictConstant;
import vip.xiaonuo.core.consts.RedisConstant;
import vip.xiaonuo.core.context.constant.ConstantContextHolder;
import vip.xiaonuo.core.enums.SerialNoEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.RedisDelayMessage;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.redis.DelayMessageType;
import vip.xiaonuo.core.redis.RedisHelper;
import vip.xiaonuo.core.redis.RedissonDelayQueueClient;
import vip.xiaonuo.core.util.DateUtil;
import vip.xiaonuo.core.util.DpDataUtils;
import vip.xiaonuo.core.util.MarketUtils;
import vip.xiaonuo.core.util.UUIDUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 现货订单service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-14 18:27:46
 */
@Service
public class SpotOrderServiceImpl extends ServiceImpl<SpotOrderMapper, SpotOrder> implements SpotOrderService, InitializingBean {
    private Logger logger = LoggerFactory.getLogger(SpotOrderServiceImpl.class);
    @Resource
    private MarketService marketService;
    @Resource
    private SpotRuleService spotRuleService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private RedisHelper redisHelper;
    @Resource
    private AccountService accountService;
    @Resource
    private CurrencyService currencyService;
    @Resource
    private PlayerService playerService;
    @Resource
    private SpotScaleHelper spotScaleHelper;
    @Autowired(required = false)
    private RedissonDelayQueueClient redissonDelayQueueClient;
    @Resource
    private MappingCache mappingCache;

    @Override
    public PageResult<SpotOrder> page(SpotOrderParam spotOrderParam) {
        Page<SpotOrder> spotOrderIPage = getBaseMapper().queryPage(PageFactory.defaultPage(), spotOrderParam);
        return new PageResult<>(spotOrderIPage);
    }

    @Override
    public PageResult<SpotOrderVo> page(Long playerId, QuerySpotOrderModel model) {
//        QueryWrapper<SpotOrder> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lambda().eq(SpotOrder::getPlayerId, playerId);
//        if (ObjectUtil.isNotNull(model)) {
//            // 根据0:持仓,1:平仓 查询
//            if (ObjectUtil.isNotEmpty(model.getStatus())) {
//                queryWrapper.lambda().eq(SpotOrder::getStatus, model.getStatus());
//            }
//        }
//        queryWrapper.lambda().orderByDesc(SpotOrder::getCreateTime);
//        PageResult<SpotOrder> pageResult = new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
//        PageResult<SpotOrderVo> page = ConvertUtils.convert(pageResult, SpotOrderVo.class);
        Page<SpotOrderVo> page = getBaseMapper().getPage(PageFactory.defaultPage(), model.getStatus(), playerId);
        List<Currency> currencies = currencyService.list();
        for (SpotOrderVo spotOrderVo : page.getRecords()) {
            currencies.stream().filter(p -> p.getCode().equalsIgnoreCase(MarketUtils.getCode(spotOrderVo.getSymbol()))).findAny().ifPresent(currency -> spotOrderVo.setLogo(currency.getLogo()));
            if (SpotOrderStatusEnum.STATUS0.getCode().equals(spotOrderVo.getStatus()) && spotOrderVo.getOrderTime() != null) {
                spotOrderVo.setSurplusSeconds(spotOrderVo.getSeconds() - DateUtil.differentSeconds(spotOrderVo.getOrderTime(), new Date()));
            }
        }
        return new PageResult<SpotOrderVo>(page);
    }

    @Override
    public List<SpotOrder> list(SpotOrderParam spotOrderParam) {
        return this.list();
    }

    @Override
    public void add(SpotOrderParam spotOrderParam) {
        SpotOrder spotOrder = new SpotOrder();
        BeanUtil.copyProperties(spotOrderParam, spotOrder);
        this.save(spotOrder);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<SpotOrderParam> spotOrderParamList) {
        spotOrderParamList.forEach(spotOrderParam -> {
            SpotOrder spotOrder = this.querySpotOrder(spotOrderParam);
            this.removeById(spotOrder.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(SpotOrderParam spotOrderParam) {
        SpotOrder spotOrder = this.querySpotOrder(spotOrderParam);
        BeanUtil.copyProperties(spotOrderParam, spotOrder);
        this.updateById(spotOrder);
    }

    @Override
    public SpotOrder detail(SpotOrderParam spotOrderParam) {
        return this.querySpotOrder(spotOrderParam);
    }

    @Resource
    private SysDictDataService sysDictDataService;

    @Override
    @Transactional
    public SpotOrder placeOrder(Long playerId, PlaceSpotOrderModel model) {
        SpotRule spotRule = spotRuleService.getById(model.getRuleId());
        if (spotRule == null) {
            throw new ServiceException(SpotOrderExceptionEnum.NOT_EXIST);
        }
        Market market = marketService.getMarketBySymbol(model.getSymbol());
        if (market == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_EXIST);
        }
        if (market.getTransactionSwitch() != 1) {
            throw new ServiceException(MarketExceptionEnum.TRADING_CLOSED);
        }
        BigDecimal lastPrice = DpDataUtils.getLatestPrice(redisTemplate, market.getSymbol());
        if (lastPrice == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_LAST_PRICE);
        }
        if (BigDecimal.ZERO.compareTo(model.getQuantity()) >= 0) {
            throw new ServiceException(SpotOrderExceptionEnum.QUANTITY_ERROR);
        }
        if ("1".equals(spotRule.getMode())) {
            if (spotRule.getMinQuantity().compareTo(model.getQuantity()) > 0) {
                throw new ServiceException(SpotOrderExceptionEnum.QUANTITY_ERROR);
            }
            if (spotRule.getMaxQuantity() != null && spotRule.getMaxQuantity().compareTo(model.getQuantity()) < 0) {
                throw new ServiceException(SpotOrderExceptionEnum.QUANTITY_ERROR);
            }
        }
        if (!SpotOrderTypeEnum.isExists(model.getType())) {
            throw new ServiceException(SpotOrderExceptionEnum.TYPE_NOT_EXIST);
        }
        BigDecimal feeRate = sysDictDataService.getCommonConfigAsDecimal(DictConstant.SPOT_FEE);
        if (feeRate == null) {
            throw new ServiceException(MarketExceptionEnum.FEE_RATE_NOT_EXIST);
        }
        feeRate = feeRate.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(playerId));

        Integer playerSpotScale = playerService.lambdaQuery().select(Player::getSpotScale).eq(Player::getId, playerId).one().getSpotScale();
        String orderNo = UUIDUtil.createOrderId(SerialNoEnum.S);
        SpotOrder spotOrder = new SpotOrder();
        spotOrder.setPlayerId(playerId);
        spotOrder.setType(model.getType());
        spotOrder.setSymbol(market.getSymbol());
        spotOrder.setRuleId(spotRule.getId());
        spotOrder.setSeconds(spotRule.getSeconds());
        spotOrder.setOrderNo(orderNo);
        spotOrder.setOrderPrice(lastPrice);
        spotOrder.setOrderTime(new Date());
        spotOrder.setQuantity(model.getQuantity());
        spotOrder.setStatus(SpotOrderStatusEnum.STATUS0.getCode());
        spotOrder.setCreateTime(new Date());
        spotOrder.setFollowerId(model.getFollowerId());
        spotOrder.setFollowerCommissionRate(model.getFollowerCommissionRate());
        spotOrder.setFollowerContractSpotOrderId(model.getFollowerContractSpotOrderId());
        //计算手续费        //保留两位小数且四舍五入
        spotOrder.setOrderFee(spotOrder.getQuantity().multiply(feeRate).setScale(2, BigDecimal.ROUND_HALF_UP));
//        spotOrder.setOrderFee(BigDecimal.ZERO);
        //控制订单输赢（如果用户设置了输赢比例，则订单默认输赢改为默认）
        if (ObjectUtil.isNull(model.getControlPre())) {
            if (playerSpotScale >= 0) {
                spotOrder.setControl(playerSpotScale == 0 ? 0 : (playerSpotScale == 100 ? 1 : -1));
            }
        } else {
            spotOrder.setControl(model.getControlPre());
        }
        save(spotOrder);
        //加上手续费
//        BigDecimal balance = spotOrder.getQuantity().add(spotOrder.getOrderFee());
        //变更余额
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder balanceParamBuilder = AccountChangeBalanceParam.builder()
                .playerId(spotOrder.getPlayerId())
                .type(AccountTypeEnum.TYPE1.getCode())
                .currencyCode(MarketUtils.getBaseCode(spotOrder.getSymbol()))
                .businessType(AccountBillBusinessTypeEnum.SPOT_ORDER)
                .optType(AccountBillOptTypeEnum.SUB)
                .businessNo(orderNo)
                .balance(spotOrder.getQuantity())
                //增加冻结金额
                .frozenOptType(AccountBillOptTypeEnum.ADD)
                .frozen(spotOrder.getQuantity())
                .updateUser(spotOrder.getPlayerId())
                .ruleId(model.getRuleId());
        accountService.changeBalance(balanceParamBuilder.build());

        //存入redis，等待过期；提前1秒处理
//        redisHelper.set(RedisConstant.SPOT_ORDER + spotOrder.getOrderNo(), spotOrder.getId(), spotRule.getSeconds() - 1);
        redissonDelayQueueClient.addDelay(new RedisDelayMessage<>(DelayMessageType.SPOT_ORDER, spotOrder.getOrderNo(), spotRule.getSeconds().longValue(), TimeUnit.SECONDS));
        long diff = spotOrder.getSeconds().longValue() - 1;
        logger.info("{}极速下单成功,间隔:{}", spotOrder.getOrderNo(), diff);
        mappingCache.put(RedisConstant.SPOT_ORDER + spotOrder.getOrderNo(), spotOrder, diff);
        return spotOrder;
    }

    @Override
    public SpotOrder getSpotOrderForLock(String orderNo) {
        return lambdaQuery().eq(SpotOrder::getOrderNo, orderNo).last(" for update").one();
    }

    private SpotOrder getSpotOrderForLock(Long id) {
        return lambdaQuery().eq(SpotOrder::getId, id).last(" for update").one();
    }

    private boolean calcWin(SpotOrder spotOrder, BigDecimal lastPrice) {
        boolean isWin = false;
        BigDecimal diff = spotOrder.getOrderPrice().subtract(lastPrice);
        if (spotOrder.getType().equals(SpotOrderTypeEnum.TYPE1.getCode())) {
            if (diff.compareTo(BigDecimal.ZERO) < 0) {
                isWin = true;
            }
        }
        if (spotOrder.getType().equals(SpotOrderTypeEnum.TYPE2.getCode())) {
            isWin = diff.compareTo(BigDecimal.ZERO) > 0;
        }
        return isWin;
    }

    @Override
    @Transactional
    public SpotOrder finishOrder(String orderNo, SpotOrder spotOrder) {
        if (spotOrder == null || !SpotOrderStatusEnum.STATUS0.getCode().equals(spotOrder.getStatus())) {
            throw new ServiceException(SpotOrderExceptionEnum.NOT_EXIST);
        }
//        logger.info("{}极速订单结算 {}", orderNo, JSON.toJSONString(spotOrder));
        Market market = marketService.getMarketBySymbol(spotOrder.getSymbol());
        if (market == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_EXIST);
        }
        BigDecimal lastPrice = DpDataUtils.getLatestPrice(redisTemplate, spotOrder.getSymbol());
        if (lastPrice == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_LAST_PRICE);
        }
        SpotRule spotRule = spotRuleService.getById(spotOrder.getRuleId());
        if (spotRule == null) {
            //兼容老订单
            if (spotOrder.getProfitability() == null) {
                throw new ServiceException(SpotOrderExceptionEnum.NOT_EXIST);
            }
        }

        lastPrice = MarketUtils.getLastPrice(spotOrder.getType(), spotOrder.getOrderPrice(), lastPrice, market.getPriceDecimal());
        boolean isWin = calcWin(spotOrder, lastPrice);
        spotOrder.setClosePrice(lastPrice);
        spotOrder.setCloseTime(new Date());
        spotOrder.setStatus(SpotOrderStatusEnum.STATUS1.getCode());
        //风控处理
        isWin = riskControl(spotOrder, isWin, market.getPriceDecimal());
        BigDecimal profitability = getProfitability(spotOrder, spotRule, isWin);
        spotOrder.setProfitability(profitability);
        BigDecimal profit = spotOrder.getQuantity().multiply(spotOrder.getProfitability().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_DOWN));
        spotOrder.setProfit(profit);
        //加上本金,扣除手续费
        BigDecimal balanceChange = profit.add(spotOrder.getQuantity()).subtract(spotOrder.getOrderFee());

        AccountBillOptTypeEnum optType = AccountBillOptTypeEnum.ADD;
        if (BigDecimal.ZERO.compareTo(balanceChange) >= 0) {
            optType = AccountBillOptTypeEnum.N;
        }
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(spotOrder.getPlayerId()));
        //变更余额
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder balanceParamBuilder = AccountChangeBalanceParam.builder()
                .playerId(spotOrder.getPlayerId())
                .type(AccountTypeEnum.TYPE1.getCode())
                .currencyCode(MarketUtils.getBaseCode(spotOrder.getSymbol()))
                .businessType(AccountBillBusinessTypeEnum.SPOT_WIN)
                .businessNo(orderNo)
                .balance(balanceChange)
                .optType(optType)
                .updateUser(spotOrder.getPlayerId())
                //变更冻结金额
                .frozen(spotOrder.getQuantity())
                .frozenOptType(AccountBillOptTypeEnum.SUB);
        accountService.changeBalance(balanceParamBuilder.build());

        spotOrder.setUpdateUser((long) -1);
        spotOrder.setUpdateTime(new Date());
//        updateById(spotOrder);
        lambdaUpdate().set(SpotOrder::getUpdateTime, spotOrder.getUpdateTime())
                .set(SpotOrder::getUpdateUser, spotOrder.getUpdateUser())
                .set(SpotOrder::getClosePrice, spotOrder.getClosePrice())
                .set(SpotOrder::getCloseTime, spotOrder.getCloseTime())
                .set(SpotOrder::getStatus, spotOrder.getStatus())
                .set(SpotOrder::getProfit, spotOrder.getProfit())
                .set(SpotOrder::getProfitability, spotOrder.getProfitability())
                .eq(SpotOrder::getId, spotOrder.getId())
                .update();
        // 8级分销
        if (BigDecimal.ZERO.compareTo(profit) < 0) {
            Player player = playerService.getById(spotOrder.getPlayerId());
            if (ObjectUtil.isNotNull(player)) {
                String inviteSeqStr = player.getInviteSeq();
                if (inviteSeqStr != null && !inviteSeqStr.isEmpty()) {
                    // 先加入直接邀请人的ID， 上级的上级
                    List<Long> inviteSeqList = Arrays.stream(inviteSeqStr.split(","))
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    List<BigDecimal> level = ConstantContextHolder.get8LevelCommissionSetting();
                    AccountBillOptTypeEnum optTypeNew = AccountBillOptTypeEnum.ADD;
                    for (int i = 0; i < inviteSeqList.size(); i++) {
                        Long inviterId = inviteSeqList.get(i);
                        BigDecimal commissionRate = level.size() > i ? level.get(i) : BigDecimal.ZERO;

                        if (commissionRate.compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal commission = profit.multiply(commissionRate);

                            if (commission.compareTo(BigDecimal.ZERO) > 0) {
                                //变更余额
                                AccountChangeBalanceParam.AccountChangeBalanceParamBuilder balanceParamBuilderNew = AccountChangeBalanceParam.builder()
                                        .playerId(inviterId)
                                        .type(AccountTypeEnum.TYPE1.getCode())
                                        .currencyCode(MarketUtils.getBaseCode(spotOrder.getSymbol()))
                                        .businessType(AccountBillBusinessTypeEnum.REFERRAL_REWARD)
                                        .businessNo(orderNo)
                                        .balance(commission)
                                        .optType(optTypeNew)
                                        .updateUser(spotOrder.getPlayerId());
                                accountService.changeBalance(balanceParamBuilderNew.build());
                            }
                        }
                    }
                }
            }
        }

        logger.info("{}极速订单结算完成,{},{},{}", orderNo, spotOrder.getSeconds(), cn.hutool.core.date.DateUtil.format(spotOrder.getOrderTime(), DatePattern.NORM_DATETIME_FORMAT), cn.hutool.core.date.DateUtil.format(spotOrder.getCloseTime(), DatePattern.NORM_DATETIME_FORMAT));
        return spotOrder;
    }

    private BigDecimal getProfitability(SpotOrder spotOrder, SpotRule spotRule, boolean isWin) {
        if (isWin) {
            if (spotRule == null) {
                return spotOrder.getProfitability();
            }
            if (spotRule.getMaxProfitRate().compareTo(spotRule.getMinProfitRate()) == 0) {
                return spotRule.getMinProfitRate();
            }
            return RandomUtil.randomBigDecimal(spotRule.getMinProfitRate(), spotRule.getMaxProfitRate());
        } else {
            if (spotRule == null) {
                return new BigDecimal("100").negate();
            }
            if (spotRule.getMaxLossRate().compareTo(spotRule.getMinLossRate()) == 0) {
                return spotRule.getMinLossRate().negate();
            }
            return RandomUtil.randomBigDecimal(spotRule.getMinLossRate(), spotRule.getMaxLossRate()).negate();
        }
    }

    /**
     * 风控处理
     *
     * @param spotOrder
     * @param isWin
     */
    private boolean riskControl(SpotOrder spotOrder, boolean isWin, Integer priceDecimal) {
        boolean control = false;
        Integer orderControl = spotOrder.getControl();
        if (orderControl != -1) {
            control = true;
            //订单上控制
            isWin = orderControl(spotOrder, isWin, priceDecimal, orderControl);
        }

        Map<String, String> playerSpotScaleMap = spotScaleHelper.getPlayerSpotScaleMap(spotOrder.getPlayerId());
        Integer playerSpotScale = playerService.lambdaQuery().select(Player::getSpotScale).eq(Player::getId, spotOrder.getPlayerId()).one().getSpotScale();
        if (playerSpotScale != -1 && !control) {
            /*如果玩家上设置了风控*/
            control = true;
            orderControl = spotScaleHelper.getSpotScaleControl(playerSpotScaleMap, playerSpotScale);
            isWin = orderControl(spotOrder, isWin, priceDecimal, orderControl);
        }
        Map.Entry<String, String> entry = spotScaleHelper.getEntryByQuantity(spotOrder.getQuantity());
        Map<String, String> spotScaleMap = null;
        if (entry != null) {
            spotScaleMap = spotScaleHelper.getSpotScaleMap(entry.getKey());
        }
        if (!control && spotScaleMap != null) {
            control = true;
            orderControl = spotScaleHelper.getSpotScaleControl(spotScaleMap, Integer.parseInt(entry.getValue()));
            isWin = orderControl(spotOrder, isWin, priceDecimal, orderControl);
        }
        spotScaleHelper.putPlayerSpotScaleNumber(spotOrder.getPlayerId(), playerSpotScaleMap, isWin);
        if (spotScaleMap != null) {
            spotScaleHelper.putSpotScaleNumber(entry.getKey(), spotScaleMap, isWin);
        }
        return isWin;
    }

    private boolean orderControl(SpotOrder spotOrder, boolean isWin, Integer priceDecimal, Integer orderControl) {
        if (orderControl == 0 && isWin) {
            //设置成了必输，结果为赢
            BigDecimal lastPrice = MarketUtils.getLastPrice(spotOrder.getType(), spotOrder.getOrderPrice(), spotOrder.getOrderPrice(), priceDecimal);
            spotOrder.setClosePrice(lastPrice);
            isWin = calcWin(spotOrder, lastPrice);
        }
        if (orderControl == 1 && !isWin) {
            BigDecimal lastPrice = MarketUtils.getLastPriceAdd(spotOrder.getType(), spotOrder.getOrderPrice(), priceDecimal);
            spotOrder.setClosePrice(lastPrice);
            isWin = calcWin(spotOrder, lastPrice);
        }
        return isWin;
    }

    @Override
    public SpotOrderStatisticModel findSpotOrderStatistic(HomeStatisticParam param) {
        return getBaseMapper().findSpotOrderStatistic(param);
    }

    @Override
    public void editControl(Long id, Integer control) {
        SpotOrder spotOrder = getSpotOrderForLock(id);
        if (spotOrder == null || SpotOrderStatusEnum.STATUS1.getCode().equals(spotOrder.getStatus())) {
            throw new ServiceException(SpotOrderExceptionEnum.NOT_EXIST);
        }
        lambdaUpdate().set(SpotOrder::getControl, control).eq(SpotOrder::getId, id).update();
    }

    @Override
    public EndSpotOrderResult endSpotOrder(Long playerId, String orderNo) {
        SpotOrder spotOrder = (SpotOrder) mappingCache.get(RedisConstant.SPOT_ORDER + orderNo);
        if (spotOrder == null) {
            logger.info(orderNo + "内存没有，查库");
            spotOrder = lambdaQuery().eq(SpotOrder::getOrderNo, orderNo).eq(SpotOrder::getPlayerId, playerId).one();
            if (spotOrder == null) {
                throw new ServiceException(SpotOrderExceptionEnum.NOT_EXIST);
            }
        }
        int i = DateUtil.differentSeconds(spotOrder.getOrderTime(), new Date());
        int surplusSeconds = spotOrder.getSeconds() - i;
        //logger.info(orderNo + "endSpotOrder-----------" + surplusSeconds + "------------{}----{}", DateUtil.format(spotOrder.getOrderTime(), DatePattern.NORM_DATETIME_PATTERN), spotOrder.getStatus());
        if (surplusSeconds > 3) {
            mappingCache.put(RedisConstant.SPOT_ORDER + spotOrder.getOrderNo(), spotOrder, Integer.valueOf(surplusSeconds).longValue() - 1);
        }
        if (surplusSeconds <= 0) {
            mappingCache.remove(RedisConstant.SPOT_ORDER + spotOrder.getOrderNo());
        }
        if (SpotOrderStatusEnum.STATUS0.getCode().equals(spotOrder.getStatus()) && spotOrder.getOrderTime() != null) {
            if (surplusSeconds < -2) {
                try {
                    logger.info("{}极速订单没有及时结算,间隔{}", orderNo, surplusSeconds);
                    spotOrder = SpringUtil.getBean(SpotOrderService.class).finishOrder(orderNo, spotOrder);
                } catch (Exception e) {
                    logger.error(orderNo + "极速订单补偿结算异常", e);
                }
            }
        }
        if (SpotOrderStatusEnum.STATUS0.getCode().equals(spotOrder.getStatus()) && spotOrder.getOrderTime() != null) {
            BigDecimal lastPrice = DpDataUtils.getLatestPrice(redisTemplate, spotOrder.getSymbol());
            spotOrder.setClosePrice(lastPrice);
        }
        EndSpotOrderResult endSpotOrderResult = new EndSpotOrderResult();
        BeanUtil.copyProperties(spotOrder, endSpotOrderResult);
        return endSpotOrderResult;
    }

    @Override
    public List<SpotOrder> getOverdueOrder() {
        return baseMapper.getOverdueOrder(new Date());
    }

    /**
     * 获取现货订单
     *
     * <AUTHOR>
     * @date 2022-04-14 18:27:46
     */
    private SpotOrder querySpotOrder(SpotOrderParam spotOrderParam) {
        SpotOrder spotOrder = this.getById(spotOrderParam.getId());
        if (ObjectUtil.isNull(spotOrder)) {
            throw new ServiceException(SpotOrderExceptionEnum.NOT_EXIST);
        }
        return spotOrder;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
//        new Thread(()->{
//            try {
//                Thread.sleep(10*1000L);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            logger.info("====================");
//            redissonDelayQueueClient.addDelay(new RedisDelayMessage<>(DelayMessageType.SPOT_ORDER, "ggg", 10L, TimeUnit.SECONDS));
//        }).start();
    }
}
