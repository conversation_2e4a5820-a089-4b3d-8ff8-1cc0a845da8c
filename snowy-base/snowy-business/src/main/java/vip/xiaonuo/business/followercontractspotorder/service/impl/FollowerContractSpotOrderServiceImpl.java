/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.followercontractspotorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.enums.CommonStatusEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.business.followercontractspotorder.entity.FollowerContractSpotOrder;
import vip.xiaonuo.business.followercontractspotorder.enums.FollowerContractSpotOrderExceptionEnum;
import vip.xiaonuo.business.followercontractspotorder.mapper.FollowerContractSpotOrderMapper;
import vip.xiaonuo.business.followercontractspotorder.param.FollowerContractSpotOrderParam;
import vip.xiaonuo.business.followercontractspotorder.service.FollowerContractSpotOrderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;

/**
 * 跟单合约极速订单预设service接口实现类
 *
 * <AUTHOR>
 * @date 2025-07-22 14:38:50
 */
@Service
public class FollowerContractSpotOrderServiceImpl extends ServiceImpl<FollowerContractSpotOrderMapper, FollowerContractSpotOrder> implements FollowerContractSpotOrderService {

    @Override
    public PageResult<FollowerContractSpotOrder> page(FollowerContractSpotOrderParam followerContractSpotOrderParam) {
        QueryWrapper<FollowerContractSpotOrder> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(followerContractSpotOrderParam)) {

            // 根据玩家ID 查询
            if (ObjectUtil.isNotEmpty(followerContractSpotOrderParam.getFollowerId())) {
                queryWrapper.lambda().eq(FollowerContractSpotOrder::getFollowerId, followerContractSpotOrderParam.getFollowerId());
            }
            // 根据类型(1:买涨;2:买跌;) 查询
            if (ObjectUtil.isNotEmpty(followerContractSpotOrderParam.getType())) {
                queryWrapper.lambda().eq(FollowerContractSpotOrder::getType, followerContractSpotOrderParam.getType());
            }
            // 根据交易类型表示,如ETH_USDT 查询
            if (ObjectUtil.isNotEmpty(followerContractSpotOrderParam.getSymbol())) {
                queryWrapper.lambda().eq(FollowerContractSpotOrder::getSymbol, followerContractSpotOrderParam.getSymbol());
            }
            // 根据秒 查询
            if (ObjectUtil.isNotEmpty(followerContractSpotOrderParam.getSeconds())) {
                queryWrapper.lambda().eq(FollowerContractSpotOrder::getSeconds, followerContractSpotOrderParam.getSeconds());
            }
            // 根据下单时间 查询
            if (ObjectUtil.isNotEmpty(followerContractSpotOrderParam.getOrderTime())) {
                queryWrapper.lambda().eq(FollowerContractSpotOrder::getOrderTime, followerContractSpotOrderParam.getOrderTime());
            }
            // 根据下单数量 查询
            if (ObjectUtil.isNotEmpty(followerContractSpotOrderParam.getQuantity())) {
                queryWrapper.lambda().eq(FollowerContractSpotOrder::getQuantity, followerContractSpotOrderParam.getQuantity());
            }
            // 根据盈亏率 查询
            if (ObjectUtil.isNotEmpty(followerContractSpotOrderParam.getProfitability())) {
                queryWrapper.lambda().eq(FollowerContractSpotOrder::getProfitability, followerContractSpotOrderParam.getProfitability());
            }
            // 根据控制(-1:默认,0:亏,1:赢) 查询
            if (ObjectUtil.isNotEmpty(followerContractSpotOrderParam.getControl())) {
                queryWrapper.lambda().eq(FollowerContractSpotOrder::getControl, followerContractSpotOrderParam.getControl());
            }
            // 根据规则ID 查询
            if (ObjectUtil.isNotEmpty(followerContractSpotOrderParam.getRuleId())) {
                queryWrapper.lambda().eq(FollowerContractSpotOrder::getRuleId, followerContractSpotOrderParam.getRuleId());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<FollowerContractSpotOrder> list(FollowerContractSpotOrderParam followerContractSpotOrderParam) {
        return this.list();
    }

    @Override
    public void add(FollowerContractSpotOrderParam followerContractSpotOrderParam) {
        FollowerContractSpotOrder followerContractSpotOrder = new FollowerContractSpotOrder();
        BeanUtil.copyProperties(followerContractSpotOrderParam, followerContractSpotOrder);
        this.save(followerContractSpotOrder);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<FollowerContractSpotOrderParam> followerContractSpotOrderParamList) {
        followerContractSpotOrderParamList.forEach(followerContractSpotOrderParam -> {
        FollowerContractSpotOrder followerContractSpotOrder = this.queryFollowerContractSpotOrder(followerContractSpotOrderParam);
            this.removeById(followerContractSpotOrder.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(FollowerContractSpotOrderParam followerContractSpotOrderParam) {
        FollowerContractSpotOrder followerContractSpotOrder = this.queryFollowerContractSpotOrder(followerContractSpotOrderParam);
        BeanUtil.copyProperties(followerContractSpotOrderParam, followerContractSpotOrder);
        this.updateById(followerContractSpotOrder);
    }

    @Override
    public FollowerContractSpotOrder detail(FollowerContractSpotOrderParam followerContractSpotOrderParam) {
        return this.queryFollowerContractSpotOrder(followerContractSpotOrderParam);
    }

    /**
     * 获取跟单合约极速订单预设
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    private FollowerContractSpotOrder queryFollowerContractSpotOrder(FollowerContractSpotOrderParam followerContractSpotOrderParam) {
        FollowerContractSpotOrder followerContractSpotOrder = this.getById(followerContractSpotOrderParam.getId());
        if (ObjectUtil.isNull(followerContractSpotOrder)) {
            throw new ServiceException(FollowerContractSpotOrderExceptionEnum.NOT_EXIST);
        }
        return followerContractSpotOrder;
    }
}
