/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.followercontractspotorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.business.followercontractspotorder.entity.FollowerContractSpotOrder;
import vip.xiaonuo.business.followercontractspotorder.param.FollowerContractSpotOrderParam;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.util.List;

/**
 * 跟单合约极速订单预设service接口
 *
 * <AUTHOR>
 * @date 2025-07-22 14:38:50
 */
public interface FollowerContractSpotOrderService extends IService<FollowerContractSpotOrder> {

    /**
     * 查询跟单合约极速订单预设
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    PageResult<FollowerContractSpotOrder> page(FollowerContractSpotOrderParam followerContractSpotOrderParam);

    /**
     * 跟单合约极速订单预设列表
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    List<FollowerContractSpotOrder> list(FollowerContractSpotOrderParam followerContractSpotOrderParam);

    /**
     * 添加跟单合约极速订单预设
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    void add(FollowerContractSpotOrderParam followerContractSpotOrderParam);

    /**
     * 删除跟单合约极速订单预设
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    void delete(List<FollowerContractSpotOrderParam> followerContractSpotOrderParamList);

    /**
     * 编辑跟单合约极速订单预设
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    void edit(FollowerContractSpotOrderParam followerContractSpotOrderParam);

    /**
     * 查看跟单合约极速订单预设
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    FollowerContractSpotOrder detail(FollowerContractSpotOrderParam followerContractSpotOrderParam);
}
