package vip.xiaonuo.business.userproductorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.business.userproductorder.entity.UserProductOrder;
import vip.xiaonuo.business.userproductorder.param.UserProductOrderParam;
import vip.xiaonuo.business.userproductorder.pojo.dto.ExecuteUserPurchaseOrderDto;
import vip.xiaonuo.business.userproductorder.pojo.dto.ExecuteUserRedeemOrderDto;
import vip.xiaonuo.business.userproductorder.pojo.vo.FindOrderForPageVo;
import vip.xiaonuo.business.userproductorder.result.UserProductOrderStatisticModel;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;

import java.math.BigDecimal;
import java.util.List;

/**
 * 理财订单service接口
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:41
 */
public interface UserProductOrderService extends IService<UserProductOrder> {

    /**
     * 查询理财订单
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    PageResult<UserProductOrder> page(UserProductOrderParam userProductOrderParam);

    /**
     * 理财订单列表
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    List<UserProductOrder> list(UserProductOrderParam userProductOrderParam);

    /**
     * 添加理财订单
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    void add(UserProductOrderParam userProductOrderParam);

    /**
     * 删除理财订单
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    void delete(List<UserProductOrderParam> userProductOrderParamList);

    /**
     * 编辑理财订单
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    void edit(UserProductOrderParam userProductOrderParam);

    /**
     * 查看理财订单
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
     UserProductOrder detail(UserProductOrderParam userProductOrderParam);

    /**
     * 分页查询订单信息
     * @param userProductOrderParam
     * @return
     */
    PageResult<FindOrderForPageVo> findOrderForPage(UserProductOrderParam userProductOrderParam);

    /**
     * 用户购买理财产品
     * @param dto
     * @return
     */
    ResponseData executeUserPurchaseOrder(ExecuteUserPurchaseOrderDto dto);
    /**
     * 用户预购买理财产品
     * @param dto
     * @return
     */
    ResponseData executeAnticipateOrder(ExecuteUserPurchaseOrderDto dto);



    /**
     * 用户赎回订单
     * @param dto
     * @return
     */
    ResponseData executeUserRedeemOrder(ExecuteUserRedeemOrderDto dto);

    /**
     * 预赎回
     * @param dto
     * @return
     */
    ResponseData executeAnticipateRedeem(ExecuteUserRedeemOrderDto dto);

    /**
     * 用户理财收益定时任务
     * @return
     */
    ResponseData executeUserFinancialIncomeTask(UserProductOrder userProductOrder);

    /**
     * 查询理财中的订单
     * @param playerId
     * @return
     */
    ResponseData executeUserOrder(Long playerId);
    /**
     * 获取当前需要计算收益的订单
     * @return
     */
    List<UserProductOrder> findUserFinancialIncome();

    /**
     * 根据类型跟时间统计
     * @param type
     * @param date
     * @return
     */
//    BigDecimal findUserOrderStatistic(Integer type,String date);

    /**
     * 根据时间查询今日赎回和赎回金额
     * @param date
     * @return
     */
//    BigDecimal findUserOrderRedeemStatistic(@Param("date") String date);
    /**
     * 今日违约金
     * @param date
     * @return
     */
//    BigDecimal findUserOrderViolateStatistic(@Param("date") String date);
    UserProductOrderStatisticModel findUserProductOrderStatistic(HomeStatisticParam param);
}
