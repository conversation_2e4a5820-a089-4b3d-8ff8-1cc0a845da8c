package vip.xiaonuo.business.transaction.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.account.param.AccountChangeBalanceParam;
import vip.xiaonuo.business.account.result.AccountChangeBalanceResult;
import vip.xiaonuo.business.account.service.AccountService;
import vip.xiaonuo.business.accountbill.enums.AccountBillBusinessTypeEnum;
import vip.xiaonuo.business.accountbill.enums.AccountBillOptTypeEnum;
import vip.xiaonuo.business.currency.entity.Currency;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.playerinfo.entity.PlayerInfo;
import vip.xiaonuo.business.playerinfo.service.PlayerInfoService;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.business.transaction.entity.Transaction;
import vip.xiaonuo.business.transaction.enums.TransactionExceptionEnum;
import vip.xiaonuo.business.transaction.mapper.TransactionMapper;
import vip.xiaonuo.business.transaction.param.TransactionParam;
import vip.xiaonuo.business.transaction.pojo.dto.*;
import vip.xiaonuo.business.transaction.pojo.vo.FindRechargeUserForPageVo;
import vip.xiaonuo.business.transaction.pojo.vo.FindUserTransactionVo;
import vip.xiaonuo.business.transaction.result.TransactionStatisticModel;
import vip.xiaonuo.business.transaction.service.TransactionService;
import vip.xiaonuo.business.upay.pojo.dto.UPayDto;
import vip.xiaonuo.business.upay.pojo.vo.UpayResp;
import vip.xiaonuo.business.upay.service.UpayService;
import vip.xiaonuo.core.enums.SerialNoEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.util.Hmac;
import vip.xiaonuo.core.util.MathUtil;
import vip.xiaonuo.core.util.MessageUtils;
import vip.xiaonuo.core.util.UUIDUtil;
import vip.xiaonuo.telegrambot.TelegramBot;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

//import cn.stylefeng.tg.ad.sdk.BusinessCategoryEnum;
//import cn.stylefeng.tg.ad.sdk.service.TelegramAdSdkService;
//import vip.xiaonuo.business.accountbill.enums.Accoucn.stylefeng.tg.ad.sdkntBillBusinessTypeEnum;

/**
 * 用户充值，提现记录表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-08 09:51:14
 */
@Slf4j
@Service
public class TransactionServiceImpl extends ServiceImpl<TransactionMapper, Transaction> implements TransactionService, InitializingBean {
    //    @Resource
//    private TelegramAdSdkService telegramAdSdkService;
    @Autowired(required = false)
    private TelegramBot telegramBot;
    @Autowired
    PlayerService playerService;
    @Autowired
    AccountService accountService;
    @Autowired
    MarketService marketService;
    @Autowired
    CurrencyService currencyService;
    @Autowired
    PlayerInfoService playerInfoService;
    @Resource
    UpayService uPayService;

    @Override
    public PageResult<Transaction> page(TransactionParam transactionParam) {
        QueryWrapper<Transaction> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(transactionParam)) {

            // 根据用户id 查询
            if (ObjectUtil.isNotEmpty(transactionParam.getPlayerId())) {
                queryWrapper.lambda().eq(Transaction::getPlayerId, transactionParam.getPlayerId());
            }
            // 根据金额 查询
            if (ObjectUtil.isNotEmpty(transactionParam.getAccount())) {
                queryWrapper.lambda().eq(Transaction::getAccount, transactionParam.getAccount());
            }
            // 根据操作类型 1 充值 2 提现  查询
            if (ObjectUtil.isNotEmpty(transactionParam.getOperationType())) {
                queryWrapper.lambda().eq(Transaction::getOperationType, transactionParam.getOperationType());
            }
            // 根据状态 1 已提交 2 审核通过  3 审核不通过 3 处理中 查询
            if (ObjectUtil.isNotEmpty(transactionParam.getStatus())) {
                queryWrapper.lambda().eq(Transaction::getStatus, transactionParam.getStatus());
            }
            // 根据交易扩展信息 查询
            if (ObjectUtil.isNotEmpty(transactionParam.getJsonInfo())) {
                queryWrapper.lambda().eq(Transaction::getJsonInfo, transactionParam.getJsonInfo());
            }
            // 根据操作用户 查询
            if (ObjectUtil.isNotEmpty(transactionParam.getOperationId())) {
                queryWrapper.lambda().eq(Transaction::getOperationId, transactionParam.getOperationId());
            }
            // 根据操作时间 查询
            if (ObjectUtil.isNotEmpty(transactionParam.getOperationTime())) {
                queryWrapper.lambda().eq(Transaction::getOperationTime, transactionParam.getOperationTime());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<Transaction> list(TransactionParam transactionParam) {
        return this.list();
    }

    @Override
    public void add(TransactionParam transactionParam) {
        Transaction transaction = new Transaction();
        BeanUtil.copyProperties(transactionParam, transaction);
        this.save(transaction);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<TransactionParam> transactionParamList) {
        transactionParamList.forEach(transactionParam -> {
            Transaction transaction = this.queryTransaction(transactionParam);
            this.removeById(transaction.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(TransactionParam transactionParam) {
        Transaction transaction = this.queryTransaction(transactionParam);
        BeanUtil.copyProperties(transactionParam, transaction);
        this.updateById(transaction);
    }

    /**
     * 获取用户充值，提现记录表
     *
     * <AUTHOR>
     * @date 2022-04-08 09:51:14
     */
    private Transaction queryTransaction(TransactionParam transactionParam) {
        Transaction transaction = this.getById(transactionParam.getId());
        if (ObjectUtil.isNull(transaction)) {
            throw new ServiceException(TransactionExceptionEnum.NOT_EXIST);
        }
        return transaction;
    }

    @Override
    public Transaction detail(TransactionParam transactionParam) {
        return this.queryTransaction(transactionParam);
    }

    /**
     * 用户充值
     *
     * @param dto
     * @return
     */
    @Transactional
    @Override
    public ResponseData executeRechargeUser(ExecuteRechargeUserDto dto) {
        if (dto.getAccount().compareTo(BigDecimal.ZERO) < 1) {
            return ResponseData.error(MessageUtils.get("transaction.recharge..account.fee.error"));
        }
        Player user = playerService.getById(dto.getPlayerId());
        if (ObjectUtils.isEmpty(user)) {
            user = playerService.findPlayerByAccount(dto.getUserAccount());
        }
        if (ObjectUtils.isEmpty(user)) {
            return ResponseData.error(MessageUtils.get("user.is.null.error"));
        }

        if (Arrays.asList("玩家充值", "管理员充值").contains(dto.getRechargeChannel())) {
            //锁玩家，在外边锁，防止死锁
            playerService.lockPlayer(Lists.newArrayList(user.getId()));

            dto.setPlayerId(user.getId());
            dto.setOperationType(1);
            Transaction transaction = new Transaction();
            BeanUtil.copyProperties(dto, transaction);
            transaction.setOrderNum(UUIDUtil.createOrderId(SerialNoEnum.D));
            transaction.setCreateTime(new Date());
            transaction.setChargeFree(BigDecimal.ZERO);
            transaction.setChargeFreeRata(BigDecimal.ZERO);
            transaction.setArrivalAccount(dto.getAccount());
            transaction.setStatus(dto.getStatus());
            transaction.setRemarks(dto.getRemarks());

            BigDecimal marketRate = marketService.getMarketRate(dto.getTransactionType(), "USDT");
            if (ObjectUtils.isEmpty(marketRate)) {
                return ResponseData.error(MessageUtils.get("rate.fee.is.null.error"));
            }
            transaction.setUsdtAccount(dto.getAccount().multiply(marketRate));

            if (Integer.valueOf(2).equals(transaction.getStatus())) {
                transaction.setCheckTime(new Date());
                transaction.setChenckId(dto.getChenckId());
                //增加资金
                AccountChangeBalanceParam.AccountChangeBalanceParamBuilder frozen = AccountChangeBalanceParam.builder()
                        .playerId(transaction.getPlayerId())
                        .type(transaction.getAccountType())
                        .currencyCode(transaction.getTransactionType())
                        .businessType(AccountBillBusinessTypeEnum.DEPOSIT)
                        .optType(AccountBillOptTypeEnum.ADD)
                        .businessNo(transaction.getOrderNum())
                        .balance(transaction.getAccount())
                        .updateUser(transaction.getChenckId());
                AccountChangeBalanceResult accountChangeBalanceResult = accountService.changeBalance(frozen.build());

                transaction.setRechargeFrontAccount(accountChangeBalanceResult.getAccountBill().getBalanceAfter().subtract(accountChangeBalanceResult.getAccountBill().getBalanceChanged()));
                transaction.setRechargeAfterAccount(accountChangeBalanceResult.getAccountBill().getBalanceAfter());
            }
            this.save(transaction);
            return ResponseData.success();
        } else {
            dto.setPlayerId(user.getId());
            dto.setOperationType(1);
            Transaction transaction = new Transaction();
            BeanUtil.copyProperties(dto, transaction);
            transaction.setOrderNum(UUIDUtil.createOrderId(SerialNoEnum.PAY));
            transaction.setCreateTime(new Date());
            transaction.setChargeFree(BigDecimal.ZERO);
            transaction.setChargeFreeRata(BigDecimal.ZERO);
            transaction.setArrivalAccount(dto.getAccount());
            transaction.setStatus(0);//待付款
            transaction.setRemarks(dto.getRemarks());
            transaction.setUsdtAccount(dto.getAccount());
            this.save(transaction);

            UPayDto payDto = new UPayDto();
            payDto.setChainType(dto.getRechargeChannel());
            payDto.setMoneyType("USD");
            payDto.setOrderSn(transaction.getOrderNum());
            payDto.setPayMoney(dto.getAccount());
            payDto.setPayUsername(user.getAccount());
            UpayResp upayResp = uPayService.createOrder(payDto);
//            if(ObjectUtils.isNotEmpty(upayResp.getRespData())){
//                if(upayResp.getRespData().getPayUrl().startsWith("http:")){
//                    upayResp.getRespData().setPayUrl(upayResp.getRespData().getPayUrl().replace("http:","https:"));
//                }
//                upayResp.getRespData().setPayUrl(upayResp.getRespData().getPayUrl().replace("\\u0026","&"));
//            }
//            return ResponseData.success(ObjectUtils.isNotEmpty(upayResp.getRespData()) ? upayResp.getRespData().getPayUrl() : "");
            return ResponseData.success();
        }
    }

    /**
     * 充值记录查询
     *
     * @param req
     * @return
     */
    @Override
    public PageResult<FindRechargeUserForPageVo> findRechargeUserForPage(FindRechargeUserForPageDto req) {
//        List<FindRechargeUserForPageVo> listAll = getBaseMapper().findRechargeUserForPage(req);
//        if(ObjectUtils.isEmpty(listAll)){
//            listAll = new ArrayList<>();
//        }
//        Page<FindRechargeUserForPageVo> page = PageFactory.defaultPage();
//        page.setTotal(listAll.size());
//        List<FindRechargeUserForPageVo> resultList = PageUtil.page(page, listAll);
//        return new PageResult<>(page, resultList);
        return new PageResult<>(getBaseMapper().findRechargeUserForPage(PageFactory.defaultPage(), req));
    }

    /**
     * 用户提现
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional
    public ResponseData executeWithdrawalUser(ExecuteWithdrawalUserDto dto, boolean isAuthCheck) {
        if (dto.getAccount().compareTo(BigDecimal.ZERO) < 1) {
            return ResponseData.error(MessageUtils.get("transaction.withdrawal..account.fee.error"));
        }
        Player user = playerService.getById(dto.getPlayerId());
        if (ObjectUtils.isEmpty(user)) {
            user = playerService.findPlayerByAccount(dto.getUserAccount());
        }
        if (ObjectUtils.isEmpty(user)) {
            return ResponseData.error(MessageUtils.get("user.is.null.error"));
        }
        PlayerInfo playerInfo = playerInfoService.queryPlayerUserInfo(user.getId());
        if (isAuthCheck && (ObjectUtils.isEmpty(playerInfo) || ObjectUtils.isEmpty(playerInfo.getAdvancedStatus()) || playerInfo.getAdvancedStatus() != 2)) {
            return ResponseData.error(MessageUtils.get("withdraw.auth.check.error"));
        }
        if (!StringUtils.equals(Hmac.md5EncryptSalt(dto.getPassword(), user.getSalt()), user.getFundsPassword())) {
            return ResponseData.error(MessageUtils.get("player.fund.password.incorrect"));
        }
        //获取用户资料信息
//        QueryWrapper<PlayerInfo> infoWrapper = new QueryWrapper<>();
//        infoWrapper.lambda().eq(PlayerInfo::getPlayerId, user.getId());
//        PlayerInfo info = playerInfoService.getOne(infoWrapper);
//        if (ObjectUtils.isEmpty(info)) {
//            return ResponseData.error(MessageUtils.get("user.is.auth.error"));
//        }
//        //没有完成高级验证
//        if (info.getAdvancedStatus() == null || !info.getAdvancedStatus().equals(2)) {
//            //抛异常，让前端拿到错误code判断
//            throw new ServiceException(TransactionExceptionEnum.REAL_NAME_AUTH_FAIL);
//        }
        //获取手续费率
        Currency currency = currencyService.getWithdrawCurrency(dto.getTransactionType());
        if (ObjectUtils.isEmpty(currency)) {
            return ResponseData.error(MessageUtils.get("transaction.fee.error"));
        }
        BigDecimal minWithdrawAmount = currency.getMinWithdrawAmount();
        minWithdrawAmount = minWithdrawAmount == null ? BigDecimal.ZERO : minWithdrawAmount;
        BigDecimal currencyWithdrawLimit = currency.getCurrencyWithdrawLimit();
        currencyWithdrawLimit = currencyWithdrawLimit == null ? BigDecimal.valueOf(99999999999999L) : currencyWithdrawLimit;

        if (minWithdrawAmount.compareTo(dto.getAccount()) > 0) {
            //最小提币数量
            return ResponseData.error(MessageUtils.get("transaction.withdrawal.minWithdrawAmount"));
        }
        if (currencyWithdrawLimit.compareTo(dto.getAccount()) < 0) {
            //单次最大提币额
            return ResponseData.error(MessageUtils.get("transaction.withdrawal.withdrawLimit"));
        }

        BigDecimal freeRate = currency.getWithdrawFee();
//        BigDecimal chargeFree = dto.getAccount().multiply(freeRate);
        if (freeRate.compareTo(dto.getAccount()) > 0) {
            return ResponseData.error(MessageUtils.get("transaction.fee.error"));
        }
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(user.getId()));

        dto.setPlayerId(user.getId());
        dto.setOperationType(2);
        Transaction transaction = new Transaction();
        BeanUtil.copyProperties(dto, transaction);
        transaction.setOrderNum(UUIDUtil.createOrderId(SerialNoEnum.W));
        transaction.setCreateTime(new Date());
        transaction.setChargeFree(dto.getAccount().multiply(freeRate.divide(BigDecimal.valueOf(100))).setScale(4, RoundingMode.HALF_UP));
        transaction.setChargeFreeRata(freeRate);
        transaction.setArrivalAccount(dto.getAccount().subtract(transaction.getChargeFree()));
        transaction.setStatus(dto.getStatus());
//        BigDecimal marketRate = marketService.getMarketRate(dto.getTransactionType(), "USDT");
//        if (ObjectUtils.isEmpty(marketRate)) {
//            return ResponseData.error(MessageUtils.get("rate.fee.is.null.error"));
//        }
//        transaction.setUsdtAccount(dto.getAccount().multiply(marketRate).setScale(2));

        if (transaction.getStatus() == 2) {
            transaction.setCheckTime(new Date());
            transaction.setChenckId(dto.getChenckId());
        }

        this.save(transaction);

        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder frozen = AccountChangeBalanceParam.builder()
                .playerId(dto.getPlayerId())
                .type(dto.getAccountType())
                .currencyCode(dto.getTransactionType())
                .businessType(AccountBillBusinessTypeEnum.WITHDRAW)
                .optType(AccountBillOptTypeEnum.SUB)
                .businessNo(transaction.getOrderNum()).balance(dto.getAccount()).updateUser(dto.getChenckId());

        AccountChangeBalanceResult accountChangeBalanceResult = accountService.changeBalance(frozen.build());

        transaction.setRechargeFrontAccount(accountChangeBalanceResult.getAccountBill().getBalanceAfter().subtract(accountChangeBalanceResult.getAccountBill().getBalanceChanged()));
        transaction.setRechargeAfterAccount(accountChangeBalanceResult.getAccountBill().getBalanceAfter());
        this.saveOrUpdate(transaction);
        String devMsg = "<b>有新的提款订单</b>\n" +
                "<i>币种</i>：<b>" + currency.getCode() + "</b>\n" +
                "<i>玩家登录名</i>：" + user.getAccount() + "\n" +
                "<i>提款金额</i>：" + MathUtil.decimalFormatMoney(transaction.getAccount()) + "\n" +
                "<i>提款单号</i>：" + transaction.getOrderNum();
//        telegramAdSdkService.push(BusinessCategoryEnum.withdraw, devMsg);
        if (telegramBot != null) {
            telegramBot.sendDevMessage(devMsg);
        }
        return ResponseData.success();
    }

    /**
     * 提现记录列表
     *
     * @param req
     * @return
     */
    @Override
    public PageResult<FindRechargeUserForPageVo> findWithdrawalUserForPage(FindWithdrawalUserForPageDto req) {
        return new PageResult<>(getBaseMapper().findWithdrawalUserForPage(PageFactory.defaultPage(), req));
    }

    /**
     * 根据时间查
     *
     * @return
     */
    @Override
    public TransactionStatisticModel findrechargeDayAccountStatistic(HomeStatisticParam param) {
        return getBaseMapper().findrechargeDayAccountStatistic(param);
    }

    /**
     * 分页查询用户充值提现交易记录
     *
     * @param dto
     * @return
     */
    @Override
    public PageResult<FindUserTransactionVo> findUserTransaction(FindUserTransactionDto dto) {
        Page<FindUserTransactionVo> listAll = getBaseMapper().findUserTransaction(PageFactory.defaultPage(), dto);
        return new PageResult<>(listAll);
    }

    /**
     * 审核提现充值
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional
    public ResponseData checkTransaction(Long loginUserId, CheckTransactionDto dto) {
        if (ObjectUtils.isEmpty(dto)) {
            return ResponseData.error("数据有误");
        }
        Transaction transaction = this.getById(dto.getTransactionId());
        if (ObjectUtils.isEmpty(transaction)) {
            return ResponseData.error("审核数据不存在");
        }
        if (ObjectUtils.isEmpty(dto.getStatus())) {
            return ResponseData.error("请选择通过或者不通过");
        }
        transaction.setChenckId(loginUserId);
        transaction.setCheckTime(new Date());
        if (dto.getStatus().equals(2)) {
            transaction.setStatus(dto.getStatus());
            transaction.setRemark(dto.getRemark());
            this.updateById(transaction);
            if (transaction.getOperationType().equals(1)) {
                //增加资金
                AccountChangeBalanceParam.AccountChangeBalanceParamBuilder frozen = AccountChangeBalanceParam.builder()
                        .playerId(transaction.getPlayerId())
                        .type(transaction.getAccountType())
                        .currencyCode(transaction.getTransactionType())
                        .businessType(AccountBillBusinessTypeEnum.DEPOSIT)
                        .optType(AccountBillOptTypeEnum.ADD)
                        .businessNo(transaction.getOrderNum()).balance(transaction.getAccount()).updateUser(transaction.getChenckId());
                AccountChangeBalanceResult accountChangeBalanceResult = accountService.changeBalance(frozen.build());

                transaction.setRechargeFrontAccount(accountChangeBalanceResult.getAccountBill().getBalanceAfter().subtract(accountChangeBalanceResult.getAccountBill().getBalanceChanged()));
                transaction.setRechargeAfterAccount(accountChangeBalanceResult.getAccountBill().getBalanceAfter());
                this.saveOrUpdate(transaction);
            }
        } else if (dto.getStatus().equals(3)) {
            transaction.setStatus(dto.getStatus());
            transaction.setRemark(dto.getRemark());
            this.updateById(transaction);
            if (dto.getStatus().equals(3)) {
                if (ObjectUtils.isEmpty(dto.getRemark())) {
                    return ResponseData.error("审核不通过需要填写原因");
                }
            }
            if (transaction.getOperationType().equals(2)) {
                //增加资金
                AccountChangeBalanceParam.AccountChangeBalanceParamBuilder frozen = AccountChangeBalanceParam.builder()
                        .playerId(transaction.getPlayerId())
                        .type(transaction.getAccountType())
                        .currencyCode(transaction.getTransactionType())
                        .businessType(AccountBillBusinessTypeEnum.WITHDRAW_CANCEL)
                        .optType(AccountBillOptTypeEnum.ADD)
                        .businessNo(transaction.getOrderNum()).balance(transaction.getAccount()).updateUser(transaction.getChenckId());
                AccountChangeBalanceResult accountChangeBalanceResult = accountService.changeBalance(frozen.build());

                transaction.setRechargeFrontAccount(accountChangeBalanceResult.getAccountBill().getBalanceAfter().subtract(accountChangeBalanceResult.getAccountBill().getBalanceChanged()));
                transaction.setRechargeAfterAccount(accountChangeBalanceResult.getAccountBill().getBalanceAfter());
                this.saveOrUpdate(transaction);
            }
        } else {
            return ResponseData.error("审核信息有误");
        }
        return ResponseData.success();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }
}
