/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.spotrule.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.spotrule.entity.SpotRule;
import vip.xiaonuo.business.spotrule.enums.SpotRuleExceptionEnum;
import vip.xiaonuo.business.spotrule.mapper.SpotRuleMapper;
import vip.xiaonuo.business.spotrule.param.SpotRuleParam;
import vip.xiaonuo.business.spotrule.result.PortalSpotRuleResult;
import vip.xiaonuo.business.spotrule.service.SpotRuleService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 极速下单规则service接口实现类
 *
 * <AUTHOR>
 * @date 2022-12-28 11:21:34
 */
@Service
public class SpotRuleServiceImpl extends ServiceImpl<SpotRuleMapper, SpotRule> implements SpotRuleService {

    @Override
    public PageResult<SpotRule> page(SpotRuleParam spotRuleParam) {
        QueryWrapper<SpotRule> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(spotRuleParam)) {

            // 根据最小下单数量 查询
            if (ObjectUtil.isNotEmpty(spotRuleParam.getMinQuantity())) {
                queryWrapper.lambda().eq(SpotRule::getMinQuantity, spotRuleParam.getMinQuantity());
            }
            // 根据秒 查询
            if (ObjectUtil.isNotEmpty(spotRuleParam.getSeconds())) {
                queryWrapper.lambda().eq(SpotRule::getSeconds, spotRuleParam.getSeconds());
            }
            // 根据最小利润率 查询
            if (ObjectUtil.isNotEmpty(spotRuleParam.getMinProfitRate())) {
                queryWrapper.lambda().eq(SpotRule::getMinProfitRate, spotRuleParam.getMinProfitRate());
            }
            // 根据最大利润率 查询
            if (ObjectUtil.isNotEmpty(spotRuleParam.getMaxProfitRate())) {
                queryWrapper.lambda().eq(SpotRule::getMaxProfitRate, spotRuleParam.getMaxProfitRate());
            }
            // 根据最小亏损率 查询
            if (ObjectUtil.isNotEmpty(spotRuleParam.getMinLossRate())) {
                queryWrapper.lambda().eq(SpotRule::getMinLossRate, spotRuleParam.getMinLossRate());
            }
            // 根据最大亏损率 查询
            if (ObjectUtil.isNotEmpty(spotRuleParam.getMaxLossRate())) {
                queryWrapper.lambda().eq(SpotRule::getMaxLossRate, spotRuleParam.getMaxLossRate());
            }
            if (ObjectUtil.isNotEmpty(spotRuleParam.getSymbol())) {
                queryWrapper.lambda().eq(SpotRule::getSymbol, spotRuleParam.getSymbol());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SpotRule> list(SpotRuleParam spotRuleParam) {
        return this.list();
    }

    @Override
    public void add(SpotRuleParam spotRuleParam) {
        SpotRule spotRule = new SpotRule();
        BeanUtil.copyProperties(spotRuleParam, spotRule);
        this.save(spotRule);
    }

    private void check(SpotRuleParam spotRuleParam) {
        LambdaQueryChainWrapper<SpotRule> lambdaQueryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        if (ObjectUtil.isNotEmpty(spotRuleParam.getId())) {
            lambdaQueryChainWrapper.ne(SpotRule::getId, spotRuleParam.getId());
        }
        if (StrUtil.isNotEmpty(spotRuleParam.getSymbol())) {
            lambdaQueryChainWrapper.eq(SpotRule::getSymbol, spotRuleParam.getSymbol());
        }
        lambdaQueryChainWrapper.eq(SpotRule::getSeconds, spotRuleParam.getSeconds());
        Integer count = lambdaQueryChainWrapper.count();
        if (count >= 1) {
            throw new ServiceException(500, "该数据已存在相同的");
        }

        if (spotRuleParam.getSeconds() <= 0) {
            throw new ServiceException(500, "秒不能为负数");
        }
        if (spotRuleParam.getMinQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException(500, "最小下单数量不能少于等于0");
        }
        BigDecimal ten = new BigDecimal("100");
        if (spotRuleParam.getMinProfitRate().compareTo(BigDecimal.ZERO) < 0 || spotRuleParam.getMinProfitRate().compareTo(ten) > 0) {
            throw new ServiceException(500, "利润率不能为负数且不能大于100");
        }
        if (spotRuleParam.getMaxProfitRate().compareTo(BigDecimal.ZERO) < 0 || spotRuleParam.getMaxProfitRate().compareTo(ten) > 0) {
            throw new ServiceException(500, "利润率不能为负数且不能大于100");
        }
        if (spotRuleParam.getMinLossRate().compareTo(BigDecimal.ZERO) < 0 || spotRuleParam.getMinLossRate().compareTo(ten) > 0) {
            throw new ServiceException(500, "亏损率不能为负数且不能大于100");
        }
        if (spotRuleParam.getMaxLossRate().compareTo(BigDecimal.ZERO) < 0 || spotRuleParam.getMaxLossRate().compareTo(ten) > 0) {
            throw new ServiceException(500, "亏损率不能为负数且不能大于100");
        }

        if (spotRuleParam.getMinProfitRate().compareTo(spotRuleParam.getMaxProfitRate()) > 0) {
            throw new ServiceException(500, "最小利润率不能大于最大利润率");
        }
        if (spotRuleParam.getMinLossRate().compareTo(spotRuleParam.getMaxLossRate()) > 0) {
            throw new ServiceException(500, "最小亏损率不能大于最大亏损率");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<SpotRuleParam> spotRuleParamList) {
        spotRuleParamList.forEach(spotRuleParam -> {
            SpotRule spotRule = this.querySpotRule(spotRuleParam);
            this.removeById(spotRule.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(SpotRuleParam spotRuleParam) {
        check(spotRuleParam);
        SpotRule spotRule = this.querySpotRule(spotRuleParam);
        BeanUtil.copyProperties(spotRuleParam, spotRule);
        this.updateById(spotRule);
    }

    @Override
    public SpotRule detail(SpotRuleParam spotRuleParam) {
        return this.querySpotRule(spotRuleParam);
    }

    @Override
    public List<PortalSpotRuleResult> queryAll(String symbol) {
        List<SpotRule> list = lambdaQuery().orderByAsc(SpotRule::getSeconds, SpotRule::getMaxProfitRate).list();
        List<SpotRule> list1 = new ArrayList<>();
        List<SpotRule> list2 = new ArrayList<>();
        for (SpotRule spotRule : list) {
            if (StrUtil.isNotEmpty(spotRule.getSymbol()) && spotRule.getSymbol().equalsIgnoreCase(symbol)) {
                list2.add(spotRule);
            }
            if (StrUtil.isEmpty(spotRule.getSymbol())) {
                list1.add(spotRule);
            }
        }
        List<PortalSpotRuleResult> portalSpotRuleResults = new ArrayList<>();
        for (SpotRule spotRule : list1) {
            PortalSpotRuleResult p = new PortalSpotRuleResult();
            p.setId(spotRule.getId());
            p.setSeconds(spotRule.getSeconds());
            p.setMinQuantity(spotRule.getMinQuantity());
            p.setMaxQuantity(spotRule.getMaxQuantity());
            p.setProfitability(spotRule.getMaxProfitRate());
            p.setSymbol(spotRule.getSymbol());
            portalSpotRuleResults.add(p);
        }
        //带有Symbol覆盖以前的
        for (SpotRule spotRule : list2) {
            PortalSpotRuleResult p = portalSpotRuleResults.stream().filter(p1 -> p1.getSeconds().equals(spotRule.getSeconds())).findFirst().orElse(null);
            if (p == null) {
                p = new PortalSpotRuleResult();
                portalSpotRuleResults.add(p);
            }
            p.setId(spotRule.getId());
            p.setSeconds(spotRule.getSeconds());
            p.setMinQuantity(spotRule.getMinQuantity());
            p.setMaxQuantity(spotRule.getMaxQuantity());
            p.setProfitability(spotRule.getMaxProfitRate());
            p.setSymbol(spotRule.getSymbol());
        }
        return portalSpotRuleResults;
    }

    /**
     * 获取极速下单规则
     *
     * <AUTHOR>
     * @date 2022-12-28 11:21:34
     */
    private SpotRule querySpotRule(SpotRuleParam spotRuleParam) {
        SpotRule spotRule = this.getById(spotRuleParam.getId());
        if (ObjectUtil.isNull(spotRule)) {
            throw new ServiceException(SpotRuleExceptionEnum.NOT_EXIST);
        }
        return spotRule;
    }
}
