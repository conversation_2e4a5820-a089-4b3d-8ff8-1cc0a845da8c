package vip.xiaonuo.business.transaction.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.business.transaction.entity.Transaction;
import vip.xiaonuo.business.transaction.param.TransactionParam;
import vip.xiaonuo.business.transaction.pojo.dto.*;
import vip.xiaonuo.business.transaction.pojo.vo.FindRechargeUserForPageVo;
import vip.xiaonuo.business.transaction.pojo.vo.FindUserTransactionVo;
import vip.xiaonuo.business.transaction.result.TransactionStatisticModel;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;

import java.util.List;

/**
 * 用户充值，提现记录表service接口
 *
 * <AUTHOR>
 * @date 2022-04-08 09:51:14
 */
public interface TransactionService extends IService<Transaction> {

    /**
     * 查询用户充值，提现记录表
     *
     * <AUTHOR>
     * @date 2022-04-08 09:51:14
     */
    PageResult<Transaction> page(TransactionParam transactionParam);

    /**
     * 用户充值，提现记录表列表
     *
     * <AUTHOR>
     * @date 2022-04-08 09:51:14
     */
    List<Transaction> list(TransactionParam transactionParam);

    /**
     * 添加用户充值，提现记录表
     *
     * <AUTHOR>
     * @date 2022-04-08 09:51:14
     */
    void add(TransactionParam transactionParam);

    /**
     * 删除用户充值，提现记录表
     *
     * <AUTHOR>
     * @date 2022-04-08 09:51:14
     */
    void delete(List<TransactionParam> transactionParamList);

    /**
     * 编辑用户充值，提现记录表
     *
     * <AUTHOR>
     * @date 2022-04-08 09:51:14
     */
    void edit(TransactionParam transactionParam);

    /**
     * 查看用户充值，提现记录表
     *
     * <AUTHOR>
     * @date 2022-04-08 09:51:14
     */
    Transaction detail(TransactionParam transactionParam);

    /**
     * 用户充值
     *
     * @return
     */
    ResponseData executeRechargeUser(ExecuteRechargeUserDto dto);

    /**
     * 充值记录查询
     *
     * @param req
     * @return
     */
    PageResult<FindRechargeUserForPageVo> findRechargeUserForPage(FindRechargeUserForPageDto req);

    /**
     * 用户提现
     *
     * @return
     */
    ResponseData executeWithdrawalUser(ExecuteWithdrawalUserDto dto,boolean isAuthCheck);

    /**
     * 提现记录列表
     *
     * @param req
     * @return
     */
    PageResult<FindRechargeUserForPageVo> findWithdrawalUserForPage(FindWithdrawalUserForPageDto req);

    /**
     * 根据时间查
     *
     * @param date
     * @return
     */
    TransactionStatisticModel findrechargeDayAccountStatistic(HomeStatisticParam param);

    /**
     * 分页查询用户充值提现交易记录
     *
     * @param dto
     * @return
     */
    PageResult<FindUserTransactionVo> findUserTransaction(FindUserTransactionDto dto);

    /**
     * 审核提现充值
     *
     * @param dto
     * @return
     */
    ResponseData checkTransaction(Long loginUserId, CheckTransactionDto dto);

}
