package vip.xiaonuo.business.account.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class FindUserAccountVo implements Serializable {
    /**
     * 账户总额
     */
    @ApiModelProperty("账户总额")
    private BigDecimal total = BigDecimal.ZERO;
    /**
     * 账户资产
     */
    @ApiModelProperty("账户资产")
    private List<UserAccountVo> list = new ArrayList<>();
    /**
     * 币币账户
     */
    @ApiModelProperty("币币账户")
    private List<UserAccountVo> coinList = new ArrayList<>();

}
