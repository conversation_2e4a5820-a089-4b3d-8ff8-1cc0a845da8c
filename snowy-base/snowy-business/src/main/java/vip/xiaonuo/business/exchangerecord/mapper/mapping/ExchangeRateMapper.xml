<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.business.exchangerecord.mapper.ExchangeRecordMapper">
    <resultMap id="BaseResultMap" type="vip.xiaonuo.business.exchangerecord.entity.ExchangeRecord">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="player_Id" property="playerId"/>
        <result column="order_No" property="orderNo"/>
        <result column="from_Currency_Code" property="fromCurrencyCode"/>
        <result column="from_Quantity" property="fromQuantity"/>
        <result column="currency_Code" property="currencyCode"/>
        <result column="quantity" property="quantity"/>
        <result column="order_Fee" property="orderFee"/>
        <result column="order_Time" property="orderTime"/>
        <result column="close_Time" property="closeTime"/>
        <result column="status" property="status"/>
    </resultMap>
    <resultMap id="BaseResultExtMap" type="vip.xiaonuo.business.exchangerecord.result.ExchangeRecordResult" extends="BaseResultMap">
        <result column="playerAccount" property="playerAccount"/>
    </resultMap>
    <select id="pageResult" resultMap="BaseResultExtMap">
        select t.*,p.account as playerAccount from tt_exchange_record t left join tt_player p on p.id = t.player_id
        <where>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
                and p.agent_id in
                <foreach item="item" index="index" collection="ew.agentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
                and p.agent_id = #{ew.loginUserId}
            </if>
        </where>
        order by t.order_time desc
    </select>
</mapper>
