package vip.xiaonuo.business.core.filter.security;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.log.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import vip.xiaonuo.business.auth.service.AuthService;
import vip.xiaonuo.business.pojo.sysuser.SysLoginUser;
import vip.xiaonuo.core.consts.SpringSecurityConstant;
import vip.xiaonuo.core.exception.AuthException;
import vip.xiaonuo.core.exception.enums.AuthExceptionEnum;
import vip.xiaonuo.core.util.ResponseUtil;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 这个过滤器，在所有请求之前，也在spring security filters之前
 * <p>
 * 这个过滤器的作用是：接口在进业务之前，添加登录上下文（SecurityContext）
 * <p>
 * 因为现在没有用session了，只能token来校验当前的登录人的身份，所以在进业务之前要给当前登录人设置登录状态
 *
 * <AUTHOR>
 * @date 2020/4/11 13:02
 */
//@Component 在admin里边开启
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    private static final Log log = Log.get();

    @Autowired
    private AuthService authService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException {
        // 1.如果当前请求带了token，判断token时效性，并获取当前登录用户信息
        SysLoginUser sysLoginUser = null;
        try {
            String token = authService.getTokenFromRequest(request);
            if (StrUtil.isNotEmpty(token)) {
                sysLoginUser = authService.getLoginUserByToken(token);
            }
        } catch (AuthException ae) {
            //如果是不需要权限校验的接口则放开
            String servletPath = request.getServletPath();
            for (String reg : SpringSecurityConstant.NONE_SECURITY_URL_PATTERNS) {
                if (new AntPathMatcher().match(reg, servletPath)) {
                    filterChain.doFilter(request, response);
                    return;
                }
            }

            //传入了token，且获取不到用户信息，则表示token错误，或token过期，统一按照token过期处理，需重新登录
            //无论如何，先删除cookie
            authService.deleteLoginCookie();
            //处理异常
            ResponseUtil.handleErrorWithWrite(AuthExceptionEnum.LOGIN_EXPIRED.getCode(), AuthExceptionEnum.LOGIN_EXPIRED.getMessage(), ae.getStackTrace()[0].toString());
            return;
        }

        // 2.如果当前登录用户不为空，就设置spring security上下文
        if (ObjectUtil.isNotNull(sysLoginUser)) {
            authService.setSpringSecurityContextAuthentication(sysLoginUser);
        }

        // 3.其他情况放开过滤
        filterChain.doFilter(request, response);
    }
}
