<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.business.dict.mapper.SysDictDataMapper">

    <select id="getDictCodesByDictTypeCode" resultType="java.lang.String">
        SELECT
        dict.`code`
        FROM
        sys_dict_data dict
        INNER JOIN sys_dict_type type ON dict.type_id = type.id
        where type.code in
        <foreach collection="array" index="index" item="i" open="(" separator="," close=")">
            #{i}
        </foreach>
    </select>


    <select id="getDictvalueByDictTypeCode" resultType="java.lang.String">
        SELECT
        dict.`code`
        FROM
        sys_dict_data dict
        INNER JOIN sys_dict_type type ON dict.type_id = type.id
        where type.code = #{code}
    </select>

    <select id="getDictValue" resultType="java.lang.String">
        SELECT
            d.`value`
        FROM
            sys_dict_type type,
            sys_dict_data d
        WHERE
            type.id = d.type_id
            AND type.`code` = #{dictTypeCode}
            AND d.`code` = #{dictCode}
            AND type.`status` = 0
            AND d.`status` = 0
    </select>
</mapper>
