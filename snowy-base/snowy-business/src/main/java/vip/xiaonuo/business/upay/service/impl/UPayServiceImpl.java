package vip.xiaonuo.business.upay.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.account.param.AccountChangeBalanceParam;
import vip.xiaonuo.business.account.result.AccountChangeBalanceResult;
import vip.xiaonuo.business.account.service.AccountService;
import vip.xiaonuo.business.accountbill.enums.AccountBillBusinessTypeEnum;
import vip.xiaonuo.business.accountbill.enums.AccountBillOptTypeEnum;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.sysconfig.entity.SysConfig;
import vip.xiaonuo.business.sysconfig.service.SysConfigService;
import vip.xiaonuo.business.transaction.entity.Transaction;
import vip.xiaonuo.business.transaction.service.TransactionService;
import vip.xiaonuo.business.upay.enums.ChainTypeEnums;
import vip.xiaonuo.business.upay.enums.MoneyTypeEnums;
import vip.xiaonuo.business.upay.pojo.dto.OrderAsynNoticeDto;
import vip.xiaonuo.business.upay.pojo.dto.UPayDto;
import vip.xiaonuo.business.upay.pojo.vo.UpayResp;
import vip.xiaonuo.business.upay.service.UpayService;
import vip.xiaonuo.core.context.constant.ConstantContextHolder;
import vip.xiaonuo.core.enums.SerialNoEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.pojo.upay.UPayConfig;
import vip.xiaonuo.core.util.*;

import javax.annotation.Resource;
import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class UPayServiceImpl implements UpayService {

    @Resource
    @Lazy
    private TransactionService transactionService;

    @Resource
    private AccountService accountService;

    @Resource
    private PlayerService playerService;

    @Resource
    private SysConfigService sysConfigService;

    @Override
    public UpayResp createOrder(UPayDto payDto) {
        MoneyTypeEnums moneyTypeEnums = MoneyTypeEnums.valueOf(payDto.getMoneyType());
        String moneyType = moneyTypeEnums.name();

        ChainTypeEnums chainTypeEnums = ChainTypeEnums.valueOf(payDto.getChainType());
        String chainType = chainTypeEnums.getCode();
        UPayConfig payConfig = getPayConfig();

        Map<String,String> paramMap = new TreeMap<>();
        paramMap.put("appId",payConfig.getUpayAppId());
        paramMap.put("chainType",chainType);
        paramMap.put("fiatAmount",String.valueOf(payDto.getPayMoney()));
        paramMap.put("fiatCurrency",moneyType);
        paramMap.put("merchantOrderNo",payDto.getOrderSn());
        paramMap.put("notifyUrl",payConfig.getUpayNotifyUrl());
//        paramMap.put("callback_url",payConfig.getUpayCallbackUrl());
        Iterator<Map.Entry<String,String>> it = paramMap.entrySet().iterator();
        StringBuilder paramString = new StringBuilder("");
        while(it.hasNext()){
            Map.Entry<String,String> entry = it.next();
            paramString.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        paramString.substring(0,paramString.length()-1);
        paramString.append("appSecret").append("=").append(payConfig.getUpayAppSecret());
        String sign = Md5Utils.hash(paramString.toString());

        paramMap.put("signature",sign.toUpperCase());
        log.info("账户：{} ### upay请求参数：{}",payDto.getPayUsername(),JSON.toJSONString(paramMap));

        try {
            String result = HttpUtils.postJson(payConfig.getUpayCreateOrderUrl(),paramMap);
            log.info("账户：{} ### upay支付返回：{}",payDto.getPayUsername(),result);
            if(StringUtils.isNotEmpty(result)){
                UpayResp resp = JSON.parseObject(result, UpayResp.class);
                return resp;
            }
        } catch (Exception e){
            log.error("账户：{} ### upay支付失败：{}",payDto.getPayUsername(),e);
            throw new ServiceException(MessageUtils.get("transaction.recharge..upay.error"));
        }
        return new UpayResp();
    }

    @Override
    @Transactional
    public String callbackNotice(OrderAsynNoticeDto orderAsynNoticeDto) {
        log.info("upay支付成功回调通知：{}",JSON.toJSONString(orderAsynNoticeDto));
        UPayConfig payConfig = getPayConfig();
        if(ObjectUtils.isNotEmpty(payConfig) && StringUtils.isNotEmpty(payConfig.getUpayAppId()) && !payConfig.getUpayAppId().equals(orderAsynNoticeDto.getAppId())){
            log.error("upay回调通知处理失败，订单号:{} msg:{}",orderAsynNoticeDto.getMerchantOrderNo(),"错误的appid");
            return "OK";
        }

        List<Transaction> transList = transactionService.lambdaQuery().eq(Transaction::getOrderNum,orderAsynNoticeDto.getMerchantOrderNo()).orderByAsc(Transaction::getCreateTime).list();
        if(CollectionUtils.isEmpty(transList)){
            log.error("upay回调通知处理失败，订单号:{} msg:{}",orderAsynNoticeDto.getMerchantOrderNo(),"找不到该订单号");
            return "OK";
        }
        Transaction transaction = transList.get(0);
        if("2".equals(Integer.valueOf(transaction.getStatus()))){
            log.error("upay回调通知处理失败，订单号:{} msg:{}",orderAsynNoticeDto.getMerchantOrderNo(),"该订单已处理");
            return "OK";
        }

        if(!"1".equals(orderAsynNoticeDto.getStatus())){
            transaction.setStatus("0".equals(orderAsynNoticeDto.getStatus()) ? 0 : ("2".equals(orderAsynNoticeDto.getStatus()) ? 5 : ("3".equals(orderAsynNoticeDto.getStatus()) ? 3 : 6)));
            transaction.setUpdateTime(new Date());
            transaction.setRemarks(JSON.toJSONString(orderAsynNoticeDto));
            try {
                transactionService.updateById(transaction);
            }catch (Exception e){
                return "OK";
            }
            return "OK";
        }

        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(transaction.getPlayerId()));

        transaction.setStatus(2);
        transaction.setUpdateTime(new Date());
        transaction.setRemarks(JSON.toJSONString(orderAsynNoticeDto));

        //增加资金
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder frozen = AccountChangeBalanceParam.builder()
                .playerId(transaction.getPlayerId())
                .type(transaction.getAccountType())
                .currencyCode(transaction.getTransactionType())
                .businessType(AccountBillBusinessTypeEnum.DEPOSIT)
                .optType(AccountBillOptTypeEnum.ADD)
                .businessNo(transaction.getOrderNum())
                .balance(transaction.getAccount())
                .updateUser(transaction.getChenckId());
        AccountChangeBalanceResult accountChangeBalanceResult = accountService.changeBalance(frozen.build());
        transaction.setRechargeFrontAccount(accountChangeBalanceResult.getAccountBill().getBalanceAfter().subtract(accountChangeBalanceResult.getAccountBill().getBalanceChanged()));
        transaction.setRechargeAfterAccount(accountChangeBalanceResult.getAccountBill().getBalanceAfter());
        transactionService.updateById(transaction);
        return "OK";
    }

    private UPayConfig getPayConfig(){
        return sysConfigService.getSysConfigList("UPAY");
//        UPayConfig uPayConfig = new UPayConfig();
//        uPayConfig.setUpayAppId("QkHgx3VD");
//        uPayConfig.setUpayAppSecret("6oKVIc50xrZ6885o");
//        uPayConfig.setUpayCreateOrderUrl("https://api.upay.ink/v1/api/open/order/apply");
//        uPayConfig.setUpayNotifyUrl("https://www.baidu.com");
//        return uPayConfig;
    }

//    public static void main(String[] args) {
//        UPayServiceImpl uPayService = new UPayServiceImpl();
//        UPayDto payDto = new UPayDto();
//        payDto.setPayMoney(BigDecimal.valueOf(3));
//        payDto.setOrderSn(UUIDUtil.createOrderId(SerialNoEnum.PAY));
//        payDto.setChainType("TRC20");
//        payDto.setMoneyType("USD");
//        UpayResp resp = uPayService.createOrder(payDto);
//        System.out.println(resp.getData().getPayUrl().replace("\\u0026","&"));
//    }
}
