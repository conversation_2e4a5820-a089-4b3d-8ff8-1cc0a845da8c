package vip.xiaonuo.business.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import vip.xiaonuo.business.account.entity.Account;

/**
 * 账户表
 *
 * <AUTHOR>
 * @date 2022-04-06 13:49:40
 */
public interface AccountMapper extends BaseMapper<Account> {
    Account queryBalanceForLock(@Param("playerId") Long playerId, @Param("type") Integer type, @Param("currencyCode") String currencyCode);
}
