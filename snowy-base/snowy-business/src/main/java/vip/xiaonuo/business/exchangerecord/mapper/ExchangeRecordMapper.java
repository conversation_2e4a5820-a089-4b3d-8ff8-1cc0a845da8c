package vip.xiaonuo.business.exchangerecord.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import vip.xiaonuo.business.exchangerecord.entity.ExchangeRecord;
import vip.xiaonuo.business.exchangerecord.param.ExchangeRecordParam;
import vip.xiaonuo.business.exchangerecord.result.ExchangeRecordResult;

public interface ExchangeRecordMapper extends BaseMapper<ExchangeRecord> {
    Page<ExchangeRecordResult> pageResult(Page<ExchangeRecordResult> defaultPage, @Param(Constants.WRAPPER) ExchangeRecordParam param);
}
