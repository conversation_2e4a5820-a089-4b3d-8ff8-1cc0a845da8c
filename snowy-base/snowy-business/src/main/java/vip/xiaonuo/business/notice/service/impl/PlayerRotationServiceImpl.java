/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.notice.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import vip.xiaonuo.business.notice.entity.PlayerRotation;
import vip.xiaonuo.business.notice.enums.PlayerRotationExceptionEnum;
import vip.xiaonuo.business.notice.enums.PlayerRotationStatusEnum;
import vip.xiaonuo.business.notice.mapper.PlayerRotationMapper;
import vip.xiaonuo.business.notice.param.PlayerRotationParam;
import vip.xiaonuo.business.notice.service.PlayerRotationService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.pojo.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.core.util.MessageUtils;

import java.util.Date;
import java.util.List;

/**
 * 轮播图service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-07 21:51:43
 */
@Service
public class PlayerRotationServiceImpl extends ServiceImpl<PlayerRotationMapper, PlayerRotation> implements PlayerRotationService {

    @Override
    public PageResult<PlayerRotation> page(PlayerRotationParam playerRotationParam) {
        QueryWrapper<PlayerRotation> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(playerRotationParam)) {

            // 根据状态（字典 0草稿 1发布 2撤回 3删除） 查询
            if (ObjectUtil.isNotEmpty(playerRotationParam.getStatus())) {
                queryWrapper.lambda().eq(PlayerRotation::getStatus, playerRotationParam.getStatus());
            }
            if (ObjectUtil.isNotEmpty(playerRotationParam.getTitle())) {
                queryWrapper.lambda().like(PlayerRotation::getTitle, playerRotationParam.getTitle());
            }
            if (ObjectUtil.isNotEmpty(playerRotationParam.getLanguageCode())) {
                queryWrapper.lambda().like(PlayerRotation::getLanguageCode, playerRotationParam.getLanguageCode());
            }
            if (ObjectUtil.isNotEmpty(playerRotationParam.getOrgCode())) {
                queryWrapper.lambda().like(PlayerRotation::getOrgCode, playerRotationParam.getOrgCode());
            }
        }
        queryWrapper.orderByAsc("SORT","CREATE_TIME");
        return new PageResult<>(this.page(new Page<>(playerRotationParam.getPage(), playerRotationParam.getLimit()), queryWrapper));

    }

    @Override
    public List<PlayerRotation> list(PlayerRotationParam playerRotationParam) {
        QueryWrapper<PlayerRotation> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(playerRotationParam)) {
            // 根据状态（字典 0草稿 1发布 2撤回 3删除） 查询
            if (ObjectUtil.isNotEmpty(playerRotationParam.getStatus())) {
                queryWrapper.lambda().eq(PlayerRotation::getStatus, playerRotationParam.getStatus());
            }
            if (ObjectUtil.isNotEmpty(playerRotationParam.getLanguageCode())) {
                queryWrapper.lambda().eq(PlayerRotation::getLanguageCode, playerRotationParam.getLanguageCode());
            }
            if (ObjectUtil.isNotEmpty(playerRotationParam.getOrgCode())) {
                queryWrapper.lambda().like(PlayerRotation::getOrgCode, playerRotationParam.getOrgCode());
            }
        }
        queryWrapper.orderByAsc("SORT","CREATE_TIME");
        return this.list(queryWrapper);
    }

    @Override
    public void add(PlayerRotationParam playerRotationParam) {
        PlayerRotation playerRotation = new PlayerRotation();
        BeanUtil.copyProperties(playerRotationParam, playerRotation);
        this.save(playerRotation);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<PlayerRotationParam> playerRotationParamList) {
        playerRotationParamList.forEach(playerRotationParam -> {
        PlayerRotation playerRotation = this.queryPlayerRotation(playerRotationParam);
            this.removeById(playerRotation.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PlayerRotationParam playerRotationParam) {
        PlayerRotation playerRotation = this.queryPlayerRotation(playerRotationParam);
        BeanUtil.copyProperties(playerRotationParam, playerRotation);
        this.updateById(playerRotation);
    }

    @Override
    public PlayerRotation detail(PlayerRotationParam playerRotationParam) {
        return this.queryPlayerRotation(playerRotationParam);
    }

    @Override
    public void changeStatus(PlayerRotationParam playerRotationParam) {
        PlayerRotation playerRotation = this.queryPlayerRotation(playerRotationParam);
        //校验参数，检查状态是否正确
        checkParam(playerRotationParam, false);
        playerRotation.setStatus(playerRotationParam.getStatus());
        playerRotation.setUpdateTime(new Date());
        this.updateById(playerRotation);
    }

    /**
     * 获取轮播图
     *
     * <AUTHOR>
     * @date 2022-04-07 21:51:43
     */
    private PlayerRotation queryPlayerRotation(PlayerRotationParam playerRotationParam) {
        PlayerRotation playerRotation = this.getById(playerRotationParam.getId());
        if (ObjectUtil.isNull(playerRotation)) {
            throw new ServiceException(PlayerRotationExceptionEnum.NOT_EXIST);
        }
        return playerRotation;
    }

    /**
     * 校验参数，判断状态是否正确
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    private void checkParam(PlayerRotationParam playerRotationParam, boolean isAddOrEdit) {
        //保存或编辑时，传递的状态参数应为草稿，或发布
        Integer status = playerRotationParam.getStatus();
        if (isAddOrEdit) {
            if (!PlayerRotationStatusEnum.DRAFT.getCode().equals(status) &&
                    !PlayerRotationStatusEnum.PUBLIC.getCode().equals(status)) {
                throw new ServiceException(PlayerRotationExceptionEnum.ROTATION_STATUS_ERROR);
            }
        } else {
            //修改状态时，传递的状态参数应为撤回或删除或发布
            if (!PlayerRotationStatusEnum.CANCEL.getCode().equals(status) &&
                    !PlayerRotationStatusEnum.DELETED.getCode().equals(status) &&
                    !PlayerRotationStatusEnum.PUBLIC.getCode().equals(status)) {
                throw new ServiceException(PlayerRotationExceptionEnum.ROTATION_STATUS_ERROR);
            }
        }
    }
}
