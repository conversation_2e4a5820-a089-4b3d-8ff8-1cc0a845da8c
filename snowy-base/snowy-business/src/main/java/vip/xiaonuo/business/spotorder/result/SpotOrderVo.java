package vip.xiaonuo.business.spotorder.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import vip.xiaonuo.core.util.MarketUtils;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SpotOrderVo {
    @ApiModelProperty("logo")
    private String logo;
    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     * 类型(1:买涨;2:买跌;)
     */
    @ApiModelProperty("类型(1:买涨;2:买跌;)")
    private Integer type;

    /**
     * 交易类型表示,如ETH_USDT
     */
    @ApiModelProperty("如ETH_USDT")
    private String symbol;

    @ApiModelProperty("交易对名称")
    public String getSymbolName() {
        return MarketUtils.getSymbolName(symbol);
    }

    /**
     * 秒
     */
    @ApiModelProperty("秒")
    private Integer seconds;
    @ApiModelProperty("剩余秒")
    private Integer surplusSeconds;
    /**
     * 下单时间
     */
    @ApiModelProperty("下单时间")
    @JsonFormat(pattern = vip.xiaonuo.core.util.DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date orderTime;

    /**
     * 下单价格
     */
    @ApiModelProperty("下单价格")
    private BigDecimal orderPrice;

    /**
     * 下单数量
     */
    @ApiModelProperty("下单数量")
    private BigDecimal quantity;

    /**
     * 平仓价格
     */
    @ApiModelProperty("平仓价格")
    private BigDecimal closePrice;

    /**
     * 盈亏
     */
    @ApiModelProperty("盈亏")
    private BigDecimal profit;

    /**
     * 0:持仓,1:平仓
     */
    @ApiModelProperty("0:持仓,1:平仓")
    private Integer status;
}
