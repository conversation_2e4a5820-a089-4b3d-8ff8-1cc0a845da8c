/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.followerapplication.param;

import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.math.BigDecimal;

/**
* 交易员申请参数类
 *
 * <AUTHOR>
 * @date 2025-07-18 15:55:41
*/
@Data
public class FollowerApplicationParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空，请检查playerId参数", groups = {add.class, edit.class})
    private Long playerId;

    /**
     * 个人简介
     */
    @NotBlank(message = "个人简介不能为空，请检查profile参数", groups = {add.class, edit.class})
    private String profile;

    /**
     * 标签（英文逗号分隔）
     */
    @NotBlank(message = "标签（英文逗号分隔）不能为空，请检查tagList参数", groups = {add.class, edit.class})
    private String tagList;

    /**
     * 交易经验
     */
    @NotBlank(message = "交易经验不能为空，请检查experience参数", groups = {add.class, edit.class})
    private String experience;

    /**
     * 预期年化收益率 (%)
     */
    @NotNull(message = "预期年化收益率 (%)不能为空，请检查expectedAnnualYield参数", groups = {add.class, edit.class})
    private String expectedAnnualYield;

    /**
     * 风险偏好
     */
    @NotBlank(message = "风险偏好不能为空，请检查riskPreference参数", groups = {add.class, edit.class})
    private String riskPreference;

    /**
     * 交易截图URL
     */
    @NotBlank(message = "交易截图URL不能为空，请检查tradeUrl参数", groups = {add.class, edit.class})
    private String tradeUrl;

    /**
     * 联系方式
     */
    @NotBlank(message = "联系方式不能为空，请检查contactInfo参数", groups = {add.class, edit.class})
    private String contactInfo;

    /**
     * 审核状态（0待审核，1通过，2拒绝）
     */
    @NotNull(message = "审核状态（0待审核，1通过，2拒绝）不能为空，请检查auditStatus参数", groups = {add.class, edit.class})
    private Integer auditStatus;

    /**
     * 审核备注
     */
//    @NotBlank(message = "审核备注不能为空，请检查auditRemark参数", groups = {add.class, edit.class})
    private String auditRemark;
}
