package vip.xiaonuo.business.transaction.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value = "用户提现请求实体")
@Data
public class BankWithdrawalParam implements Serializable {
    @ApiModelProperty(value = "提币数量", required = true)
    @NotNull
    private BigDecimal quantity;
    @ApiModelProperty(value = "（币种代码）", required = true)
    @NotBlank
    private String currencyCode;
    @ApiModelProperty(value = "提款银行账号Id")
    @NotBlank
    private String bankAccountId;
    @ApiModelProperty(value = "提款密码")
    @NotBlank
    private String password;
}
