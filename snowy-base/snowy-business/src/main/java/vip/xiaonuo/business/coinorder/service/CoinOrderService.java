/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.coinorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.business.coinorder.entity.CoinOrder;
import vip.xiaonuo.business.coinorder.param.CoinOrderParam;
import vip.xiaonuo.business.coinorder.param.PlaceCoinOrderModel;
import vip.xiaonuo.business.coinorder.param.QueryCoinOrderModel;
import vip.xiaonuo.business.coinorder.result.CoinOrderStatisticModel;
import vip.xiaonuo.business.coinorder.result.CoinOrderVo;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.core.pojo.dto.TickerDTO;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * 币币订单service接口
 *
 * <AUTHOR>
 * @date 2022-09-14 11:02:20
 */
public interface CoinOrderService extends IService<CoinOrder> {

    /**
     * 查询币币订单
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    PageResult<CoinOrder> page(CoinOrderParam coinOrderParam);

    PageResult<CoinOrderVo> page(Long playerId, QueryCoinOrderModel model);

    /**
     * 币币订单列表
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    List<CoinOrder> list(CoinOrderParam coinOrderParam);

    /**
     * 添加币币订单
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    void add(CoinOrderParam coinOrderParam);

    /**
     * 删除币币订单
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    void delete(List<CoinOrderParam> coinOrderParamList);

    /**
     * 编辑币币订单
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    void edit(CoinOrderParam coinOrderParam);

    /**
     * 查看币币订单
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    CoinOrder detail(CoinOrderParam coinOrderParam);

    /**
     * 下单
     *
     * @param playerId
     * @param model
     * @return
     */
    CoinOrder placeOrder(Long playerId, PlaceCoinOrderModel model);

    /**
     * 撤销委托
     *
     * @param playerId
     * @param orderNo
     */
    void revokeEntrust(Long playerId, String orderNo, Long updateBy);

    void endCoinOrder(String coinOrderNo, BigDecimal c, Long updateBy);

    CoinOrderStatisticModel findCoinOrderStatistic(HomeStatisticParam param);
}
