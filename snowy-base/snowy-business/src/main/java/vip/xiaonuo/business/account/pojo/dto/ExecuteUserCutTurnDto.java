package vip.xiaonuo.business.account.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户割转
 */
@ApiModel(value = "用户割转")
@Data
public class ExecuteUserCutTurnDto implements Serializable {

    /**
     * 用户id
     */
    private Long playerId;
    /**
     * 转出类型(1:币币账户;2:合约账户;3:现货账户)
     */
    @ApiModelProperty(value = "转出类型(1:币币账户;2:合约账户;3:现货账户)", required = true)
    private String transferOutType;
    /**
     * 转出code 如 BTC
     */
    @ApiModelProperty(value = "转出code 如 BTC", required = true)
    private String transferOutCode;
    /**
     * 转出金额
     */
    @ApiModelProperty(value = "转出金额", required = true)
    private BigDecimal transferOutAccount;

    /**
     * 转出类型(1:币币账户;2:合约账户;3:现货账户)
     */
    @ApiModelProperty(value = " 转出类型(1:币币账户;2:合约账户;3:现货账户)", required = true)
    private String transferInType;

}
