package vip.xiaonuo.business.userproductorder.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import vip.xiaonuo.core.pojo.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.*;
import java.math.BigDecimal;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 理财订单
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:41
 */
@ApiModel(value = "订单")
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_user_product_order")
public class UserProductOrder extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "托管资金", required = true)
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", required = true)
    private Long playerId;
    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号", required = true)
    private String orderNum;
    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id", required = true)
    private Long productId;

    /**
     * 购买金额
     */
    @ApiModelProperty(value = "购买金额", required = true)
    private BigDecimal purchaseAccount;

    /**
     * 周期
     */
    @ApiModelProperty(value = "周期", required = true)
    private Integer cycle;

    /**
     * 购买时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "购买时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    @ApiModelProperty(value = "购买时间", required = true)
    private Date purchaseTime;

    /**
     * 计息开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计息开始时间", databaseFormat = "yyyy-MM-dd", format = "yyyy-MM-dd", width = 20)
    @ApiModelProperty(value = "计息开始时间", required = true)
    private Date startInterestData;

    /**
     * 截止计息时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "截止计息时间", databaseFormat = "yyyy-MM-dd", format = "yyyy-MM-dd", width = 20)
    @ApiModelProperty(value = "截止计息时间", required = true)
    private Date endInterestData;

    /**
     * 下一计息时间
     */
    @Excel(name = "下一计息时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "下一计息时间", required = true)
    private Date nextInterestData;

    /**
     * 计息利率
     */
    @ApiModelProperty(value = "计息利率", required = true)
    private BigDecimal profitRate;

    /**
     * 认购金额区间
     */
    @ApiModelProperty(value = "认购金额区间", required = true)
    private String productAccountInfo;

    /**
     * 认购利息区间
     */
    @ApiModelProperty(value = "认购利息区间", required = true)
    private String profitRateInfo;

    /**
     * 预计收益区间
     */
    @ApiModelProperty(value = "预计收益区间", required = true)
    private String anticipateProfit;

    /**
     * 剩余天数
     */
    @ApiModelProperty(value = "剩余天数", required = true)
    private Integer surplusDay;

    /**
     * 收益金额
     */
    @ApiModelProperty(value = "收益金额", required = true)
    private BigDecimal profitAccount;

    /**
     * 违约金比例
     */
    @ApiModelProperty(value = "违约金比例", required = true)
    private BigDecimal violationRate;

    /**
     * 违约金额
     */
    @ApiModelProperty(value = "违约金额", required = true)
    private BigDecimal violationAccount;

    /**
     * 退还本金
     */
    @ApiModelProperty(value = "退还本金", required = true)
    private BigDecimal returnPrincipal;

    /**
     * 订单状态 1 进行中 2 结束 3 赎回
     */
    @ApiModelProperty(value = "订单状态 1 进行中 2 结束 3 赎回", required = true)
    private Integer productOrderStatus;

    /**
     * 产品信息
     */
    @ApiModelProperty(value = "产品信息", required = true)
    private String productInfo;
    /**
     * 当日违约金金额
     */
    @ApiModelProperty(value = "当日违约金金额", required = true)
    private BigDecimal violationDayAccount;

    /**
     * 计算违约金时间
     */
    @ApiModelProperty(value = "计算违约金时间", required = true)
    private Date violationDate;



}
