package vip.xiaonuo.business.exchangerecord.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PortalExchangeRecordResult {
    private Long id;
    private String orderNo;
    private String fromCurrencyCode;
    @ApiModelProperty("兑换数量")
    private BigDecimal fromQuantity;
    @ApiModelProperty("兑换币种")
    private String currencyCode;
    @ApiModelProperty("到账数量")
    private BigDecimal quantity;
    private BigDecimal price;
    @ApiModelProperty("手续费")
    private BigDecimal orderFee;
    private String createTime;
    //0:委托,1:成交
    @ApiModelProperty("0:委托,1:成交")
    private Integer status;
}
