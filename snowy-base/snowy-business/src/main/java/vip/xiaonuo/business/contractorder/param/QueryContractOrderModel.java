package vip.xiaonuo.business.contractorder.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("合约单查询")
public class QueryContractOrderModel {
    //0:委托,1:持仓,2:平仓,3:撤销委托
    @ApiModelProperty("0:委托,1:持仓,2:已完成单,3:撤销委托,4:锁仓")
    @NotNull
    private Integer status;
    @ApiModelProperty("如ETH_USDT")
    private String symbol;
}
