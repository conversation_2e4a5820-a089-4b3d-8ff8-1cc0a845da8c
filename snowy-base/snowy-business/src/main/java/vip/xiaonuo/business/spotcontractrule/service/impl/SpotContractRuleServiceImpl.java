/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.spotcontractrule.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import vip.xiaonuo.business.spotcontractrule.entity.SpotContractRule;
import vip.xiaonuo.business.spotcontractrule.enums.SpotContractRuleExceptionEnum;
import vip.xiaonuo.business.spotcontractrule.mapper.SpotContractRuleMapper;
import vip.xiaonuo.business.spotcontractrule.param.SpotContractRuleParam;
import vip.xiaonuo.business.spotcontractrule.service.SpotContractRuleService;
import vip.xiaonuo.business.spotrule.entity.SpotRule;
import vip.xiaonuo.business.spotrule.result.PortalSpotRuleResult;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.enums.CommonStatusEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 合约下单规则表service接口实现类
 *
 * <AUTHOR>
 * @date 2024-04-08 20:04:39
 */
@Service
public class SpotContractRuleServiceImpl extends ServiceImpl<SpotContractRuleMapper, SpotContractRule> implements SpotContractRuleService {

    @Override
    public PageResult<SpotContractRule> page(SpotContractRuleParam spotContractRuleParam) {
        QueryWrapper<SpotContractRule> queryWrapper = new QueryWrapper<>();
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SpotContractRule> list(SpotContractRuleParam spotContractRuleParam) {
        return this.list();
    }

    @Override
    public void add(SpotContractRuleParam spotContractRuleParam) {
        SpotContractRule spotContractRule = new SpotContractRule();
        BeanUtil.copyProperties(spotContractRuleParam, spotContractRule);
        this.save(spotContractRule);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<SpotContractRuleParam> spotContractRuleParamList) {
        spotContractRuleParamList.forEach(spotContractRuleParam -> {
        SpotContractRule spotContractRule = this.querySpotContractRule(spotContractRuleParam);
            this.removeById(spotContractRule.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(SpotContractRuleParam spotContractRuleParam) {
        SpotContractRule spotContractRule = this.querySpotContractRule(spotContractRuleParam);
        BeanUtil.copyProperties(spotContractRuleParam, spotContractRule);
        this.updateById(spotContractRule);
    }

    @Override
    public SpotContractRule detail(SpotContractRuleParam spotContractRuleParam) {
        return this.querySpotContractRule(spotContractRuleParam);
    }

    /**
     * 获取合约下单规则表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:04:39
     */
    private SpotContractRule querySpotContractRule(SpotContractRuleParam spotContractRuleParam) {
        SpotContractRule spotContractRule = this.getById(spotContractRuleParam.getId());
        if (ObjectUtil.isNull(spotContractRule)) {
            throw new ServiceException(SpotContractRuleExceptionEnum.NOT_EXIST);
        }
        return spotContractRule;
    }

    @Override
    public List<PortalSpotRuleResult> queryAll() {
        List<SpotContractRule> list = lambdaQuery().orderByAsc(SpotContractRule::getSeconds, SpotContractRule::getMaxProfitRate).list();
        List<PortalSpotRuleResult> portalSpotRuleResults = new ArrayList<>();
        for (SpotContractRule spotRule : list) {
            PortalSpotRuleResult p = new PortalSpotRuleResult();
            p.setId(spotRule.getId());
            p.setSeconds(spotRule.getSeconds());
            p.setMinQuantity(spotRule.getMinQuantity());
            p.setMaxQuantity(spotRule.getMaxQuantity());
            p.setProfitability(spotRule.getMaxProfitRate());
            portalSpotRuleResults.add(p);
        }
        return portalSpotRuleResults;
    }
}
