<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.business.spotorder.mapper.SpotOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="vip.xiaonuo.business.spotorder.entity.SpotOrder">
        <id column="id" property="id" />
        <result column="player_id" property="playerId" />
        <result column="order_no" property="orderNo" />
        <result column="type" property="type" />
        <result column="symbol" property="symbol" />
        <result column="seconds" property="seconds" />
        <result column="order_time" property="orderTime" />
        <result column="order_price" property="orderPrice" />
        <result column="quantity" property="quantity" />
        <result column="close_price" property="closePrice" />
        <result column="close_time" property="closeTime" />
        <result column="profitability" property="profitability" />
        <result column="order_fee" property="orderFee" />
        <result column="profit" property="profit" />
        <result column="status" property="status" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="playerAccount" property="playerAccount" />
        <result column="remark" property="remark" />
    </resultMap>

    <select id="getOverdueOrder" resultMap="BaseResultMap">
        SELECT
        o.*
        FROM
        tt_spot_order o
        WHERE
        o.`status` = 0
        AND date_add( o.order_time, INTERVAL o.seconds + 1 SECOND ) <![CDATA[ < ]]> #{date}
    </select>

    <select id="queryPage" resultMap="BaseResultMap">
        SELECT
            so.*,
            p.account playerAccount,
            p.remark
        FROM
            tt_spot_order so
            LEFT JOIN tt_player p ON p.id = so.player_id
        <where>
            <if test="ew.playerAccount != null and ew.playerAccount != ''">
                and p.account = #{ew.playerAccount}
            </if>
            <if test="ew.orderNo != null and ew.orderNo != ''">
                and so.order_no = #{ew.orderNo}
            </if>
            <if test="ew.type != null">
                and so.type = #{ew.type}
            </if>
            <if test="ew.status != null">
                and so.status = #{ew.status}
            </if>
            <if test="ew.quantity != null">
                and so.quantity = #{ew.quantity}
            </if>
            <if test="ew.symbol != null and ew.symbol != ''">
                and so.symbol = #{ew.symbol}
            </if>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
                and p.agent_id in
                <foreach item="item" index="index" collection="ew.agentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
                and p.agent_id = #{ew.loginUserId}
            </if>
            <if test="ew.orgId != null and ew.orgId != ''">
                AND p.agent_id in(select id from sys_emp where org_id = #{ew.orgId})
            </if>
        </where>
        order by so.create_time desc
    </select>

    <select id="findSpotOrderStatistic" resultType="vip.xiaonuo.business.spotorder.result.SpotOrderStatisticModel">
        SELECT
            SUM(IFNULL(so.quantity, 0 )) spotOrderTotalAmount,
            SUM( CASE WHEN DATE ( so.create_time ) = #{ew.date} THEN IFNULL(so.quantity, 0 ) ELSE 0 END ) spotOrderDayAmount
        FROM
            tt_spot_order so
            LEFT JOIN tt_player p ON p.id = so.player_id
        <where>
            so.status = 1
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT_LINE">
                and p.org_code = #{ew.loginUserOrgCode}
            </if>
            <if test="ew.adminRoleType == @vip.xiaonuo.core.enums.AdminRoleTypeEnum@AGENT">
                and p.agent_id = #{ew.loginUserId}
            </if>
        </where>
    </select>

    <select id="getPage" resultType="vip.xiaonuo.business.spotorder.result.SpotOrderVo">
        select tt.* from (
            SELECT
                so.*,
                p.account playerAccount,
                p.remark
            FROM
                tt_spot_order so
            LEFT JOIN tt_player p ON p.id = so.player_id
            <where>
                and so.player_id = #{playerId}
                <if test="status != null">
                    and so.status = #{status}
                </if>
            </where>
            union
            SELECT
                so.*,
                p.account playerAccount,
                p.remark
            FROM
                tt_contract_spot_order so
            LEFT JOIN tt_player p ON p.id = so.player_id
            <where>
                and so.player_id = #{playerId}
                <if test="status != null">
                    and so.status = #{status}
                </if>
            </where>
        ) tt
        order by tt.create_time desc
    </select>
</mapper>
