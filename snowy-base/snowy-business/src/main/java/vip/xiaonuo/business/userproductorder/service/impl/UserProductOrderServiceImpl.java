package vip.xiaonuo.business.userproductorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.account.param.AccountChangeBalanceParam;
import vip.xiaonuo.business.account.service.AccountService;
import vip.xiaonuo.business.accountbill.enums.AccountBillBusinessTypeEnum;
import vip.xiaonuo.business.accountbill.enums.AccountBillOptTypeEnum;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.product.entity.Product;
import vip.xiaonuo.business.product.service.ProductService;
import vip.xiaonuo.business.statistic.param.HomeStatisticParam;
import vip.xiaonuo.business.userproductorder.entity.UserProductOrder;
import vip.xiaonuo.business.userproductorder.enums.UserProductOrderExceptionEnum;
import vip.xiaonuo.business.userproductorder.mapper.UserProductOrderMapper;
import vip.xiaonuo.business.userproductorder.param.UserProductOrderParam;
import vip.xiaonuo.business.userproductorder.pojo.dto.ExecuteUserPurchaseOrderDto;
import vip.xiaonuo.business.userproductorder.pojo.dto.ExecuteUserRedeemOrderDto;
import vip.xiaonuo.business.userproductorder.pojo.vo.ExecuteUserOrderVo;
import vip.xiaonuo.business.userproductorder.pojo.vo.FindOrderForPageVo;
import vip.xiaonuo.business.userproductorder.result.UserProductOrderStatisticModel;
import vip.xiaonuo.business.userproductorder.service.UserProductOrderService;
import vip.xiaonuo.core.enums.SerialNoEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.util.DateUtil;
import vip.xiaonuo.core.util.MessageUtils;
import vip.xiaonuo.core.util.PageUtil;
import vip.xiaonuo.core.util.UUIDUtil;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 理财订单service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:41
 */
@Slf4j
@Service
public class UserProductOrderServiceImpl extends ServiceImpl<UserProductOrderMapper, UserProductOrder> implements UserProductOrderService {

    @Autowired
    UserProductOrderMapper userProductOrderMapper;

    @Autowired
    ProductService productService;

    @Autowired
    PlayerService playerService;
    @Autowired
    AccountService accountService;

    private Lock lock = new ReentrantLock();

    @Override
    public PageResult<UserProductOrder> page(UserProductOrderParam userProductOrderParam) {
        QueryWrapper<UserProductOrder> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(userProductOrderParam)) {

            // 根据用户id 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getPlayerId())) {
                queryWrapper.lambda().eq(UserProductOrder::getPlayerId, userProductOrderParam.getPlayerId());
            }
            // 根据产品id 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getProductId())) {
                queryWrapper.lambda().eq(UserProductOrder::getProductId, userProductOrderParam.getProductId());
            }
            // 根据购买金额 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getPurchaseAccount())) {
                queryWrapper.lambda().eq(UserProductOrder::getPurchaseAccount, userProductOrderParam.getPurchaseAccount());
            }
            // 根据周期 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getCycle())) {
                queryWrapper.lambda().eq(UserProductOrder::getCycle, userProductOrderParam.getCycle());
            }
            // 根据购买时间 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getPurchaseTime())) {
                queryWrapper.lambda().eq(UserProductOrder::getPurchaseTime, userProductOrderParam.getPurchaseTime());
            }
            // 根据计息开始时间 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getStartInterestData())) {
                queryWrapper.lambda().eq(UserProductOrder::getStartInterestData, userProductOrderParam.getStartInterestData());
            }
            // 根据截止计息时间 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getEndInterestData())) {
                queryWrapper.lambda().eq(UserProductOrder::getEndInterestData, userProductOrderParam.getEndInterestData());
            }
            // 根据下一计息时间 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getNextInterestData())) {
                queryWrapper.lambda().eq(UserProductOrder::getNextInterestData, userProductOrderParam.getNextInterestData());
            }
            // 根据计息利率 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getProfitRate())) {
                queryWrapper.lambda().eq(UserProductOrder::getProfitRate, userProductOrderParam.getProfitRate());
            }
            // 根据认购金额区间 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getProductAccountInfo())) {
                queryWrapper.lambda().eq(UserProductOrder::getProductAccountInfo, userProductOrderParam.getProductAccountInfo());
            }
            // 根据认购利息区间 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getProfitRateInfo())) {
                queryWrapper.lambda().eq(UserProductOrder::getProfitRateInfo, userProductOrderParam.getProfitRateInfo());
            }
            // 根据剩余天数 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getSurplusDay())) {
                queryWrapper.lambda().eq(UserProductOrder::getSurplusDay, userProductOrderParam.getSurplusDay());
            }
            // 根据收益金额 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getProfitAccount())) {
                queryWrapper.lambda().eq(UserProductOrder::getProfitAccount, userProductOrderParam.getProfitAccount());
            }
            // 根据违约金比例 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getViolationRate())) {
                queryWrapper.lambda().eq(UserProductOrder::getViolationRate, userProductOrderParam.getViolationRate());
            }
            // 根据违约金额 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getViolationAccount())) {
                queryWrapper.lambda().eq(UserProductOrder::getViolationAccount, userProductOrderParam.getViolationAccount());
            }
            // 根据退还本金 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getReturnPrincipal())) {
                queryWrapper.lambda().eq(UserProductOrder::getReturnPrincipal, userProductOrderParam.getReturnPrincipal());
            }
            // 根据订单状态 1 进行中 2 结束 3 赎回 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getProductOrderStatus())) {
                queryWrapper.lambda().eq(UserProductOrder::getProductOrderStatus, userProductOrderParam.getProductOrderStatus());
            }
            // 根据产品信息 查询
            if (ObjectUtil.isNotEmpty(userProductOrderParam.getProductInfo())) {
                queryWrapper.lambda().eq(UserProductOrder::getProductInfo, userProductOrderParam.getProductInfo());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<UserProductOrder> list(UserProductOrderParam userProductOrderParam) {
        return this.list();
    }

    @Override
    public void add(UserProductOrderParam userProductOrderParam) {
        UserProductOrder userProductOrder = new UserProductOrder();
        BeanUtil.copyProperties(userProductOrderParam, userProductOrder);
        this.save(userProductOrder);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<UserProductOrderParam> userProductOrderParamList) {
        userProductOrderParamList.forEach(userProductOrderParam -> {
            UserProductOrder userProductOrder = this.queryUserProductOrder(userProductOrderParam);
            this.removeById(userProductOrder.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(UserProductOrderParam userProductOrderParam) {
        UserProductOrder userProductOrder = this.queryUserProductOrder(userProductOrderParam);
        BeanUtil.copyProperties(userProductOrderParam, userProductOrder);
        this.updateById(userProductOrder);
    }

    @Override
    public UserProductOrder detail(UserProductOrderParam userProductOrderParam) {
        return this.queryUserProductOrder(userProductOrderParam);
    }

    /**
     * 分页查询订单信息
     *
     * @param userProductOrderParam
     * @return
     */
    @Override
    public PageResult<FindOrderForPageVo> findOrderForPage(UserProductOrderParam userProductOrderParam) {
        return new PageResult<>(userProductOrderMapper.findOrderForPage(PageFactory.defaultPage(), userProductOrderParam));
    }

    /**
     * 用户购买理财产品
     *
     * @param dto
     * @return
     */
    @Transactional
    @Override
    public ResponseData executeUserPurchaseOrder(ExecuteUserPurchaseOrderDto dto) {
        log.info("用户购买理财产品=>:{}", JSONObject.toJSONString(dto));
        if (ObjectUtils.isEmpty(dto)) {
            return ResponseData.error(MessageUtils.get("error"));
        }
        if (ObjectUtils.isEmpty(dto.getProductId())) {
            return ResponseData.error(MessageUtils.get("user.order.product.id.error"));
        }
        if (ObjectUtils.isEmpty(dto.getPurchaseChannel())) {
            return ResponseData.error(MessageUtils.get("user.order.chennel.error"));
        }
        if (dto.getAccount().compareTo(BigDecimal.ZERO) < 1) {
            return ResponseData.error(MessageUtils.get("user.order.account.error"));
        }
        if (ObjectUtils.isEmpty(dto.getOrderNum())) {
            return ResponseData.error(MessageUtils.get("user.order.num.null.error"));
        }
        //获取产品
        Product product = productService.getById(dto.getProductId());
        if (ObjectUtils.isEmpty(product)) {
            return ResponseData.error(MessageUtils.get("product.is.null.error"));
        }
        if (product.getProductStatus().equals("2")) {
            return ResponseData.error(MessageUtils.get("product.status.end.error"));
        }
        if (dto.getAccount().compareTo(product.getMinPurchaseAccount()) == -1) {
            return ResponseData.error(MessageUtils.get("product.account.min.error"));
        }

        if (dto.getAccount().compareTo(product.getMaxPurchaseAccount()) == 1) {
            return ResponseData.error(MessageUtils.get("product.account.max.error"));
        }
        //校验用户是否存在
        Player user = playerService.getById(dto.getPlayerId());
        if (ObjectUtils.isEmpty(user)) {
            return ResponseData.error(MessageUtils.get("product.is.null.error"));
        }
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(user.getId()));

        //生成购买订单
        UserProductOrder order = new UserProductOrder();
        order.setPlayerId(dto.getPlayerId());
        order.setOrderNum(dto.getOrderNum());
        order.setProductId(product.getId());
        order.setPurchaseAccount(dto.getAccount());
        order.setCycle(product.getCycle());
        order.setPurchaseTime(new Date());
        order.setStartInterestData(DateUtil.executeDate(new Date(), 1));
        order.setEndInterestData(DateUtil.executeDate(new Date(), product.getCycle()));
        order.setNextInterestData(DateUtil.executeDate(new Date(), 1));
        //获取费率
        BigDecimal rate = BigDecimal.ZERO;
        if (product.getMinProfitAccount().compareTo(BigDecimal.ZERO) == 0) {
            rate = product.getMaxProfitAccount();
        }
        if (product.getMaxProfitAccount().compareTo(BigDecimal.ZERO) == 0) {
            rate = product.getMaxProfitAccount();
        }
        if (product.getMinProfitAccount().compareTo(product.getMaxProfitAccount()) == 0) {
            rate = product.getMaxProfitAccount();
        }
        if (product.getMinProfitAccount().compareTo(BigDecimal.ZERO) == 1 && product.getMaxProfitAccount().compareTo(BigDecimal.ZERO) == 1) {
            rate = getRandom(product.getMinProfitAccount().multiply(new BigDecimal(100)).intValue(),product.getMaxProfitAccount().multiply(new BigDecimal(100)).intValue());
        }
        order.setProfitRate(rate);
        order.setProductAccountInfo(product.getMinPurchaseAccount() + "-" + product.getMaxPurchaseAccount());
        order.setProfitRateInfo(product.getMinProfitAccount().setScale(2) + "-" + product.getMaxProfitAccount().setScale(2));
        order.setSurplusDay(product.getCycle());
        order.setProfitAccount(BigDecimal.ZERO);
        order.setViolationRate(product.getAdvRedeemRate());
        order.setViolationAccount(BigDecimal.ZERO);
        order.setReturnPrincipal(BigDecimal.ZERO);
        order.setProductOrderStatus(1);
        order.setProductInfo(JSONObject.toJSONString(product));

        BigDecimal minAccoun = product.getMinPurchaseAccount().multiply(product.getMinProfitAccount()).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal maxAccount = product.getMaxPurchaseAccount().multiply(product.getMaxProfitAccount()).setScale(2, BigDecimal.ROUND_HALF_UP);
        order.setAnticipateProfit(minAccoun + "-" + maxAccount);
        this.save(order);
        //扣除用户资金
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder frozen = AccountChangeBalanceParam.builder()
                .playerId(dto.getPlayerId())
                .type(dto.getAccountType())
                .currencyCode(dto.getCurrencyCode())
                .businessType(AccountBillBusinessTypeEnum.COIN_POOL_ORDER)
                .optType(AccountBillOptTypeEnum.SUB)
                .businessNo(order.getOrderNum()).balance(dto.getAccount()).updateUser(dto.getPlayerId());

        accountService.changeBalance(frozen.build());

        return ResponseData.success();
    }
    /**
     * 获取两数之间的随机值
     *
     * @param x
     * @param y
     * @return
     */
    public BigDecimal getRandom(int x, int y) {
        double num = 0;
        //两个数在合法范围内，并不限制输入的数哪个更大一些
        if (x > 0 || y > 0) {
            int max = Math.max(x, y);
            int min = Math.min(x, y);
            int mid = max - min;//求差
            //产生随机数
            num = (Math.random() * (mid + 1)) + min;
        }
        if(num > 0){
            num = num/100;
        }
        return new BigDecimal(num).setScale(6,BigDecimal.ROUND_HALF_UP);
    }


    public static void main(String[] args) {
        int x = (int)(0.2*100);
        int y = (int)(0.5*100);
        double num = 0;
        //两个数在合法范围内，并不限制输入的数哪个更大一些
        if (x > 0 || y > 0) {
            int max = Math.max(x, y);
            int min = Math.min(x, y);
            int mid = max - min;//求差
            //产生随机数
            num = (Math.random() * (mid + 1)) + min;
        }
        if(num > 0){
            num = num/100;
        }
        System.out.println(num);
        System.out.println(new BigDecimal(num).setScale(6,BigDecimal.ROUND_HALF_UP));
    }
    /**
     * 用户购买理财产品
     *
     * @param dto
     * @return
     */
    @Transactional
    @Override
    public ResponseData executeAnticipateOrder(ExecuteUserPurchaseOrderDto dto) {
        log.info("用户预购买理财产品=>:{}", JSONObject.toJSONString(dto));
        if (ObjectUtils.isEmpty(dto)) {
            return ResponseData.error(MessageUtils.get("error"));
        }
        if (ObjectUtils.isEmpty(dto.getProductId())) {
            return ResponseData.error(MessageUtils.get("user.order.product.id.error"));
        }
        if (ObjectUtils.isEmpty(dto.getPurchaseChannel())) {
            return ResponseData.error(MessageUtils.get("user.order.chennel.error"));
        }
        if (dto.getAccount().compareTo(BigDecimal.ZERO) < 1) {
            return ResponseData.error(MessageUtils.get("user.order.account.error"));
        }
        //获取产品
        Product product = productService.getById(dto.getProductId());
        if (ObjectUtils.isEmpty(product)) {
            return ResponseData.error(MessageUtils.get("product.is.null.error"));
        }
        if (product.getProductStatus().equals("2")) {
            return ResponseData.error(MessageUtils.get("product.status.end.error"));
        }
        if (dto.getAccount().compareTo(product.getMinPurchaseAccount()) == -1) {
            return ResponseData.error(MessageUtils.get("product.account.min.error"));
        }

        if (dto.getAccount().compareTo(product.getMaxPurchaseAccount()) == 1) {
            return ResponseData.error(MessageUtils.get("product.account.max.error"));
        }
        //校验用户是否存在
        Player user = playerService.getById(dto.getPlayerId());
        if (ObjectUtils.isEmpty(user)) {
            return ResponseData.error(MessageUtils.get("user.is.null.error"));
        }
        //生成购买订单
        UserProductOrder order = new UserProductOrder();
        order.setPlayerId(dto.getPlayerId());
        order.setOrderNum(UUIDUtil.createOrderId(SerialNoEnum.P));
        order.setProductId(product.getId());
        order.setPurchaseAccount(dto.getAccount());
        order.setCycle(product.getCycle());
        order.setPurchaseTime(new Date());
        order.setStartInterestData(DateUtil.executeDate(new Date(), 1));
        order.setEndInterestData(DateUtil.executeDate(new Date(), product.getCycle()));
        order.setNextInterestData(DateUtil.executeDate(new Date(), 1));
        //获取费率
        BigDecimal rate = BigDecimal.ZERO;
        if (product.getMinProfitAccount().compareTo(BigDecimal.ZERO) == 0) {
            rate = product.getMaxProfitAccount();
        }
        if (product.getMaxProfitAccount().compareTo(BigDecimal.ZERO) == 0) {
            rate = product.getMaxProfitAccount();
        }
        if (product.getMinProfitAccount().compareTo(product.getMaxProfitAccount()) == 0) {
            rate = product.getMaxProfitAccount();
        }
        if (product.getMinProfitAccount().compareTo(BigDecimal.ZERO) == 1 && product.getMaxProfitAccount().compareTo(BigDecimal.ZERO) == 1) {
            double random = Math.random();
            rate = product.getMaxProfitAccount().subtract(product.getMinProfitAccount()).multiply(new BigDecimal(random)).add(product.getMaxProfitAccount());
        }
        order.setProfitRate(rate);
        order.setProductAccountInfo(product.getMinPurchaseAccount().setScale(2) + "-" + product.getMaxPurchaseAccount().setScale(2));
        order.setProfitRateInfo(product.getMinProfitAccount().setScale(2, BigDecimal.ROUND_HALF_UP) + "-" + product.getMaxProfitAccount().setScale(2, BigDecimal.ROUND_HALF_UP));
        order.setSurplusDay(product.getCycle());
        order.setProfitAccount(BigDecimal.ZERO);
        order.setViolationRate(product.getAdvRedeemRate());
        order.setViolationAccount(BigDecimal.ZERO);
        order.setReturnPrincipal(BigDecimal.ZERO);
        order.setProductOrderStatus(1);
        //只是为了
        BigDecimal minAccoun = product.getMinPurchaseAccount().multiply(product.getMinProfitAccount()).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal maxAccount = product.getMaxPurchaseAccount().multiply(product.getMaxProfitAccount()).setScale(2, BigDecimal.ROUND_HALF_UP);
        order.setAnticipateProfit(minAccoun + "-" + maxAccount);

        return ResponseData.success(order);
    }


    /**
     * 用户赎回订单
     *
     * @param dto
     * @return
     */
    @Transactional
    @SneakyThrows
    @Override
    public ResponseData executeUserRedeemOrder(ExecuteUserRedeemOrderDto dto) {
        log.info("用户赎回理财产品=>:{}", JSONObject.toJSONString(dto));
        if (ObjectUtils.isEmpty(dto)) {
            return ResponseData.error("对不起，参数有误");
        }
        //判断是否时间是否在禁止赎回之内
        if (DateUtil.belongCalendar(new Date(), DateUtil.getDayStart(new Date()), DateUtil.getDayLastTime(new Date()))) {
            return ResponseData.error(MessageUtils.get("user.order.date.end.error"));
        }
        //校验用户是否存在
        Player user = playerService.getById(dto.getPlayerId());
        if (ObjectUtils.isEmpty(user)) {
            return ResponseData.error(MessageUtils.get("user.is.null.error"));
        }
        UserProductOrder order = this.getById(dto.getUserOrderId());
        if (ObjectUtils.isEmpty(order)) {
            return ResponseData.error(MessageUtils.get("user.order.is.error"));
        }
        if (!order.getProductOrderStatus().equals(1)) {
            return ResponseData.error(MessageUtils.get("user.order.status.ing.error"));
        }
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(user.getId()));

        //计算剩余多少天
        int i = DateUtil.differentDays(new Date(), order.getEndInterestData()) + 1;
        //计算罚息
        BigDecimal violationAccount = order.getPurchaseAccount().multiply(order.getViolationRate()).multiply(new BigDecimal(i)).setScale(2, BigDecimal.ROUND_HALF_UP);
        if (violationAccount.compareTo(order.getPurchaseAccount()) == 1) {
            violationAccount = order.getPurchaseAccount();
        }
        order.setViolationAccount(violationAccount);
        order.setProductOrderStatus(3);
        order.setReturnPrincipal(order.getPurchaseAccount().subtract(violationAccount));
        this.updateById(order);
        //操作用户资金
        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder frozen = AccountChangeBalanceParam.builder()
                .playerId(dto.getPlayerId())
                .type(dto.getAccountType())
                .currencyCode(dto.getCurrencyCode())
                .businessType(AccountBillBusinessTypeEnum.REDEMPTION)
                .optType(AccountBillOptTypeEnum.ADD)
                .businessNo(order.getOrderNum()).balance(order.getReturnPrincipal()).updateUser(dto.getPlayerId());
        accountService.changeBalance(frozen.build());
        return ResponseData.success();
    }

    /**
     * 预赎回
     *
     * @param dto
     * @return
     */
    @SneakyThrows
    @Override
    public ResponseData executeAnticipateRedeem(ExecuteUserRedeemOrderDto dto) {
        log.info("用户赎回理财产品=>:{}", JSONObject.toJSONString(dto));
        if (ObjectUtils.isEmpty(dto)) {
            return ResponseData.error(MessageUtils.get("error"));
        }
        //判断是否时间是否在禁止赎回之内
        if (DateUtil.belongCalendar(new Date(), DateUtil.getDayStart(new Date()), DateUtil.getDayLastTime(new Date()))) {
            return ResponseData.error(MessageUtils.get("user.order.date.end.error"));
        }
        //校验用户是否存在
        Player user = playerService.getById(dto.getPlayerId());
        if (ObjectUtils.isEmpty(user)) {
            return ResponseData.error(MessageUtils.get("user.is.null.error"));
        }
        UserProductOrder order = this.getById(dto.getUserOrderId());
        if (ObjectUtils.isEmpty(order)) {
            return ResponseData.error(MessageUtils.get("user.order.is.error"));
        }
        if (!order.getProductOrderStatus().equals(1)) {
            return ResponseData.error(MessageUtils.get("user.order.status.ing.error"));
        }
        //计算剩余多少天
        int i = DateUtil.differentDays(new Date(), order.getEndInterestData()) + 1;
        //计算罚息
        BigDecimal violationAccount = order.getPurchaseAccount().multiply(order.getViolationRate()).multiply(new BigDecimal(i)).setScale(2, BigDecimal.ROUND_HALF_UP);
        if (violationAccount.compareTo(order.getPurchaseAccount()) == 1) {
            violationAccount = order.getPurchaseAccount();
        }
        order.setViolationAccount(violationAccount);
        order.setReturnPrincipal(order.getPurchaseAccount().subtract(violationAccount));

        Map<String, Object> map = new HashMap<>();
        map.put("purchaseAccount", order.getPurchaseAccount());
        map.put("violationAccount", violationAccount);
        map.put("id", dto.getPlayerId());
        map.put("surplusDay", i);
        map.put("cycle", order.getCycle());
        map.put("anticipateProfit", order.getAnticipateProfit());
        map.put("profitRateInfo", order.getProfitRateInfo());
        map.put("violationRate", order.getViolationRate());
        map.put("orderNum", order.getOrderNum());
        map.put("purchaseTime", order.getPurchaseTime());
        map.put("profitAccount", order.getProfitAccount());
        return ResponseData.success(map);
    }

    /**
     * 用户理财收益定时任务
     *
     * @return
     */
    @Transactional
    @Override
    public ResponseData executeUserFinancialIncomeTask(UserProductOrder order) {
        //锁玩家，在外边锁，防止死锁
        playerService.lockPlayer(Lists.newArrayList(order.getPlayerId()));

        if (DateUtil.differentDays(order.getNextInterestData(), order.getEndInterestData()) == 0) {
            order.setProductOrderStatus(2);
            order.setReturnPrincipal(order.getPurchaseAccount());
        } else {
            order.setNextInterestData(DateUtil.executeDate(order.getNextInterestData(), 1));
        }
        order.setSurplusDay(order.getSurplusDay() - 1);
        //计算收益
        BigDecimal profitAccount = order.getPurchaseAccount().multiply(order.getProfitRate()).setScale(2, BigDecimal.ROUND_HALF_UP);
        order.setProfitAccount(order.getProfitAccount().add(profitAccount));
        order.setViolationDayAccount(profitAccount);
        order.setViolationDate(new Date());
        this.updateById(order);
        if (order.getProductOrderStatus().equals(2)) {
            //扣除用户资金
            AccountChangeBalanceParam.AccountChangeBalanceParamBuilder frozen = AccountChangeBalanceParam.builder()
                    .playerId(order.getPlayerId())
                    .type(1)
                    .currencyCode("USDT")
                    .businessType(AccountBillBusinessTypeEnum.COIN_POOL_PROFIT)
                    .optType(AccountBillOptTypeEnum.ADD)
                    .businessNo(order.getOrderNum()).balance(order.getReturnPrincipal().add(order.getProfitAccount())).updateUser(order.getPlayerId());
            accountService.changeBalance(frozen.build());
        }
        return ResponseData.success();
    }

    /**
     * 获取当前需要计算收益的订单
     *
     * @return
     */
    public List<UserProductOrder> findUserFinancialIncome() {
        return userProductOrderMapper.findUserFinancialIncome();
    }

    /**
     * 查询理财中的订单
     *
     * @param playerId
     * @return
     */
    @Override
    public ResponseData executeUserOrder(Long playerId) {
        ExecuteUserOrderVo vo = new ExecuteUserOrderVo();
        QueryWrapper<UserProductOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserProductOrder::getPlayerId, playerId);
        queryWrapper.lambda().eq(UserProductOrder::getProductOrderStatus, 1);
        List<UserProductOrder> userProductOrders = userProductOrderMapper.selectList(queryWrapper);
        if (ObjectUtils.isEmpty(userProductOrders)) {
            return ResponseData.success(vo);
        }
        userProductOrders.forEach(order -> {
            vo.setDepositOrderCount(vo.getDepositOrderCount() + 1);
            vo.setDepositAccount(vo.getDepositAccount().add(order.getPurchaseAccount()));
            vo.setEstimatedIncomeDay(vo.getEstimatedIncomeDay().add((order.getPurchaseAccount().multiply(order.getProfitRate()))));
        });
        //获取累计收益
        BigDecimal bigDecimal = userProductOrderMapper.findprofitSum(playerId);
        vo.setProfitSum(bigDecimal);
        return ResponseData.success(vo);
    }

    /**
     * 根据类型跟时间统计
     *
     * @param type
     * @param date
     * @return
     */
//    @Override
//    public BigDecimal findUserOrderStatistic(Integer type, String date) {
//        return getBaseMapper().findUserOrderStatistic(type,date);
//    }

    /**
     * 根据时间查询今日赎回和赎回金额
     *
     * @param date
     * @return
     */
//    @Override
//    public BigDecimal findUserOrderRedeemStatistic(String date) {
//        return getBaseMapper().findUserOrderRedeemStatistic(date);
//    }

    /**
     * 今日违约金
     *
     * @param date
     * @return
     */
//    @Override
//    public BigDecimal findUserOrderViolateStatistic(String date) {
//        return getBaseMapper().findUserOrderViolateStatistic(date);
//    }

    public UserProductOrderStatisticModel findUserProductOrderStatistic(HomeStatisticParam param){
        return getBaseMapper().findUserProductOrderStatistic(param);
    }

    /**
     * 获取理财订单
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    private UserProductOrder queryUserProductOrder(UserProductOrderParam userProductOrderParam) {
        UserProductOrder userProductOrder = this.getById(userProductOrderParam.getId());
        if (ObjectUtil.isNull(userProductOrder)) {
            throw new ServiceException(UserProductOrderExceptionEnum.NOT_EXIST);
        }
        return userProductOrder;
    }

}
