/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.productintroduce.param;

import vip.xiaonuo.core.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.*;

/**
* 产品简介参数类
 *
 * <AUTHOR>
 * @date 2022-04-25 15:27:42
*/
@Data
public class ProductIntroduceParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 语言编码
     */
    @NotBlank(message = "语言编码不能为空，请检查languageCode参数", groups = {add.class, edit.class})
    private String languageCode;

    /**
     * 币种id
     */
    @NotNull(message = "币种编码不能为空，请检查currencyCode参数", groups = {add.class, edit.class})
    private String currencyCode;

    /**
     * 发行时间
     */
    @NotNull(message = "发行时间不能为空，请检查releaseTime参数", groups = {add.class, edit.class})
    private String releaseTime;

    /**
     * 发行总量
     */
    @NotBlank(message = "发行总量不能为空，请检查totalIssuance参数", groups = {add.class, edit.class})
    private String totalIssuance;

    /**
     * 发行总量
     */
    @NotBlank(message = "流通总量不能为空，请检查totalIssuance参数", groups = {add.class, edit.class})
    private String totalCirculation;

    /**
     * 官方网站
     */
    @NotBlank(message = "官方网站不能为空，请检查officialWebsite参数", groups = {add.class, edit.class})
    private String officialWebsite;

    /**
     * 白皮书
     */
    @NotBlank(message = "白皮书不能为空，请检查whitePaper参数", groups = {add.class, edit.class})
    private String whitePaper;

    /**
     * 区块查询
     */
    @NotBlank(message = "区块查询不能为空，请检查blockQuery参数", groups = {add.class, edit.class})
    private String blockQuery;

    /**
     * 简介
     */
    @NotBlank(message = "简介不能为空，请检查briefIntroduce参数", groups = {add.class, edit.class})
    private String briefIntroduce;

}
