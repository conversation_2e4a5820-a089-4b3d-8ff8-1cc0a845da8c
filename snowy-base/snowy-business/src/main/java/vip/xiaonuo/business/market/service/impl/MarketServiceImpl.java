/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.business.market.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.business.market.enums.MarketExceptionEnum;
import vip.xiaonuo.business.market.mapper.MarketMapper;
import vip.xiaonuo.business.market.param.MarketParam;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.util.DpDataUtils;
import vip.xiaonuo.core.util.TickerPriceHashMap;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 市场service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-11 10:55:42
 */
@Service
public class MarketServiceImpl extends ServiceImpl<MarketMapper, Market> implements MarketService, InitializingBean {
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired(required = false)
    private TickerPriceHashMap tickerPriceHashMap;

    @Override
    public PageResult<Market> page(MarketParam marketParam) {
        QueryWrapper<Market> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(marketParam)) {
            // 根据交易类型表示,如ETH_BTC 查询
            if (ObjectUtil.isNotEmpty(marketParam.getSymbol())) {
                queryWrapper.lambda().like(Market::getSymbol, marketParam.getSymbol());
            }
            // 根据币种展现名称,如ETH/BTC 查询
            if (ObjectUtil.isNotEmpty(marketParam.getName())) {
                queryWrapper.lambda().eq(Market::getName, marketParam.getName());
            }
            // 根据0:下线,1:上线 查询
            if (ObjectUtil.isNotEmpty(marketParam.getStatus())) {
                queryWrapper.lambda().eq(Market::getStatus, marketParam.getStatus());
            }
            if (ObjectUtil.isNotEmpty(marketParam.getTransactionSwitch())) {
                queryWrapper.lambda().eq(Market::getTransactionSwitch, marketParam.getTransactionSwitch());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<Market> list(MarketParam marketParam) {
        return this.list(Wrappers.lambdaQuery());
    }

    @Override
    public void add(MarketParam marketParam) {
        Market market = new Market();
        BeanUtil.copyProperties(marketParam, market);
        this.save(market);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<MarketParam> marketParamList) {
        marketParamList.forEach(marketParam -> {
            Market market = this.queryMarket(marketParam);
            this.removeById(market.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(MarketParam marketParam) {
        Market market = this.queryMarket(marketParam);
        BeanUtil.copyProperties(marketParam, market);
        this.updateById(market);
    }

    @Override
    public Market detail(MarketParam marketParam) {
        return this.queryMarket(marketParam);
    }

    @Override
    public List<Market> getAllEnabledMarkets() {
        return lambdaQuery().eq(Market::getStatus, 1).list();
    }

    @Override
    public Market getMarketBySymbol(String symbol) {
        return lambdaQuery().eq(Market::getSymbol, symbol).eq(Market::getStatus, 1).one();
    }

    /**
     * 获取市场
     *
     * <AUTHOR>
     * @date 2022-04-11 10:55:42
     */
    private Market queryMarket(MarketParam marketParam) {
        Market market = this.getById(marketParam.getId());
        if (ObjectUtil.isNull(market)) {
            throw new ServiceException(MarketExceptionEnum.NOT_EXIST);
        }
        return market;
    }

    private static final String BASE_CODE = "USDT";

    public BigDecimal getMarketRate(String currencyCode, String baseCurrencyCode) {
        if (StringUtils.equalsIgnoreCase(currencyCode, baseCurrencyCode)) {
            return BigDecimal.ONE;
        }
        //交易对反过来，比如：USDT_ETH
        if (StringUtils.equalsIgnoreCase(currencyCode, BASE_CODE)) {
            baseCurrencyCode = (baseCurrencyCode + "_" + BASE_CODE).toUpperCase();
            BigDecimal lastPrice = DpDataUtils.getLatestPrice(redisTemplate, baseCurrencyCode);
            if (lastPrice == null) {
                throw new ServiceException(MarketExceptionEnum.NOT_LAST_PRICE);
            }
            return BigDecimal.ONE.divide(lastPrice, 8, BigDecimal.ROUND_HALF_DOWN);
        }
        //正常的交易对，比如：ETH_USDT
        if (StringUtils.equalsIgnoreCase(baseCurrencyCode, BASE_CODE)) {
            currencyCode = (currencyCode + "_" + BASE_CODE).toUpperCase();
            BigDecimal lastPrice = DpDataUtils.getLatestPrice(redisTemplate, currencyCode);
            if (lastPrice == null) {
                throw new ServiceException(MarketExceptionEnum.NOT_LAST_PRICE);
            }
            return lastPrice;
        }
        currencyCode = (currencyCode + "_" + BASE_CODE).toUpperCase();
        BigDecimal lastPrice = DpDataUtils.getLatestPrice(redisTemplate, currencyCode);
        if (lastPrice == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_LAST_PRICE);
        }
        baseCurrencyCode = (baseCurrencyCode + "_" + BASE_CODE).toUpperCase();
        BigDecimal baseLastPrice = DpDataUtils.getLatestPrice(redisTemplate, baseCurrencyCode);
        if (baseLastPrice == null) {
            throw new ServiceException(MarketExceptionEnum.NOT_LAST_PRICE);
        }
        return lastPrice.divide(baseLastPrice, 8, BigDecimal.ROUND_HALF_DOWN);
    }

    @Override
    public BigDecimal getLatestPrice(String symbol) {
        if (tickerPriceHashMap != null) {
            BigDecimal lastPrice = tickerPriceHashMap.get(symbol.toUpperCase());
            if (lastPrice != null) {
                return lastPrice;
            }
        }
        return DpDataUtils.getLatestPrice(redisTemplate, symbol);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
//        System.out.println("xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx");
//        System.out.println(getMarketRate("eth", "usdt"));
//        System.out.println(getMarketRate("eth", "btc"));
//        System.out.println(getMarketRate("usdt", "eth"));
    }
}
