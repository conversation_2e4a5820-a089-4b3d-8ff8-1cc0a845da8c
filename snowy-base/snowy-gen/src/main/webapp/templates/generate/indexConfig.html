<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>字段配置</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        /* 防止下拉框的下拉列表被隐藏---必须设置--- */
        .layui-table-cell {
            overflow: visible !important;
        }
        td .layui-form-select {
            margin-top: -10px;
            margin-left: -15px;
            margin-right: -15px;
        }
        /*.xnStyle {
            height: 28px;
        }*/
    </style>
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!--<script type="text/html" id="purchaseTotal">
    <input type="text" min="0" class="layui-input" lay-vertype="tips" name="purchase_total" value="" lay-event="purchaseTotal" lay-filter="purchaseTotal">
</script>-->
<!-- 字段模板 -->
<script type="text/html" id="whetherTableTemp">
    <div class="layui-form" style="text-align: center">
        <input type='checkbox' value="{{d.id}}" id='{{d.columnName}}' name='whetherTable' {{ d.whetherTable == 'Y' ? 'checked' : '' }} lay-skin="primary" lay-filter="whetherTableFilter">
    </div>
</script>
<script type="text/html" id="whetherAddUpdateTemp">
    <div class="layui-form" style="text-align: center">
        {{# if(d.columnKey == 'PRI') { }}
            <input type='checkbox' value="{{d.id}}" id='{{d.columnName}}' disabled="" name='whetherAddUpdate' {{ d.whetherAddUpdate == 'Y' ? 'checked' : '' }} lay-skin="primary" lay-filter="whetherAddUpdateFilter">
        {{# }else{ }}
            <input type='checkbox' value="{{d.id}}" id='{{d.columnName}}' name='whetherAddUpdate' {{ d.whetherAddUpdate == 'Y' ? 'checked' : '' }} lay-skin="primary" lay-filter="whetherAddUpdateFilter">
        {{# } }}
    </div>
</script>
<script type="text/html" id="whetherRequiredTemp">
    <div class="layui-form" style="text-align: center">
        {{# if(d.columnKey == 'PRI') { }}
            <input type='checkbox' value="{{d.id}}" id='{{d.columnName}}' disabled="" name='whetherRequired' {{ d.whetherRequired == 'Y' ? 'checked' : '' }} lay-skin="primary" lay-filter="whetherRequiredFilter">
        {{# }else{ }}
            <input type='checkbox' value="{{d.id}}" id='{{d.columnName}}' name='whetherRequired' {{ d.whetherRequired == 'Y' ? 'checked' : '' }} lay-skin="primary" lay-filter="whetherRequiredFilter">
        {{# } }}
    </div>
</script>
<script type="text/html" id="queryWhetherTemp">
    <input type="checkbox" name="queryWhether" value="{{d.id}}" lay-skin="switch" lay-text="是|否" lay-filter="queryWhetherFilter" {{ d.queryWhether == 'Y' ? 'checked' : '' }}>
</script>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'formX', 'tableX', 'notice', 'xnUtil'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var xnUtil = layui.xnUtil;
        var formX = layui.formX;
        var configDataList = [];

        // 上个界面过来的缓存
        var codeGenId = admin.getTempData('codeGenId');

        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'sysCodeGenerateConfig/list?codeGenId=' + codeGenId ,
            parseData: function (res) {
                //将其整体缓存
                configDataList = res.data;
                return res;
            },
            page: false,
            height: 2000,
            limit: 100,
            toolbar: ['<p>',
                '<button lay-event="save" class="layui-btn layui-btn-sm icon-btn" lay-filter="formSubmitBtn"><i class="layui-icon">&#xe654;</i>保存</button>&nbsp;',
                '<button lay-event="goback" class="layui-btn layui-btn-sm ">返回</button>',
                '</p>'].join(''),
            cellMinWidth: 100,
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'id', title: 'id', hide:true, sort: true},
                    {field: 'javaName', title: 'java字段', sort: true},
                    {field: 'columnComment', title: '字段描述', sort: true, edit: 'dataTable', totalRow: true},
                    {field: 'dataType', title: '物理类型', sort: true},
                    {field: 'javaType', title: 'java类型', sort: true, templet: function (d) {return '<select lay-filter="javaTypeFilter" name="javaType'+d.id+'"></select>'}},
                    {field: 'effectType', title: '作用类型', sort: true, templet: function (d) {  return '<select lay-filter="effectTypeFilter" name="effectType'+d.id+'"></select>' }},
                    {field: 'dictTypeCode', title: '字典', width: 160, sort: true, templet: function (d) {  return '<select lay-filter="dictTypeCodeFilter" name="dictTypeCode'+d.id+'"></select>' }},
                    {field: 'whetherTable', width: 100, title: '列显示', sort: true, center: true, templet: '#whetherTableTemp'},
                    // {field: 'whetherRetract', title: '列字段省略', sort: true},
                    {field: 'whetherAddUpdate', width: 80, title: '增改', sort: true, templet: '#whetherAddUpdateTemp'},
                    {field: 'whetherRequired', width: 80, title: '必填', sort: true, templet: '#whetherRequiredTemp'},
                    {field: 'queryWhether', width: 80, title: '查询', center: true, sort: true, templet: '#queryWhetherTemp'},
                    {field: 'queryType', title: '查询方式', sort: true, templet: function (d) {  return '<select lay-filter="queryTypeFilter" name="queryType'+d.id+'" ></select>' }}
                ]
            ],
            done: function(res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);

                count || this.elem.next('.layui-table-view').find('.layui-table-header').css('overflow', 'auto');
                layui.each($('select'), function (index, item) {
                    var elem = $(item);
                    elem.val(elem.data('value')).parents('div.layui-table-cell').css('overflow', 'visible');
                });
                initTableDone(res)
            }
        });

        // 字段描述
        table.on('edit(dataTable)', function (data) {
            editConfigDataList(data.field, data.data.id, data.value);
        });

        // java类型
        form.on('select(javaTypeFilter)', function (data) {
            var elem = data.othis.parents('tr');
            var rowId = elem.first().find('td').eq(1).text();
            editConfigDataList('javaType',rowId, data.value);
        });

        // 作用类型
        form.on('select(effectTypeFilter)', function (data) {
            var elem = data.othis.parents('tr');
            var rowId = elem.first().find('td').eq(1).text();
            editConfigDataList('effectType',rowId, data.value);
            // 选择作用时候加载字典或清理已有数据
            initDictData(data.value, rowId, null)
        });

        // 字典
        form.on('select(dictTypeCodeFilter)', function (data) {
            var elem = data.othis.parents('tr');
            var rowId = elem.first().find('td').eq(1).text();
            editConfigDataList('dictTypeCode',rowId, data.value);
        });

        // 查询方式
        form.on('select(queryTypeFilter)', function (data) {
            var elem = data.othis.parents('tr');
            var rowId = elem.first().find('td').eq(1).text();
            editConfigDataList('queryType',rowId, data.value);
        });

        // 列显示 增改 必填
        form.on('checkbox', function(obj){
            var value = obj.elem.checked ? 'Y' : 'N';
            editConfigDataList(this.name, this.value, value);
        });

        form.on('switch(queryWhetherFilter)', function(obj){
            var value = obj.elem.checked ? 'Y' : 'N';
            editConfigDataList(this.name, this.value, value);
        });

        /* 表格工具条点击事件 */
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'save') {
                layer.load(2);

                // 剔除JSON中乱七八糟没用的
                configDataList.forEach(function (value) {
                    delete value.LAY_TABLE_INDEX
                })

                var param = {
                    'codeGenId': codeGenId,
                    'sysCodeGenerateConfigParamList': configDataList
                }

                admin.req(getProjectUrl() + 'sysCodeGenerateConfig/edit', JSON.stringify(param), function(res){
                    console.log(JSON.stringify(res))
                    if (res.success) {
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            window.location.href = getProjectUrl() + 'codeGenerate/index';
                        });
                    } else {
                        layer.msg(res.message, {icon: 2, time: 1000}, null);
                    }
                }, 'post');
            }
            if (obj.event === 'goback') {
                window.location.href = getProjectUrl() + 'codeGenerate/index';
            }
        });

        // 加载完table表格后初始化数据
        function initTableDone (res) {
            // 给列表的下拉框赋值
            res.data.forEach(function (item) {
                rendDictDropDown("javaType"+ item.id, 'code_gen_java_type', '请选择', null, item.javaType);
                // rendDictDropDown("javaType", 'code_gen_java_type', '请选择', null, item.javaType);
                rendDictDropDown("effectType"+ item.id, 'code_gen_effect_type', '请选择', null, item.effectType);
                rendDictDropDown("queryType"+ item.id, 'code_gen_query_type', '请选择', null, item.queryType);
                initDictData(item.effectType, item.id, item.dictTypeCode)
            })
        }

        // 初始化字典下拉框
        function initDictData (effectType, id, defvalue) {
            if (effectType === "radio" || effectType === "select" || effectType === "checkbox" ) {
                var dictData = layui.data(admin.setter.tableName).dictData;
                var elem = 'select[name="dictTypeCode'+id+'"]';
                return formX.renderSelect({
                    elem: elem,
                    data: dictData,
                    name: 'name',
                    value: 'code',
                    hint: "请选择字典",
                    initValue: defvalue
                });
            } else {
                // 锁定字典框，并将其值置空
                var elem = 'select[name="dictTypeCode'+id+'"], [disabled="true"]';
                return formX.renderSelect({
                    elem: elem,
                    data: [],
                    name: 'name',
                    value: 'code',
                    hint: "不可选择",
                    initValue: defvalue
                });
            }
        }

        // 加载下拉框
        function rendDictDropDown(fieldName, dictTypeCode, placeholderName, exclude, initValue) {
            var elem = 'select[name="' + fieldName + '"]';
            return formX.renderSelect({
                elem: elem,
                data: xnUtil.getDictDataByDictTypeCode(dictTypeCode, exclude),
                name: 'name',
                value: 'code',
                hint: placeholderName,
                initValue: initValue
            });
        }

        // 变更缓存中数据
        function editConfigDataList(name, rowId, value) {
            for (var i=0; i<configDataList.length; i++) {
                if (configDataList[i].id == rowId) {
                    configDataList[i][name] = value;
                    break;
                }
            }
        };

    });
</script>
</body>
</html>
