/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package ${packageName}.${modularName}.${busName}.controller;

import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import ${packageName}.${modularName}.${busName}.entity.${ClassName};
import ${packageName}.${modularName}.${busName}.param.${ClassName}Param;
import ${packageName}.${modularName}.${busName}.service.${ClassName}Service;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.List;

/**
 * ${functionName}控制器
 *
 * <AUTHOR>
 * @date ${createDateString}
 */
@Controller
public class ${ClassName}Controller {

    private String PATH_PREFIX = "${className}/";

    @Resource
    private ${ClassName}Service ${className}Service;

    /**
     * ${functionName}页面
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @Permission
    @GetMapping("/${className}/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * ${functionName}表单页面
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @GetMapping("/${className}/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @Permission
    @ResponseBody
    @GetMapping("/${className}/page")
    @BusinessLog(title = "${functionName}_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<${ClassName}> page(${ClassName}Param ${className}Param) {
        return ${className}Service.page(${className}Param);
    }

    /**
     * 添加${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @Permission
    @ResponseBody
    @PostMapping("/${className}/add")
    @BusinessLog(title = "${functionName}_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(${ClassName}Param.add.class) ${ClassName}Param ${className}Param) {
        ${className}Service.add(${className}Param);
        return new SuccessResponseData();
    }

    /**
     * 删除${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @Permission
    @ResponseBody
    @PostMapping("/${className}/delete")
    @BusinessLog(title = "${functionName}_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(${ClassName}Param.delete.class) List<${ClassName}Param> ${className}ParamList) {
        ${className}Service.delete(${className}ParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @Permission
    @ResponseBody
    @PostMapping("/${className}/edit")
    @BusinessLog(title = "${functionName}_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(${ClassName}Param.edit.class) ${ClassName}Param ${className}Param) {
        ${className}Service.edit(${className}Param);
        return new SuccessResponseData();
    }

    /**
     * 查看${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @Permission
    @ResponseBody
    @GetMapping("/${className}/detail")
    @BusinessLog(title = "${functionName}_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(${ClassName}Param.detail.class) ${ClassName}Param ${className}Param) {
        return new SuccessResponseData(${className}Service.detail(${className}Param));
    }

    /**
     * ${functionName}列表
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @Permission
    @ResponseBody
    @GetMapping("/${className}/list")
    @BusinessLog(title = "${functionName}_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(${ClassName}Param ${className}Param) {
        return new SuccessResponseData(${className}Service.list(${className}Param));
    }

}
