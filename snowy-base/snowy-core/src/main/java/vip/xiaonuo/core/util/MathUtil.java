package vip.xiaonuo.core.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/11/19.
 */
public class MathUtil {
    private static DecimalFormat VN_MONEY_FORMAT = new DecimalFormat("###,###");

    public static BigDecimal stripZeros(BigDecimal bigDecimal) {
        if(bigDecimal != null) {
            return bigDecimal.stripTrailingZeros();
        }
        return null;
    }

    /**
     * 乘法运算
     * @return
     */
    public static BigDecimal multiply(BigDecimal val1, BigDecimal val2) {
        return val1.multiply(val2);
    }

    /**
     * 减法运算
     * @return
     */
    public static BigDecimal subtract(BigDecimal val1, BigDecimal val2) {
        return val1.subtract(val2);
    }

    /**
     * 加法运算
     * @return
     */
    public static BigDecimal add(BigDecimal val1, BigDecimal val2) {
        return val1.add(val2);
    }

    private static final BigDecimal DECIMAL_100 = new BigDecimal(100);

    public static String formatPercent(BigDecimal v1, BigDecimal v2, int scale) {
        return v1.divide(v2, scale + 2, BigDecimal.ROUND_HALF_UP).multiply(DECIMAL_100).setScale(scale) + "%";
    }

    /**
     * 获取BigDecimal的小数点后0的个数
     * @return
     */
    public static int getMinFraction(BigDecimal target) {
        if(target.compareTo(new BigDecimal(1)) >= 0) {
            return 0;
        }
        int i = 0;
        while(true) {
            target = target.movePointRight(1);
            if(target.compareTo(new BigDecimal(1)) >= 0) {
                return i;
            }
            i++;
        }
    }

    public static String decimalFormatMoney(BigDecimal money) {
        if (money == null) {
            return "";
        }
        return VN_MONEY_FORMAT.format(money);
    }
}
