package vip.xiaonuo.core.util;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

public class MarketUtils {
    public static String getSymbolName(String symbol) {
        return StrUtil.trimToEmpty(symbol).replace("_", "/");
    }

    public static String getCode(String symbol) {
        return symbol.split("_")[0];
    }

    public static String getBaseCode(String symbol) {
        return symbol.split("_")[1];
    }

    /**
     * 如果 orderPrice、lastPrice相等，则减少一个最小额度
     *
     * @param type
     * @param orderPrice
     * @param lastPrice
     * @param priceDecimal
     * @return
     */
    public static BigDecimal getLastPrice(Integer type, BigDecimal orderPrice, BigDecimal lastPrice, Integer priceDecimal) {
        BigDecimal min = getMin(priceDecimal);
        BigDecimal diff = orderPrice.subtract(lastPrice);
        if (diff.compareTo(BigDecimal.ZERO) == 0) {
            if (type.equals(1)) {
                //买涨
                lastPrice = lastPrice.subtract(min);
            }
            if (type.equals(2)) {
                //买跌
                lastPrice = lastPrice.add(min);
            }
        }
        return lastPrice;
    }

    /**
     * 往上添加最小额度
     *
     * @param type
     * @param orderPrice
     * @param priceDecimal
     * @return
     */
    public static BigDecimal getLastPriceAdd(Integer type, BigDecimal orderPrice, Integer priceDecimal) {
        BigDecimal min = getMin(priceDecimal);
        if (type.equals(1)) {
            //买涨
            orderPrice = orderPrice.add(min);
        }
        if (type.equals(2)) {
            //买跌
            orderPrice = orderPrice.subtract(min);
        }
        return orderPrice;
    }

    public static BigDecimal getMin(Integer priceDecimal) {
        StringBuilder builder = new StringBuilder("1");
        for (int i = 0; i < priceDecimal; i++) {
            builder.append("0");
        }
        return BigDecimal.ONE.divide(new BigDecimal(builder.toString()), priceDecimal, BigDecimal.ROUND_HALF_UP);
    }

    public static int getDigits(String p) {
        int i = p.indexOf(".");
        if (i == -1) {
            return 0;
        }
        String a = p.substring(i + 1);
        if (StringUtils.isEmpty(a)) {
            return 0;
        }
        int d = 0;
        for (int k = 0; k < a.length(); k++) {
            if (a.charAt(k) == '1') {
                break;
            }
            d++;
        }
        if (d == a.length()) {
            return 0;
        }
        return d + 1;
    }
}
