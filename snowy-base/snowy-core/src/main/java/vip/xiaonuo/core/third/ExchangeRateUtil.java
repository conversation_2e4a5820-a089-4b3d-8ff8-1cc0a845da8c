package vip.xiaonuo.core.third;

import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import vip.xiaonuo.core.pojo.exechangeRate.QueryExchangeRateVo;
import vip.xiaonuo.core.util.HttpUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 汇率同步信息
 * https://www.jtl-global.com/#/pages/coin_pool_rules/index
 * <EMAIL>
 *5860955wjF$exchange_rate_api
 */
@Slf4j
public class ExchangeRateUtil {


    //请求key
    private static String KEY = "693048e4d14d3508b886b123";
    //请求地址
    private static String PATH_URL="https://v6.exchangerate-api.com/v6/"+KEY+"/latest/USD";
    /**
     * 获取费率
     * @return 返回费率列表
     */
    @SneakyThrows
    public static List<QueryExchangeRateVo> queryExchangeRate(){
        String res = HttpUtil.get(PATH_URL);
        log.info("请求汇率数据为：{}",res);
        if(ObjectUtils.isEmpty(res)){
            return null;
        }
        JSONObject parse = JSONObject.parseObject(res);
        if(!parse.containsKey("result")){
            log.info("请求费率结果为：{}",parse.get("result"));
            return null;
        }
        if(!parse.get("result").equals("success")){
            log.info("请求费率结果为：{}",parse.get("result"));
            return null;
        }
        JSONObject conversionRates = parse.getJSONObject("conversion_rates");
        if(ObjectUtils.isEmpty(conversionRates)){
            System.out.println("获取数据为空");
            return null;
        }
        List<QueryExchangeRateVo> list = new ArrayList<>();
        for(String str:conversionRates.keySet()){
            QueryExchangeRateVo vo = new QueryExchangeRateVo();
            vo.setExchangeRateName(str);
            vo.setExchangeRate(conversionRates.getBigDecimal(str));
            list.add(vo);
        }
        return list;
    }

  /*  public static void main(String[] args) {
        List<QueryExchangeRateVo> list = ExchangeRateUtil.queryExchangeRate();
        System.out.println(JSONObject.toJSONString(list));
    }*/

}
