package vip.xiaonuo.core.pojo.dto;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import vip.xiaonuo.core.pojo.BigDecimalSerializer;
import vip.xiaonuo.core.util.MathUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

public class TradeDTO implements Serializable {
    private static final long serialVersionUID = -4244658065930870905L;
    private Long id;
    private BigDecimal quantity;
    private BigDecimal price;
    private Long transTime;
    private String mainMarket;
    @JsonIgnore
    private int priceDecimal = 8;
    @JsonIgnore
    private int quantityDecimal = 3;
    private String symbol;

    //类型(1:买;2:卖;)
    public Integer getType() {
        return "sell".equalsIgnoreCase(mainMarket) ? 2 : 1;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public int getPriceDecimal() {
        return priceDecimal;
    }

    public void setPriceDecimal(int priceDecimal) {
        this.priceDecimal = priceDecimal;
    }

    public int getQuantityDecimal() {
        return quantityDecimal;
    }

    public void setQuantityDecimal(int quantityDecimal) {
        this.quantityDecimal = quantityDecimal;
    }

    public String getMainMarket() {
        return mainMarket;
    }

    public void setMainMarket(String mainMarket) {
        this.mainMarket = mainMarket;
    }

    @JsonSerialize(using = BigDecimalSerializer.class)
    public BigDecimal getQuantity() {
        return quantity.setScale(quantityDecimal, RoundingMode.FLOOR);
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @JsonSerialize(using = BigDecimalSerializer.class)
    public BigDecimal getPrice() {
        return MathUtil.stripZeros(price).setScale(priceDecimal, RoundingMode.FLOOR);
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getTransTime() {
        return transTime;
    }

    public void setTransTime(Long transTime) {
        this.transTime = transTime;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
}
