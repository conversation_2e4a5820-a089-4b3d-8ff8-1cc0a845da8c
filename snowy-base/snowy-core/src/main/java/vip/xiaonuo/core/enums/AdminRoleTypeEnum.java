package vip.xiaonuo.core.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;

@Getter
public enum AdminRoleTypeEnum {

    NONE(0, "none"),

    /**
     * 管理员
     */
    ADMIN(1, "管理员"),

    /**
     * 代理线
     */
    AGENT_LINE(2, "代理线"),

    /**
     * 代理人
     */
    AGENT(3, "代理人");

    private final Integer code;

    private final String message;

    AdminRoleTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static AdminRoleTypeEnum getEnumByCode(Integer code){
        if(ObjectUtil.isEmpty(code)){
            return null;
        }
        for (AdminRoleTypeEnum type : AdminRoleTypeEnum.values()){
            if(code == type.code){
                return type;
            }
        }
        return null;
    }
}
