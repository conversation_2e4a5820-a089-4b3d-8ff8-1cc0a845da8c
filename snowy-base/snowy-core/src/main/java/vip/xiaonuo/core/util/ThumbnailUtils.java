package vip.xiaonuo.core.util;

import cn.hutool.core.io.IoUtil;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vip.xiaonuo.core.exception.CompressionException;

import java.io.*;

public class ThumbnailUtils {
//    public static void main(String[] args) throws IOException {
//        InputStream inputStream =  ThumbnailUtils.compressPicForScale(new FileInputStream("D:\\20221018100740.jpg"), 30, "g",0.9);
//        IOUtils.write(toByteArray(inputStream), new FileOutputStream("D:\\11.jpg"));
//    }
    private static Logger logger = LoggerFactory.getLogger(ThumbnailUtils.class);

    public static InputStream compressPicForScale(
            InputStream inputStream, long desFileSize, String imageId, Double quality) {
        if (inputStream == null) {
            return null;
        }
        try {
            int srcSize = inputStream.available();
            if (srcSize <= 0) {
                return null;
            }
            int srcSizeKb = srcSize / 1024;
            if (srcSizeKb < desFileSize) {
                return new ByteArrayInputStream(IOUtils.toByteArray(inputStream));
            }
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream(srcSize);
            double scale = getScale(srcSizeKb);
            Thumbnails.of(inputStream).scale(scale).outputQuality(quality).toOutputStream(outputStream);
            logger.info(
                    "【图片压缩】imageId={} | 图片原大小={}kb | 压缩后大小={}kb",
                    imageId,
                    srcSizeKb,
                    outputStream.size() / 1024);
            return new ByteArrayInputStream(outputStream.toByteArray());
        } catch (Exception e) {
            throw new CompressionException("compressPicForScale失败", e);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    private static double getScale(int size) {
        if (size <= 1024) {
            return 0.9;
        }
        if (size <= (2 * 1024)) {
            return 0.8;
        }
        if (size <= (3 * 1024)) {
            return 0.6;
        }
        if (size <= (4 * 1024)) {
            return 0.5;
        }
        if (size <= (5 * 1024)) {
            return 0.44;
        }
        if (size <= (6 * 1024)) {
            return 0.38;
        }
        if (size <= (7 * 1024)) {
            return 0.32;
        }
        if (size <= (8 * 1024)) {
            return 0.25;
        }
        if (size <= (9 * 1024)) {
            return 0.22;
        }
        if (size <= (10 * 1024)) {
            return 0.21;
        }
        if (size <= (12 * 1024)) {
            return 0.2;
        }
        if (size <= (15 * 1024)) {
            return 0.18;
        }
        return 0.17;
    }

    public static byte[] toByteArray(InputStream inputStream) {
        try {
            return IOUtils.toByteArray(inputStream);
        } catch (IOException e) {
            throw new CompressionException("toByteArray失败", e);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    public static void write(byte[] data, OutputStream outputStream) {
        try {
            IOUtils.write(data, outputStream);
        } catch (IOException e) {
            throw new CompressionException("write失败", e);
        } finally {
            IOUtils.closeQuietly(outputStream);
        }
    }
}