package vip.xiaonuo.core.redis;

import cn.hutool.extra.spring.SpringUtil;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import vip.xiaonuo.core.pojo.RedisDelayMessage;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

public class RedissonDelayQueueClient implements ApplicationRunner {
    private Logger logger = LoggerFactory.getLogger(RedissonDelayQueueClient.class);
    private final Map<String, RDelayedQueue<RedisDelayMessage<?>>> delayQueueMap = new ConcurrentHashMap<>(16);
    @Resource
    private RedissonClient redissonClient;

    public <T> void addDelay(RedisDelayMessage<T> delayMessage) {
        if (delayQueueMap.get(delayMessage.getType().name()) == null) {
            logger.warn("queueName={},该延迟队列不存在，请确认后再试...", delayMessage.getType().name());
            return;
        }
        RDelayedQueue<RedisDelayMessage<?>> rDelayedQueue = delayQueueMap.get(delayMessage.getType().name());
        rDelayedQueue.offer(delayMessage, delayMessage.getDelayTime(), delayMessage.getTimeUnit() == null ? TimeUnit.SECONDS : delayMessage.getTimeUnit());
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        for (DelayMessageType type : DelayMessageType.values()) {
            DelayQueueConsumer delayQueueConsumer = SpringUtil.getBean(type.name() + "_consumer");
            if (delayQueueConsumer == null) {
                logger.error(type.name() + "延迟队列实现类不存在");
                continue;
            }
            // Redisson的延时队列是对另一个队列的再包装，使用时要先将延时消息添加到延时队列中，当延时队列中的消息达到设定的延时时间后，
            // 该延时消息才会进行进入到被包装队列中，因此，我们只需要对被包装队列进行监听即可。
            RBlockingQueue<RedisDelayMessage<?>> rBlockingQueue = redissonClient.getBlockingDeque("delayMessage:" + type.name());
            RDelayedQueue<RedisDelayMessage<?>> rDelayedQueue = redissonClient.getDelayedQueue(rBlockingQueue);
            delayQueueMap.put(type.name(), rDelayedQueue);

            // 订阅新元素的到来，调用的是takeAsync()，异步执行
            rBlockingQueue.subscribeOnElements(delayQueueConsumer::execute);
        }
    }
}
