package vip.xiaonuo.core.redis;

import cn.hutool.json.JSONUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
public class RedisHelper {

    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    private HashOperations<String, String, String> stringHashOperations;

    @Resource
    public void setStringRedisTemplate(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.stringHashOperations = stringRedisTemplate.opsForHash();
    }

    public StringRedisTemplate getStringRedisTemplate() {
        return stringRedisTemplate;
    }

    public RedisTemplate<String, Object> getRedisTemplate() {
        return redisTemplate;
    }

    public HashOperations<String, String, String> getStringHashOperations() {
        return stringHashOperations;
    }

    public void put(String key, String hk, String hv) {
        stringHashOperations.put(key, hk, hv);
    }

    public String get(String key, String hk) {
        return stringHashOperations.get(key, hk);
    }

    public void delete(String key) {
        stringRedisTemplate.delete(key);
    }

    public void delete(String key, String hk) {
        stringHashOperations.delete(key, hk);
    }

    public void set(String key, Object value, long expire) {
        redisTemplate.opsForValue().set(key, value, expire, TimeUnit.SECONDS);
    }

    public void set(String key, String value, long expire) {
        stringRedisTemplate.opsForValue().set(key, value, expire, TimeUnit.SECONDS);
    }

    public Boolean setIfAbsent(String key, String value, long expire) {
        return stringRedisTemplate.opsForValue().setIfAbsent(key, value, expire, TimeUnit.SECONDS);
    }

    public Boolean setIfAbsent(String key, String value) {
        return stringRedisTemplate.opsForValue().setIfAbsent(key, value);
    }

    public long increment(String key) {
        return redisTemplate.opsForValue().increment(key, 1L);
    }

    public void set(String key, String value) {
        stringRedisTemplate.opsForValue().set(key, value);
    }

    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public void set(String key, Integer value, long expire) {
        stringRedisTemplate.opsForValue().set(key, value != null ? value.toString() : null, expire, TimeUnit.SECONDS);
    }

    public Boolean hasKey(String key) {
        return stringRedisTemplate.hasKey(key);
    }

    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    public Integer getInteger(String key) {
        String value = get(key);
        return StringUtils.isEmpty(value) ? null : Integer.parseInt(value);
    }

    public Integer getInteger(String key, Integer defualtValue) {
        Integer value = getInteger(key);
        return value == null ? defualtValue : value;
    }

    public String getStr(String key, String defualtValue) {
        String value = get(key);
        return StringUtils.isEmpty(value) ? defualtValue : value;
    }

    public boolean expire(String key, long expire) {
        return stringRedisTemplate.expire(key, expire, TimeUnit.SECONDS);
    }

    public boolean expire(String key, long maxCount, long timeSeconds) {
        String count = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(count) || stringRedisTemplate.getExpire(key) == -1L) {
            stringRedisTemplate.opsForValue().set(key, "1", timeSeconds, TimeUnit.SECONDS);
            return true;
        }
        if (Long.parseLong(count) >= maxCount) {
            return false;
        }
        stringRedisTemplate.opsForValue().increment(key, 1);
        return true;
    }

    public <T> void setList(String key, List<T> list) {
        String value = JSONUtil.toJsonStr(list);
        set(key, value);
    }

    public <T> List<T> getList(String key, Class<T> clz) {
        String json = get(key);
        if (json != null) {
            List<T> list = JSONUtil.toList(json, clz);
            return list;
        }
        return null;
    }

    public <T> void setList(String key, List<T> list, long expire) {
        String value = JSONUtil.toJsonStr(list);
        set(key, value, expire);
    }

    public void setBean(String key, Object obj, long expire) {
        stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(obj), expire, TimeUnit.SECONDS);
    }

    public <T> T getBean(String key, Class<T> cls) {
        String str = get(key);
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        return JSONUtil.toBean(str, cls);
    }

    public void deleteByPattern(String s) {
        Set<String> keys = redisTemplate.keys(s);
        if (CollectionUtils.isNotEmpty(keys)) {
            redisTemplate.delete(keys);
        }
    }
}
