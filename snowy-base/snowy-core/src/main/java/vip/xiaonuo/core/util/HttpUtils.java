package vip.xiaonuo.core.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;


public class HttpUtils {

    private static CloseableHttpClient httpclient;

    static {
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        cm.setMaxTotal(200);//设置连接池的最大连接数
        cm.setDefaultMaxPerRoute(100);//设置每个路由上的默认连接个数
        httpclient = HttpClients.custom().setConnectionManager(cm).build();
    }

    public static String post(String url, Map<String, String> params) throws Exception {
        //CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);
        List param = new ArrayList();
        Set<String> keySet = params.keySet();
        for(String key : keySet) {
            param.add(new BasicNameValuePair(key, params.get(key)));
        }
        post.setEntity(new UrlEncodedFormEntity(param, "UTF-8"));
        CloseableHttpResponse response = httpclient.execute(post);
        HttpEntity entity = response.getEntity();
        String body = EntityUtils.toString(entity, "UTF-8");
        response.close();
        return body;
    }


    /**
     * 发送XML参数请求
     * */
    public static String postXML(String url, String xml) throws Exception {
        //CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);
        StringEntity se = new StringEntity(xml, "UTF-8");
        post.setEntity(se);
        CloseableHttpResponse response = httpclient.execute(post);
        HttpEntity entity = response.getEntity();
        String body = EntityUtils.toString(entity, "UTF-8");
        response.close();
        return body;
    }

    /**
     * 发送json请求
     * */
    public static String postJson(String url, Map map) throws Exception {
        //CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);
        JSONObject jsonParam = new JSONObject();
        Set<String> keySet = map.keySet();
        for(String key : keySet) {
            jsonParam.put(key, map.get(key));
        }
        StringEntity entity = new StringEntity(jsonParam.toString(),"utf-8");//解决中文乱码问题
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        post.setEntity(entity);
        CloseableHttpResponse response = httpclient.execute(post);
        HttpEntity res = response.getEntity();
        String body = EntityUtils.toString(res, "UTF-8");
        response.close();
        return body;
    }
}
