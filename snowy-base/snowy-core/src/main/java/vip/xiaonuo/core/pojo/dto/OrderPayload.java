package vip.xiaonuo.core.pojo.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class OrderPayload implements Serializable {
    private static final long serialVersionUID = 1L;

    private String symbol;
    private Map<String, List<AggOrderDTO>> list;

    public OrderPayload() {
    }

    public OrderPayload(String symbol, Map<String, List<AggOrderDTO>> list) {
        this.symbol = symbol;
        this.list = list;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public Map<String, List<AggOrderDTO>> getList() {
        return list;
    }

    public void setList(Map<String, List<AggOrderDTO>> list) {
        this.list = list;
    }
}
