package vip.xiaonuo.core.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.system.oshi.CpuInfo;
import cn.hutool.system.oshi.OshiUtil;
import org.springframework.util.StringUtils;
import oshi.hardware.GlobalMemory;
import oshi.hardware.NetworkIF;
import oshi.util.FormatUtil;

import java.io.File;
import java.text.DecimalFormat;
import java.util.List;
import java.util.StringJoiner;

public class HardwareUtils {

    public static String getHardwareInfo() {
        try {
            DecimalFormat format = new DecimalFormat("#.00");
            CpuInfo cpuInfo = OshiUtil.getCpuInfo();
            StringJoiner info = new StringJoiner("\n");
            info.add("Cpu{核心数：" + cpuInfo.getCpuNum() + "，CPU利用率：" + Double.parseDouble(format.format(100.0D - cpuInfo.getFree())) + "}");
            GlobalMemory memory = OshiUtil.getMemory();
            info.add("内存{总量：" + FormatUtil.formatBytes(memory.getTotal()) + "，剩余量：" + FormatUtil.formatBytes(memory.getAvailable()) + "}");
            info.add(getDiskUsed());
            info.add(OshiUtil.getOs().toString());
            return info.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 获取硬盘使用量
     */
    public static String getDiskUsed() {
        File win = new File("/");
        if (win.exists()) {
            long total = win.getTotalSpace();
            long freeSpace = win.getFreeSpace();
            return "磁盘{总量：" + FormatUtil.formatBytes(total) + "；剩余总量：" + FormatUtil.formatBytes(freeSpace)
                    + "；已用总量：" + FormatUtil.formatBytes((total - freeSpace)) + "}";
        }
        return "";
    }

    private static String macAddr = "";

    public static String getMacAddr() {
        if (StringUtils.hasLength(macAddr)) {
            return macAddr;
        }
        List<NetworkIF> list = OshiUtil.getNetworkIFs();
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        macAddr = list.get(0).getMacaddr();
        return macAddr;
    }

    public static String getMacAddr(boolean isColon) {
        String tmp = getMacAddr();
        if (!isColon) {
            tmp = tmp.replaceAll(":", "_");
        }
        return tmp;
    }
}
