/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.core.pojo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 响应结果数据
 *
 * <AUTHOR>
 * @date 2020/3/30 15:04
 */
@Data
@ApiModel(value = "响应")
public class ResponseData<T> {

    public static final String DEFAULT_SUCCESS_MESSAGE = "请求成功";

    public static final String DEFAULT_ERROR_MESSAGE = "网络异常";

    public static final Integer DEFAULT_SUCCESS_CODE = 200;

    public static final Integer DEFAULT_ERROR_CODE = 500;

    /**
     * 请求是否成功
     */
    @ApiModelProperty(value = "请求是否成功")
    private Boolean success;

    /**
     * 响应状态码
     */
    @ApiModelProperty(value = "响应状态码", notes = "成功：200，异常：500 正常是这2个，其他情况下另外定")
    private Integer code;

    /**
     * 响应信息
     */
    @ApiModelProperty(value = "响应信息")
    private String message;

    /**
     * 响应对象
     */
    @ApiModelProperty(value = "响应对象")
    private T data;

    public ResponseData() {
    }

    public ResponseData(Boolean success, Integer code, String message, T data) {
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static SuccessResponseData<?> success() {
        return new SuccessResponseData<>();
    }

    public static <T> SuccessResponseData<T> success(T object) {
        return new SuccessResponseData<>(object);
    }

    public static <T> SuccessResponseData<T> success(Integer code, String message, T object) {
        return new SuccessResponseData<>(code, message, object);
    }

    public static ErrorResponseData<?> error(String message) {
        return new ErrorResponseData<>(message);
    }

    public static ErrorResponseData<?> error(Integer code, String message) {
        return new ErrorResponseData<>(code, message);
    }

    public static <T> ErrorResponseData<T> error(Integer code, String message, T object) {
        return new ErrorResponseData<>(code, message, object);
    }
}
