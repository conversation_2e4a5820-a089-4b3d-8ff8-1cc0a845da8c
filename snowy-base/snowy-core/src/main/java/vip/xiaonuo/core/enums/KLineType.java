package vip.xiaonuo.core.enums;

import java.util.concurrent.TimeUnit;

public enum KLineType {

    M1("1m", "1分钟", TimeUnit.MINUTES.toMillis(1)),
    M5("5m", "5分钟", TimeUnit.MINUTES.toMillis(5)),
    M15("15m", "15分钟", TimeUnit.MINUTES.toMillis(15)),
    M30("30m", "30分钟", TimeUnit.MINUTES.toMillis(30)),
    H1("1h", "1小时", TimeUnit.HOURS.toMillis(1)),
    H2("2h", "2小时", TimeUnit.HOURS.toMillis(2)),
    H4("4h", "4小时", TimeUnit.HOURS.toMillis(4)),
    H6("6h", "6小时", TimeUnit.HOURS.toMillis(6)),
    H12("12h", "12小时", TimeUnit.HOURS.toMillis(12)),
    D1("1d", "1天", TimeUnit.DAYS.toMillis(1)),
    W1("1w", "1周", TimeUnit.DAYS.toMillis(7));

    private String code;
    private String describe;
    private long period;

    KLineType(String code, String describe, long period) {
        this.code = code;
        this.describe = describe;
        this.period = period;
    }

    public String getCode() {
        return code;
    }

    public long getPeriod() {
        return period;
    }

    public String getDescribe() {
        return describe;
    }

    public static KLineType of(long period) {
        for (KLineType k : KLineType.values()) {
            if (k.period == period) {
                return k;
            }
        }
        throw new RuntimeException("period " + period + " 没有对应的值");
    }

    public static KLineType of(String code) {
        for (KLineType k : KLineType.values()) {
            if (k.code.equalsIgnoreCase(code)) {
                return k;
            }
        }
        throw new RuntimeException("code " + code + " 没有对应的值");
    }

    @Override
    public String toString() {
        return code;
    }
}
