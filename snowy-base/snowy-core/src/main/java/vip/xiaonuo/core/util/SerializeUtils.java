package vip.xiaonuo.core.util;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.UnpooledByteBufAllocator;
import vip.xiaonuo.core.pojo.dto.TradeDTO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2018/8/31 9:31
 */
public class SerializeUtils {

    public static String serializeTradeDTO(TradeDTO tradeDTO) {
        ByteBuf buffer = UnpooledByteBufAllocator.DEFAULT.buffer(64);
        buffer.writeLong(tradeDTO.getTransTime());
        buffer.writeDouble(tradeDTO.getPrice().stripTrailingZeros().doubleValue());
        buffer.writeDouble(tradeDTO.getQuantity().stripTrailingZeros().doubleValue());
        buffer.writeBoolean("buy".equalsIgnoreCase(tradeDTO.getMainMarket()));
        buffer.writeByte(tradeDTO.getPriceDecimal());
        buffer.writeByte(tradeDTO.getQuantityDecimal());
//        buffer.writeInt(tradeDTO.getBuyUserId());
//        buffer.writeInt(tradeDTO.getSellUserId());
        int bytes = buffer.readableBytes();
        byte[] b = new byte[bytes];
        buffer.readBytes(b);
        buffer.release();
        return Base64.getEncoder().encodeToString(b);
    }

    public static TradeDTO deserializeTradeDTO(String value) {
        ByteBuf buffer = UnpooledByteBufAllocator.DEFAULT.buffer(64);
        buffer.writeBytes(Base64.getDecoder().decode(value));
        TradeDTO tradeDTO = new TradeDTO();
        tradeDTO.setTransTime(buffer.readLong());
        tradeDTO.setPrice(BigDecimal.valueOf(buffer.readDouble()));
        tradeDTO.setQuantity(BigDecimal.valueOf(buffer.readDouble()));
        tradeDTO.setMainMarket(buffer.readBoolean() ? "buy" : "sell");
        tradeDTO.setPriceDecimal(buffer.readByte());
        tradeDTO.setQuantityDecimal(buffer.readByte());
//        tradeDTO.setBuyUserId(buffer.readInt());
//        tradeDTO.setSellUserId(buffer.readInt());
        buffer.release();
        return tradeDTO;
    }

    public static List<BigDecimal> deserializeKLineData(String value) {
        ByteBuf buffer = UnpooledByteBufAllocator.DEFAULT.buffer(64);
        buffer.writeBytes(Base64.getDecoder().decode(value));
        long time = buffer.readLong();
        List<BigDecimal> list = new ArrayList<>(6);
        list.add(BigDecimal.valueOf(time));
        list.add(BigDecimal.valueOf(buffer.readDouble()));
        list.add(BigDecimal.valueOf(buffer.readDouble()));
        list.add(BigDecimal.valueOf(buffer.readDouble()));
        list.add(BigDecimal.valueOf(buffer.readDouble()));
        list.add(BigDecimal.valueOf(buffer.readDouble()));
        buffer.release();
        return list;
    }
}
