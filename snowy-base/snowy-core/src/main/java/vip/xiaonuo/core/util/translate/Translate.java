package vip.xiaonuo.core.util.translate;

import com.mysql.cj.util.LogUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vip.xiaonuo.core.exception.TranslateOutOfLimitException;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;

public abstract class Translate {
    Logger logger = LoggerFactory.getLogger(this.getClass());
    private final int CONNECT_ERROR_MAX = 3;
    private ThreadLocal<AtomicInteger> connectErrorCountThreadLocal = new ThreadLocal<>();
    private ThreadLocal<Long> connectErrorDateThreadLocal = new ThreadLocal<>();
    private Map<String, String> translateMap = new HashMap<>();
    private Long lastTimeMillis;

    protected String check(String text) {
        text = StringUtils.trimToEmpty(text);
        if (StringUtils.isEmpty(text)) {
            return "";
        }
        if (text.contains("@") || text.contains("http") || text.contains("www.")) {
            //邮箱不翻译,网址不翻译
            return "";
        }
        String textTrim = text.replaceAll(" ", "");
        if (NumberUtils.isCreatable(textTrim)) {
            //如果为纯数字，不翻译
            return "";
        }
        Pattern patPunch =
                Pattern.compile("[^`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？0-9]");
        if (!patPunch.matcher(textTrim).find()) {
            //如果出现纯标点符号与数字，不翻译
            return "";
        }
        return text;
    }

    public String translate(String text, String to) throws IOException, TranslateOutOfLimitException {
        text = check(text);
        if (StringUtils.isEmpty(text)) {
            return "";
        }
        String tText = translateMap.get(text + "_" + to);
        if (StringUtils.isNotEmpty(tText)) {
            //缓存，减少调用次数
            return tText;
        }
        initAutoCount();
        try {
            Word word = execute(text, to);
            if (word == null || StringUtils.isEmpty(word.getTargetText())) {
                return "";
            }
            String translateText = word.getTargetText();
            translateMap.put(text + "_" + to, translateText);
            return translateText;
        } catch (java.net.ConnectException e) {
            logger.error("Google翻译失败," + e.getMessage(), e);
            autoCount(e);
            return "";
        }
    }


    private void initAutoCount() {
        Long date = connectErrorDateThreadLocal.get();
        if (date == null) {
            connectErrorDateThreadLocal.set(System.currentTimeMillis());
            connectErrorCountThreadLocal.set(new AtomicInteger(0));
            return;
        }
        Long c = System.currentTimeMillis();
        if ((c - date) >= (1000 * 60 * 10)) {
            //如果大于等于10分钟，重新计算
            connectErrorDateThreadLocal.set(System.currentTimeMillis());
            connectErrorCountThreadLocal.set(new AtomicInteger(0));
        }
    }

    private void autoCount(Exception e) throws TranslateOutOfLimitException {
        AtomicInteger atomicInteger = connectErrorCountThreadLocal.get();
        int count = atomicInteger.incrementAndGet();
        if (count > CONNECT_ERROR_MAX) {
            throw new TranslateOutOfLimitException("翻译连接失败超出限制次数 " + CONNECT_ERROR_MAX);
        }
    }

    protected abstract Word execute(String text, String to) throws IOException;

    public static class Word {
        private String sourceCode;
        private String targetText;

        public Word(String sourceCode, String targetText) {
            this.sourceCode = sourceCode;
            this.targetText = targetText;
        }

        public String getSourceCode() {
            return sourceCode;
        }


        public String getTargetText() {
            return targetText;
        }

        @Override
        public String toString() {
            return "Word{" +
                    "sourceCode='" + sourceCode + '\'' +
                    ", targetText='" + targetText + '\'' +
                    '}';
        }
    }
}
