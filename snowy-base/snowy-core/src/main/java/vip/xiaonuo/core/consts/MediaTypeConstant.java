/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.core.consts;

/**
 * 常用媒体类型常量
 *
 * <AUTHOR>
 * @date 2020/7/9 14:14
 */
public interface MediaTypeConstant {

    /**
     * 图片jpg格式
     */
    String IMG_JPG = "jpg";

    /**
     * 图片png格式
     */
    String IMG_PNG = "png";

    /**
     * 图片jpeg格式
     */
    String IMG_JPEG = "jpeg";

    /**
     * 图片tif格式
     */
    String IMG_TIF = "tif";

    /**
     * 图片gif格式
     */
    String IMG_GIF = "gif";

    /**
     * 图片bmp格式
     */
    String IMG_BMP = "bmp";
    String IMG_JFIF = "jfif";
    /**
     * 文档txt格式
     */
    String DOC_TXT = "txt";

    /**
     * 文档doc格式
     */
    String DOC_DOC = "doc";

    /**
     * 文档docx格式
     */
    String DOC_DOCX = "docx";

    /**
     * 文档xls格式
     */
    String DOC_XLS = "xls";

    /**
     * 文档xlsx格式
     */
    String DOC_XLSX = "xlsx";

    /**
     * 文档ppt格式
     */
    String DOC_PPT = "ppt";

    /**
     * 文档pptx格式
     */
    String DOC_PPTX = "pptx";

    /**
     * 文档pdf格式
     */
    String DOC_PDF = "pdf";

    /**
     * 文件html格式
     */
    String FILE_HTML = "html";

    /**
     * 文件htm格式
     */
    String FILE_HTM = "htm";

    /**
     * 文件swf格式
     */
    String FILE_SWF = "swf";

    /**
     * 文件flash格式
     */
    String FILE_FLASH = "flash";

}
