package vip.xiaonuo.core.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.system.ApplicationHome;

import java.io.File;

/**
 * shiny
 */
public class PathUtils {
    private static String pathDir = null;

    public static String getPathDir(Class<?> clz) {
        if (StringUtils.isNotEmpty(pathDir)) {
            return pathDir;
        }
        ApplicationHome h = new ApplicationHome(clz);
        File jar = h.getSource();
        pathDir = jar.getParentFile().toString();
        return pathDir;
    }

    public static String getPidPath(Class<?> clz) {
        String rootDir = getPathDir(clz);
        return rootDir + File.separator + "application.pid";
    }
}
