package vip.xiaonuo.core.pojo.dto;

import java.math.BigDecimal;
import java.util.List;

public class KLineUpdateDTO {
    private String symbol;
    private long period;
    private List<List<BigDecimal>> lines;

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public long getPeriod() {
        return period;
    }

    public void setPeriod(long period) {
        this.period = period;
    }

    public List<List<BigDecimal>> getLines() {
        return lines;
    }

    public void setLines(List<List<BigDecimal>> lines) {
        this.lines = lines;
    }
}
