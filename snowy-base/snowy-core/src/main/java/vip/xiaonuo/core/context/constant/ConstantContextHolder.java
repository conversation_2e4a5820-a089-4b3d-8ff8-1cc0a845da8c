/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.core.context.constant;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.log.Log;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.consts.SymbolConstant;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.file.modular.aws.prop.AwsS3Properties;
import vip.xiaonuo.core.pojo.oauth.OauthConfigs;
import vip.xiaonuo.core.pojo.upay.UPayConfig;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static vip.xiaonuo.core.exception.enums.ServerExceptionEnum.CONSTANT_EMPTY;

/**
 * 系统参数配置获取
 *
 * <AUTHOR>
 * @date 2020/4/14 15:34
 */
public class ConstantContextHolder {

    private static final Log log = Log.get();

    /**
     * 获取租户功能是否开启
     *
     * <AUTHOR>
     * @date 2020/9/3
     */
    public static Boolean getTenantOpenFlag() {
        return getSysConfigWithDefault("SNOWY_TENANT_OPEN", Boolean.class, false);
    }

    /**
     * 获取验证码功能是否开启
     *
     * <AUTHOR>
     * @date 2020/9/3
     */
    public static Boolean getCaptchaFlag() {
        return getSysConfigWithDefault("SNOWY_CAPTCHA_OPEN", Boolean.class, false);
    }

    /**
     * 获取放开xss过滤的接口
     *
     * <AUTHOR>
     * @date 2020/6/20 22:13
     */
    public static List<String> getUnXssFilterUrl() {
        String snowyUnXssFilterUrl = getSysConfigWithDefault("SNOWY_UN_XSS_FILTER_URL", String.class, null);
        if (ObjectUtil.isEmpty(snowyUnXssFilterUrl)) {
            return CollectionUtil.newArrayList();
        } else {
            return CollectionUtil.toList(snowyUnXssFilterUrl.split(SymbolConstant.COMMA));
        }
    }

    /**
     * 获取演示环境开关是否开启，默认为false
     *
     * <AUTHOR>
     * @date 2020/6/20 22:13
     */
    public static Boolean getDemoEnvFlag() {
        return getSysConfigWithDefault("SNOWY_DEMO_ENV_FLAG", Boolean.class, false);
    }

    /**
     * 获取jwt密钥，默认是32位随机字符串
     *
     * <AUTHOR>
     * @date 2020/6/19 18:08
     */
    public static String getJwtSecret() {
        return getSysConfigWithDefault("SNOWY_JWT_SECRET", String.class, RandomUtil.randomString(32));
    }

    /**
     * 获取默认密码
     *
     * <AUTHOR>
     * @date 2020/6/19 18:08
     */
    public static String getDefaultPassWord() {
        return getSysConfigWithDefault("SNOWY_DEFAULT_PASSWORD", String.class, CommonConstant.DEFAULT_PASSWORD);
    }

    /**
     * 获取会话过期时间，默认2小时
     *
     * <AUTHOR>
     * @date 2020/7/9 16:18
     */
    public static Long getSessionTokenExpireSec() {
        return getSysConfigWithDefault("SNOWY_SESSION_EXPIRE", Long.class, 2 * 60 * 60L);
    }

    /**
     * 获取token过期时间（单位：秒）
     * <p>
     * 默认时间1天
     *
     * <AUTHOR>
     * @date 2020/6/19 18:08
     */
    public static Long getTokenExpireSec() {
        return getSysConfigWithDefault("SNOWY_TOKEN_EXPIRE", Long.class, 86400L);
    }

    /**
     * 获取自定义的windows环境本地文件上传路径
     *
     * <AUTHOR>
     * @date 2020/6/19 18:09
     */
    public static String getDefaultFileUploadPathForWindows() {
        return getSysConfigWithDefault("SNOWY_FILE_UPLOAD_PATH_FOR_WINDOWS", String.class, "");
    }

    /**
     * 获取自定义的linux环境本地文件上传路径
     *
     * <AUTHOR>
     * @date 2020/6/19 18:09
     */
    public static String getDefaultFileUploadPathForLinux() {
        return getSysConfigWithDefault("SNOWY_FILE_UPLOAD_PATH_FOR_LINUX", String.class, "");
    }

    /**
     * 获取是否允许单用户登陆的开关， 默认为false
     *
     * <AUTHOR>
     * @date 2020/6/19 18:09
     */
    public static Boolean getEnableSingleLogin() {
        return getSysConfigWithDefault("SNOWY_ENABLE_SINGLE_LOGIN", Boolean.class, false);
    }

    /**
     * 获取Oauth码云第三方登录的配置
     *
     * <AUTHOR>
     * @date 2020/7/28 17:16
     **/
    public static OauthConfigs getGiteeOauthConfigs() {
        String snowyClientId = getSysConfig("SNOWY_OAUTH_GITEE_CLIENT_ID", String.class, true);
        String snowyClientSecret = getSysConfig("SNOWY_OAUTH_GITEE_CLIENT_SECRET", String.class, true);
        String snowyRedirectUri = getSysConfig("SNOWY_OAUTH_GITEE_REDIRECT_URI", String.class, true);

        OauthConfigs oauthConfigs = new OauthConfigs();
        oauthConfigs.setClientId(snowyClientId);
        oauthConfigs.setClientSecret(snowyClientSecret);
        oauthConfigs.setRedirectUri(snowyRedirectUri);
        return oauthConfigs;
    }

    /**
     * 获取OauthGithub第三方登录的配置
     *
     * <AUTHOR>
     * @date 2020/7/28 17:16
     **/
    public static OauthConfigs getGithubOauthConfigs() {
        String snowyClientId = getSysConfig("SNOWY_OAUTH_GITHUB_CLIENT_ID", String.class, true);
        String snowyClientSecret = getSysConfig("SNOWY_OAUTH_GITHUB_CLIENT_SECRET", String.class, true);
        String snowyRedirectUri = getSysConfig("SNOWY_OAUTH_GITHUB_REDIRECT_URI", String.class, true);

        OauthConfigs oauthConfigs = new OauthConfigs();
        oauthConfigs.setClientId(snowyClientId);
        oauthConfigs.setClientSecret(snowyClientSecret);
        oauthConfigs.setRedirectUri(snowyRedirectUri);
        return oauthConfigs;
    }

    /**
     * 获取是否允许Oauth用户登陆的开关， 默认为false
     *
     * <AUTHOR>
     * @date 2020/7/28 16:37
     **/
    public static Boolean getEnableOauthLogin() {
        return getSysConfigWithDefault("SNOWY_ENABLE_OAUTH_LOGIN", Boolean.class, false);
    }

    /**
     * 获取前端项目的地址
     *
     * <AUTHOR>
     * @date 2020/7/29 14:08
     **/
    public static String getWebUrl() {
        return getSysConfig("SNOWY_WEB_URL", String.class, true);
    }

    /**
     * 获取支付宝支付成功转发地址
     *
     * <AUTHOR>
     * @date 2020/7/29 14:08
     **/
    public static String getAlipayReturnUrl() {
        return getSysConfig("SNOWY_ALIPAY_RETURN_URL", String.class, true);
    }


    /**
     * 获取OnlyOffice地址
     *
     * <AUTHOR>
     * @date 2020/7/29 14:08
     **/
    public static String getOnlyOfficeUrl() {
        return getSysConfig("SNOWY_ONLY_OFFICE_SERVICE_URL", String.class, true);
    }

    /**
     * 获取config表中的配置，如果为空返回默认值
     *
     * @param configCode   变量名称，对应sys_config表中的code
     * @param clazz        返回变量值的类型
     * @param defaultValue 如果结果为空返回此默认值
     * <AUTHOR>
     * @date 2020/6/20 22:03
     */
    public static <T> T getSysConfigWithDefault(String configCode, Class<T> clazz, T defaultValue) {
        String configValue = ConstantContext.me().getStr(configCode);
        if (ObjectUtil.isEmpty(configValue)) {
            // 将默认值加入到缓存常量
            log.warn(">>> 系统配置sys_config表中存在空项，configCode为：{}，系统采用默认值：{}", configCode, defaultValue);
            ConstantContext.me().put(configCode, defaultValue);
            return defaultValue;
        } else {
            try {
                return Convert.convert(clazz, configValue);
            } catch (Exception e) {
                return defaultValue;
            }
        }
    }

    /**
     * 获取config表中的配置，如果为空，是否抛出异常
     *
     * @param configCode   变量名称，对应sys_config表中的code
     * @param clazz        返回变量值的类型
     * @param nullThrowExp 若为空是否抛出异常
     * <AUTHOR>
     * @date 2020/6/20 22:03
     */
    public static <T> T getSysConfig(String configCode, Class<T> clazz, Boolean nullThrowExp) {
        String configValue = ConstantContext.me().getStr(configCode);
        if (ObjectUtil.isEmpty(configValue)) {
            if (nullThrowExp) {
                String format = StrUtil.format(">>> 系统配置sys_config表中存在空项，configCode为：{}", configCode);
                log.error(format);
                throw new ServiceException(CONSTANT_EMPTY.getCode(), format);
            } else {
                return null;
            }
        } else {
            try {
                return Convert.convert(clazz, configValue);
            } catch (Exception e) {
                if (nullThrowExp) {
                    String format = StrUtil.format(">>> 系统配置sys_config表中存在格式错误的值，configCode={}，configValue={}", configCode, configValue);
                    log.error(format);
                    throw new ServiceException(CONSTANT_EMPTY.getCode(), format);
                } else {
                    return null;
                }
            }
        }
    }

    public static AwsS3Properties getAwsS3Properties() {
        String accessKey = getSysConfig("SNOWY_AWS_S3_ACCESS_KEY", String.class, true);
        String secretKey = getSysConfig("SNOWY_AWS_S3_SECRET_KEY", String.class, true);
        String region = getSysConfig("SNOWY_AWS_S3_REGION", String.class, true);
        String serviceEndpoint = getSysConfig("SNOWY_AWS_S3_SERVICEENDPOINT", String.class, true);
        String bucketName = getSysConfig("SNOWY_AWS_S3_BUCKET_NAME", String.class, true);

        AwsS3Properties awsS3Properties = new AwsS3Properties();
        awsS3Properties.setAccessKey(accessKey);
        awsS3Properties.setSecretKey(secretKey);
        awsS3Properties.setRegion(region);
        awsS3Properties.setBucketName(bucketName);
        awsS3Properties.setServiceEndpoint(serviceEndpoint);
        return awsS3Properties;
    }

    /**
     * 获取现货订单风控
     *
     * <AUTHOR>
     * @date 2020/6/19 18:08
     */
    public static String getRiskControlSpotScale() {
        return getSysConfigWithDefault(CommonConstant.RISK_CONTROL_SPOT_SCALE, String.class, "1-1000:40|1001-5000:25|5001-9999999:10");
    }

    /**
     * 获取upay支付配置信息
     *
     * <AUTHOR>
     * @date 2020/7/28 17:16
     **/
    public static UPayConfig getUpayConfigs() {
        String upayAppId = getSysConfig("UPAY_APPID", String.class, true);
        String upayAppSecret = getSysConfig("UPAY_APP_SECRET", String.class, true);
        String upayNotifyUrl = getSysConfig("UPAY_NOTICE_URL", String.class, true);
        String upayCallbackUrl = getSysConfig("UPAY_CALLBACK_URL", String.class, true);
        String createOrderUrl = getSysConfig("UPAY_CREATE_ORDER_URL", String.class, true);

        UPayConfig uPayConfig = new UPayConfig();
        uPayConfig.setUpayAppId(upayAppId);
        uPayConfig.setUpayAppSecret(upayAppSecret);
        uPayConfig.setUpayNotifyUrl(upayNotifyUrl);
        uPayConfig.setUpayCallbackUrl(upayCallbackUrl);
        uPayConfig.setUpayCreateOrderUrl(createOrderUrl);
        return uPayConfig;
    }

    public static List<BigDecimal> get8LevelCommissionSetting() {
        String configStr = getSysConfigWithDefault("SNOWY_8_LEVEL_COMMISSION_SETTING", String.class, CommonConstant.SNOWY_8_LEVEL_COMMISSION_SETTING);

        return Arrays.stream(configStr.split(","))
                .map(String::trim)                      // 去除空格
                .map(BigDecimal::new)                   // 转换为 BigDecimal
                .collect(Collectors.toList());          // 收集为 List
    }


    public static BigDecimal getFollowerCommissionSetting() {
        String configStr = getSysConfigWithDefault("SNOWY_FOLLOWER_COMMISSION_SETTING", String.class, CommonConstant.SNOWY_8_LEVEL_COMMISSION_SETTING);

        return new BigDecimal(configStr);
    }
}
