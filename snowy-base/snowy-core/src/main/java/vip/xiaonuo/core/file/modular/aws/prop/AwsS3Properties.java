package vip.xiaonuo.core.file.modular.aws.prop;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
public class AwsS3Properties {
    //秘钥id
    private String accessKey;

    //秘钥secret
    private String secretKey;

    private String region;

    private String serviceEndpoint;

    private String bucketName;
}
