package vip.xiaonuo.core.util;


import org.springframework.dao.InvalidDataAccessApiUsageException;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.util.CollectionUtils;
import vip.xiaonuo.core.consts.DpConstants;
import vip.xiaonuo.core.enums.KLineType;
import vip.xiaonuo.core.pojo.dto.MarketDTO;
import vip.xiaonuo.core.pojo.dto.TradeDTO;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


public class DpDataUtils {

    /**
     * 获取交易对的最新成交列表
     */
    public static List<TradeDTO> getRecentTrades(RedisTemplate<String, Object> redisTemplate, String symbol) {
        String redisKey = String.format(DpConstants.MARKET_RECENT_TRADES, symbol);
        List<?> range = redisTemplate.opsForList().range(redisKey, 0, -1);
        range = range == null ? new ArrayList<String>() : range;
        return ((List<String>) range).stream().map(SerializeUtils::deserializeTradeDTO).collect(Collectors.toList());
    }

    /**
     * 获取交易对某个周期的K线数据列表（最多1000条）
     */
    public static List<List<BigDecimal>> getKLineData(StringRedisTemplate stringRedisTemplate,
                                                      String symbol, KLineType kLineType, int limit, Long endTime) {
        if (limit <= 0) {
            throw new IllegalArgumentException("limit <= 0");
        }
        String key = String.format(DpConstants.REDIS_KLINE_KEY, symbol, kLineType.getCode());
        ZSetOperations<String, String> setOperations = stringRedisTemplate.opsForZSet();
        Set<String> objects;
        if (endTime != null) {
            long startTime = endTime - kLineType.getPeriod() * limit;
            endTime = endTime - 1;//不包含最后一个
            objects = setOperations.rangeByScore(key, startTime, endTime);
        } else {
            objects = setOperations.range(key, -limit, -1);
        }
        if (CollectionUtils.isEmpty(objects)) {
            return Collections.emptyList();
        }
        return objects.stream().map(e -> e + "").map(SerializeUtils::deserializeKLineData)
                .collect(Collectors.toList());
    }

    /**
     * 获取交易对某个周期的K线数据列表（最多1000条）
     */
    public static List<List<BigDecimal>> getKLineData(StringRedisTemplate stringRedisTemplate,
                                                      String symbol, KLineType kLineType, int limit) {
        return getKLineData(stringRedisTemplate, symbol, kLineType, limit, null);
    }

    /**
     * // todo gjn 控制价格
     * 获取交易对的最新成交价
     */
    public static BigDecimal getLatestPrice(RedisTemplate<String, Object> redisTemplate, String symbol) {
        Object o = redisTemplate.opsForValue().get(String.format(DpConstants.MARKET_LATEST_PRICE, symbol));
        return o == null ? null : (BigDecimal) o;
    }

    /**
     * 获取交易对24小时前的价格
     */
//    public static BigDecimal get24hOpen(RedisTemplate<String, Object> redisTemplate, String symbol) {
//        String redisKey1 = String.format(DpConstants.MARKET_24H_PRICE, symbol);
//        ZSetOperations<String, Object> zSetOperations = redisTemplate.opsForZSet();
//
//        Set<Object> range = zSetOperations.range(redisKey1, 0, 1);
//
//        if (CollectionUtils.isEmpty(range)) {
//            return BigDecimal.ZERO;
//        }
//        MarketPriceDTO priceDTO = (MarketPriceDTO) range.iterator().next();
//        if(priceDTO.getTime() < System.currentTimeMillis() - 24 * 3600000) {
//            boolean b = deleteExtraTradeData(zSetOperations, redisKey1);
//            if(b) {
//                return get24hOpen(redisTemplate, symbol);
//            }
//        }
//
//        return priceDTO.getPrice();
//    }

//    public static boolean deleteExtraTradeData(ZSetOperations<String, Object> zSetOperations, String redisKey1) {
//        if (zSetOperations.size(redisKey1) <= 1) {
//            return false;
//        }
//        long firstTimePoint = System.currentTimeMillis() - 24 * 3600 * 1000;
//        Set<Object> range = zSetOperations.range(redisKey1, 0, 2);
//
//        //拿到第二个元素，如果时间在24小时之前的时间点之后，则要保留第一个元素，结束递归；
//        // 否则要把第一个元素干掉，继续递归
//        //目的：保证zSet的第一个元素就是24小时之前的时间点的当时价格
//        Iterator<Object> iterator = range.iterator();
//        MarketPriceDTO first = (MarketPriceDTO) iterator.next();
//        MarketPriceDTO second = (MarketPriceDTO) iterator.next();
//        if (second.getTime() >= firstTimePoint) {
//            return false;
//        }
//        zSetOperations.removeRange(redisKey1, 0, 1);
//        deleteExtraTradeData(zSetOperations, redisKey1);
//        return true;
//    }

    /**
     * 获取所有交易对的ticker
     */
    public static List<MarketDTO> getTickers(HashOperations<String, String, MarketDTO> hashOperations) {
        try {
            return hashOperations.values(DpConstants.PREFIX_MARKET_24H);
        } catch (InvalidDataAccessApiUsageException exception) {
            return new ArrayList<>();
        }
    }

    /**
     * 获取某个交易对的所有ticker
     */
    public static MarketDTO getMarket24hTicker(HashOperations<String, String, MarketDTO> hashOperations, String symbol) {
        List<MarketDTO> tickers = getTickers(hashOperations);
        return tickers.stream().filter(e -> symbol.equalsIgnoreCase(e.getSymbol())).findAny().orElse(null);
    }

    /**
     * 获取交易对的深度合并的深度选项
     */
    public static List<Integer> getMarketGroupDepth(RedisTemplate<String, Object> redisTemplate, String symbol) {
        return (List<Integer>) redisTemplate.opsForValue().get(String.format(DpConstants.ORDER_DEPTH_GROUP, symbol));
    }

    /**
     * 获取交易对的k线周期
     */
    public static List<String> getDisabledKLinePeriods(RedisTemplate<String, Object> redisTemplate, String symbol) {
        List<String> disabledPeriods = (List<String>) redisTemplate.opsForValue().get(String.format(DpConstants.KLINE_DISABLED_PERIODS, symbol));
        return disabledPeriods == null ? new ArrayList<>() : disabledPeriods;
    }

    public static BigDecimal getEstimateValue(RedisTemplate<String, Object> redisTemplate,
                                              String currency, String unitCurrency) {
        if (!"USD".equalsIgnoreCase(unitCurrency) && !"BTC".equalsIgnoreCase(unitCurrency)
                && !"CNY".equalsIgnoreCase(unitCurrency)) {
            throw new RuntimeException("unitCurrency 必须为BTC/USD/CNY");
        }
        unitCurrency = unitCurrency.toUpperCase();
        String redisKey = MessageFormat.format(DpConstants.ASSETS_ESTIMATED, currency.toUpperCase(), unitCurrency);
        return (BigDecimal) redisTemplate.opsForValue().get(redisKey);
    }

    public static BigDecimal getAdjustPrice(RedisTemplate<String, Object> redisTemplate, String symbol) {
        Object o = redisTemplate.opsForValue().get(String.format(DpConstants.MARKET_ADJUST_PRICE, symbol));
        return o == null ? null : (BigDecimal) o;
    }

    public static BigDecimal getCompletedAdjustPrice(RedisTemplate<String, Object> redisTemplate, String symbol) {
        String key = String.format(DpConstants.ADJUST_COMPLETED_PRICE, symbol);
        Object o = redisTemplate.opsForHash().get(key, "P");
        return o == null ? null : (BigDecimal) o;
    }

    public static Long getCompletedAdjustTs(RedisTemplate<String, Object> redisTemplate, String symbol) {
        String key = String.format(DpConstants.ADJUST_COMPLETED_PRICE, symbol);
        Object o = redisTemplate.opsForHash().get(key, "T");
        return o == null ? null : Long.valueOf(o.toString());
    }
}
