package vip.xiaonuo.core.util;

import lombok.Getter;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum Language {
    en_US("en", "US"),//英语
    ja_JP("ja", "JP"),//日语
    vi_VN("vi", "VN"),//越南语
    ru_RU("ru", "RU"),//俄语
    es_ES("es", "ES"),//西班牙语
    tr_TR("tr", "TR"),//土耳其语
    it_IT("it", "IT"),//意大利语(意大利)
    fr_FR("fr", "FR"),//法语(法国)
    de_DE("de", "DE"),//德语(德国)
    zh_CN("zh", "CN"),
    zh_TW("zh", "TW"),
    pt_BR("pt", "BR"),//葡萄牙语(巴西)
    id_ID("id", "ID"),//印度尼西亚
    th_TH("th", "TH"),//泰语
    pl_PL("pl", "PL"),//波兰语
    uk_UA("uk", "UA"),//乌克兰语
    pt_PT("pt", "PT"),//葡萄牙语(葡萄牙)
    uz_UZ("uz", "UZ"),//乌兹别克语(拉丁文)
    ar_SA("ar", "SA"),//阿拉伯语(沙特阿拉伯)
    fil_PH("fil", "PH"),//菲律宾
    ;
    String lang;
    String country;

    Language(String lang, String country) {
        this.lang = lang;
        this.country = country;
    }

    public static List<String> toArray() {
        return Arrays.stream(Language.values()).map(Enum::name).collect(Collectors.toList());
    }

    public static final String CLIENT_LANGUAGE = "lang";

    public static Language getLanguage() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String language = request.getHeader(Language.CLIENT_LANGUAGE);
        try {
            return Language.valueOf(language);
        } catch (NoClassDefFoundError | NullPointerException | IllegalArgumentException e) {
            return Language.en_US;
        }
    }
}
