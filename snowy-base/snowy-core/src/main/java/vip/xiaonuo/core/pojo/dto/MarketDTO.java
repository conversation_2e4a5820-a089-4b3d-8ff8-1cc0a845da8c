package vip.xiaonuo.core.pojo.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import vip.xiaonuo.core.pojo.BigDecimalSerializer;
import vip.xiaonuo.core.util.MathUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

@ApiModel("行情")
public class MarketDTO implements Serializable {
    private static final long serialVersionUID = 6789651318664410579L;
    @ApiModelProperty("交易对")
    private String symbol;
    @ApiModelProperty("交易对名称")
    private String symbolName;
    @ApiModelProperty("最高价")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal h;
    @ApiModelProperty("最低价")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal l;
    @ApiModelProperty("最新价")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal c;
    @ApiModelProperty("总成交额")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal obv;
    @ApiModelProperty("总成交量")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal vol;
    @ApiModelProperty("开盘价")
    private BigDecimal o;
    private String logo;
    private String baseCurrencyCode;
    private String currencyCode;
    private Integer sort;
    @ApiModelProperty("价格小数位")
    private int priceDecimal;
    @ApiModelProperty("数量小数位")
    private int quantityDecimal;
    @ApiModelProperty("24小时前价格")
    private BigDecimal prevPrice;
    @ApiModelProperty("涨幅")
    private String changePercent;
    //0:非热门,1:热门
    @ApiModelProperty("0:非热门,1:热门")
    private Integer hot;

    public int getPriceDecimal() {
        return priceDecimal;
    }

    public void setPriceDecimal(int priceDecimal) {
        this.priceDecimal = priceDecimal;
    }

    public int getQuantityDecimal() {
        return quantityDecimal;
    }

    public void setQuantityDecimal(int quantityDecimal) {
        this.quantityDecimal = quantityDecimal;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getBaseCurrencyCode() {
        return baseCurrencyCode;
    }

    public void setBaseCurrencyCode(String baseCurrencyCode) {
        this.baseCurrencyCode = baseCurrencyCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public void setChangePercent(String changePercent) {
        this.changePercent = changePercent;
    }

    public String getChangePercent() {
        return changePercent;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public String getSymbolName() {
        return symbolName;
    }

    public void setSymbolName(String symbolName) {
        this.symbolName = symbolName;
    }

    public BigDecimal getH() {
        return MathUtil.stripZeros(h);
    }

    public void setH(BigDecimal highest) {
        this.h = highest;
    }

    public BigDecimal getL() {
        return MathUtil.stripZeros(l);
    }

    public void setL(BigDecimal lowest) {
        this.l = lowest;
    }

    public BigDecimal getC() {
        if (c == null) {
            c = BigDecimal.ZERO;
        }
        return c.stripTrailingZeros();
    }

    public void setC(BigDecimal current) {
        this.c = current;
    }

    public BigDecimal getObv() {
        BigDecimal a = MathUtil.stripZeros(obv);

        return a == null ? BigDecimal.ZERO : a.setScale(2, RoundingMode.CEILING);
    }

    public void setObv(BigDecimal totalTradeAmount) {
        this.obv = totalTradeAmount;
    }

    public BigDecimal getVol() {
        BigDecimal a = MathUtil.stripZeros(vol);
        return a == null ? BigDecimal.ZERO : a.setScale(2, RoundingMode.CEILING);
    }

    public void setVol(BigDecimal totalTradeQuantity) {
        this.vol = totalTradeQuantity;
    }

    public BigDecimal getO() {
        return MathUtil.stripZeros(o);
    }

    public void setO(BigDecimal openPrice) {
        this.o = openPrice;
    }

    public BigDecimal getPrevPrice() {
        return prevPrice;
    }

    public void setPrevPrice(BigDecimal prevPrice) {
        this.prevPrice = prevPrice;
    }

    public Integer getHot() {
        return hot;
    }

    public void setHot(Integer hot) {
        this.hot = hot;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }
}
