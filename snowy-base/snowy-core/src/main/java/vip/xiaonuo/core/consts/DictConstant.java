package vip.xiaonuo.core.consts;

public interface DictConstant {
    //邀请码注册开关
    String NEED_INVITE_CODE_SWITCH = "need_invite_code_switch";

    //需要短信开关
    String NEED_SMS_SWITCH = "need_sms_switch";

    //币币是否自动结算 0否 1是
    String COIN_AUTOSETTLED = "coin_autoSettled";

    //币币是否维护 0否 1是
    String COIN_MAINTAIN = "coin_maintain";

    //币币手续费%
    String COIN_FEE = "coin_fee";

    //合约手续费%
    String CONTRACT_FEE = "contract_fee";

    //合约是否维护 0否 1是
    String CONTRACT_MAINTAIN = "contract_maintain";

    //极速手续费%
    String SPOT_FEE = "spot_fee";

    //兑换手续费%
    String EXCHANGE_FEE = "exchange_fee";

    //可兑换币种,逗号隔开
    String EXCHANGE_CURRENCY_CODE = "exchange_currency_code";
}
