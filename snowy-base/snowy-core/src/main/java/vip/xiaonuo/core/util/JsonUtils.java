package vip.xiaonuo.core.util;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;

public class JsonUtils {
    public static <T> T convert(String text, Class<T> eventClass) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(text, eventClass);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
