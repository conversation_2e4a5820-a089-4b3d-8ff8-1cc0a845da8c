package vip.xiaonuo.core.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;
import vip.xiaonuo.core.pojo.page.PageResult;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

public class ConvertUtils {

    public static <S, P extends IPage<T>, T> P convert(IPage<S> objPage, Class<P> pageClazz, Class<T> clazz) {
        try {
            P resp = pageClazz.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(objPage, resp);
            resp.setRecords(convert(objPage.getRecords(), clazz));
            return resp;
        } catch (Exception e) {
            throw new RuntimeException("转换错误", e);
        }
    }

    public static <S, T> IPage<T> convert(IPage<S> objPage, Class<T> clazz) {
        try {
            IPage<T> respPage = new Page<T>();
            BeanUtils.copyProperties(objPage, respPage);
            respPage.setRecords(convert(objPage.getRecords(), clazz));
            return respPage;
        } catch (Exception e) {
            throw new RuntimeException("转换错误", e);
        }
    }

    public static <S, T> PageResult<T> convert(PageResult<S> objPage, Class<T> clazz) {
        try {
            PageResult<T> respPage = new PageResult<T>();
            BeanUtils.copyProperties(objPage, respPage);
            respPage.setData(convert(objPage.getData(), clazz));
            return respPage;
        } catch (Exception e) {
            throw new RuntimeException("转换错误", e);
        }
    }

    public static <S, T> List<T> convert(List<S> list, Class<T> clazz) {
        if (list == null) {
            return null;
        }
        List<T> respList = new ArrayList<T>();
        for (S obj : list) {
            respList.add(convert(obj, clazz));
        }
        return respList;
    }

    public static <S, T> T convert(S obj, Class<T> clazz) {
        if (obj == null) {
            return null;
        }
        try {
            T resp = clazz.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(obj, resp);
            return resp;
        } catch (Exception e) {
            throw new RuntimeException("对象转换错误", e);
        }
    }

    public static BigDecimal calcPercentage(BigDecimal value, BigDecimal rate) {
        if (value == null || rate == null || value.equals(BigDecimal.ZERO) || rate.equals(BigDecimal.ZERO)) {
            return BigDecimal.ZERO;
        }
        return value.multiply(rate).divide(new BigDecimal("100"), 2, RoundingMode.DOWN);
    }

    public static BigDecimal isNullDefaultZero(BigDecimal value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        return value;
    }
}
