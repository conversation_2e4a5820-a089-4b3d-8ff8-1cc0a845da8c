package vip.xiaonuo.core.pojo;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;

public class BigDecimalSerializer extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException, JsonProcessingException {
        BigDecimal _value = value;
        if(value == null){
            _value = new BigDecimal(0);
        }

        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setMaximumFractionDigits(8);
        numberFormat.setMinimumFractionDigits(Math.min(_value.scale(), 8));
        numberFormat.setGroupingUsed(false);
        numberFormat.setRoundingMode(RoundingMode.FLOOR);
        gen.writeString(numberFormat.format(_value));
    }
}