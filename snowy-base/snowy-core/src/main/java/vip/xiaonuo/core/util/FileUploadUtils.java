package vip.xiaonuo.core.util;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.log.Log;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.IOUtils;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import vip.xiaonuo.core.consts.MediaTypeConstant;
import vip.xiaonuo.core.consts.SymbolConstant;
import vip.xiaonuo.core.context.constant.ConstantContextHolder;
import vip.xiaonuo.core.file.common.exp.FileServiceException;
import vip.xiaonuo.core.file.modular.aws.AwsFileOperator;
import vip.xiaonuo.core.file.modular.aws.prop.AwsS3Properties;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.UUID;

/**
 * 文件上传工具类
 */
public class FileUploadUtils {

    private static final Log log = Log.get();

    /**
     * 文件上传-aws
     *
     * @param multipartFile
     * @return
     * @throws IOException
     */
    public static final String uploadS3(MultipartFile multipartFile) throws FileServiceException {
        return uploadS3(null, multipartFile, false);
    }

    /**
     * 文件上传-aws
     *
     * @param baseDir
     * @param multipartFile
     * @return
     * @throws FileServiceException
     */
    public static final String uploadS3(String baseDir, MultipartFile multipartFile, boolean isReturnUrl) throws FileServiceException {
        AwsFileOperator fileOperator = (AwsFileOperator) SpringContextUtil.getBean("fileOperator");
        String uploadError = MessageUtils.get("file.upload.error");
        if (ObjectUtils.isEmpty(fileOperator)) {
            log.error("获取文件上传操作类出错！");
            throw new FileServiceException(uploadError);
        }

        String extension = FileUtils.getExtension(multipartFile);
        if (!FileUtils.isAllowedExtension(extension, MimeTypeUtils.IMAGE_EXTENSION)) {
            log.error("不支持的文件类型！");
            throw new FileServiceException(MessageUtils.get("file.upload.type.error"));
        }

        AwsS3Properties awsS3Properties = ConstantContextHolder.getAwsS3Properties();
        if (ObjectUtils.isEmpty(awsS3Properties)) {
            log.error("获取配置文件出错！");
            throw new FileServiceException(uploadError);
        }

        //文件过期时间
        Long expire = ConstantContextHolder.getSysConfig("SNOWY_FILE_EXPIRE", Long.class, true);
        if (ObjectUtils.isEmpty(expire)) {
            expire = 7L;
        }
        String bucketName = awsS3Properties.getBucketName();
        String path = StringUtils.isEmpty(baseDir) ? "/coin/temp/" : baseDir;
        try {
            File file = FileUtils.convertMultiPartToFile(multipartFile);
            if(FileUtils.isAllowedExtension(extension, MimeTypeUtils.IMAGE_EXTENSION)){
                //文件压缩
                dealImg(file.getAbsolutePath(),1024,0.8f);
            }

            String key = path + UUID.randomUUID() + multipartFile.getOriginalFilename()
                    .substring(multipartFile.getOriginalFilename().lastIndexOf("."));

            ((AmazonS3)fileOperator.getClient()).putObject(new PutObjectRequest(bucketName, key, file)
                    .withCannedAcl(CannedAccessControlList.PublicRead));

            file.delete();
            return isReturnUrl ? fileOperator.getFileAuthUrl(bucketName, key, expire) : key;
        } catch (Exception e) {
            log.error("文件上传出错！", e);
            throw new FileServiceException(uploadError);
        }
    }

    private static void dealImg(String desPath,long desFileSize,double accuracy) throws Exception{
        File imgFile = new File(desPath);
        long fileSize = imgFile.length();
        //判断大小,如果小于500k,不压缩,如果大于等于500k,压缩
        if(fileSize <= desFileSize * 500){
            log.info("压缩后文件路径：{}  大小：{}",desPath,fileSize);
            return;
        }
        //计算宽高
        BufferedImage bim = ImageIO.read(imgFile);
        int imgWidth = bim.getWidth();
        int imgHeight = bim.getHeight();
        int desWidth = new BigDecimal(imgWidth).multiply(
                new BigDecimal(accuracy)).intValue();
        int desHeight = new BigDecimal(imgHeight).multiply(
                new BigDecimal(accuracy)).intValue();
        Thumbnails.of(desPath).size(desWidth, desHeight).outputQuality(accuracy).toFile(desPath);
        dealImg(desPath, desFileSize, accuracy);
    }

    /**
     * 下载图片
     *
     * @param objectName
     * @param response
     */
    public static void preview(String objectName, HttpServletResponse response) {
        // 获取文件后缀
        String fileSuffix = null;
        if (ObjectUtil.isNotEmpty(objectName)) {
            fileSuffix = StrUtil.subAfter(objectName, SymbolConstant.PERIOD, true);
        }
        String uploadError = MessageUtils.get("file.upload.error");
        AwsS3Properties awsS3Properties = ConstantContextHolder.getAwsS3Properties();
        if (ObjectUtils.isEmpty(awsS3Properties)) {
            log.error("获取配置文件出错！");
            throw new FileServiceException(uploadError);
        }
        AwsFileOperator fileOperator = (AwsFileOperator) SpringContextUtil.getBean("fileOperator");
        String bucketName = awsS3Properties.getBucketName();
        //如果是图片类型，则直接输出
        if (isPic(fileSuffix.toLowerCase())) {
            try {
                S3Object s3Object = fileOperator.getObject(bucketName, objectName);
                // 返回文件字节码
                byte[] fileBytes = IOUtils.toByteArray(s3Object.getObjectContent());
                //设置contentType
                response.setContentType(s3Object.getObjectMetadata().getContentType());
                //获取outputStream
                ServletOutputStream outputStream = response.getOutputStream();
                //输出
                IoUtil.write(outputStream, true, fileBytes);
            } catch (IOException e) {
                throw new FileServiceException(uploadError);
            }
        } else {
            //否则不支持预览（暂时）
            throw new FileServiceException(uploadError);
        }
    }

    /**
     * 根据文件后缀判断是否图片
     *
     * <AUTHOR>
     * @date 2020/7/6 15:31
     */
    public static boolean isPic(String fileSuffix) {
        return MediaTypeConstant.IMG_JPG.equals(fileSuffix)
                || MediaTypeConstant.IMG_JPEG.equals(fileSuffix)
                || MediaTypeConstant.IMG_PNG.equals(fileSuffix)
                || MediaTypeConstant.IMG_GIF.equals(fileSuffix)
                || MediaTypeConstant.IMG_TIF.equals(fileSuffix)
                || MediaTypeConstant.IMG_BMP.equals(fileSuffix)
                || MediaTypeConstant.IMG_JFIF.equals(fileSuffix);
    }
}