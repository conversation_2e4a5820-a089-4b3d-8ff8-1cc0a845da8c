package vip.xiaonuo.core.pojo;

import lombok.Data;
import vip.xiaonuo.core.redis.DelayMessageType;

import java.io.Serializable;
import java.util.concurrent.TimeUnit;

@Data
public class RedisDelayMessage<T> implements Serializable {
    private DelayMessageType type;
    private T data;
    private Long delayTime;
    private TimeUnit timeUnit;

    public RedisDelayMessage(DelayMessageType type, T data, Long delayTime, TimeUnit timeUnit) {
        this.type = type;
        this.data = data;
        this.delayTime = delayTime;
        this.timeUnit = timeUnit;
    }
}
