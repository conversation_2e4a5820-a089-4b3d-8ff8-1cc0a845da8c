package vip.xiaonuo.core.util;

import org.apache.commons.lang3.StringUtils;
import vip.xiaonuo.core.consts.RedisConstant;
import vip.xiaonuo.core.enums.SmsSendSourceEnum;

public class CommonUtils {

    public static String getVcLastKey(SmsSendSourceEnum type, String phone) {
        return RedisConstant.REDIS_SMS_VC_KEY + type.name() + ":" + phone;
    }

    public static String getVctLastKey(SmsSendSourceEnum type, String token) {
        return RedisConstant.REDIS_SMS_VCT_KEY + type.name() + ":" + token;
    }

    public static String getVcLtKey(SmsSendSourceEnum type, String accountNum) {
        return RedisConstant.REDIS_SMS_PHONE_LIMIT_KEY + type.name() + ":" + accountNum;
    }

    public static String getVcIpLtKey(String ip) {
        return RedisConstant.REDIS_SMS_IP_LIMIT_KEY + ip;
    }

    public static String star(String str) {
        StringBuilder builder = new StringBuilder();
        if (StringUtils.length(str) >= 7) {
            builder.append(str.substring(0, 3)).append("***").append(str.substring(str.length() - 2));
        } else if (StringUtils.length(str) >= 5) {
            builder.append(str.substring(0, 2)).append("***").append(str.substring(str.length() - 2));
        } else if (StringUtils.length(str) >= 3) {
            builder.append("***").append(str.substring(str.length() - 2));
        } else {
            builder.append("***");
        }
        return builder.toString();
    }
}
