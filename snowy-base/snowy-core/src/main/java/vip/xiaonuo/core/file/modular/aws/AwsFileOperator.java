/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.core.file.modular.aws;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.amazonaws.services.s3.transfer.Upload;
import org.apache.commons.io.IOUtils;
import vip.xiaonuo.core.file.FileOperator;
import vip.xiaonuo.core.file.common.enums.BucketAuthEnum;
import vip.xiaonuo.core.file.modular.aws.exp.AwsFileServiceException;
import vip.xiaonuo.core.file.modular.aws.prop.AwsS3Properties;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class AwsFileOperator implements FileOperator {

    /**
     * aws文件操作客户端
     */
    private AmazonS3 s3Client;

    /**
     * aws的配置
     */
    private final AwsS3Properties awsS3Properties;

    public AwsFileOperator(AwsS3Properties awsS3Properties) {
        this.awsS3Properties = awsS3Properties;
        this.initClient();
    }

    @Override
    public void initClient() {
        s3Client = AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials(awsS3Properties.getAccessKey(), awsS3Properties.getSecretKey())))
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(awsS3Properties.getServiceEndpoint(), awsS3Properties.getRegion()))
                .enablePathStyleAccess()
                .build();
    }

    @Override
    public void destroyClient() {
    }

    @Override
    public Object getClient() {
        return s3Client;
    }

    @Override
    public boolean doesBucketExist(String bucketName) {
        try {
            List<Bucket> buckets = s3Client.listBuckets();
            for (Bucket bucket : buckets) {
                if (Objects.equals(bucket.getName(), bucketName)) {
                    return true;
                }
            }
            return false;
        } catch (AmazonServiceException e) {
            throw new AwsFileServiceException(e);
        } catch (SdkClientException e) {
            throw new AwsFileServiceException(e);
        }
    }

    @Override
    public void setBucketAcl(String bucketName, BucketAuthEnum bucketAuthEnum) {

    }

    @Override
    public boolean isExistingFile(String bucketName, String key) {
        return false;
    }

    @Override
    public void storageFile(String bucketName, String key, byte[] bytes) {

    }

    @Override
    public void storageFile(String bucketName, String key, InputStream inputStream) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, inputStream, objectMetadata);
        s3Client.putObject(putObjectRequest);
    }

    @Override
    public byte[] getFileBytes(String bucketName, String key) {
        S3Object s3Object = getObject(bucketName, key);
        try {
            return IOUtils.toByteArray(s3Object.getObjectContent());
        } catch (IOException e) {
            throw new AwsFileServiceException(e.getMessage());
        }
    }

    @Override
    public void setFileAcl(String bucketName, String key, BucketAuthEnum bucketAuthEnum) {

    }

    @Override
    public void copyFile(String originBucketName, String originFileKey, String newBucketName, String newFileKey) {

    }

    @Override
    public String getFileAuthUrl(String bucketName, String key, Long timeoutMillis) {
        Date expiration = new Date();
        expiration.setTime(expiration.getTime() + 1000 * 60 * 60 * 24 * timeoutMillis);
        GeneratePresignedUrlRequest urlRequest = new GeneratePresignedUrlRequest(
                bucketName, key).withExpiration(expiration);
        URL url = s3Client.generatePresignedUrl(urlRequest);
        return url.toString();
    }

    @Override
    public void deleteFile(String bucketName, String key) {
        String fileName = key.substring(key.lastIndexOf("/") + 1);
        s3Client.deleteObject(new DeleteObjectRequest(bucketName, fileName));
    }

    @Override
    public void storageFile(String bucketName, String key, File file) {
        try {
            TransferManager tm = TransferManagerBuilder.standard()
                    .withS3Client(s3Client)
                    .build();

            // TransferManager processes all transfers asynchronously,
            // so this call returns immediately.
            Upload upload = tm.upload(bucketName, key, file);

            // Optionally, wait for the upload to finish before continuing.
            upload.waitForCompletion();
        } catch (AmazonServiceException e) {
            throw new AwsFileServiceException(e.getMessage());
        } catch (AmazonClientException e) {
            throw new AwsFileServiceException(e.getMessage());
        } catch (InterruptedException e) {
            throw new AwsFileServiceException(e.getMessage());
        }
    }

    /**
     * 获取文件
     *
     * @param bucketName bucket名称
     * @param objectName 文件名称
     * @return 二进制流
     * @see <a href= "http://docs.aws.amazon.com/goto/WebAPI/s3-2006-03-01/GetObject">AWS
     * API Documentation</a>
     */
    public S3Object getObject(String bucketName, String objectName) {
        return s3Client.getObject(new GetObjectRequest(bucketName, objectName));
    }
}
