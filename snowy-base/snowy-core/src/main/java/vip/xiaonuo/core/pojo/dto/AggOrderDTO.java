package vip.xiaonuo.core.pojo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.springframework.util.CollectionUtils;
import vip.xiaonuo.core.pojo.BigDecimalSerializer;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

public class AggOrderDTO implements Serializable, Comparable<AggOrderDTO> {

    private BigDecimal price;
    private BigDecimal quantity;
    private BigDecimal maxTotal;
    private BigDecimal minTotal;

    @JsonProperty("priceDecimal")
    private int priceDecimal = 8;

    @JsonProperty("quantityDecimal")
    private int quantityDecimal = 3;

    private BigDecimal sum;

    // getter/setter

    public void setPriceDecimal(int priceDecimal) {
        this.priceDecimal = priceDecimal;
    }

    public int getPriceDecimal() {
        return priceDecimal;
    }

    public void setQuantityDecimal(int quantityDecimal) {
        this.quantityDecimal = quantityDecimal;
    }

    public int getQuantityDecimal() {
        return quantityDecimal;
    }

    public void setMaxTotal(BigDecimal maxTotal) {
        this.maxTotal = maxTotal;
    }

    public BigDecimal getMaxTotal() {
        return maxTotal;
    }

    public void setMinTotal(BigDecimal minTotal) {
        this.minTotal = minTotal;
    }

    public BigDecimal getMinTotal() {
        return minTotal;
    }

    @JsonProperty("w")
    public BigDecimal getWidthPercent() {
        if (maxTotal == null || minTotal == null ||
                maxTotal.compareTo(BigDecimal.ZERO) == 0 ||
                minTotal.compareTo(BigDecimal.ZERO) == 0 ||
                maxTotal.compareTo(minTotal) == 0) {
            return BigDecimal.ZERO;
        }
        return (getQuantity().subtract(minTotal))
                .divide(maxTotal.subtract(minTotal), 2, RoundingMode.FLOOR)
                .setScale(2, RoundingMode.FLOOR);
    }

    @JsonSerialize(using = BigDecimalSerializer.class)
    public BigDecimal getTotal() {
        return price.multiply(quantity).setScale(priceDecimal, RoundingMode.HALF_UP);
    }

    @JsonSerialize(using = BigDecimalSerializer.class)
    public BigDecimal getPrice() {
        return price.setScale(priceDecimal, RoundingMode.HALF_UP);
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @JsonSerialize(using = BigDecimalSerializer.class)
    public BigDecimal getQuantity() {
        return quantity.setScale(quantityDecimal, RoundingMode.HALF_UP);
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @JsonSerialize(using = BigDecimalSerializer.class)
    public BigDecimal getSum() {
        return sum;
    }

    public void setSum(BigDecimal sum) {
        this.sum = sum;
    }

    public AggOrderDTO cloneByPriceScale(Integer priceScale) {
        AggOrderDTO orderDTO = new AggOrderDTO();
        orderDTO.setPriceDecimal(priceScale);
        orderDTO.setQuantityDecimal(this.quantityDecimal);
        orderDTO.setPrice(this.price.setScale(priceScale, RoundingMode.FLOOR));
        orderDTO.setQuantity(this.quantity);
        return orderDTO;
    }

    public static void calcMinMax(List<AggOrderDTO> orderDTOS) {
        BigDecimal maxTotal = BigDecimal.ZERO;
        BigDecimal minTotal = null;

        if (!CollectionUtils.isEmpty(orderDTOS)) {
            for (AggOrderDTO orderDTO : orderDTOS) {
                BigDecimal total = orderDTO.getQuantity();
                maxTotal = total.compareTo(maxTotal) > 0 ? total : maxTotal;
                if (minTotal == null) {
                    minTotal = total;
                    continue;
                }
                minTotal = total.compareTo(minTotal) < 0 ? total : minTotal;
            }
            for (AggOrderDTO orderDTO : orderDTOS) {
                orderDTO.setMaxTotal(maxTotal);
                orderDTO.setMinTotal(minTotal);
            }
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AggOrderDTO)) return false;
        AggOrderDTO that = (AggOrderDTO) o;
        return price.compareTo(that.price) == 0;
    }

    @Override
    public int hashCode() {
        return Objects.hash(price.doubleValue());
    }

    @Override
    public int compareTo(AggOrderDTO o) {
        return o.getPrice().compareTo(this.price);
    }
}

//package vip.xiaonuo.core.pojo.dto;
//
//import com.fasterxml.jackson.annotation.JsonIgnore;
//import com.fasterxml.jackson.annotation.JsonProperty;
//import com.fasterxml.jackson.databind.annotation.JsonSerialize;
//import org.springframework.util.CollectionUtils;
//import vip.xiaonuo.core.pojo.BigDecimalSerializer;
//
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.util.List;
//import java.util.Objects;
//
//public class AggOrderDTO implements Serializable, Comparable {
//    private BigDecimal price;
//    private BigDecimal quantity;
////    @JsonIgnore
//    private BigDecimal maxTotal;
////    @JsonIgnore
//    private BigDecimal minTotal;
////    @JsonIgnore
//    private int priceDecimal = 8;
////    @JsonIgnore
//    private int quantityDecimal = 3;
//    private BigDecimal sum;
//
//    public void setPriceDecimal(int priceDecimal) {
//        this.priceDecimal = priceDecimal;
//    }
//
//    public void setQuantityDecimal(int quantityDecimal) {
//        this.quantityDecimal = quantityDecimal;
//    }
//
//    public void setMaxTotal(BigDecimal maxTotal) {
//        this.maxTotal = maxTotal;
//    }
//
//    public void setMinTotal(BigDecimal minTotal) {
//        this.minTotal = minTotal;
//    }
//
//    @JsonProperty("w")
//    public BigDecimal getWidthPercent() {
//        if (maxTotal == null || minTotal == null || maxTotal.compareTo(BigDecimal.ZERO) == 0 || minTotal.compareTo(BigDecimal.ZERO) == 0) {
//            return BigDecimal.ZERO;
//        }
//        if (maxTotal.compareTo(minTotal) == 0) {
//            return BigDecimal.ZERO;
//        }
//        return (getQuantity().subtract(minTotal)).divide(maxTotal.subtract(minTotal), 2, RoundingMode.FLOOR)
//                .setScale(2, RoundingMode.FLOOR);
//    }
//
//    @JsonSerialize(using = BigDecimalSerializer.class)
//    public BigDecimal getTotal() {
//        return price.multiply(quantity).setScale(priceDecimal, RoundingMode.HALF_UP);
//    }
//
//    @JsonSerialize(using = BigDecimalSerializer.class)
//    public BigDecimal getPrice() {
//        return price.setScale(priceDecimal, RoundingMode.HALF_UP);
//    }
//
//    public void setPrice(BigDecimal price) {
//        this.price = price;
//    }
//
//    @JsonSerialize(using = BigDecimalSerializer.class)
//    public BigDecimal getQuantity() {
//        return quantity.setScale(quantityDecimal, RoundingMode.HALF_UP);
//    }
//
//    public void setQuantity(BigDecimal quantity) {
//        this.quantity = quantity;
//    }
//
//    @JsonSerialize(using = BigDecimalSerializer.class)
//    public BigDecimal getSum() {
//        return sum;
//    }
//
//    public void setSum(BigDecimal sum) {
//        this.sum = sum;
//    }
//
//    public AggOrderDTO cloneByPriceScale(Integer priceScale) {
//        AggOrderDTO orderDTO = new AggOrderDTO();
//        orderDTO.setPriceDecimal(priceScale);
//        orderDTO.setQuantityDecimal(quantityDecimal);
//        orderDTO.setQuantity(quantity);
//        orderDTO.setPrice(price.setScale(priceScale, RoundingMode.FLOOR));
//        return orderDTO;
//    }
//
//    public static void calcMinMax(List orderDTOS) {
//        BigDecimal maxTotal = BigDecimal.ZERO;
//        BigDecimal minTotal = null;
//
//        if (!CollectionUtils.isEmpty(orderDTOS)) {
//            for (Object obj : orderDTOS) {
//                AggOrderDTO orderDTO = (AggOrderDTO) obj;
//                BigDecimal total = orderDTO.getQuantity();
//                maxTotal = total.compareTo(maxTotal) > 0 ? total : maxTotal;
//                if (minTotal == null) {
//                    minTotal = total;
//                    continue;
//                }
//                minTotal = total.compareTo(minTotal) < 0 ? total : minTotal;
//            }
//            for (Object obj : orderDTOS) {
//                AggOrderDTO orderDTO = (AggOrderDTO) obj;
//                orderDTO.setMaxTotal(maxTotal);
//                orderDTO.setMinTotal(minTotal);
//            }
//        }
//    }
//
//    @Override
//    public boolean equals(Object o) {
//        if (this == o) return true;
//        if (o == null || getClass() != o.getClass()) return false;
//        AggOrderDTO orderDTO = (AggOrderDTO) o;
//        return price.compareTo(orderDTO.price) == 0;
//    }
//
//    @Override
//    public int hashCode() {
//        return Objects.hash(price.doubleValue());
//    }
//
//    @Override
//    public int compareTo(Object o) {
//        if (o instanceof AggOrderDTO) {
//            return ((AggOrderDTO) o).getPrice().compareTo(this.price);
//        }
//        return 0;
//    }
//}
