package vip.xiaonuo.core.pojo.dto;


import vip.xiaonuo.core.util.MathUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class BatchKLineDTO {

    private Integer id;
    private Date time;
    private String symbol;
    private BigDecimal open;
    private BigDecimal close;
    private BigDecimal high;
    private BigDecimal low;
    private BigDecimal vol;
    private BigDecimal obv;
    private BigDecimal avl;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public BigDecimal getOpen() {
        return open;
    }

    public void setOpen(BigDecimal open) {
        this.open = open;
    }

    public BigDecimal getClose() {
        return close;
    }

    public void setClose(BigDecimal close) {
        this.close = close;
    }

    public BigDecimal getHigh() {
        return high;
    }

    public void setHigh(BigDecimal high) {
        this.high = high;
    }

    public BigDecimal getLow() {
        return low;
    }

    public void setLow(BigDecimal low) {
        this.low = low;
    }

    public BigDecimal getVol() {
        return vol;
    }

    public void setVol(BigDecimal vol) {
        this.vol = vol;
    }

    public BigDecimal getObv() {
        return obv;
    }

    public void setObv(BigDecimal obv) {
        this.obv = obv;
    }

    public BigDecimal getAvl() {
        return avl;
    }

    public void setAvl(BigDecimal avl) {
        this.avl = avl;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public static BatchKLineDTO zero() {
        BatchKLineDTO kLineDTO = new BatchKLineDTO();
        kLineDTO.vol = BigDecimal.ZERO;
        kLineDTO.obv = BigDecimal.ZERO;
        kLineDTO.open = BigDecimal.ZERO;
        kLineDTO.close = BigDecimal.ZERO;
        kLineDTO.high = BigDecimal.ZERO;
        kLineDTO.low = BigDecimal.ZERO;
        kLineDTO.avl = BigDecimal.ZERO;
        return kLineDTO;
    }

    public static BatchKLineDTO copy(BatchKLineDTO batchKLineDTO) {
        BatchKLineDTO kLineDTO = new BatchKLineDTO();
        kLineDTO.vol = batchKLineDTO.vol;
        kLineDTO.obv = batchKLineDTO.obv;
        kLineDTO.open = batchKLineDTO.open;
        kLineDTO.close = batchKLineDTO.close;
        kLineDTO.high = batchKLineDTO.high;
        kLineDTO.low = batchKLineDTO.low;
        kLineDTO.avl = batchKLineDTO.avl;
        kLineDTO.time = batchKLineDTO.time;
        kLineDTO.symbol = batchKLineDTO.symbol;
        return kLineDTO;
    }

    private BigDecimal scale8(BigDecimal value) {
        value = value == null ? new BigDecimal(0) : value;
        return MathUtil.stripZeros(value.setScale(8, RoundingMode.FLOOR));
    }

    // todo gjn 这里设置的小数点
    public List<BigDecimal> toList() {
        return Arrays.asList(new BigDecimal(time.getTime()), scale8(open), scale8(high), scale8(low), scale8(close), scale8(vol));
    }

}
