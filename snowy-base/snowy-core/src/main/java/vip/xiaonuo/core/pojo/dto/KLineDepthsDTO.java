package vip.xiaonuo.core.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class KLineDepthsDTO implements Serializable {

    private static final long serialVersionUID = 5098251110447152755L;
    private List<BigDecimal[]> asks = new ArrayList<>();

    private List<BigDecimal[]> bids = new ArrayList<>();

    @JsonIgnore
    private BigDecimal buyOne = BigDecimal.ZERO;
    @JsonIgnore
    private BigDecimal sellOne = BigDecimal.ZERO;

    public BigDecimal getBuyOne() {
        return buyOne;
    }

    public void setBuyOne(BigDecimal buyOne) {
        this.buyOne = buyOne;
    }

    public BigDecimal getSellOne() {
        return sellOne;
    }

    public void setSellOne(BigDecimal sellOne) {
        this.sellOne = sellOne;
    }

    public List<BigDecimal[]> getAsks() {
        return asks;
    }

    public void setAsks(List<BigDecimal[]> asks) {
        this.asks = asks;
    }

    public List<BigDecimal[]> getBids() {
        return bids;
    }

    public void setBids(List<BigDecimal[]> bids) {
        this.bids = bids;
    }

}
