package vip.xiaonuo.core.util;

public class KafkaTopics {
    /**
     * DP加工完最新成交数据后，将最新成交记录转发到dp集群
     */
    public static final String DP_NEW_TRADE = "DP_NEW_TRADE";

    /**
     * DP加工完最新成交数据后，将K线转发到dp集群
     */
    public static final String DP_NEW_KLINE = "DP_NEW_KLINE";
    /**
     * DP重启成功后，通知撮合引擎推送全量委托订单数据
     */
    public static final String DP_REQUEST_ORDER_DATA = "DP_REQUEST_ORDER_DATA";

    public static final String DP_MARKET_TICKER = "DP_MARKET_TICKER";

}
