package vip.xiaonuo.core.pojo.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ContractOrderInfoDTO implements Serializable {
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 类型(1:做多;2:做空;)
     */
    private Integer type;
    /**
     * 杠杆
     */
    private Integer lever;
    /**
     * 下单数量
     */
    private BigDecimal quantity;
    //止损价
    private BigDecimal stopLoss;
    //止盈价
    private BigDecimal stopProfit;
    /**
     * 下单价格
     */
    private BigDecimal orderPrice;
    /**
     * 保证金
     */
    private BigDecimal earnestMoney;
    /**
     * 平仓价格
     */
    private BigDecimal closePrice;
    /**
     * 盈亏
     */
    private BigDecimal profit;

    /**
     * 0:委托,1:持仓,2:平仓,3:撤销委托
     */
    private Integer status;
}
