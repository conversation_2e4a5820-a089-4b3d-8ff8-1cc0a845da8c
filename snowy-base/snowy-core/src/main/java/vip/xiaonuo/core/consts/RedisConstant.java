package vip.xiaonuo.core.consts;

public interface RedisConstant {
    /*短信验证码redis相关*/
    String REDIS_SMS_VC_KEY = "p_sms_vc:";
    String REDIS_SMS_VCT_KEY = "p_sms_vct:";//验证码token
    String REDIS_SMS_PHONE_LIMIT_KEY = "p_sms_limit:";
    String REDIS_SMS_IP_LIMIT_KEY = "p_sms_ip_limit:";

    //现货订单
    String SPOT_ORDER = "spot_order:";
    //合约订单
    String CONTRACT_ORDER = "contract_order:";

    //现货玩家控制概率
    String PLAYER_SPOT_SCALE = "player_spot_scale:";
    //整个系统现货控制概率
    String SPOT_SCALE = "spot_scale_all:";
    //整个系统现货控制概率设置
    String SPOT_SCALE_SETTING = "spot_scale_all_setting";
    //整个系统现货控制概率设置
    String REDIS_CONTRACT_RULE_LOOPHOLE_CACHE = "redis_contract_rule_loophole_cache";
}
