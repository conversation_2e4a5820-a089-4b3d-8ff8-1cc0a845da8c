package vip.xiaonuo.core.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum LogWebOpTypeEnum {

    /**
     * 正常日志类型
     */
    NORMAL(0, "正常日志"),
    LOGIN_SUCCESS(1, "登录成功"),
    LOGIN_FAIL(2, "登录失败"),
    LOGOUT_SUCCESS(3, "登出成功"),
    /**
     * 错误日志类型
     */
    ERROR(4, "错误日志");

    /**
     * 类型
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String description;
}
