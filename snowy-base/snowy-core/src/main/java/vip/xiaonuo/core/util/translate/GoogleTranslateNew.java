package vip.xiaonuo.core.util.translate;

import vip.xiaonuo.core.util.translate.Translate.Word;
import com.alibaba.fastjson.JSONArray;
import okhttp3.*;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

public class GoogleTranslateNew extends Translate {
    public static String url = "https://translate.google.cn/_/TranslateWebserverUi/data/batchexecute?rpcids=MkEWBc&f.sid=3235945542298650989&bl=boq_translate-webserver_20210315.13_p0&hl=zh-CN&soc-app=1&soc-platform=1&soc-device=1&_reqid=968381&rt=c";

    private OkHttpClient okHttpClient = new OkHttpClient.Builder().build();

    private vip.xiaonuo.core.util.translate.Translate.Word getContent(String url, String q) throws IOException {
        return getContent(url, q, "en");
    }

    public Word getContent(String url, String q, String language) throws IOException {
        q = URLEncoder.encode(q, "UTF-8");
        FormBody.Builder builder = new FormBody.Builder();
        builder.addEncoded("f.req", "[[[\"MkEWBc\",\"[[\\\"" + q + "\\\",\\\"en\\\",\\\"" + language + "\\\",true],[null]]\",null,\"generic\"]]]");
        Request postRequest = new Request.Builder().post(builder.build()).url(url)
                .addHeader("Host", "translate.google.cn")
//                .addHeader("Connection","keep-alive")
//                .addHeader("Content-Length","167")
//                .addHeader("sec-ch-ua","\"Chromium\";v=\"88\", \"Google Chrome\";v=\"88\", \";Not A Brand\";v=\"99\"")
                .addHeader("X-Goog-BatchExecute-Bgr", "[\"!CAulC0fNAAUnQOwtJ0LUegp4akByETsAKQAjCPxGC7pL6Q4QXp3AF7Iu8M0yc2ICZDEzTHlqRg8cXgI4lAYCOCdkAgAAAH9SAAAAFGgBBwoAvIocaVd3nwOhuOxb_XOBwxVV6PKoTYgjbGf6ySDCIEuRKnHPj6QaIqDf7f2ZBAu8jHaBJo0WPClz5KSp1akF2hn5bR3NCf4zu3SMNJ3d7CfEs9sGvGsf2L3rdkiWr6q89TMjrHs-IhjhHiRTRnFwA7xG4MwWdtigDijhd8_-cWthy426tb_9Ujjn9nwqmhEUFa2u4yniiIZtqwKcU9--8vUr6E1uqu-EoV-Ln9ysQnn1BWCKnz62mAjj3bc-mQHmcRwK7q544HNSPlvhrmGAnAENFvDwWAF7n-ORZuicPfzxCEGxaF8zDZnpjmB0hXPjVk_TKd8Af_It3mCWs-r0lut107bgPd4eeX8V98PBswyD62lps-ctAQGFYF-0-hHLW4gEllhoPHTjDBuAKJmkGGT8w7Vvlpi2perDMO0N_iNbrmyIfntSwNNQUMHStXN3LusPzEkM_sHge48HreylSl9QRvE4AUJxMiTsF1_pfBG7DFttufscfhby3rurUeR87yIITb262oGkePRRjb7oryKyGbIor1f0EqWJcDxLGor0h97rx86G5SROF-lyy8YSxyWV1mYBalzgGW0xXYHk3wYH7kchNHSjSHE-fr7HwH8dtx7DXXSW4t2JbZO8G6ZLr2EZYDm4h4zrgbCqMijrfHNirORpB7df9tD0ccQA4_BPlIRoKk_uDtJTnfZp3JlTHAY8ToYgBaCJieGva81u9zXMP3aVipPc9bfWT74a5xw0BNuwk_pf67ufDkbztn1rjEoMzLAhaP4PcYhkXIm9K8Dz8YUf0xWkbgurCb9gQWgcMQzkYtKZPEXKnT6YYRAqr16Cx5u0faO5w-q8MbtCR81pPO1rE2YLbS-Hx8NbR3QZQF8KpbLbbFAh827kdHLqeWkuJjSz\",null,null,23,null,null,null,0]")
                .addHeader("X-Same-Domain", "1")
//                .addHeader("sec-ch-ua-mobile","?0")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.104 Safari/537.36")
                .addHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
//                .addHeader("Accept","*/*")
                .addHeader("Origin", "https://translate.google.cn")
                .addHeader("X-Client-Data", "CIm2yQEIprbJAQjDtskBCKmdygEIhsLKAQj4x8oBCKPNygEI3NXKAQik/soBCNWcywEI45zLAQioncsB")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Referer", "https://translate.google.cn/")
//                .addHeader("Accept-Encoding","gzip, deflate, br")
//                .addHeader("Accept-Language","zh-CN,zh;q=0.9,en;q=0.8")
                .addHeader("Cookie","NID=210=SnSK6KnpBmzLcEWwnpEKnQHnRNo1rsxJKcgTO--BR9qfqzc4WIORK0WooY-n-FdOmZ2TIRwutYlCIsdkXvplTolA-vlgGY9iNff9u1t4Y2PI1RJHvDMnjqXkH8OyoxvO4pZHunHkShuW_C6AfpZptMEnjRvujC1NRqOg2Wszx58; _ga=GA1.3.431643496.1614672166; OTZ=5872803_24_24__24_; _gid=GA1.3.349191396.1615978761")
//                .addHeader("","")
//                .addHeader("","")

                .build();
        Call call = okHttpClient.newCall(postRequest);
        Response response = call.execute();
        Word word = null;
        try {
//        System.out.println(response.code());
//        System.out.println(response.message());
            if (response.isSuccessful()) {
                String result = response.body().string();
//            System.out.println(result);
//            System.out.println("=====================");
                int fi = result.indexOf("[");
                String jsonStr = result.substring(fi);
                int ei = getIndex(jsonStr, 0);
                jsonStr = jsonStr.substring(0, ei + 1);
//            System.out.println(jsonStr);
//            System.out.println("======================2");
                JSONArray jsonArray = JSONArray.parseArray(jsonStr);
                String real = jsonArray.getJSONArray(0).getString(2);
//            System.out.println(real);
                JSONArray realJson = JSONArray.parseArray(real);
//            System.out.println("获取值");
                String sourceCode = realJson.getString(2);
                JSONArray valueArr = realJson.getJSONArray(1).getJSONArray(0).getJSONArray(0).getJSONArray(5);
                StringJoiner valueBuf = new StringJoiner(" ");
                for (int j = 0; j < valueArr.size(); j++) {
                    JSONArray sub = valueArr.getJSONArray(j);
                    if (sub == null) {
                        continue;
                    }
                    valueBuf.add(sub.getString(0));
                }
                if (!valueBuf.toString().isEmpty()) {
                    word = new Word(sourceCode, valueBuf.toString());
                }
            } else {
                logger.error("谷歌翻译响应错误(" + response.code() + ")" + response.message() + "[" + language + "]" + q);
            }
        } finally {
            response.close();
        }
        return word;
    }

    private int getIndex(String result, int fromIndex) {
        int i = result.indexOf("]\n", fromIndex);
        if (i != -1) {
            String f = result.substring(i + 2, i + 3);
            if (NumberUtils.isDigits(f)) {
                return i;
            }
            return getIndex(result, i + 1);
        }
        return -1;
    }


    public Word execute(String text, String to) throws IOException {
        return getContent(url, text);
    }

    public static void main(String[] args) throws IOException {
        Word word = new GoogleTranslateNew().getContent(url, "Germany", "vi");
        System.out.println(word.getTargetText());
        System.out.println(word.getSourceCode());
    }

    public static void main1(String[] args) throws Exception {
        Set<String> phoneList = new HashSet<>();
        getLang().forEach(t -> {
            try {
                Word word = new GoogleTranslateNew().getContent(url, "phone", t);
                phoneList.add(word.getTargetText());
                word = new GoogleTranslateNew().getContent(url, "telefon", t);
                phoneList.add(word.getTargetText());
                word = new GoogleTranslateNew().getContent(url, "tel", t);
                phoneList.add(word.getTargetText());
                word = new GoogleTranslateNew().getContent(url, "Mob", t);
                phoneList.add(word.getTargetText());
            } catch (IOException e) {
                System.out.println("错误" + t);
                e.printStackTrace();
            }
        });

        phoneList.forEach(System.out::println);

        // Telefon
//        System.out.println(new GoogleTranslateNew().execute("哈皮", ""));
//        System.out.println(new GoogleTranslateNew().getContent(url, "phone", "ZH"));
    }

    public static List<String> getLang() {
        List<String> list = new ArrayList<>();
        list.add("zh-CN");
        list.add("en");
        list.add("sq");
        list.add("ar");
        list.add("am");
        list.add("az");
        list.add("ga");
        list.add("et");
        list.add("or");
        list.add("eu");
        list.add("be");
        list.add("bg");
        list.add("is");
        list.add("pl");
        list.add("bs");
        list.add("fa");
        list.add("af");
        list.add("tt");
        list.add("da");
        list.add("de");
        list.add("ru");
        list.add("fr");
        list.add("tl");
        list.add("fi");
        list.add("fy");
        list.add("km");
        list.add("ka");
        list.add("gu");
        list.add("kk");
        list.add("ht");
        list.add("ko");
        list.add("ha");
        list.add("nl");
        list.add("ky");
        list.add("gl");
        list.add("ca");
        list.add("cs");
        list.add("kn");
        list.add("co");
        list.add("hr");
        list.add("ku");
        list.add("la");
        list.add("lv");
        list.add("lo");
        list.add("lt");
        list.add("lb");
        list.add("rw");
        list.add("ro");
        list.add("mg");
        list.add("mt");
        list.add("mr");
        list.add("ml");
        list.add("ms");
        list.add("mk");
        list.add("mi");
        list.add("mn");
        list.add("bn");
        list.add("my");
        list.add("hmn");
        list.add("xh");
        list.add("zu");
        list.add("ne");
        list.add("no");
        list.add("pa");
        list.add("pt");
        list.add("ps");
        list.add("ny");
        list.add("ja");
        list.add("sv");
        list.add("sm");
        list.add("sr");
        list.add("st");
        list.add("si");
        list.add("eo");
        list.add("sk");
        list.add("sl");
        list.add("sw");
        list.add("gd");
        list.add("ceb");
        list.add("so");
        list.add("tg");
        list.add("te");
        list.add("ta");
        list.add("th");
        list.add("tr");
        list.add("tk");
        list.add("cy");
        list.add("ug");
        list.add("ur");
        list.add("uk");
        list.add("uz");
        list.add("es");
        list.add("iw");
        list.add("el");
        list.add("haw");
        list.add("sd");
        list.add("hu");
        list.add("sn");
        list.add("hy");
        list.add("ig");
        list.add("it");
        list.add("yi");
        list.add("hi");
        list.add("su");
        list.add("id");
        list.add("jw");
        list.add("en");
        list.add("yo");
        list.add("vi");
        list.add("zh-TW");
        list.add("zh-CN");
        return list;
    }
}