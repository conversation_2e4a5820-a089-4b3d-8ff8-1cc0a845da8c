package vip.xiaonuo.core.util;

import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

@Component
public class Locker {

    private ConcurrentHashMap<String, ReentrantLock> lockHashMap = new ConcurrentHashMap<>();

    public ReentrantLock getLock(String key) {
        ReentrantLock reentrantLock = lockHashMap.get(key);
        if (reentrantLock == null) {
            reentrantLock = new ReentrantLock();
            if (lockHashMap.putIfAbsent(key, reentrantLock) == null) {
                return reentrantLock;
            } else {
                return lockHashMap.get(key);
            }
        }
        return reentrantLock;
    }


}
