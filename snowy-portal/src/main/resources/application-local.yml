# Mysql数据库
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************************************
    username: root
    password: Gjn7758258@
  #redis 配置
  redis:
    database: 0
    host: 127.0.0.1
    lettuce:
      pool:
        max-active: 8   #最大连接数据库连接数,设 0 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
    #password: RXc4YmGNVox3pwqaWJrN
    port: 6379
  kafka:
    producer:
      bootstrap-servers: *************:9092
    consumer:
      bootstrap-servers: *************:9092
    template:
      default-topic: dev

mybatis-plus:
  configuration:
    #不打印sql
#    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

telegram-ad:
  enable: true
  api-domain: http://127.0.0.1:8001/api
  code: coin_yindu01
  name: 币印度盘

# xxl-job
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:8088/tt2025
    accessToken: bH5UWr5IstsWNZjekzbZ7PnMm15w5miJ
    executor:
      appname: portal-job-executor
      ip:
      port: 6666
      logpath: /Users/<USER>/www/java/xxl-job-2.5.0/logs
      logretentiondays: 30
