#\u767B\u5F55\u6CE8\u518C
player.login.nologin=l\u00E0m \u01A1n \u0111\u0103ng nh\u1EADp
register.mobile.already.exists=S\u1ED1 \u0111i\u1EC7n tho\u1EA1i di \u0111\u1ED9ng \u0111\u00E3 \u0111\u01B0\u1EE3c \u0111\u0103ng k\u00FD
register.password.incorrect=\u0111\u1ECBnh d\u1EA1ng m\u1EADt kh\u1EA9u sai
register.invitation.invalid=M\u00E3 l\u1EDDi m\u1EDDi kh\u00F4ng h\u1EE3p l\u1EC7
login.password.incorrect=T\u00EAn ng\u01B0\u1EDDi d\u00F9ng ho\u1EB7c m\u1EADt kh\u1EA9u kh\u00F4ng h\u1EE3p l\u1EC7
account.freeze.error=T\u00E0i kho\u1EA3n \u0111\u00E3 b\u1ECB \u0111\u00F3ng b\u0103ng, vui l\u00F2ng li\u00EAn h\u1EC7 v\u1EDBi qu\u1EA3n tr\u1ECB vi\u00EAn
account.is.forbid.trade=T\u00E0i kho\u1EA3n \u0111\u00E3 b\u1ECB c\u1EA5m giao d\u1ECBch
account.is.exist=T\u00E0i kho\u1EA3n \u0111\u00E3 t\u1ED3n t\u1EA1i
account.is.not.null=T\u00E0i kho\u1EA3n kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng
account.pattern.not.correct=Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n c\u00F3 \u00EDt nh\u1EA5t 6 k\u00FD t\u1EF1 ch\u1EEF v\u00E0 s\u1ED1

#profile
profile.old.password.empty=M\u1EADt kh\u1EA9u c\u0169 kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng
profile.old.password.error=m\u1EADt kh\u1EA9u c\u0169 sai
profile.funds.password.incorrect=\u0110\u1ECBnh d\u1EA1ng c\u1EE7a m\u1EADt kh\u1EA9u qu\u1EF9 kh\u00F4ng ch\u00EDnh x\u00E1c

#SMS\u77ED\u4FE1
sms.enter.registered.phone=Vui l\u00F2ng nh\u1EADp s\u1ED1 \u0111i\u1EC7n tho\u1EA1i di \u0111\u1ED9ng \u0111\u00E3 \u0111\u0103ng k\u00FD c\u1EE7a b\u1EA1n
sms.format.number.phone=\u0110\u1ECBnh d\u1EA1ng sai c\u1EE7a s\u1ED1 \u0111i\u1EC7n tho\u1EA1i
sms.send.too.frequently=G\u1EEDi tin nh\u1EAFn v\u0103n b\u1EA3n qu\u00E1 th\u01B0\u1EDDng xuy\u00EAn
sms.code.error=L\u1ED7i m\u00E3 x\u00E1c minh SMS

error=Tham s\u1ED1 sai

#\u8D39\u7387
rate.company.is.null.error=Chuy\u1EC3n \u0111\u1ED5i \u0111\u01A1n v\u1ECB hi\u1EC7n t\u1EA1i kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng
rate.account.is.null.error=S\u1ED1 ti\u1EC1n sai
rate.fee.is.null.error=T\u1EF7 l\u1EC7 sai
# \u8D22\u52A1\u5145\u503C
transaction.fee.error = C\u00F3 l\u1ED7i trong ph\u00ED c\u1EA5u h\u00ECnh, vui l\u00F2ng li\u00EAn h\u1EC7 v\u1EDBi qu\u1EA3n tr\u1ECB vi\u00EAn n\u1EC1n
transaction.withdrawal..account.fee.error=S\u1ED1 ti\u1EC1n r\u00FAt ph\u1EA3i l\u1EDBn h\u01A1n 0
transaction.withdrawal.minWithdrawAmount=\u00CDt h\u01A1n s\u1ED1 ti\u1EC1n r\u00FAt t\u1ED1i thi\u1EC3u
transaction.withdrawal.withdrawLimit=Nhi\u1EC1u h\u01A1n s\u1ED1 ti\u1EC1n r\u00FAt t\u1ED1i \u0111a
transaction.recharge..account.fee.error=S\u1ED1 ti\u1EC1n n\u1EA1p ph\u1EA3i l\u1EDBn h\u01A1n 0
transaction.recharge..account.fee.error2=S\u1ED1 ti\u1EC1n n\u1EA1p ph\u1EA3i l\u1EDBn h\u01A1n
transaction.recharge..in.error=Lo\u1EA1i chuy\u1EC3n kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng
transaction.recharge..out.error=Lo\u1EA1i chuy\u1EC3n kh\u00F4ng \u0111\u01B0\u1EE3c r\u1ED7ng
transaction.recharge..money.type.error=\u0110\u01A1n v\u1ECB n\u1EA1p ti\u1EC1n sai
transaction.recharge..upay.error=N\u1EA1p ti\u1EC1n b\u1EA5t th\u01B0\u1EDDng, vui l\u00F2ng li\u00EAn h\u1EC7 v\u1EDBi d\u1ECBch v\u1EE5 kh\u00E1ch h\u00E0ng
ransaction.withdraw..error=Hi\u1EC7n t\u1EA1i b\u1EA1n ch\u01B0a n\u1EA1p ti\u1EC1n v\u00E0 kh\u00F4ng th\u1EC3 r\u00FAt ti\u1EC1n m\u1EB7t. Vui l\u00F2ng li\u00EAn h\u1EC7 b\u1ED9 ph\u1EADn ch\u0103m s\u00F3c kh\u00E1ch h\u00E0ng
#\u7528\u6237\u8BA2\u5355
user.order.product.id.error=Xin l\u1ED7i qu\u00FD kh\u00E1ch vui l\u00F2ng ch\u1ECDn mua s\u1EA3n ph\u1EA9m
user.order.chennel.error=Xin l\u1ED7i, vui l\u00F2ng \u0111i\u1EC1n v\u00E0o k\u00EAnh mua h\u00E0ng
user.order.account.error=Xin l\u1ED7i, s\u1ED1 ti\u1EC1n mua kh\u00F4ng ch\u00EDnh x\u00E1c
user.order.num.null.error=Xin l\u1ED7i, s\u1ED1 th\u1EE9 t\u1EF1 tr\u1ED1ng
user.order.date.end.error=Xin l\u1ED7i, vi\u1EC7c \u0111\u1ED5i qu\u00E0 b\u1ECB c\u1EA5m v\u00E0o th\u1EDDi \u0111i\u1EC3m hi\u1EC7n t\u1EA1i
user.order.is.error=Xin l\u1ED7i, \u0111\u01A1n \u0111\u1EB7t h\u00E0ng t\u00E0i ch\u00EDnh kh\u00F4ng t\u1ED3n t\u1EA1i
user.order.status.ing.error=Xin l\u1ED7i, \u0111\u01A1n \u0111\u1EB7t h\u00E0ng t\u00E0i ch\u00EDnh kh\u00F4ng \u0111\u01B0\u1EE3c th\u1EF1c hi\u1EC7n v\u00E0 kh\u00F4ng th\u1EC3 \u0111\u1ED5i \u0111\u01B0\u1EE3c

#\u4EA7\u54C1
product.is.null.error =Xin l\u1ED7i, s\u1EA3n ph\u1EA9m \u0111\u00E3 mua kh\u00F4ng t\u1ED3n t\u1EA1i
product.status.end.error =Xin l\u1ED7i, s\u1EA3n ph\u1EA9m n\u00E0y \u0111\u00E3 d\u1EEBng s\u1EA3n xu\u1EA5t
product.account.min.error =Xin l\u1ED7i, s\u1ED1 ti\u1EC1n mua \u00EDt h\u01A1n s\u1ED1 ti\u1EC1n \u0111\u0103ng k\u00FD t\u1ED1i thi\u1EC3u
product.account.max.error =Xin l\u1ED7i, s\u1ED1 ti\u1EC1n mua l\u1EDBn h\u01A1n s\u1ED1 ti\u1EC1n \u0111\u0103ng k\u00FD t\u1ED1i \u0111a
user.is.null.error = Ng\u01B0\u1EDDi d\u00F9ng kh\u00F4ng t\u1ED3n t\u1EA1i
user.is.auth.error = Th\u00F4ng tin d\u1EEF li\u1EC7u ch\u01B0a \u0111\u01B0\u1EE3c x\u00E1c th\u1EF1c

#\u6587\u4EF6
file.upload.error = T\u1EA3i l\u00EAn t\u1EC7p kh\u00F4ng th\u00E0nh c\u00F4ng
file.upload.type.error = Lo\u1EA1i t\u1EADp tin kh\u00F4ng \u0111\u01B0\u1EE3c h\u1ED7 tr\u1EE3

#\u94F6\u884C\u8D26\u6237
player.not.exist = Ng\u01B0\u1EDDi d\u00F9ng kh\u00F4ng t\u1ED3n t\u1EA1i
player.fund.password.incorrect = M\u1EADt kh\u1EA9u qu\u1EF9 kh\u00F4ng ch\u00EDnh x\u00E1c

#\u5408\u7EA6\u8BA2\u5355
contract.order.entrust.type1=\u0110\u00F2n b\u1EA9y
contract.order.entrust.type2=Gi\u1EDBi h\u1EA1n

#\u8D26\u53D8
account.bill.business.type.DEPOSIT=\u0110\u1EB7t c\u1ECDc
account.bill.business.type.WITHDRAW=R\u00FAt ti\u1EC1n
account.bill.business.type.WITHDRAW_CANCEL=H\u1EE7y r\u00FAt ti\u1EC1n
account.bill.business.type.TRANSFER=Chuy\u1EC3n kho\u1EA3n
account.bill.business.type.CONVERT=Trao \u0111\u1ED5i
account.bill.business.type.COIN_POOL_ORDER=L\u1EC7nh Pool ti\u1EC1n xu
account.bill.business.type.REDEMPTION=Quy \u0111\u1ED5i nh\u00F3m ti\u1EC1n xu
account.bill.business.type.COIN_POOL_PROFIT=Doanh thu g\u1ED9p ti\u1EC1n xu
account.bill.business.type.SPOT_ORDER=X\u00E2y d\u1EF1ng t\u1EA1i ch\u1ED7
account.bill.business.type.SPOT_WIN=Giao d\u1ECBch giao ngay
account.bill.business.type.CONTRACT_ORDER=M\u1EDF h\u1EE3p \u0111\u1ED3ng
account.bill.business.type.REVOKE_ENTRUST=Thu h\u1ED3i ph\u00E1i \u0111o\u00E0n
account.bill.business.type.END_CONTRACT_ORDER=H\u1EE3p \u0111\u1ED3ng \u0111\u00F3ng
account.bill.business.type.SPOT_CONTRACT_ORDER=M\u1EDF h\u1EE3p \u0111\u1ED3ng

#\u679A\u4E3E
2028006=Ch\u1ED1t l\u1EDDi v\u00E0 l\u1ED7i tham s\u1ED1 c\u1EAFt l\u1ED7
1011007=T\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n \u0111\u00E3 b\u1ECB c\u1EA5m, vui l\u00F2ng li\u00EAn h\u1EC7 v\u1EDBi b\u1ED9 ph\u1EADn ch\u0103m s\u00F3c kh\u00E1ch h\u00E0ng
2027002=Thi\u1EBFu c\u00E2n b\u1EB1ng
2027003=S\u1ED1 d\u01B0 c\u1ED1 \u0111\u1ECBnh kh\u00F4ng \u0111\u1EE7
1016002=M\u00E1y ch\u1EE7 kh\u00F4ng b\u00ECnh th\u01B0\u1EDDng, vui l\u00F2ng li\u00EAn h\u1EC7 b\u1ED9 ph\u1EADn ch\u0103m s\u00F3c kh\u00E1ch h\u00E0ng
2018002=S\u1ED1 l\u01B0\u1EE3ng kh\u00F4ng \u0111\u00FAng

withdraw.auth.check.error=Vui l\u00F2ng ho\u00E0n th\u00E0nh ch\u1EE9ng ch\u1EC9 n\u00E2ng cao tr\u01B0\u1EDBc
spot.order.not.settle=B\u1EA1n c\u00F3 \u0111\u01A1n h\u00E0ng ch\u01B0a \u0111\u01B0\u1EE3c gi\u1EA3i quy\u1EBFt, vui l\u00F2ng \u0111\u1EE3i

order.num.lt.min=S\u1ED1 l\u01B0\u1EE3ng \u0111\u1EB7t h\u00E0ng nh\u1ECF h\u01A1n s\u1ED1 l\u01B0\u1EE3ng t\u1ED1i thi\u1EC3u
order.num.gt.max=S\u1ED1 l\u01B0\u1EE3ng \u0111\u1EB7t h\u00E0ng l\u1EDBn h\u01A1n s\u1ED1 l\u01B0\u1EE3ng t\u1ED1i \u0111a
balance.not.allow=B\u1EA1n ch\u1EC9 c\u00F3 th\u1EC3 \u0111\u1EB7t l\u1EC7nh khi ph\u1EA1m vi s\u1ED1 d\u01B0 l\u00E0 
balance.min.amount=S\u1ED1 d\u01B0 \u00EDt nh\u1EA5t l\u00E0
