1011007=ÐÐ°Ñ Ð°ÐºÐºÐ°ÑÐ½Ñ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½, Ð¾Ð±ÑÐ°ÑÐ¸ÑÐµÑÑ Ð² ÑÐ»ÑÐ¶Ð±Ñ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶ÐºÐ¸ ÐºÐ»Ð¸ÐµÐ½ÑÐ¾Ð².
1016002=Ð¡ÐµÑÐ²ÐµÑ Ð½ÐµÐ¸ÑÐ¿ÑÐ°Ð²ÐµÐ½, Ð¾Ð±ÑÐ°ÑÐ¸ÑÐµÑÑ Ð² ÑÐ»ÑÐ¶Ð±Ñ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶ÐºÐ¸ ÐºÐ»Ð¸ÐµÐ½ÑÐ¾Ð².
2027002=ÐÐµÐ´Ð¾ÑÑÐ°ÑÐ¾ÑÐ½ÑÐ¹ Ð±Ð°Ð»Ð°Ð½Ñ
2027003=ÐÐµÐ´Ð¾ÑÑÐ°ÑÐ¾ÑÐ½ÑÐ¹ Ð·Ð°Ð¼Ð¾ÑÐ¾Ð¶ÐµÐ½Ð½ÑÐ¹ Ð±Ð°Ð»Ð°Ð½Ñ
2028006=ÐÐ°ÑÐ°Ð¼ÐµÑÑÑ ÑÐµÐ¹Ðº-Ð¿ÑÐ¾ÑÐ¸ÑÐ° Ð¸ ÑÑÐ¾Ð¿-Ð»Ð¾ÑÑÐ° Ð½ÐµÐ²ÐµÑÐ½Ñ.
player.login.nologin=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð²Ð¾Ð¹Ð´Ð¸ÑÐµ Ð² ÑÐ¸ÑÑÐµÐ¼Ñ
register.mobile.already.exists=ÐÐ¾Ð±Ð¸Ð»ÑÐ½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ Ð·Ð°ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð½
register.password.incorrect=ÐÑÐ¸Ð±ÐºÐ° ÑÐ¾ÑÐ¼Ð°ÑÐ° Ð¿Ð°ÑÐ¾Ð»Ñ
register.invitation.invalid=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ ÐºÐ¾Ð´ Ð¿ÑÐ¸Ð³Ð»Ð°ÑÐµÐ½Ð¸Ñ
login.password.incorrect=ÐÐµÐ²ÐµÑÐ½Ð°Ñ ÑÑÐµÑÐ½Ð°Ñ Ð·Ð°Ð¿Ð¸ÑÑ Ð¸Ð»Ð¸ Ð¿Ð°ÑÐ¾Ð»Ñ
account.freeze.error=ÐÐºÐºÐ°ÑÐ½Ñ Ð·Ð°Ð¼Ð¾ÑÐ¾Ð¶ÐµÐ½, Ð¾Ð±ÑÐ°ÑÐ¸ÑÐµÑÑ Ðº Ð°Ð´Ð¼Ð¸Ð½Ð¸ÑÑÑÐ°ÑÐ¾ÑÑ
account.is.forbid.trade=Ð­ÑÐ¾Ñ Ð°ÐºÐºÐ°ÑÐ½Ñ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½ Ð´Ð»Ñ ÑÐ¾ÑÐ³Ð¾Ð²Ð»Ð¸
account.is.exist=ÐÐºÐºÐ°ÑÐ½Ñ ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
account.is.not.null=ÐÐºÐºÐ°ÑÐ½Ñ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
account.pattern.not.correct=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð²Ð²ÐµÐ´Ð¸ÑÐµ Ð½Ð¾Ð¼ÐµÑ ÑÑÐµÑÐ°, ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÐ¸Ð¹ Ð½Ðµ Ð¼ÐµÐ½ÐµÐµ 6 Ð±ÑÐºÐ²ÐµÐ½Ð½Ð¾-ÑÐ¸ÑÑÐ¾Ð²ÑÑ ÑÐ¸Ð¼Ð²Ð¾Ð»Ð¾Ð².
profile.old.password.empty=Ð¡ÑÐ°ÑÑÐ¹ Ð¿Ð°ÑÐ¾Ð»Ñ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
profile.old.password.error=Ð¡ÑÐ°ÑÑÐ¹ Ð¿Ð°ÑÐ¾Ð»Ñ Ð½ÐµÐ²ÐµÑÐµÐ½
profile.funds.password.incorrect=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ Ð¿Ð°ÑÐ¾Ð»Ñ ÑÐ¾Ð½Ð´Ð°
sms.enter.registered.phone=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð²Ð²ÐµÐ´Ð¸ÑÐµ Ð·Ð°ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ Ð¼Ð¾Ð±Ð¸Ð»ÑÐ½Ð¾Ð³Ð¾ ÑÐµÐ»ÐµÑÐ¾Ð½Ð°
sms.format.number.phone=ÐÑÐ¸Ð±ÐºÐ° ÑÐ¾ÑÐ¼Ð°ÑÐ° Ð½Ð¾Ð¼ÐµÑÐ° Ð¼Ð¾Ð±Ð¸Ð»ÑÐ½Ð¾Ð³Ð¾ ÑÐµÐ»ÐµÑÐ¾Ð½Ð°
sms.send.too.frequently=ÐÑÐ¿ÑÐ°Ð²ÐºÐ° ÑÐµÐºÑÑÐ¾Ð²ÑÑ ÑÐ¾Ð¾Ð±ÑÐµÐ½Ð¸Ð¹ ÑÐ»Ð¸ÑÐºÐ¾Ð¼ ÑÐ°ÑÑÐ¾
sms.code.error=ÐÑÐ¸Ð±ÐºÐ° ÐºÐ¾Ð´Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ Ð¿Ð¾ Ð¡ÐÐ¡
error=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½ÑÐµ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÑ
rate.company.is.null.error=ÐÐ¾Ð½Ð²ÐµÑÑÐ¸ÑÐ¾Ð²Ð°ÑÑ ÑÐµÐºÑÑÑÑ ÐµÐ´Ð¸Ð½Ð¸ÑÑ Ð¸Ð·Ð¼ÐµÑÐµÐ½Ð¸Ñ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼.
rate.account.is.null.error=ÐÐµÐ²ÐµÑÐ½Ð°Ñ ÑÑÐ¼Ð¼Ð°
rate.fee.is.null.error=ÐÑÐ¸Ð±ÐºÐ° Ð¾ÑÐµÐ½ÐºÐ¸
transaction.fee.error=ÐÐ»Ð°ÑÐ° Ð·Ð° ÐºÐ¾Ð½ÑÐ¸Ð³ÑÑÐ°ÑÐ¸Ñ ÑÐºÐ°Ð·Ð°Ð½Ð° Ð½ÐµÐ²ÐµÑÐ½Ð¾. ÐÐ±ÑÐ°ÑÐ¸ÑÐµÑÑ Ðº Ð°Ð´Ð¼Ð¸Ð½Ð¸ÑÑÑÐ°ÑÐ¾ÑÑ ÑÐµÑÐ²ÐµÑÐ½Ð¾Ð¹ ÑÐ°ÑÑÐ¸.
transaction.withdrawal..account.fee.error=Ð¡ÑÐ¼Ð¼Ð° Ð²ÑÐ²Ð¾Ð´Ð° Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð±Ð¾Ð»ÑÑÐµ 0
transaction.withdrawal.minWithdrawAmount=ÐÐµÐ½ÑÑÐµ Ð¼Ð¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð¾Ð¹ ÑÑÐ¼Ð¼Ñ Ð²ÑÐ²Ð¾Ð´Ð°
transaction.withdrawal.withdrawLimit=Ð¡ÑÐ¼Ð¼Ð° Ð²ÑÐ²Ð¾Ð´Ð° Ð¿ÑÐµÐ²ÑÑÐ°ÐµÑ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½ÑÑ ÑÑÐ¼Ð¼Ñ Ð²ÑÐ²Ð¾Ð´Ð°
transaction.recharge..account.fee.error=Ð¡ÑÐ¼Ð¼Ð° Ð¿Ð¾Ð¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð±Ð¾Ð»ÑÑÐµ 0
transaction.recharge..account.fee.error2=ÐÐ¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð¿Ð¾Ð¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ ÑÐ¾ÑÑÐ°Ð²Ð»ÑÐµÑ
transaction.recharge..in.error=Ð¢Ð¸Ð¿ Ð¿ÐµÑÐµÐ²Ð¾Ð´Ð° Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼.
transaction.recharge..out.error=Ð¢Ð¸Ð¿ Ð¿ÐµÑÐµÐ²Ð¾Ð´Ð° Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼.
transaction.recharge..money.type.error=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½ÑÐ¹ Ð±Ð»Ð¾Ðº Ð¿ÐµÑÐµÐ·Ð°ÑÑÐ´ÐºÐ¸
transaction.recharge..upay.error=ÐÐµÐ¸ÑÐ¿ÑÐ°Ð²Ð½Ð¾ÑÑÑ Ð¿ÑÐ¸ Ð¿ÐµÑÐµÐ·Ð°ÑÑÐ´ÐºÐµ, Ð¾Ð±ÑÐ°ÑÐ¸ÑÐµÑÑ Ð² ÑÐ»ÑÐ¶Ð±Ñ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶ÐºÐ¸ ÐºÐ»Ð¸ÐµÐ½ÑÐ¾Ð².
ransaction.withdraw..error=ÐÑ ÐµÑÐµ Ð½Ðµ Ð¿Ð¾Ð¿Ð¾Ð»Ð½Ð¸Ð»Ð¸ ÑÑÐµÑ Ð¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ½ÑÑÑ Ð½Ð°Ð»Ð¸ÑÐ½ÑÐµ. 
user.order.product.id.error=ÐÐ·Ð²Ð¸Ð½Ð¸ÑÐµ, Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð²ÑÐ±ÐµÑÐ¸ÑÐµ Ð¿Ð¾ÐºÑÐ¿ÐºÑ Ð¿ÑÐ¾Ð´ÑÐºÑÐ°
user.order.chennel.error=ÐÐ·Ð²Ð¸Ð½Ð¸ÑÐµ, Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð·Ð°Ð¿Ð¾Ð»Ð½Ð¸ÑÐµ ÐºÐ°Ð½Ð°Ð» Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸
user.order.account.error=ÐÐ·Ð²Ð¸Ð½Ð¸ÑÐµ, ÑÑÐ¼Ð¼Ð° Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸ ÑÐºÐ°Ð·Ð°Ð½Ð° Ð½ÐµÐ²ÐµÑÐ½Ð¾.
user.order.num.null.error=ÐÐ·Ð²Ð¸Ð½Ð¸ÑÐµ, Ð½Ð¾Ð¼ÐµÑ Ð·Ð°ÐºÐ°Ð·Ð° Ð¿ÑÑÑ.
user.order.date.end.error=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð² Ð½Ð°ÑÑÐ¾ÑÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¿Ð¾Ð³Ð°ÑÐµÐ½Ð¸Ðµ Ð·Ð°Ð¿ÑÐµÑÐµÐ½Ð¾.
user.order.is.error=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¿ÑÐ¸ÐºÐ°Ð·Ð° Ð¾ ÑÐ¸Ð½Ð°Ð½ÑÐ¾Ð²Ð¾Ð¼ ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½Ð¸Ð¸ Ð½Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ.
user.order.status.ing.error=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð·Ð°ÐºÐ°Ð· ÑÐ¸Ð½Ð°Ð½ÑÐ¾Ð²Ð¾Ð³Ð¾ ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½Ð¸Ñ Ð½Ðµ Ð²ÑÐ¿Ð¾Ð»Ð½ÑÐµÑÑÑ Ð¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿Ð¾Ð³Ð°ÑÐµÐ½.
product.is.null.error=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, ÐºÑÐ¿Ð»ÐµÐ½Ð½ÑÐ¹ ÑÐ¾Ð²Ð°Ñ Ð½Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
product.status.end.error=ÐÐ·Ð²Ð¸Ð½Ð¸ÑÐµ, ÑÑÐ¾Ñ ÑÐ¾Ð²Ð°Ñ Ð±Ð¾Ð»ÑÑÐµ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿ÐµÐ½
product.account.min.error=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, ÑÑÐ¼Ð¼Ð° Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸ Ð¼ÐµÐ½ÑÑÐµ Ð¼Ð¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð¾Ð¹ ÑÑÐ¼Ð¼Ñ Ð¿Ð¾Ð´Ð¿Ð¸ÑÐºÐ¸.
product.account.max.error=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, ÑÑÐ¼Ð¼Ð° Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸ Ð¿ÑÐµÐ²ÑÑÐ°ÐµÑ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½ÑÑ ÑÑÐ¼Ð¼Ñ Ð¿Ð¾Ð´Ð¿Ð¸ÑÐºÐ¸.
user.is.null.error=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
user.is.auth.error=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð´Ð°Ð½Ð½ÑÑ Ð½Ðµ Ð±ÑÐ»Ð° Ð°ÑÑÐµÐ½ÑÐ¸ÑÐ¸ÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð°
file.upload.error=ÐÐ°Ð³ÑÑÐ·ÐºÐ° ÑÐ°Ð¹Ð»Ð° Ð½Ðµ ÑÐ´Ð°Ð»Ð°ÑÑ
file.upload.type.error=ÐÐµÐ¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÐ¼ÑÐµ ÑÐ¸Ð¿Ñ ÑÐ°Ð¹Ð»Ð¾Ð²
player.not.exist=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
player.fund.password.incorrect=ÐÐ°ÑÐ¾Ð»Ñ ÑÐ¾Ð½Ð´Ð° Ð½ÐµÐ²ÐµÑÐµÐ½
contract.order.entrust.type1=ÑÑÐ½Ð¾ÑÐ½ÑÐ¹ Ð¾ÑÐ´ÐµÑ
contract.order.entrust.type2=Ð»Ð¸Ð¼Ð¸ÑÐ½ÑÐ¹ Ð¾ÑÐ´ÐµÑ
account.bill.business.type.DEPOSIT=Ð´ÐµÐ¿Ð¾Ð·Ð¸Ñ
account.bill.business.type.WITHDRAW=ÐÑÐ²Ð¾Ð´ Ð´ÐµÐ½ÐµÐ³
account.bill.business.type.WITHDRAW_CANCEL=ÐÑÐ¼ÐµÐ½Ð¸ÑÑ Ð²ÑÐ²Ð¾Ð´ ÑÑÐµÐ´ÑÑÐ²
account.bill.business.type.TRANSFER=Ð¿ÐµÑÐµÐ´Ð°ÑÐ°
account.bill.business.type.CONVERT=Ð¾Ð±Ð¼ÐµÐ½
account.bill.business.type.COIN_POOL_ORDER=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ»Ð° Ð¼Ð¾Ð½ÐµÑ
account.bill.business.type.REDEMPTION=ÐÐ¾Ð³Ð°ÑÐµÐ½Ð¸Ðµ Ð¿ÑÐ»Ð° Ð¼Ð¾Ð½ÐµÑ
account.bill.business.type.COIN_POOL_PROFIT=ÐÐ¾ÑÐ¾Ð´ Ð¿ÑÐ»Ð° Ð¼Ð¾Ð½ÐµÑ
account.bill.business.type.SPOT_ORDER=ÐÑÐºÑÐ¾Ð¹ÑÐµ ÑÐ¿Ð¾ÑÐ¾Ð²ÑÑ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ñ
account.bill.business.type.SPOT_WIN=ÑÐ¿Ð¾ÑÐ¾Ð²Ð°Ñ ÑÐ¾ÑÐ³Ð¾Ð²Ð»Ñ
account.bill.business.type.CONTRACT_ORDER=ÐÑÐºÑÑÑÐ¸Ðµ ÐºÐ¾Ð½ÑÑÐ°ÐºÑÐ°
account.bill.business.type.REVOKE_ENTRUST=ÐÑÐ¼ÐµÐ½Ð¸ÑÑ Ð´ÐµÐ»ÐµÐ³Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ
account.bill.business.type.END_CONTRACT_ORDER=ÐÐ°ÐºÑÑÑÐ¸Ðµ ÐºÐ¾Ð½ÑÑÐ°ÐºÑÐ°
account.bill.business.type.SPOT_CONTRACT_ORDER=ÐÑÐºÑÑÑÐ¸Ðµ ÐºÐ¾Ð½ÑÑÐ°ÐºÑÐ°
withdraw.auth.check.error=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, ÑÐ½Ð°ÑÐ°Ð»Ð° Ð¿ÑÐ¾Ð¹Ð´Ð¸ÑÐµ ÑÐ°ÑÑÐ¸ÑÐµÐ½Ð½ÑÑ ÑÐµÑÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¸Ñ
spot.order.not.settle=Ð£ Ð²Ð°Ñ Ð½ÐµÐ²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð½ÑÐ¹ Ð·Ð°ÐºÐ°Ð·, Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿Ð¾Ð´Ð¾Ð¶Ð´Ð¸ÑÐµ.
order.num.lt.min=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð·Ð°ÐºÐ°Ð·Ð° Ð¼ÐµÐ½ÑÑÐµ Ð¼Ð¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð¾Ð³Ð¾ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð°
order.num.gt.max=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð·Ð°ÐºÐ°Ð·Ð° Ð¿ÑÐµÐ²ÑÑÐ°ÐµÑ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð¾Ðµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾
balance.min.amount=ÐÐ°Ð»Ð°Ð½Ñ ÐºÐ°Ðº Ð¼Ð¸Ð½Ð¸Ð¼ÑÐ¼