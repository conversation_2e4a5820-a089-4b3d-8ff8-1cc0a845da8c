player.login.nologin=Please log in
register.mobile.already.exists=Mobile number has been registered
register.password.incorrect=Password format error
register.invitation.invalid=Invalid invitation code
login.password.incorrect=Incorrect account or password
account.freeze.error=The account has been frozen, please contact the administrator
account.is.forbid.trade=This account has been banned from trading
account.is.exist=The account already exists
account.is.not.null=Account cannot be empty
account.pattern.not.correct=Please enter an account number with at least 6 alphanumeric characters
profile.old.password.empty=The old password cannot be empty
profile.old.password.error=Old password is wrong
profile.funds.password.incorrect=Fund password format is wrong
sms.enter.registered.phone=Please enter your registered mobile number
sms.format.number.phone=Mobile number format error
sms.send.too.frequently=Sending text messages too frequently
sms.code.error=SMS verification code error
error=Wrong parameters
rate.company.is.null.error=Convert current unit cannot be empty
rate.account.is.null.error=Wrong amount
rate.fee.is.null.error=Rate error
transaction.fee.error=The configuration fee is incorrect, please contact the backend administrator.
transaction.withdrawal..account.fee.error=The withdrawal amount must be greater than 0
transaction.withdrawal.minWithdrawAmount=Less than the minimum withdrawal amount
transaction.withdrawal.withdrawLimit=Greater than the maximum withdrawal amount
transaction.recharge..account.fee.error=The recharge amount must be greater than 0
transaction.recharge..account.fee.error2=The minimum recharge is
transaction.recharge..in.error=Transfer type cannot be empty
transaction.recharge..out.error=The transfer type cannot be empty
transaction.recharge..money.type.error=Wrong recharge unit
transaction.recharge..upay.error=Recharge abnormality, please contact customer service
ransaction.withdraw..error=You have not currently recharged and cannot withdraw cash. Please contact customer service.
user.order.product.id.error=Sorry, please choose to buy the product
user.order.chennel.error=Sorry, please fill in the purchase channel
user.order.account.error=Sorry, the purchase amount is wrong
user.order.num.null.error=Sorry, the order number is empty
user.order.date.end.error=Sorry, redemption is prohibited at the current time
user.order.is.error=Sorry, the financial management order does not exist
user.order.status.ing.error=Sorry, the financial management order is not in progress and cannot be redeemed
product.is.null.error=Sorry, the purchased product does not exist
product.status.end.error=Sorry, this product is no longer available
product.account.min.error=Sorry, the purchase amount is less than the minimum subscription amount
product.account.max.error=Sorry, the purchase amount is greater than the maximum subscription amount
user.is.null.error=User does not exist
user.is.auth.error=Data information has not been authenticated
file.upload.error=File upload failed
file.upload.type.error=Unsupported file types
player.not.exist=User does not exist
player.fund.password.incorrect=Fund password is incorrect
contract.order.entrust.type1=market order
contract.order.entrust.type2=limit order
account.bill.business.type.DEPOSIT=deposit
account.bill.business.type.WITHDRAW=Withdraw money
account.bill.business.type.WITHDRAW_CANCEL=Cancel withdrawal
account.bill.business.type.TRANSFER=transfer
account.bill.business.type.CONVERT=exchange
account.bill.business.type.COIN_POOL_ORDER=Coin Pool Order
account.bill.business.type.REDEMPTION=Coin pool redemption
account.bill.business.type.COIN_POOL_PROFIT=Coin pool income
account.bill.business.type.SPOT_ORDER=Open a spot position
account.bill.business.type.SPOT_WIN=spot trading
account.bill.business.type.CONTRACT_ORDER=Contract opening
account.bill.business.type.REVOKE_ENTRUST=Revoke delegation
account.bill.business.type.END_CONTRACT_ORDER=Contract closing
account.bill.business.type.SPOT_CONTRACT_ORDER=Contract opening

1011007=Your account has been banned, please contact customer service
1016002=The server is abnormal, please contact customer service
2027002=Insufficient balance
2027003=Insufficient frozen balance
2028006=Take profit and stop loss parameters are wrong

withdraw.auth.check.error=Please complete advanced certification first
spot.order.not.settle=You have an unsettled order, please wait.
order.num.lt.min=The order quantity is less than the minimum quantity
order.num.gt.max=The order quantity is greater than the maximum quantity
balance.min.amount=The balance is at least