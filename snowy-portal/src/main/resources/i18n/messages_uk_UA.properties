1011007=ÐÐ°Ñ Ð¾Ð±Ð»ÑÐºÐ¾Ð²Ð¸Ð¹ Ð·Ð°Ð¿Ð¸Ñ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¾Ð²Ð°Ð½Ð¾, Ð·Ð²ÐµÑÐ½ÑÑÑÑÑ Ð´Ð¾ ÑÐ»ÑÐ¶Ð±Ð¸ Ð¿ÑÐ´ÑÑÐ¸Ð¼ÐºÐ¸ ÐºÐ»ÑÑÐ½ÑÑÐ²
1016002=Ð¡ÐµÑÐ²ÐµÑ Ð½ÐµÑÐ¿ÑÐ°Ð²Ð½Ð¸Ð¹, Ð·Ð²ÐµÑÐ½ÑÑÑÑÑ Ð´Ð¾ ÑÐ»ÑÐ¶Ð±Ð¸ Ð¿ÑÐ´ÑÑÐ¸Ð¼ÐºÐ¸ ÐºÐ»ÑÑÐ½ÑÑÐ²
2027002=ÐÐµÐ´Ð¾ÑÑÐ°ÑÐ½ÑÐ¹ Ð±Ð°Ð»Ð°Ð½Ñ
2027003=ÐÐµÐ´Ð¾ÑÑÐ°ÑÐ½ÑÐ¹ Ð·Ð°Ð¼Ð¾ÑÐ¾Ð¶ÐµÐ½Ð¸Ð¹ Ð±Ð°Ð»Ð°Ð½Ñ
2028006=ÐÐ°ÑÐ°Ð¼ÐµÑÑÐ¸ ÑÐµÐ¹Ðº-Ð¿ÑÐ¾ÑÑÑÑ ÑÐ° ÑÑÐ¾Ð¿-Ð»Ð¾ÑÑÑ Ð½ÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ñ
player.login.nologin=ÐÑÐ´Ñ Ð»Ð°ÑÐºÐ°, ÑÐ²ÑÐ¹Ð´ÑÑÑ
register.mobile.already.exists=ÐÐ¾Ð±ÑÐ»ÑÐ½Ð¸Ð¹ Ð½Ð¾Ð¼ÐµÑ Ð·Ð°ÑÐµÑÑÑÑÐ¾Ð²Ð°Ð½Ð¾
register.password.incorrect=ÐÐ¾Ð¼Ð¸Ð»ÐºÐ° ÑÐ¾ÑÐ¼Ð°ÑÑ Ð¿Ð°ÑÐ¾Ð»Ñ
register.invitation.invalid=ÐÐµÐ´ÑÐ¹ÑÐ½Ð¸Ð¹ ÐºÐ¾Ð´ Ð·Ð°Ð¿ÑÐ¾ÑÐµÐ½Ð½Ñ
login.password.incorrect=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¸Ð¹ Ð¾Ð±Ð»ÑÐºÐ¾Ð²Ð¸Ð¹ Ð·Ð°Ð¿Ð¸Ñ Ð°Ð±Ð¾ Ð¿Ð°ÑÐ¾Ð»Ñ
account.freeze.error=ÐÐ±Ð»ÑÐºÐ¾Ð²Ð¸Ð¹ Ð·Ð°Ð¿Ð¸Ñ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¾Ð²Ð°Ð½Ð¾, Ð·Ð²ÐµÑÐ½ÑÑÑÑÑ Ð´Ð¾ Ð°Ð´Ð¼ÑÐ½ÑÑÑÑÐ°ÑÐ¾ÑÐ°
account.is.forbid.trade=Ð¦ÐµÐ¹ Ð¾Ð±Ð»ÑÐºÐ¾Ð²Ð¸Ð¹ Ð·Ð°Ð¿Ð¸Ñ Ð·Ð°Ð±Ð¾ÑÐ¾Ð½ÐµÐ½Ð¾ ÑÐ¾ÑÐ³ÑÐ²Ð°ÑÐ¸
account.is.exist=ÐÐ±Ð»ÑÐºÐ¾Ð²Ð¸Ð¹ Ð·Ð°Ð¿Ð¸Ñ ÑÐ¶Ðµ ÑÑÐ½ÑÑ
account.is.not.null=Ð Ð°ÑÑÐ½Ð¾Ðº Ð½Ðµ Ð¼Ð¾Ð¶Ðµ Ð±ÑÑÐ¸ Ð¿Ð¾ÑÐ¾Ð¶Ð½ÑÐ¼
account.pattern.not.correct=ÐÐ²ÐµÐ´ÑÑÑ Ð½Ð¾Ð¼ÐµÑ ÑÐ°ÑÑÐ½ÐºÑ Ð¿ÑÐ¸Ð½Ð°Ð¹Ð¼Ð½Ñ Ð· 6 Ð±ÑÐºÐ²ÐµÐ½Ð¾-ÑÐ¸ÑÑÐ¾Ð²Ð¸Ñ ÑÐ¸Ð¼Ð²Ð¾Ð»ÑÐ²
profile.old.password.empty=Ð¡ÑÐ°ÑÐ¸Ð¹ Ð¿Ð°ÑÐ¾Ð»Ñ Ð½Ðµ Ð¼Ð¾Ð¶Ðµ Ð±ÑÑÐ¸ Ð¿Ð¾ÑÐ¾Ð¶Ð½ÑÐ¼
profile.old.password.error=Ð¡ÑÐ°ÑÐ¸Ð¹ Ð¿Ð°ÑÐ¾Ð»Ñ Ð½ÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¸Ð¹
profile.funds.password.incorrect=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¸Ð¹ ÑÐ¾ÑÐ¼Ð°Ñ Ð¿Ð°ÑÐ¾Ð»Ñ ÑÐ¾Ð½Ð´Ñ
sms.enter.registered.phone=ÐÐ²ÐµÐ´ÑÑÑ ÑÐ²ÑÐ¹ Ð·Ð°ÑÐµÑÑÑÑÐ¾Ð²Ð°Ð½Ð¸Ð¹ Ð½Ð¾Ð¼ÐµÑ Ð¼Ð¾Ð±ÑÐ»ÑÐ½Ð¾Ð³Ð¾ ÑÐµÐ»ÐµÑÐ¾Ð½Ñ
sms.format.number.phone=ÐÐ¾Ð¼Ð¸Ð»ÐºÐ° ÑÐ¾ÑÐ¼Ð°ÑÑ Ð¼Ð¾Ð±ÑÐ»ÑÐ½Ð¾Ð³Ð¾ Ð½Ð¾Ð¼ÐµÑÐ°
sms.send.too.frequently=ÐÐ°Ð½Ð°Ð´ÑÐ¾ ÑÐ°ÑÑÐµ Ð½Ð°Ð´ÑÐ¸Ð»Ð°Ð½Ð½Ñ ÑÐµÐºÑÑÐ¾Ð²Ð¸Ñ Ð¿Ð¾Ð²ÑÐ´Ð¾Ð¼Ð»ÐµÐ½Ñ
sms.code.error=ÐÐ¾Ð¼Ð¸Ð»ÐºÐ° ÐºÐ¾Ð´Ñ Ð¿ÑÐ´ÑÐ²ÐµÑÐ´Ð¶ÐµÐ½Ð½Ñ SMS
error=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ñ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÐ¸
rate.company.is.null.error=ÐÐµÑÐµÑÐ²Ð¾ÑÐ¸ÑÐ¸ Ð¿Ð¾ÑÐ¾ÑÐ½Ñ Ð¾Ð´Ð¸Ð½Ð¸ÑÑ Ð½Ðµ Ð¼Ð¾Ð¶Ðµ Ð±ÑÑÐ¸ Ð¿Ð¾ÑÐ¾Ð¶Ð½ÑÐ¼
rate.account.is.null.error=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð° ÑÑÐ¼Ð°
rate.fee.is.null.error=ÐÐ¾Ð¼Ð¸Ð»ÐºÐ° Ð¾ÑÑÐ½ÐºÐ¸
transaction.fee.error=ÐÐ¾Ð¼ÑÑÑÑ Ð·Ð° ÐºÐ¾Ð½ÑÑÐ³ÑÑÐ°ÑÑÑ Ð½ÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð°, Ð±ÑÐ´Ñ Ð»Ð°ÑÐºÐ°, Ð·Ð²ÐµÑÐ½ÑÑÑÑÑ Ð´Ð¾ Ð°Ð´Ð¼ÑÐ½ÑÑÑÑÐ°ÑÐ¾ÑÐ° ÑÐµÑÐ²ÐµÑÐ½Ð¾Ñ ÑÐ°ÑÑÐ¸Ð½Ð¸.
transaction.withdrawal..account.fee.error=Ð¡ÑÐ¼Ð° Ð²Ð¸Ð²ÐµÐ´ÐµÐ½Ð½Ñ Ð¼Ð°Ñ Ð±ÑÑÐ¸ Ð±ÑÐ»ÑÑÐµ 0
transaction.withdrawal.minWithdrawAmount=ÐÐµÐ½ÑÐµ Ð¼ÑÐ½ÑÐ¼Ð°Ð»ÑÐ½Ð¾Ñ ÑÑÐ¼Ð¸ Ð´Ð»Ñ Ð·Ð½ÑÑÑÑ
transaction.withdrawal.withdrawLimit=ÐÑÐ»ÑÑÐµ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð¾Ñ ÑÑÐ¼Ð¸ Ð·Ð½ÑÑÑÑ
transaction.recharge..account.fee.error=Ð¡ÑÐ¼Ð° Ð¿Ð¾Ð¿Ð¾Ð²Ð½ÐµÐ½Ð½Ñ Ð¼Ð°Ñ Ð±ÑÑÐ¸ Ð±ÑÐ»ÑÑÐµ 0
transaction.recharge..account.fee.error2=ÐÑÐ½ÑÐ¼Ð°Ð»ÑÐ½Ðµ Ð¿Ð¾Ð¿Ð¾Ð²Ð½ÐµÐ½Ð½Ñ ÑÐºÐ»Ð°Ð´Ð°Ñ
transaction.recharge..in.error=Ð¢Ð¸Ð¿ Ð¿ÐµÑÐµÐºÐ°Ð·Ñ Ð½Ðµ Ð¼Ð¾Ð¶Ðµ Ð±ÑÑÐ¸ Ð¿ÑÑÑÐ¸Ð¼
transaction.recharge..out.error=Ð¢Ð¸Ð¿ Ð¿ÐµÑÐµÐºÐ°Ð·Ñ Ð½Ðµ Ð¼Ð¾Ð¶Ðµ Ð±ÑÑÐ¸ Ð¿ÑÑÑÐ¸Ð¼
transaction.recharge..money.type.error=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¸Ð¹ Ð±Ð»Ð¾Ðº Ð¿ÑÐ´Ð·Ð°ÑÑÐ´ÐºÐ¸
transaction.recharge..upay.error=ÐÐµÑÐ¿ÑÐ°Ð²Ð½ÑÑÑÑ Ð¿Ð¾Ð¿Ð¾Ð²Ð½ÐµÐ½Ð½Ñ Ð·Ð°ÑÑÐ´Ñ, Ð·Ð²ÐµÑÐ½ÑÑÑÑÑ Ð´Ð¾ ÑÐ»ÑÐ¶Ð±Ð¸ Ð¿ÑÐ´ÑÑÐ¸Ð¼ÐºÐ¸ ÐºÐ»ÑÑÐ½ÑÑÐ²
ransaction.withdraw..error=ÐÐ°ÑÐ°Ð· Ð²Ð¸ Ð½Ðµ Ð¿Ð¾Ð¿Ð¾Ð²Ð½ÑÐ²Ð°Ð»Ð¸ ÑÐ°ÑÑÐ½Ð¾Ðº Ñ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð·Ð½ÑÑÐ¸ Ð³Ð¾ÑÑÐ²ÐºÑ. 
user.order.product.id.error=ÐÐ¸Ð±Ð°ÑÑÐµ, Ð±ÑÐ´Ñ Ð»Ð°ÑÐºÐ°, Ð²Ð¸Ð±ÐµÑÑÑÑ ÐºÑÐ¿Ð¸ÑÐ¸ Ð¿ÑÐ¾Ð´ÑÐºÑ
user.order.chennel.error=ÐÐ¸Ð±Ð°ÑÑÐµ, Ð±ÑÐ´Ñ Ð»Ð°ÑÐºÐ°, Ð·Ð°Ð¿Ð¾Ð²Ð½ÑÑÑ ÐºÐ°Ð½Ð°Ð» Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸
user.order.account.error=ÐÐ° Ð¶Ð°Ð»Ñ, ÑÑÐ¼Ð° Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸ Ð½ÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð°
user.order.num.null.error=ÐÐ¸Ð±Ð°ÑÑÐµ, Ð½Ð¾Ð¼ÐµÑ Ð·Ð°Ð¼Ð¾Ð²Ð»ÐµÐ½Ð½Ñ Ð¿Ð¾ÑÐ¾Ð¶Ð½ÑÐ¹
user.order.date.end.error=ÐÐ° Ð¶Ð°Ð»Ñ, Ð¿Ð¾Ð³Ð°ÑÐµÐ½Ð½Ñ Ð½Ð°ÑÐ°Ð·Ñ Ð·Ð°Ð±Ð¾ÑÐ¾Ð½ÐµÐ½Ð¾
user.order.is.error=ÐÐ° Ð¶Ð°Ð»Ñ, Ð½Ð°ÐºÐ°Ð· ÑÑÐ½Ð°Ð½ÑÐ¾Ð²Ð¾Ð³Ð¾ ÑÐ¿ÑÐ°Ð²Ð»ÑÐ½Ð½Ñ Ð½Ðµ ÑÑÐ½ÑÑ
user.order.status.ing.error=ÐÐ° Ð¶Ð°Ð»Ñ, Ð·Ð°Ð¼Ð¾Ð²Ð»ÐµÐ½Ð½Ñ ÑÑÐ½Ð°Ð½ÑÐ¾Ð²Ð¾Ð³Ð¾ ÑÐ¿ÑÐ°Ð²Ð»ÑÐ½Ð½Ñ Ð½Ðµ Ð²Ð¸ÐºÐ¾Ð½ÑÑÑÑÑÑ, Ñ Ð¹Ð¾Ð³Ð¾ Ð½Ðµ Ð¼Ð¾Ð¶Ð½Ð° Ð°ÐºÑÐ¸Ð²ÑÐ²Ð°ÑÐ¸
product.is.null.error=ÐÐ¸Ð±Ð°ÑÑÐµ, Ð¿ÑÐ¸Ð´Ð±Ð°Ð½Ð¸Ð¹ ÑÐ¾Ð²Ð°Ñ Ð½Ðµ ÑÑÐ½ÑÑ
product.status.end.error=ÐÐ¸Ð±Ð°ÑÑÐµ, ÑÐµÐ¹ Ð¿ÑÐ¾Ð´ÑÐºÑ Ð±ÑÐ»ÑÑÐµ Ð½Ðµ Ð´Ð¾ÑÑÑÐ¿Ð½Ð¸Ð¹
product.account.min.error=ÐÐ° Ð¶Ð°Ð»Ñ, ÑÑÐ¼Ð° Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸ Ð¼ÐµÐ½ÑÐ° Ð·Ð° Ð¼ÑÐ½ÑÐ¼Ð°Ð»ÑÐ½Ñ ÑÑÐ¼Ñ Ð¿ÑÐ´Ð¿Ð¸ÑÐºÐ¸
product.account.max.error=ÐÐ° Ð¶Ð°Ð»Ñ, ÑÑÐ¼Ð° Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸ Ð¿ÐµÑÐµÐ²Ð¸ÑÑÑ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ñ ÑÑÐ¼Ñ Ð¿ÑÐ´Ð¿Ð¸ÑÐºÐ¸
user.is.null.error=ÐÐ¾ÑÐ¸ÑÑÑÐ²Ð°Ñ Ð½Ðµ ÑÑÐ½ÑÑ
user.is.auth.error=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð¿ÑÐ¾ Ð´Ð°Ð½Ñ Ð½Ðµ Ð¿ÑÐ´ÑÐ²ÐµÑÐ´Ð¶ÐµÐ½Ð¾
file.upload.error=ÐÐ¾Ð¼Ð¸Ð»ÐºÐ° Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÐµÐ½Ð½Ñ ÑÐ°Ð¹Ð»Ñ
file.upload.type.error=ÐÐµÐ¿ÑÐ´ÑÑÐ¸Ð¼ÑÐ²Ð°Ð½Ñ ÑÐ¸Ð¿Ð¸ ÑÐ°Ð¹Ð»ÑÐ²
player.not.exist=ÐÐ¾ÑÐ¸ÑÑÑÐ²Ð°Ñ Ð½Ðµ ÑÑÐ½ÑÑ
player.fund.password.incorrect=ÐÐ°ÑÐ¾Ð»Ñ ÑÐ¾Ð½Ð´Ñ Ð½ÐµÐ²ÑÑÐ½Ð¸Ð¹
contract.order.entrust.type1=ÑÐ¸Ð½ÐºÐ¾Ð²Ð¸Ð¹ Ð¿Ð¾ÑÑÐ´Ð¾Ðº
contract.order.entrust.type2=Ð»ÑÐ¼ÑÑÐ½Ðµ Ð·Ð°Ð¼Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
account.bill.business.type.DEPOSIT=Ð´ÐµÐ¿Ð¾Ð·Ð¸Ñ
account.bill.business.type.WITHDRAW=ÐÐ½ÑÑÐ¸ Ð³ÑÐ¾ÑÑ
account.bill.business.type.WITHDRAW_CANCEL=Ð¡ÐºÐ°ÑÑÐ²Ð°ÑÐ¸ Ð·Ð½ÑÑÑÑ
account.bill.business.type.TRANSFER=Ð¿ÐµÑÐµÐ´Ð°ÑÐ°
account.bill.business.type.CONVERT=Ð¾Ð±Ð¼ÑÐ½
account.bill.business.type.COIN_POOL_ORDER=ÐÑÐ´ÐµÑ Coin Pool
account.bill.business.type.REDEMPTION=ÐÐ¾Ð³Ð°ÑÐµÐ½Ð½Ñ Ð¿ÑÐ»Ñ Ð¼Ð¾Ð½ÐµÑ
account.bill.business.type.COIN_POOL_PROFIT=ÐÐ¾ÑÑÐ´ Ð¿ÑÐ»Ñ Ð¼Ð¾Ð½ÐµÑ
account.bill.business.type.SPOT_ORDER=ÐÑÐ´ÐºÑÐ¸Ð¹ÑÐµ ÑÐ¿Ð¾ÑÐ¾Ð²Ñ Ð¿Ð¾Ð·Ð¸ÑÑÑ
account.bill.business.type.SPOT_WIN=ÑÐ¿Ð¾ÑÐ¾Ð²Ð° ÑÐ¾ÑÐ³ÑÐ²Ð»Ñ
account.bill.business.type.CONTRACT_ORDER=ÐÑÐ´ÐºÑÐ¸ÑÑÑ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÑ
account.bill.business.type.REVOKE_ENTRUST=ÐÑÐ´ÐºÐ»Ð¸ÐºÐ°ÑÐ¸ Ð´ÐµÐ»ÐµÐ³ÑÐ²Ð°Ð½Ð½Ñ
account.bill.business.type.END_CONTRACT_ORDER=ÐÐ°ÐºÑÐ¸ÑÑÑ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÑ
account.bill.business.type.SPOT_CONTRACT_ORDER=Ð Ð¾Ð·ÐºÑÐ¸ÑÑÑ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÑ
withdraw.auth.check.error=Ð¡Ð¿Ð¾ÑÐ°ÑÐºÑ Ð¿ÑÐ¾Ð¹Ð´ÑÑÑ ÑÐ¾Ð·ÑÐ¸ÑÐµÐ½Ñ ÑÐµÑÑÐ¸ÑÑÐºÐ°ÑÑÑ
spot.order.not.settle=Ð£ Ð²Ð°Ñ Ñ Ð½ÐµÐ²Ð¸ÐºÐ¾Ð½Ð°Ð½Ðµ Ð·Ð°Ð¼Ð¾Ð²Ð»ÐµÐ½Ð½Ñ, Ð·Ð°ÑÐµÐºÐ°Ð¹ÑÐµ.
order.num.lt.min=ÐÑÐ»ÑÐºÑÑÑÑ Ð·Ð°Ð¼Ð¾Ð²Ð»ÐµÐ½Ð½Ñ Ð¼ÐµÐ½ÑÐ° Ð·Ð° Ð¼ÑÐ½ÑÐ¼Ð°Ð»ÑÐ½Ñ ÐºÑÐ»ÑÐºÑÑÑÑ
order.num.gt.max=ÐÑÐ»ÑÐºÑÑÑÑ Ð·Ð°Ð¼Ð¾Ð²Ð»ÐµÐ½Ð½Ñ Ð¿ÐµÑÐµÐ²Ð¸ÑÑÑ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ñ ÐºÑÐ»ÑÐºÑÑÑÑ
balance.min.amount=ÐÐ°Ð»Ð°Ð½Ñ Ð¿ÑÐ¸Ð½Ð°Ð¹Ð¼Ð½Ñ