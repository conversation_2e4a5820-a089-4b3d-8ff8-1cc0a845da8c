1011007="ããªãã®ã¢ã«ã¦ã³ãã¯ç¦æ­¢ããã¦ãã¾ããã«ã¹ã¿ãã¼ ãµã¼ãã¹ã«ãåãåãããã ããã"
1016002=ãµã¼ãã¼ãç°å¸¸ã§ããã«ã¹ã¿ãã¼ãµã¼ãã¹ã«ãé£çµ¡ãã ããã
2027002=æ®é«ä¸è¶³
2027003=åçµæ®é«ãä¸ååã§ã
2028006=ãã¤ã¯ãã­ãã£ããã¨ã¹ãããã­ã¹ã®ãã©ã¡ã¼ã¿ãééã£ã¦ãã¾ã
player.login.nologin=ã­ã°ã¤ã³ãã¦ãã ãã
register.mobile.already.exists=æºå¸¯çªå·ãç»é²ããã¦ãã¾ã
register.password.incorrect=ãã¹ã¯ã¼ãå½¢å¼ã¨ã©ã¼
register.invitation.invalid=ç¡å¹ãªæå¾ã³ã¼ãã§ã
login.password.incorrect=ã¢ã«ã¦ã³ãã¾ãã¯ãã¹ã¯ã¼ããééã£ã¦ãã¾ã
account.freeze.error=ã¢ã«ã¦ã³ããåçµããã¾ãããç®¡çèã«é£çµ¡ãã¦ãã ãã
account.is.forbid.trade=ãã®ã¢ã«ã¦ã³ãã¯åå¼ãç¦æ­¢ããã¾ãã
account.is.exist=ã¢ã«ã¦ã³ãã¯ãã§ã«å­å¨ãã¾ã
account.is.not.null=ã¢ã«ã¦ã³ããç©ºã«ãããã¨ã¯ã§ãã¾ãã
account.pattern.not.correct=å£åº§çªå·ã6æå­ä»¥ä¸ã®è±æ°å­ã§å¥åãã¦ãã ãã
profile.old.password.empty=å¤ããã¹ã¯ã¼ããç©ºã«ãããã¨ã¯ã§ãã¾ãã
profile.old.password.error=å¤ããã¹ã¯ã¼ããééã£ã¦ãã¾ã
profile.funds.password.incorrect=ãã¡ã³ãã®ãã¹ã¯ã¼ãå½¢å¼ãééã£ã¦ãã¾ã
sms.enter.registered.phone=ç»é²ããã¦ããæºå¸¯é»è©±çªå·ãå¥åãã¦ãã ãã
sms.format.number.phone=æºå¸¯é»è©±çªå·ãã©ã¼ãããã¨ã©ã¼
sms.send.too.frequently=ãã­ã¹ãã¡ãã»ã¼ã¸ãé »ç¹ã«éä¿¡ãããã
sms.code.error=SMSèªè¨¼ã³ã¼ãã¨ã©ã¼
error=ãã©ã¡ã¼ã¿ãééã£ã¦ãã¾ã
rate.company.is.null.error=ç¾å¨ã®åä½ãç©ºã«å¤æãããã¨ã¯ã§ãã¾ãã
rate.account.is.null.error=éé¡ãééã£ã¦ãã¾ã
rate.fee.is.null.error=ã¬ã¼ãã¨ã©ã¼
transaction.fee.error=æ§ææéãééã£ã¦ãã¾ããããã¯ã¨ã³ãç®¡çèã«åãåããã¦ãã ããã
transaction.withdrawal..account.fee.error=å¼ãåºãéé¡ã¯0ããå¤§ãããªããã°ãªãã¾ãã
transaction.withdrawal.minWithdrawAmount=æä½åºéé¡æªæº
transaction.withdrawal.withdrawLimit=åºéé¡ã®ä¸éãè¶ãã¦ãã
transaction.recharge..account.fee.error=ãªãã£ã¼ã¸éé¡ã¯0ããå¤§ãããªããã°ãªãã¾ãã
transaction.recharge..account.fee.error2=æä½ãã£ã¼ã¸é¡ã¯ã
transaction.recharge..in.error=è»¢éã¿ã¤ããç©ºã«ãããã¨ã¯ã§ãã¾ãã
transaction.recharge..out.error=è»¢éã¿ã¤ããç©ºã«ãããã¨ã¯ã§ãã¾ãã
transaction.recharge..money.type.error=ééã£ãåé»ã¦ããã
transaction.recharge..upay.error=åé»ç°å¸¸ã§ããã«ã¹ã¿ãã¼ãµã¼ãã¹ã«ãé£çµ¡ãã ãã
ransaction.withdraw..error=ç¾å¨ãªãã£ã¼ã¸ãã¦ããããç¾éãå¼ãåºããã¨ã¯ã§ãã¾ããã
user.order.product.id.error=ç³ãè¨³ããã¾ããããè£½åãè³¼å¥ãããã¨ãé¸æãã¦ãã ãã
user.order.chennel.error=ç³ãè¨³ããã¾ããããè³¼å¥ãã£ãã«ãå¥åãã¦ãã ãã
user.order.account.error=ç³ãè¨³ããã¾ããããè³¼å¥éé¡ãééã£ã¦ãã¾ã
user.order.num.null.error=ç³ãè¨³ããã¾ããããæ³¨æçªå·ãç©ºã§ã
user.order.date.end.error=ç³ãè¨³ãããã¾ããããç¾æç¹ã§ã¯å¼ãæãã¯ç¦æ­¢ããã¦ãã¾ã
user.order.is.error=ç³ãè¨³ããã¾ããããè²¡åç®¡çå½ä»¤ã¯å­å¨ãã¾ãã
user.order.status.ing.error=ç³ãè¨³ããã¾ããããè²¡åç®¡çå½ä»¤ã¯é²è¡ä¸­ã§ãªããããå¼ãæãããã¨ã¯ã§ãã¾ãã
product.is.null.error=ç³ãè¨³ããã¾ããããè³¼å¥ããååã¯å­å¨ãã¾ãã
product.status.end.error=ç³ãè¨³ããã¾ãããããã®è£½åã¯è²©å£²ãããªããªãã¾ãã
product.account.min.error=ç³ãè¨³ããã¾ããããè³¼å¥éé¡ãæä½ãµãã¹ã¯ãªãã·ã§ã³éé¡ãä¸åã£ã¦ãã¾ã
product.account.max.error=ç³ãè¨³ããã¾ããããè³¼å¥éé¡ããµãã¹ã¯ãªãã·ã§ã³ã®æå¤§éé¡ãè¶ãã¦ãã¾ã
user.is.null.error=ã¦ã¼ã¶ã¼ãå­å¨ãã¾ãã
user.is.auth.error=ãã¼ã¿æå ±ãèªè¨¼ããã¦ãã¾ãã
file.upload.error=ãã¡ã¤ã«ã®ã¢ããã­ã¼ãã«å¤±æãã¾ãã
file.upload.type.error=ãµãã¼ãããã¦ããªããã¡ã¤ã«å½¢å¼
player.not.exist=ã¦ã¼ã¶ã¼ãå­å¨ãã¾ãã
player.fund.password.incorrect=ãã¡ã³ãã®ãã¹ã¯ã¼ããééã£ã¦ãã¾ã
contract.order.entrust.type1=æè¡æ³¨æ
contract.order.entrust.type2=æå¤æ³¨æ
account.bill.business.type.DEPOSIT=ããã¸ãã
account.bill.business.type.WITHDRAW=ãéãå¼ãåºã
account.bill.business.type.WITHDRAW_CANCEL=åºéãã­ã£ã³ã»ã«ãã
account.bill.business.type.TRANSFER=ç§»è¡
account.bill.business.type.CONVERT=äº¤æ
account.bill.business.type.COIN_POOL_ORDER=ã³ã¤ã³ãã¼ã«æ³¨æ
account.bill.business.type.REDEMPTION=ã³ã¤ã³ãã¼ã«ã®åé
account.bill.business.type.COIN_POOL_PROFIT=ã³ã¤ã³ãã¼ã«åå¥
account.bill.business.type.SPOT_ORDER=ã¹ããããã¸ã·ã§ã³ããªã¼ãã³ãã
account.bill.business.type.SPOT_WIN=ç¾ç©åå¼
account.bill.business.type.CONTRACT_ORDER=å¥ç´ã®éå§
account.bill.business.type.REVOKE_ENTRUST=å§ä»»ãåãæ¶ã
account.bill.business.type.END_CONTRACT_ORDER=å¥ç´ç· çµ
account.bill.business.type.SPOT_CONTRACT_ORDER=å¥ç´ã®éå§
withdraw.auth.check.error=ã¾ãé«åº¦ãªèªå®ãå®äºãã¦ãã ãã
spot.order.not.settle=æªæ±ºæ¸ã®æ³¨æãããã¾ãã®ã§ãå¾ã¡ãã ããã
order.num.lt.min=æ³¨ææ°éãæä½æ°éæªæºã§ã
order.num.gt.max=æ³¨ææ°éãæå¤§æ°éãè¶ãã¦ãã¾ã
balance.min.amount=æ®é«ã¯å°ãªãã¨ã