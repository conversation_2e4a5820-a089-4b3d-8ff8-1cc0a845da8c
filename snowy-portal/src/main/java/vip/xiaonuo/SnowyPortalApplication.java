/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo;

import cn.hutool.log.Log;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.context.ApplicationPidFileWriter;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.Environment;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import vip.xiaonuo.core.redis.RedissonDelayQueueClient;
import vip.xiaonuo.core.util.PathUtils;
import vip.xiaonuo.core.util.TickerPriceHashMap;
import vip.xiaonuo.telegrambot.TelegramBotConfiguration;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * SpringBoot方式启动类
 *
 * <AUTHOR>
 * @date 2017/5/21 12:06
 */
@SpringBootApplication(exclude = {SecurityAutoConfiguration.class, ManagementWebSecurityAutoConfiguration.class})
@ServletComponentScan
@EnableTransactionManagement
@EnableScheduling
@EnableKafka
@Import(TelegramBotConfiguration.class)
public class SnowyPortalApplication {

    private static final Log log = Log.get();

    public static void main(String[] args) throws UnknownHostException {
        SpringApplication springApplication = new SpringApplication(SnowyPortalApplication.class);
        springApplication.addListeners(new ApplicationPidFileWriter(PathUtils.getPidPath(SnowyPortalApplication.class)));
        ConfigurableApplicationContext application = springApplication.run(args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        log.info("[{}:{}]启动成功", ip, port);
    }

    @Bean
    public RedissonDelayQueueClient redissonDelayQueueClient() {
        return new RedissonDelayQueueClient();
    }

    @Bean
    public TickerPriceHashMap tickerPriceHashMap() {
        return new TickerPriceHashMap();
    }

}
