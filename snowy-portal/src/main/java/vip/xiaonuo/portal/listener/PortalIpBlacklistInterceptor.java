package vip.xiaonuo.portal.listener;

import org.springframework.stereotype.Component;
import vip.xiaonuo.business.ipblacklist.service.IpBlacklistService;
import vip.xiaonuo.session.security.interceptors.IpBlacklistInterceptor;

import javax.annotation.Resource;

@Component
public class PortalIpBlacklistInterceptor extends IpBlacklistInterceptor {
    @Resource
    private IpBlacklistService ipBlacklistService;

    @Override
    public boolean hasBlacklist(String clientIP) {
        return ipBlacklistService.hasBlackList(clientIP);
    }
}
