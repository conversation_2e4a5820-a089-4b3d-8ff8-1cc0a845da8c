//package vip.xiaonuo.portal.listener;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.data.redis.connection.Message;
//import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
//import org.springframework.data.redis.listener.RedisMessageListenerContainer;
//import org.springframework.stereotype.Component;
//import vip.xiaonuo.session.security.UserSessionServiceImpl;
//
//@Component
//@Slf4j
//public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {
//    private Logger logger = LoggerFactory.getLogger(RedisKeyExpirationListener.class);
//
//    public RedisKeyExpirationListener(RedisMessageListenerContainer listenerContainer) {
//        super(listenerContainer);
//    }
//
//    public void onMessage(Message message, byte[] pattern) {
//        String key = message.toString();
//        if (StringUtils.startsWith(key, UserSessionServiceImpl.SESSION_KEY)) {
//            logger.info("玩家登录key过期{}", key);
//        }
//    }
//}
