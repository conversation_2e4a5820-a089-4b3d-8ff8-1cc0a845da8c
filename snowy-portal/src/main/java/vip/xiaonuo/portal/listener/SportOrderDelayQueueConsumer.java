package vip.xiaonuo.portal.listener;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import vip.xiaonuo.business.contractspotorder.controller.entity.ContractSpotOrder;
import vip.xiaonuo.business.contractspotorder.controller.service.ContractSpotOrderService;
import vip.xiaonuo.business.spotorder.entity.SpotOrder;
import vip.xiaonuo.business.spotorder.service.SpotOrderService;
import vip.xiaonuo.core.pojo.RedisDelayMessage;
import vip.xiaonuo.core.redis.DelayQueueConsumer;

import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;

@Component("SPOT_ORDER_consumer")
public class SportOrderDelayQueueConsumer implements DelayQueueConsumer<String> {
    private Logger logger = LoggerFactory.getLogger(SportOrderDelayQueueConsumer.class);
    @Resource
    private SpotOrderService spotOrderService;
    @Resource
    private ContractSpotOrderService contractSpotOrderService;
    @Resource
    private ExecutorService executorService;

    @Override
    public void execute(RedisDelayMessage<String> delayMessage) {
        logger.info("进入Redis延迟队列{}", JSON.toJSONString(delayMessage));
        executorService.execute(() -> {
            //异步执行
            try {
                //结束订单
                SpotOrder spotOrder = spotOrderService.getSpotOrderForLock(delayMessage.getData());
                if (ObjectUtils.isNotEmpty(spotOrder)) {
                    spotOrderService.finishOrder(delayMessage.getData(), spotOrder);
                } else {
                    ContractSpotOrder contractspotOrder = contractSpotOrderService.getContractSpotOrderForLock(delayMessage.getData());
                    if (ObjectUtils.isNotEmpty(contractspotOrder)) {
                        contractSpotOrderService.finishOrder(delayMessage.getData(), contractspotOrder);
                    }
                }
            } catch (Exception e) {
                logger.error("结束极速订单异常{}", JSON.toJSONString(delayMessage), e);
            }
        });
    }
}
