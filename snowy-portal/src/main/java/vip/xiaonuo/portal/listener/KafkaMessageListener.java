package vip.xiaonuo.portal.listener;

import com.alibaba.fastjson.JSON;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import vip.xiaonuo.business.coinorder.service.CoinOrderService;
import vip.xiaonuo.business.contractorder.service.ContractOrderCloseService;
import vip.xiaonuo.core.consts.DpConstants;
import vip.xiaonuo.core.pojo.dto.TickerDTO;
import vip.xiaonuo.core.util.DpDataUtils;
import vip.xiaonuo.core.util.KafkaTopics;
import vip.xiaonuo.core.util.TickerPriceHashMap;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * kafka消费队列
 */
@Component
public class KafkaMessageListener {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private ContractOrderCloseService contractOrderCloseService;
    @Resource
    private CoinOrderService coinOrderService;
    @Resource
    private TickerPriceHashMap tickerPriceHashMap;

    @KafkaListener(topics = KafkaTopics.DP_MARKET_TICKER)
    public void marketTickerListener(ConsumerRecord<?, ?> record) {
        String value = (String) record.value();
        try {
            TickerDTO tickerDTO = JSON.parseObject(value, TickerDTO.class);
            long ct = System.currentTimeMillis() - (5 * 60 * 1000);
            if (tickerDTO.getT() == null || tickerDTO.getT() < ct) {
                //5分钟之后的数据不处理
                return;
            }
//            logger.info("Portal收到信息：" + new DateTime(tickerDTO.getT()));
            tickerPriceHashMap.put(tickerDTO.getSymbol().toUpperCase(), tickerDTO.getC());

            BigDecimal adjustPrice = DpDataUtils.getAdjustPrice(redisTemplate, tickerDTO.getSymbol());
            // 加上控制的逻辑
            if (adjustPrice == null) {
                redisTemplate.opsForValue().set(String.format(DpConstants.MARKET_LATEST_PRICE, tickerDTO.getSymbol()), tickerDTO.getC());
            } else {
                redisTemplate.opsForValue().set(String.format(DpConstants.MARKET_LATEST_PRICE, tickerDTO.getSymbol()), tickerDTO.getC().add(adjustPrice));
            }
//            logger.info("Portal收到信息：{}, {}", tickerDTO.getSymbol(), tickerDTO.getC());
            contractOrderCloseService.calcContractOrderProfit(tickerDTO.getSymbol().toUpperCase(), tickerDTO.getC());
//        contractOrderCloseService.handleContractTicker(tickerDTO);
//        coinOrderService.handleCoinOrderTicker(tickerDTO);
        } catch (Exception e) {
            logger.error("处理市场数据异常" + value, e);
        }

    }
}
