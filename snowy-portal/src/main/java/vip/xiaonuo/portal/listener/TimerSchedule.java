package vip.xiaonuo.portal.listener;

import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vip.xiaonuo.business.account.enums.AccountExceptionEnum;
import vip.xiaonuo.business.coinorder.service.CoinOrderEndService;
import vip.xiaonuo.business.contractorder.service.ContractOrderCloseService;
import vip.xiaonuo.business.contractspotorder.controller.entity.ContractSpotOrder;
import vip.xiaonuo.business.contractspotorder.controller.service.ContractSpotOrderService;
import vip.xiaonuo.business.spotorder.entity.SpotOrder;
import vip.xiaonuo.business.spotorder.service.SpotOrderService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.util.RedisLock;

import javax.annotation.Resource;
import java.util.List;

@Component
public class TimerSchedule {
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    private Logger logger = LoggerFactory.getLogger(TimerSchedule.class);
    @Resource
    private SpotOrderService spotOrderService;
    @Resource
    private ContractOrderCloseService contractOrderCloseService;
    @Resource
    private CoinOrderEndService coinOrderEndService;
    @Resource
    private ContractSpotOrderService contractSpotOrderService;

    @Scheduled(initialDelay = 20 * 1000L, fixedDelay = 5000L)
    public void endSpotOrder() {
        String redisKey = "r_spot_order_cron";
        try {
            RedisLock redisLock = new RedisLock(redisTemplate, redisKey, 0, 2 * 60 * 1000);
            try {
                if (redisLock.lock()) {
                    List<SpotOrder> spotOrders = spotOrderService.getOverdueOrder();
                    for (SpotOrder spotOrder : spotOrders) {
                        try {
                            //结束订单
                            spotOrderService.finishOrder(spotOrder.getOrderNo(),spotOrder);
                        } catch (Exception e) {
                            if (e instanceof ServiceException) {
                                ServiceException se = (ServiceException) e;
                                if (se.getExceptionEnum() == AccountExceptionEnum.FROZEN_INSUFFICIENT_BALANCE) {
                                    //余额不足
                                }
                            }
                            logger.error("结束极速订单异常{}", spotOrder.getOrderNo(), e);

                        }
                    }

                    List<ContractSpotOrder> contractSpotOrders = contractSpotOrderService.getOverdueOrder();
                    for (ContractSpotOrder spotOrder : contractSpotOrders) {
                        try {
                            //结束订单
                            contractSpotOrderService.finishOrder(spotOrder.getOrderNo(),spotOrder);
                        } catch (Exception e) {
                            if (e instanceof ServiceException) {
                                ServiceException se = (ServiceException) e;
                                if (se.getExceptionEnum() == AccountExceptionEnum.FROZEN_INSUFFICIENT_BALANCE) {
                                    //余额不足
                                }
                            }
                            logger.error("结束极速合约订单异常{}", spotOrder.getOrderNo(), e);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("获取极速合约待结束异常", e);
            } finally {
                redisLock.unlock();
            }
        } catch (Exception e) {
            logger.error("获取极速合约待结束异常", e);
        }
    }

    @Scheduled(initialDelay = 5 * 1000L, fixedDelay = 1000L)
    public void endTicker() {
        contractOrderCloseService.matchContractOrder();
        coinOrderEndService.handleCoinOrderTicker();
    }
}
