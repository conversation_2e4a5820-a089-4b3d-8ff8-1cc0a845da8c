package vip.xiaonuo.portal.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.business.coinorder.param.PlaceCoinOrderModel;
import vip.xiaonuo.business.coinorder.param.QueryCoinOrderModel;
import vip.xiaonuo.business.coinorder.result.CoinOrderVo;
import vip.xiaonuo.business.coinorder.service.CoinOrderService;
import vip.xiaonuo.business.contractorder.entity.ContractOrder;
import vip.xiaonuo.business.contractorder.enums.ContractOrderTypeEnum;
import vip.xiaonuo.business.contractorder.param.PlaceContractOrderModel;
import vip.xiaonuo.business.contractorder.param.QueryContractOrderModel;
import vip.xiaonuo.business.contractorder.param.SetStopProfitAndLossModel;
import vip.xiaonuo.business.contractorder.result.ContractOrderVo;
import vip.xiaonuo.business.contractorder.service.ContractOrderService;
import vip.xiaonuo.business.contractspotorder.controller.entity.ContractSpotOrder;
import vip.xiaonuo.business.contractspotorder.controller.service.ContractSpotOrderService;
import vip.xiaonuo.business.dict.service.SysDictDataService;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.spotorder.entity.SpotOrder;
import vip.xiaonuo.business.spotorder.param.PlaceSpotOrderModel;
import vip.xiaonuo.business.spotorder.param.QuerySpotOrderModel;
import vip.xiaonuo.business.spotorder.result.EndSpotOrderResult;
import vip.xiaonuo.business.spotorder.result.PlaceSpotOrderResult;
import vip.xiaonuo.business.spotorder.result.SpotOrderVo;
import vip.xiaonuo.business.spotorder.service.SpotOrderService;
import vip.xiaonuo.core.consts.DictConstant;
import vip.xiaonuo.core.context.constant.ConstantContextHolder;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.redis.RedisHelper;
import vip.xiaonuo.core.util.MessageUtils;
import vip.xiaonuo.portal.model.resp.TransInfoResp;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;

@RestController
@RequestMapping("order")
@Api(tags = "订单")
public class OrderController {
    @Resource
    private SysDictDataService sysDictDataService;
    @Resource
    private CoinOrderService coinOrderService;
    @Resource
    private SpotOrderService spotOrderService;
    @Resource
    private ContractOrderService contractOrderService;
    @Resource
    private PlayerService playerService;
    @Resource
    private RedisHelper redisHelper;
    @Resource
    private ContractSpotOrderService contractSpotOrderService;

    @ApiOperation("极速下单")
    @PostMapping("/spotOrder")
    public ResponseData<PlaceSpotOrderResult> spotOrder(@RequestBody @Valid PlaceSpotOrderModel model) {
        Long playerId = PrincipalUtils.getPlayerId();
        playerService.isForbidTrade(playerId);
        Integer count = spotOrderService.lambdaQuery().eq(SpotOrder::getPlayerId,playerId).eq(SpotOrder::getStatus,Integer.valueOf(0)).count();
        if(count > 0){
            throw new ServiceException(MessageUtils.get("spot.order.not.settle"));
        }

        model.setFollowerId(0L);
        model.setFollowerContractSpotOrderId(0L);
        model.setFollowerCommissionRate(BigDecimal.ZERO);
        model.setControlPre(null);
        SpotOrder spotOrder = spotOrderService.placeOrder(playerId, model);
        PlaceSpotOrderResult placeSpotOrderResult = new PlaceSpotOrderResult();
        placeSpotOrderResult.setOrderNo(spotOrder.getOrderNo());
        placeSpotOrderResult.setOrderPrice(spotOrder.getOrderPrice());
        return ResponseData.success(placeSpotOrderResult);
    }

    @ApiOperation("获取极速单")
    @PostMapping("/endSpotOrder/{orderNo}")
    public ResponseData<EndSpotOrderResult> endSpotOrder(@PathVariable(name = "orderNo") String orderNo) {
        Long playerId = PrincipalUtils.getPlayerId();
        return ResponseData.success(spotOrderService.endSpotOrder(playerId, orderNo));
    }

    @ApiOperation("极速单查询")
    @PostMapping("/getSpotOrder")
    public ResponseData<PageResult<SpotOrderVo>> getSpotOrder(@RequestBody @Valid QuerySpotOrderModel model) {
        Long playerId = PrincipalUtils.getPlayerId();
        return ResponseData.success(spotOrderService.page(playerId, model));
    }

    @ApiOperation("合约下单")
    @PostMapping("/contractOrder")
    public ResponseData<?> contractOrder(@RequestBody @Valid PlaceContractOrderModel model) {
        Boolean contractMaintain = sysDictDataService.getCommonConfigAsBoolean(DictConstant.CONTRACT_MAINTAIN);
        if(contractMaintain){
            return ResponseData.error("The system is under maintenance and currency transactions are temporarily unavailable");
        }
        Long playerId = PrincipalUtils.getPlayerId();
        playerService.isForbidTrade(playerId);
        contractOrderService.placeOrder(playerId, model);
        return ResponseData.success();
    }

    @ApiOperation("合约撤销委托")
    @PostMapping("/revokeEntrust/{orderNo}")
    public ResponseData<?> revokeEntrust(@PathVariable(name = "orderNo") String orderNo) {
        Boolean contractMaintain = sysDictDataService.getCommonConfigAsBoolean(DictConstant.CONTRACT_MAINTAIN);
        if(contractMaintain){
            return ResponseData.error("The system is under maintenance and currency transactions are temporarily unavailable");
        }
        Long playerId = PrincipalUtils.getPlayerId();
        playerService.isForbidTrade(playerId);
        contractOrderService.revokeEntrust(playerId, orderNo, playerId);
        return ResponseData.success();
    }

    @ApiOperation("合约手动平仓")
    @PostMapping("/endContractOrder/{orderNo}")
    public ResponseData<?> endContractOrder(@PathVariable(name = "orderNo") String orderNo) {
        Boolean contractMaintain = sysDictDataService.getCommonConfigAsBoolean(DictConstant.CONTRACT_MAINTAIN);
        if(contractMaintain){
            return ResponseData.error("The system is under maintenance and currency transactions are temporarily unavailable");
        }
        Long playerId = PrincipalUtils.getPlayerId();
        contractOrderService.endContractOrder(playerId, orderNo, null, 0);
        return ResponseData.success();
    }

    @ApiOperation("合约手动平仓并且追反单")
    @PostMapping("/endAndChaseContractOrder/{orderNo}")
    public ResponseData<?> endAndChaseContractOrder(@PathVariable(name = "orderNo") String orderNo) {
        Boolean contractMaintain = sysDictDataService.getCommonConfigAsBoolean(DictConstant.CONTRACT_MAINTAIN);
        if(contractMaintain){
            return ResponseData.error("The system is under maintenance and currency transactions are temporarily unavailable");
        }
        Long playerId = PrincipalUtils.getPlayerId();
        playerService.isForbidTrade(playerId);
        contractOrderService.endContractOrder(playerId, orderNo, null, 0);
        //追反单
        contractChaseOrder(playerId, orderNo, true);
        return ResponseData.success();
    }

    @ApiOperation("合约设置止盈止损")
    @PostMapping("/setStopProfitAndLoss")
    public ResponseData<?> setStopProfitAndLoss(@RequestBody @Valid SetStopProfitAndLossModel model) {
        Boolean contractMaintain = sysDictDataService.getCommonConfigAsBoolean(DictConstant.CONTRACT_MAINTAIN);
        if(contractMaintain){
            return ResponseData.error("The system is under maintenance and currency transactions are temporarily unavailable");
        }
        Long playerId = PrincipalUtils.getPlayerId();
        playerService.isForbidTrade(playerId);
        contractOrderService.setStopProfitAndLoss(playerId, model);
        return ResponseData.success();
    }

    @ApiOperation("合约追单")
    @PostMapping("/contractChaseOrder/{orderNo}")
    public ResponseData<?> contractChaseOrder(@PathVariable(name = "orderNo") String orderNo) {
        Boolean contractMaintain = sysDictDataService.getCommonConfigAsBoolean(DictConstant.CONTRACT_MAINTAIN);
        if(contractMaintain){
            return ResponseData.error("The system is under maintenance and currency transactions are temporarily unavailable");
        }
        Long playerId = PrincipalUtils.getPlayerId();
        playerService.isForbidTrade(playerId);
        contractChaseOrder(playerId, orderNo, false);
        return ResponseData.success();
    }

    private void contractChaseOrder(Long playerId, String orderNo, boolean reverse) {
        ContractOrder contractOrder = contractOrderService.getContractOrder(playerId, orderNo);
        PlaceContractOrderModel model = new PlaceContractOrderModel();
        model.setSymbol(contractOrder.getSymbol());
        if (reverse) {
            model.setType(contractOrder.getType().equals(ContractOrderTypeEnum.TYPE1.getCode()) ? ContractOrderTypeEnum.TYPE2.getCode() : ContractOrderTypeEnum.TYPE1.getCode());
        } else {
            model.setType(contractOrder.getType());
        }
        model.setEntrustType(contractOrder.getEntrustType());
        model.setQuantity(contractOrder.getQuantity());
        model.setLever(contractOrder.getLever());
        model.setPrice(contractOrder.getOrderPrice());
        if (reverse) {
            model.setStopLoss(contractOrder.getStopProfit());
            model.setStopProfit(contractOrder.getStopLoss());
        } else {
            model.setStopLoss(contractOrder.getStopLoss());
            model.setStopProfit(contractOrder.getStopProfit());
        }
        contractOrderService.placeOrder(playerId, model);
    }

    @ApiOperation("合约单查询")
    @PostMapping("/getContractOrder")
    public ResponseData<PageResult<ContractOrderVo>> getContractOrder(@RequestBody @Valid QueryContractOrderModel model) {
        Long playerId = PrincipalUtils.getPlayerId();
        return ResponseData.success(contractOrderService.page(playerId, model));
    }

    @ApiOperation("币币下单")
    @PostMapping("/coinOrder")
    public ResponseData<?> spotOrder(@RequestBody PlaceCoinOrderModel model) {
        Boolean coinMaintain = sysDictDataService.getCommonConfigAsBoolean(DictConstant.COIN_MAINTAIN);
        if(coinMaintain){
            return ResponseData.error("The system is under maintenance and currency transactions are temporarily unavailable");
        }
        Long playerId = PrincipalUtils.getPlayerId();
        playerService.isForbidTrade(playerId);
        coinOrderService.placeOrder(playerId, model);
        return ResponseData.success();
    }

    @ApiOperation("币币撤销委托")
    @PostMapping("/revokeCoin/{orderNo}")
    public ResponseData<?> revokeCoin(@PathVariable(name = "orderNo") String orderNo) {
        Boolean coinMaintain = sysDictDataService.getCommonConfigAsBoolean(DictConstant.COIN_MAINTAIN);
        if(coinMaintain){
            return ResponseData.error("The system is under maintenance and currency transactions are temporarily unavailable");
        }
        Long playerId = PrincipalUtils.getPlayerId();
        playerService.isForbidTrade(playerId);
        coinOrderService.revokeEntrust(playerId, orderNo, playerId);
        return ResponseData.success();
    }

    @ApiOperation("币币单查询")
    @PostMapping("/getCoinOrder")
    public ResponseData<PageResult<CoinOrderVo>> getCoinOrder(@RequestBody @Valid QueryCoinOrderModel model) {
        Long playerId = PrincipalUtils.getPlayerId();
        return ResponseData.success(coinOrderService.page(playerId, model));
    }

    @ApiOperation("查询成交量")
    @PostMapping("/getTransInfo")
    public ResponseData<TransInfoResp> getTransInfo() {
        String transAmountKey = "TOTAL_TRANS_AMOUNT1";
        String transOutKey = "TOTAL_TRANS_COUNT1";
        String transAmount = redisHelper.get(transAmountKey);
        Integer transCount = redisHelper.getInteger(transOutKey);
        if(ObjectUtils.isNotEmpty(transCount) && StringUtils.isNotEmpty(transAmount)){
            return ResponseData.success(new TransInfoResp(new BigDecimal(transAmount),transCount));
        }
        return ResponseData.success(new TransInfoResp());
    }


    @ApiOperation("合约极速下单")
    @PostMapping("/contractSpotOrder")
    public ResponseData<PlaceSpotOrderResult> contractSpotOrder(@RequestBody @Valid PlaceSpotOrderModel model) {
        Long playerId = PrincipalUtils.getPlayerId();
        playerService.isForbidTrade(playerId);
        Integer count = contractSpotOrderService.lambdaQuery().eq(ContractSpotOrder::getPlayerId,playerId).eq(ContractSpotOrder::getRuleId,model.getRuleId()).eq(ContractSpotOrder::getStatus,Integer.valueOf(0)).count();
        if(count > 0){
            throw new ServiceException(MessageUtils.get("spot.order.not.settle"));
        }

        model.setFollowerId(0L);
        model.setFollowerContractSpotOrderId(0L);
        model.setFollowerCommissionRate(BigDecimal.ZERO);
        model.setControlPre(null);
        ContractSpotOrder spotOrder = contractSpotOrderService.placeOrder(playerId, model);
        PlaceSpotOrderResult placeSpotOrderResult = new PlaceSpotOrderResult();
        placeSpotOrderResult.setOrderNo(spotOrder.getOrderNo());
        placeSpotOrderResult.setOrderPrice(spotOrder.getOrderPrice());
        return ResponseData.success(placeSpotOrderResult);
    }

    @ApiOperation("获取合约极速单")
    @PostMapping("/endContractSpotOrder/{orderNo}")
    public ResponseData<EndSpotOrderResult> endContractSpotOrder(@PathVariable(name = "orderNo") String orderNo) {
        Long playerId = PrincipalUtils.getPlayerId();
        return ResponseData.success(contractSpotOrderService.endSpotOrder(playerId, orderNo));
    }

    @ApiOperation("合约极速单查询")
    @PostMapping("/getContractSpotOrder")
    public ResponseData<PageResult<SpotOrderVo>> getContractSpotOrder(@RequestBody @Valid QuerySpotOrderModel model) {
        Long playerId = PrincipalUtils.getPlayerId();
        return ResponseData.success(contractSpotOrderService.page(playerId, model));
    }

}
