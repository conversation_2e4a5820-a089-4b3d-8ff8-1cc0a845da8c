package vip.xiaonuo.portal.controller.account;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.business.account.pojo.dto.ExecuteUserExchangeDto;
import vip.xiaonuo.business.account.pojo.vo.FindUserAccountVo;
import vip.xiaonuo.business.account.pojo.vo.UserAccountVo;
import vip.xiaonuo.business.account.service.AccountService;
import vip.xiaonuo.business.accountbill.entity.AccountBill;
import vip.xiaonuo.business.accountbill.param.AccountBillParam;
import vip.xiaonuo.business.accountbill.service.AccountBillService;
import vip.xiaonuo.business.currency.entity.Currency;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.currencytype.entity.CurrencyType;
import vip.xiaonuo.business.currencytype.service.CurrencyTypeService;
import vip.xiaonuo.business.depositbankaccount.entity.DepositBankAccount;
import vip.xiaonuo.business.depositbankaccount.service.DepositBankAccountService;
import vip.xiaonuo.business.exchangerecord.entity.ExchangeRecord;
import vip.xiaonuo.business.exchangerecord.result.PortalExchangeRecordResult;
import vip.xiaonuo.business.exchangerecord.service.ExchangeRecordService;
import vip.xiaonuo.business.followerrelation.entity.FollowerRelation;
import vip.xiaonuo.business.followerrelation.param.FollowerRelationParam;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.transaction.entity.Transaction;
import vip.xiaonuo.business.transaction.param.BankRechargeParam;
import vip.xiaonuo.business.transaction.param.BankWithdrawalParam;
import vip.xiaonuo.business.transaction.param.RechargeParam;
import vip.xiaonuo.business.transaction.param.WithdrawalParam;
import vip.xiaonuo.business.transaction.pojo.dto.ExecuteRechargeUserDto;
import vip.xiaonuo.business.transaction.pojo.dto.ExecuteWithdrawalUserDto;
import vip.xiaonuo.business.transaction.pojo.dto.FindUserTransactionDto;
import vip.xiaonuo.business.transaction.pojo.vo.FindUserTransactionVo;
import vip.xiaonuo.business.transaction.service.TransactionService;
import vip.xiaonuo.business.upay.service.UpayService;
import vip.xiaonuo.business.userbankaccount.entity.PlayerBankAccount;
import vip.xiaonuo.business.userbankaccount.service.PlayerBankAccountService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.util.ConvertUtils;
import vip.xiaonuo.core.util.DateUtil;
import vip.xiaonuo.core.util.Language;
import vip.xiaonuo.core.util.MessageUtils;
import vip.xiaonuo.business.upay.pojo.dto.OrderAsynNoticeDto;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;
import javax.xml.ws.Service;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户账户信息
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:39
 */
@Api(tags = "账户")
@RestController
@Slf4j
public class AccountController {
    @Resource
    private TransactionService transactionService;
    @Resource
    private AccountService accountService;
    @Resource
    private AccountBillService accountBillService;
    @Resource
    private ExchangeRecordService exchangeRecordService;
    @Resource
    private DepositBankAccountService depositBankAccountService;
    @Resource
    private PlayerBankAccountService playerBankAccountService;
    @Resource
    private PlayerService playerService;
    @Resource
    private UpayService uPayService;
    @Resource
    private CurrencyTypeService currencyTypeService;
//    @ApiOperation("用户-割转")
//    @PostMapping("/account/cutTurn")
//    public ResponseData cutTurn(@RequestBody ExecuteUserCutTurnDto dto) {
//        if (ObjectUtils.isEmpty(dto)) {
//            return ResponseData.error(MessageUtils.get("error"));
//        }
//        dto.setPlayerId(PrincipalUtils.getPlayerId());
//        return accountService.executeUserCutTurn(dto);
//    }

    @ApiOperation("兑换币种列表")
    @PostMapping("/account/exchangeList")
    public ResponseData<List<UserAccountVo>> exchangeList() {
        Long playerId = PrincipalUtils.getPlayerId();
        List<UserAccountVo> userAccountVos = accountService.exchangeList(playerId);
        return ResponseData.success(userAccountVos);
    }

    @ApiOperation("用户-兑换")
    @PostMapping("/account/exchange")
    public ResponseData exchange(@RequestBody ExecuteUserExchangeDto dto) {
        if (ObjectUtils.isEmpty(dto)) {
            return ResponseData.error(MessageUtils.get("error"));
        }
        dto.setPlayerId(PrincipalUtils.getPlayerId());
        if (ObjectUtils.isEmpty(dto)) {
            return ResponseData.error(MessageUtils.get("error"));
        }
        if (ObjectUtils.isEmpty(dto.getPlayerId())) {
            return ResponseData.error(MessageUtils.get("user.is.null.error"));
        }
        if (ObjectUtils.isEmpty(dto.getTransferOutCode())) {
            return ResponseData.error(MessageUtils.get("error"));
        }
        if (ObjectUtils.isEmpty(dto.getTransferOutAccount())) {
            return ResponseData.error(MessageUtils.get("rate.account.is.null.error"));
        }
        if (ObjectUtils.isEmpty(dto.getChangeIntoCode())) {
            return ResponseData.error(MessageUtils.get("error"));
        }
        playerService.isForbidTrade(PrincipalUtils.getPlayerId());
        return accountService.executeUserExchange(dto);
    }

    @ApiOperation("用户-兑换记录")
    @GetMapping("/account/exchangeRecord")
    public ResponseData<PageResult<PortalExchangeRecordResult>> exchangeRecord() {
        Long playerId = PrincipalUtils.getPlayerId();
        Page<ExchangeRecord> page = exchangeRecordService.page(PageFactory.defaultPage(), Wrappers.<ExchangeRecord>lambdaQuery().eq(ExchangeRecord::getPlayerId, playerId).orderByDesc(ExchangeRecord::getOrderTime));
        Map<Long, ExchangeRecord> exchangeRecordMap = page.getRecords().stream().collect(Collectors.toMap(ExchangeRecord::getId, t -> t));
        IPage<PortalExchangeRecordResult> page1 = ConvertUtils.convert(page, PortalExchangeRecordResult.class);
        Language lang = Language.getLanguage();
        for (PortalExchangeRecordResult record : page1.getRecords()) {
            ExchangeRecord exchangeRecord = exchangeRecordMap.get(record.getId());
            if (exchangeRecord != null) {
                if (lang == Language.vi_VN) {
                    record.setCreateTime(DateUtil.format(exchangeRecord.getCreateTime(), DateUtil.VN_DATEFORMATSECOND));
                } else {
                    record.setCreateTime(DateUtil.format(exchangeRecord.getCreateTime(), DateUtil.DATEFORMATSECOND));
                }
            }
        }
        return ResponseData.success(new PageResult<>(page1));
    }

    @ApiOperation("用户-资产")
    @PostMapping("/account/list")
    public ResponseData<FindUserAccountVo> findUserAccount() {
        return accountService.findUserAccount(PrincipalUtils.getPlayerId());
    }

//    @ApiOperation("用户-币币账户余额")
//    @PostMapping("/account/findUserCurrencyAccount")
//    public ResponseData findUserCurrencyAccount() {
//        return accountService.findAccountByTypeAndCurrencyCode(PrincipalUtils.getPlayerId(), "USDT", AccountTypeEnum.TYPE1.getCode());
//    }

//    @ApiOperation("用户-币币账户")
//    @PostMapping("/account/findUserCurrencyAccountList")
//    public ResponseData findUserCurrencyAccountList() {
//        return accountService.findUserCurrencyAccountList(PrincipalUtils.getPlayerId());
//    }

    @ApiOperation("用户-账户币种余额")
    @PostMapping("/account/balance")
    public ResponseData<BigDecimal> findAccountByTypeAndCurrencyCode(@ApiParam(value = "类型:1账户资产,2币币资产") @RequestParam Integer type, @RequestParam String currencyCode) {
        return accountService.findAccountByTypeAndCurrencyCode(PrincipalUtils.getPlayerId(), currencyCode, type);
    }

    @ApiOperation("用户-账户记录")
    @ResponseBody
    @PostMapping("/account/bill")
    public ResponseData<PageResult<AccountBill>> page(@RequestBody AccountBillParam param) {
        if (ObjectUtils.isEmpty(param)) {
            param = new AccountBillParam();
        }
        //用户id
        param.setPlayerId(PrincipalUtils.getPlayerId());
        PageResult<AccountBill> pageResult = accountBillService.page(param);
        pageResult.getData().forEach(p -> {
            p.setCreateUser(null);
            p.setUpdateUser(null);
        });
        return ResponseData.success(pageResult);
    }

    @ApiOperation("获取充值银行账户")
    @ResponseBody
    @GetMapping("/transaction/getRechargeBankAccount")
    public ResponseData<DepositBankAccount> rechargeBankAccount() {
        DepositBankAccount depositBankAccount = depositBankAccountService.lambdaQuery().last(" limit 1").one();
        return ResponseData.success(depositBankAccount);
    }

    /**
     * 用户充值
     */
    @ApiOperation("充值-虚拟币")
    @ResponseBody
    @PostMapping("/transaction/recharge")
    public ResponseData executeRechargeUser(@RequestBody RechargeParam rechargeParam) {
        CurrencyType currencyType = currencyTypeService.lambdaQuery().eq(CurrencyType::getCurrencyCode,rechargeParam.getCurrencyCode()).one();
        if (ObjectUtils.isEmpty(rechargeParam.getQuantity()) || (ObjectUtils.isNotEmpty(currencyType) && rechargeParam.getQuantity().compareTo(currencyType.getMinAmount()) < 0)) {
            return ResponseData.error(MessageUtils.get("transaction.recharge..account.fee.error2")+" "+currencyType.getMinAmount()+rechargeParam.getCurrencyCode());
        }
        ExecuteRechargeUserDto executeRechargeUserDto = new ExecuteRechargeUserDto();
        executeRechargeUserDto.setAccountType(1);
        executeRechargeUserDto.setPlayerId(PrincipalUtils.getPlayerId());
        executeRechargeUserDto.setStatus(1);
        executeRechargeUserDto.setTransactionType(rechargeParam.getCurrencyCode());
        executeRechargeUserDto.setAccount(rechargeParam.getQuantity());
        executeRechargeUserDto.setCurrencyAddress(rechargeParam.getCurrencyAddress());
        executeRechargeUserDto.setPictureUrl(rechargeParam.getPictureUrl());
        executeRechargeUserDto.setToAddress(rechargeParam.getToAddress());
        executeRechargeUserDto.setRechargeChannel(StringUtils.isNotEmpty(rechargeParam.getChainType()) ? rechargeParam.getChainType() : "玩家充值");
        return transactionService.executeRechargeUser(executeRechargeUserDto);
    }

    @ApiOperation("充值-法币")
    @ResponseBody
    @PostMapping("/transaction/bankRecharge")
    public ResponseData executeRechargeUser(@RequestBody BankRechargeParam rechargeParam) {
        CurrencyType currencyType = currencyTypeService.lambdaQuery().eq(CurrencyType::getCurrencyCode,rechargeParam.getCurrencyCode()).one();
        if (ObjectUtils.isEmpty(rechargeParam.getQuantity()) || (ObjectUtils.isNotEmpty(currencyType) && rechargeParam.getQuantity().compareTo(currencyType.getMinAmount()) < 0)) {
            return ResponseData.error(MessageUtils.get("transaction.recharge..account.fee.error2")+" "+currencyType.getMinAmount()+rechargeParam.getCurrencyCode());
        }
        ExecuteRechargeUserDto executeRechargeUserDto = new ExecuteRechargeUserDto();
        executeRechargeUserDto.setAccountType(1);
        executeRechargeUserDto.setPlayerId(PrincipalUtils.getPlayerId());
        executeRechargeUserDto.setStatus(1);
        executeRechargeUserDto.setTransactionType(rechargeParam.getCurrencyCode());
        executeRechargeUserDto.setAccount(rechargeParam.getQuantity());
        executeRechargeUserDto.setPictureUrl(rechargeParam.getPictureUrl());

        executeRechargeUserDto.setBankDeposit(rechargeParam.getUsername());
        executeRechargeUserDto.setBankCard(rechargeParam.getBankCardNo());
        executeRechargeUserDto.setToAddress(rechargeParam.getToAddress());
        executeRechargeUserDto.setRechargeChannel("玩家充值");
        return transactionService.executeRechargeUser(executeRechargeUserDto);
    }

    @ApiOperation("提现-虚拟币")
    @ResponseBody
    @PostMapping("/transaction/withdrawal")
    public ResponseData executeWithdrawalUser(@RequestBody WithdrawalParam withdrawalParam) {
        Long playerId = PrincipalUtils.getPlayerId();
        playerService.isForbidTrade(playerId);
        ExecuteWithdrawalUserDto transactionParam = new ExecuteWithdrawalUserDto();
        transactionParam.setAccountType(1);
        transactionParam.setPlayerId(PrincipalUtils.getPlayerId());
        transactionParam.setStatus(1);
        transactionParam.setTransactionType(withdrawalParam.getCurrencyCode());
        transactionParam.setAccount(withdrawalParam.getQuantity());
        transactionParam.setCurrencyAddress(withdrawalParam.getCurrencyAddress());
        transactionParam.setPassword(withdrawalParam.getPassword());
//        List<Transaction> transactionList = transactionService.lambdaQuery().eq(Transaction::getPlayerId,playerId).eq(Transaction::getOperationType,"1").eq(Transaction::getStatus,"2").list();
//        if(CollectionUtils.isEmpty(transactionList)) {
//            throw new ServiceException(MessageUtils.get("ransaction.withdraw..error"));
//        }
        return transactionService.executeWithdrawalUser(transactionParam,true);
    }

    @ApiOperation("提现-法币")
    @ResponseBody
    @PostMapping("/transaction/bankWithdrawal")
    public ResponseData bankWithdrawal(@RequestBody BankWithdrawalParam withdrawalParam) {
        Long playerId = PrincipalUtils.getPlayerId();
        playerService.isForbidTrade(playerId);
        PlayerBankAccount playerBankAccount = playerBankAccountService.getById(withdrawalParam.getBankAccountId());
        if (playerBankAccount == null || !playerId.equals(playerBankAccount.getPlayerId())) {
            return ResponseData.error("bank null");
        }
        ExecuteWithdrawalUserDto transactionParam = new ExecuteWithdrawalUserDto();
        transactionParam.setAccountType(1);
        transactionParam.setPlayerId(PrincipalUtils.getPlayerId());
        transactionParam.setStatus(1);
        transactionParam.setTransactionType(withdrawalParam.getCurrencyCode());
        transactionParam.setAccount(withdrawalParam.getQuantity());
        transactionParam.setPassword(withdrawalParam.getPassword());
        transactionParam.setBankDeposit(playerBankAccount.getUserName());
        transactionParam.setBankName(playerBankAccount.getBankName());
        transactionParam.setBankCard(playerBankAccount.getBankCardNumber());
        return transactionService.executeWithdrawalUser(transactionParam,true);
    }

    /**
     * 充值提现记录
     *
     * <AUTHOR>
     * @date 2022-04-25 15:48:06
     */
    @ApiOperation("用户-充值提现记录")
    @ResponseBody
    @PostMapping("/transaction/page")
    public ResponseData<PageResult<FindUserTransactionVo>> page(@RequestBody FindUserTransactionDto currencyTypeParam) {
        currencyTypeParam.setPlayerId(PrincipalUtils.getPlayerId());
        return ResponseData.success(transactionService.findUserTransaction(currencyTypeParam));
    }

    @ApiOperation("upay-回调通知")
    @PostMapping("/transaction/order/notice")
    public String notice(@RequestBody OrderAsynNoticeDto orderAsynNoticeDto) {
        log.info("upay-回调请求参数: {}", JSONObject.toJSONString(orderAsynNoticeDto));
        if(ObjectUtils.isEmpty(orderAsynNoticeDto) || StringUtils.isAnyEmpty(orderAsynNoticeDto.getAppId(),orderAsynNoticeDto.getMerchantOrderNo(),orderAsynNoticeDto.getOrderNo(),orderAsynNoticeDto.getCrypto(),orderAsynNoticeDto.getCompletedAt(),orderAsynNoticeDto.getSignature())){
            return MessageUtils.get("error");
        }
        String result = uPayService.callbackNotice(orderAsynNoticeDto);
        return result;
    }
}
