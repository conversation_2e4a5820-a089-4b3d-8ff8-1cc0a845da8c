package vip.xiaonuo.portal.controller.currency;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.currency.entity.Currency;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.currencytype.entity.CurrencyType;
import vip.xiaonuo.business.currencytype.param.CurrencyTypeParam;
import vip.xiaonuo.business.currencytype.service.CurrencyTypeService;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags = "币种")
@Controller
public class CurrencyController {
    @Resource
    private CurrencyTypeService currencyTypeService;
    @Resource
    private CurrencyService currencyService;
//
//    @ApiOperation("可用用充值币种")
//    @ResponseBody
//    @GetMapping("/currency/chargeCurrencyList")
//    public ResponseData<List<Currency>> getChargeCurrencyList() {
//        return new SuccessResponseData(currencyService.getChargeCurrencyList());
//    }

    @ApiOperation("可用用提现币种")
    @ResponseBody
    @GetMapping("/currency/withdrawCurrencyList")
    public ResponseData<List<Currency>> getWithdrawCurrencyList() {
        return new SuccessResponseData(currencyService.getWithdrawCurrencyList());
    }

    @ApiOperation("可用用充值币种")
    @ResponseBody
    @GetMapping("/currency/chargeCurrencyList")
    public ResponseData<List<CurrencyType>> list() {
        CurrencyTypeParam currencyTypeParam = new CurrencyTypeParam();
        List<CurrencyType> list = currencyTypeService.queryList(currencyTypeParam);
        List<String> codes = list.stream().map(CurrencyType::getCurrencyCode).collect(Collectors.toList());
        Map<String, String> logoMap = currencyService.getLogoMap(codes);
        list.forEach(p -> {
            p.setCurrencyLogo(logoMap.get(p.getCurrencyCode()));
        });
        return new SuccessResponseData(list);
    }
}
