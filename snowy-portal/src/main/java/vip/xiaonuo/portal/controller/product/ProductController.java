//package vip.xiaonuo.portal.controller.product;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.apache.commons.lang3.ObjectUtils;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.ResponseBody;
//import vip.xiaonuo.business.product.param.ProductParam;
//import vip.xiaonuo.business.product.service.ProductService;
//import vip.xiaonuo.core.annotion.BusinessLog;
//import vip.xiaonuo.core.annotion.Permission;
//import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
//import vip.xiaonuo.core.pojo.response.ResponseData;
//import vip.xiaonuo.core.util.Language;
//import vip.xiaonuo.core.util.MessageUtils;
//import vip.xiaonuo.session.utils.PrincipalUtils;
//
//import javax.annotation.Resource;
//
///**
// * 理财产品控制器
// *
// * <AUTHOR>
// * @date 2022-04-06 18:42:39
// */
//@Api(tags = "理财产品")
//@Controller
//public class ProductController {
//
//    private String PATH_PREFIX = "product/";
//
//    @Resource
//    private ProductService productService;
//
//    /**
//     * 查询理财产品
//     *
//     * <AUTHOR>
//     * @date 2022-04-06 18:42:39
//     */
//    @ApiOperation("理财产品_查询列表")
//    @Permission
//    @ResponseBody
//    @GetMapping("/product/page")
//    @BusinessLog(title = "理财产品_查询", opType = LogAnnotionOpTypeEnum.QUERY)
//    public ResponseData page(ProductParam productParam) {
//        if(ObjectUtils.isNotEmpty(productParam)){
//            //前端只查询启用的理财产品
//            productParam.setProductStatus(1);
//            productParam.setOrgCode(PrincipalUtils.getOrgCode());
//            productParam.setLanguageCode(Language.getLanguage().name());
//        }
//        return ResponseData.success(productService.page(productParam));
//    }
//
//    /**
//     * 查询理财产品
//     *
//     * <AUTHOR>
//     * @date 2022-04-06 18:42:39
//     */
//    @ApiOperation("理财产品_根据id查询详情")
//    @Permission
//    @ResponseBody
//    @GetMapping("/product/findProductById")
//    @BusinessLog(title = "理财产品_查询", opType = LogAnnotionOpTypeEnum.QUERY)
//    public ResponseData findProductById(ProductParam productParam) {
//        if(ObjectUtils.isEmpty(productParam) || ObjectUtils.isEmpty(productParam.getId())){
//            //前端只查询启用的理财产品
//            return ResponseData.error(MessageUtils.get("error"));
//        }
//        return ResponseData.success(productService.getById(productParam.getId()));
//    }
//}
