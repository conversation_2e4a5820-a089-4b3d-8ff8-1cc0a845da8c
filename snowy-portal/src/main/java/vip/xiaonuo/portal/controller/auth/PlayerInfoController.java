package vip.xiaonuo.portal.controller.auth;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.playerinfo.entity.PlayerInfo;
import vip.xiaonuo.business.playerinfo.param.PlayerAdvancedCertificationParam;
import vip.xiaonuo.business.playerinfo.param.PlayerJuniorCertificationParam;
import vip.xiaonuo.business.playerinfo.service.PlayerInfoService;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;

/**
 * 认证提交
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:38
 */
@Api(tags = "玩家认证")
@Controller
public class PlayerInfoController {

    @Resource
    private PlayerInfoService playerInfoService;

    /**
     * 编辑认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    @ApiOperation("提交-初级认证")
    @ResponseBody
    @PostMapping("/playerInfo/junior")
    public ResponseData junior(@RequestBody @Validated PlayerJuniorCertificationParam playerInfoParam) {
        Long playerId = PrincipalUtils.getPlayerId();
        playerInfoService.junior(playerId, playerInfoParam);
        return new SuccessResponseData();
    }

    @ApiOperation("提交-高级认证")
    @ResponseBody
    @PostMapping("/playerInfo/advanced")
    public ResponseData advanced(@RequestBody @Validated PlayerAdvancedCertificationParam playerInfoParam) {
        Long playerId = PrincipalUtils.getPlayerId();
        playerInfoService.advanced(playerId, playerInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 根据用户id获取认证信息
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    @ApiOperation("用户-查看提交认证信息")
    @ResponseBody
    @GetMapping("/playerInfo/detail")
    public ResponseData<PlayerInfo> detail() {
        Long usId = PrincipalUtils.getPlayerId();
        return new SuccessResponseData(playerInfoService.queryPlayerUserInfo(usId));
    }


}
