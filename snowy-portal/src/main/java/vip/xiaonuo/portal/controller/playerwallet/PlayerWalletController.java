package vip.xiaonuo.portal.controller.playerwallet;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.playerwallet.entity.PlayerWallet;
import vip.xiaonuo.business.playerwallet.param.PortalPlayerWalletParam;
import vip.xiaonuo.business.playerwallet.service.PlayerWalletService;
import vip.xiaonuo.core.factory.PageFactory;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户钱包控制器
 */
@Api(tags = "钱包地址")
@Controller
public class PlayerWalletController {


    @Resource
    private PlayerWalletService playerWalletService;
    @Resource
    private CurrencyService currencyService;

    /**
     * 查询用户钱包
     *
     * <AUTHOR>
     * @date 2022-04-26 10:36:18
     */
    @ApiOperation("用户-地址分页列表")
    @ResponseBody
    @GetMapping("/playerWallet/page")
    public ResponseData<PageResult<PlayerWallet>> page() {
        Page<PlayerWallet> playerWalletPage = playerWalletService.page(PageFactory.defaultPage(), Wrappers.<PlayerWallet>lambdaQuery().eq(PlayerWallet::getPlayerId, PrincipalUtils.getPlayerId()).orderByDesc(PlayerWallet::getCreateTime));
        List<String> codes = playerWalletPage.getRecords().stream().map(PlayerWallet::getCurrencyCode).distinct().collect(Collectors.toList());
        Map<String, String> logoMap = currencyService.getLogoMap(codes);
        playerWalletPage.getRecords().forEach(p -> {
            p.setLogo(logoMap.get(p.getCurrencyCode()));
        });
        return ResponseData.success(new PageResult<>(playerWalletPage));
    }

    /**
     * 添加用户钱包
     *
     * <AUTHOR>
     * @date 2022-04-26 10:36:18
     */
    @ApiOperation("用户-钱包地址新增")
    @ResponseBody
    @PostMapping("/playerWallet/add")
    public ResponseData add(@RequestBody @Validated PortalPlayerWalletParam playerWalletParam) {
        PlayerWallet playerWallet = new PlayerWallet();
        playerWallet.setPlayerId(PrincipalUtils.getPlayerId());
        playerWallet.setCurrencyCode(playerWalletParam.getCurrencyCode());
        playerWallet.setWalletAddress(playerWalletParam.getWalletAddress());
        playerWallet.setStatus(0);
        playerWalletService.save(playerWallet);
        return new SuccessResponseData();
    }

    /**
     * 删除用户钱包
     *
     * <AUTHOR>
     * @date 2022-04-26 10:36:18
     */
    @ApiOperation("用户-钱包地址删除")
    @ResponseBody
    @PostMapping("/playerWallet/delete")
    public ResponseData delete(@RequestParam Long id) {
        Long playerId = PrincipalUtils.getPlayerId();
        playerWalletService.lambdaUpdate().eq(PlayerWallet::getPlayerId, playerId).eq(PlayerWallet::getId, id).remove();
        return new SuccessResponseData();
    }

//    /**
//     * 编辑用户钱包
//     *
//     * <AUTHOR>
//     * @date 2022-04-26 10:36:18
//     */
//    @ApiOperation("用户-钱包地址编辑")
//    @ResponseBody
//    @PostMapping("/playerWallet/edit")
//    public ResponseData edit(@RequestBody @Validated(PlayerWalletParam.edit.class) PlayerWalletParam playerWalletParam) {
//        playerWalletParam.setPlayerId(PrincipalUtils.getPlayerId());
//        playerWalletService.edit(playerWalletParam);
//        return new SuccessResponseData();
//    }

//    /**
//     * 查看用户钱包
//     *
//     * <AUTHOR>
//     * @date 2022-04-26 10:36:18
//     */
//    @ApiOperation("用户-钱包地址查看")
//    @ResponseBody
//    @GetMapping("/playerWallet/detail")
//    public ResponseData detail(@Validated(PlayerWalletParam.detail.class) PlayerWalletParam playerWalletParam) {
//        playerWalletParam.setPlayerId(PrincipalUtils.getPlayerId());
//        return new SuccessResponseData(playerWalletService.detail(playerWalletParam));
//    }

    /**
     * 用户钱包列表
     *
     * <AUTHOR>
     * @date 2022-04-26 10:36:18
     */
//    @ApiOperation("用户-钱包地址列表")
//    @ResponseBody
//    @GetMapping("/playerWallet/list")
//    public ResponseData<List<PlayerWallet>> list() {
//        PlayerWalletParam playerWalletParam = new PlayerWalletParam();
//        playerWalletParam.setPlayerId(PrincipalUtils.getPlayerId());
//        return new SuccessResponseData(playerWalletService.list(playerWalletParam));
//    }

}
