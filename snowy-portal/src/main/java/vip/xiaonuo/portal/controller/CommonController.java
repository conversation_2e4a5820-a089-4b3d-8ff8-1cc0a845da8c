package vip.xiaonuo.portal.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vip.xiaonuo.business.areacode.entity.AreaCode;
import vip.xiaonuo.business.areacode.service.AreaCodeService;
import vip.xiaonuo.business.bank.entity.Bank;
import vip.xiaonuo.business.bank.service.BankService;
import vip.xiaonuo.business.contractorder.enums.ContractOrderLeverEnum;
import vip.xiaonuo.business.dict.service.SysDictDataService;
import vip.xiaonuo.business.notice.entity.PlayerNotice;
import vip.xiaonuo.business.notice.enums.PlayerNoticeStatusEnum;
import vip.xiaonuo.business.notice.service.PlayerNoticeService;
import vip.xiaonuo.business.richtext.entity.RichText;
import vip.xiaonuo.business.richtext.service.RichTextService;
import vip.xiaonuo.business.spotcontractrule.service.SpotContractRuleService;
import vip.xiaonuo.business.spotrule.result.PortalSpotRuleResult;
import vip.xiaonuo.business.spotrule.service.SpotRuleService;
import vip.xiaonuo.business.syscountry.entity.SysCountry;
import vip.xiaonuo.business.syscountry.service.SysCountryService;
import vip.xiaonuo.core.consts.DictConstant;
import vip.xiaonuo.core.context.constant.ConstantContextHolder;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.core.util.DateUtils;
import vip.xiaonuo.core.util.FileUploadUtils;
import vip.xiaonuo.core.util.Language;
import vip.xiaonuo.core.util.MessageUtils;
import vip.xiaonuo.portal.model.resp.ReportResp;
import vip.xiaonuo.session.utils.PrincipalUtils;
import vip.xiaonuo.telegrambot.TelegramBot;
import vip.xiaonuo.telegrambot.TelegramBotConfig;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

@RestController
@Api(tags = "公共")
@RequestMapping("common")
public class CommonController {
    @Resource
    private TelegramBot telegramBot;
    @Resource
    private AreaCodeService areaCodeService;
    @Resource
    private SpotRuleService spotRuleService;
    @Autowired
    SysCountryService sysCountryService;
    @Resource
    private SysDictDataService sysDictDataService;
    @Resource
    private BankService bankService;
    @Resource
    private PlayerNoticeService playerNoticeService;
    @Resource
    private SpotContractRuleService spotContractRuleService;
    @Resource
    private RichTextService richTextService;

    @GetMapping("/telegramBot/set")
    public ResponseData<Boolean> telegramBotSet(HttpServletRequest request) {
        String devChatId = request.getParameter("devChatId");
        String busChatId = request.getParameter("busChatId");
        String testSend = request.getParameter("testSend");
        TelegramBotConfig telegramBotConfig = telegramBot.getTelegramBotConfig();
        if (StrUtil.isNotEmpty(devChatId)) {
            telegramBotConfig.setDevChatId(devChatId);
        }
        if (StrUtil.isNotEmpty(busChatId)) {
            telegramBotConfig.setBusChatId(busChatId);
        }
        if (StrUtil.isNotEmpty(testSend)) {
            telegramBot.sendDevMessage(testSend);
        }
        return new SuccessResponseData<>(Boolean.TRUE);
    }

    @GetMapping("/richText/info")
    @ApiOperation(value = "获取富文本信息")
    public ResponseData<List<RichText>> richText(@RequestParam Integer type) {
        Language language = Language.getLanguage();
        List<RichText> list = richTextService.lambdaQuery().eq(RichText::getLang, language.name()).eq(RichText::getType, type).list();
        if (CollectionUtil.isEmpty(list)) {
            list = richTextService.lambdaQuery().eq(RichText::getLang, Language.vi_VN.name()).eq(RichText::getType, type).list();
        }
        return new SuccessResponseData<>(list);
    }

    @GetMapping("/areaCode/list")
    @ApiOperation(value = "获取国际区号")
    public ResponseData<List<AreaCode>> list() {
        String orgCode = PrincipalUtils.getOrgCode();
        Language language = Language.getLanguage();
        List<AreaCode> areaCodeList = areaCodeService.list(Wrappers.<AreaCode>lambdaQuery().eq(AreaCode::getOrgCode, orgCode).eq(AreaCode::getStatus, 1));
        for (AreaCode areaCode : areaCodeList) {
            areaCode.setId(null);
            areaCode.setCreateUser(null);
            areaCode.setUpdateUser(null);
            if (language != Language.zh_CN) {
                areaCode.setCnName(areaCode.getEnName());
            }
            //这里后续只会返回一个国际区号,先写死一个国际化提示
            areaCode.setPhoneErrorMessage(MessageUtils.get("sms.format.number.phone"));
        }
        return new SuccessResponseData<>(areaCodeList);
    }

    @GetMapping("/sysCountry/list")
    @ApiOperation(value = "获取国家")
    public ResponseData<List<SysCountry>> sysCountry() {
        List<SysCountry> countryList = sysCountryService.lambdaQuery().eq(SysCountry::getDelFlag, Boolean.FALSE).list();
        Language language = Language.getLanguage();
        countryList.forEach(sysCountry -> {
            if (language == Language.en_US) {
                sysCountry.setCountryName(sysCountry.getEnglishName());
            } else if (language == Language.vi_VN) {
                sysCountry.setCountryName(sysCountry.getViName());
            } else if (language == Language.id_ID) {
                sysCountry.setCountryName(sysCountry.getIdName());
            }
        });
        return ResponseData.success(countryList);
    }

    @GetMapping("/spotRule/list")
    @ApiOperation(value = "获取极速下单规则")
    public ResponseData<HashMap<String, Object>> spotRule(@RequestParam(required = false) String symbol) {
        List<PortalSpotRuleResult> list = spotRuleService.queryAll(symbol);
        return ResponseData.success(new HashMap<String, Object>() {{
            put("fee", sysDictDataService.getCommonConfigAsString(DictConstant.SPOT_FEE));
            put("list", list);
        }});
    }

    @GetMapping("/contractRule")
    @ApiOperation(value = "获取合约下单规则")
    public ResponseData<HashMap<String, Object>> contractRule() {
        return ResponseData.success(new HashMap<String, Object>() {{
            put("fee", sysDictDataService.getCommonConfigAsString(DictConstant.CONTRACT_FEE));
//            put("entrustType", ContractOrderEntrustTypeEnum.toWebList());
            put("lever", ContractOrderLeverEnum.toList());
        }});
    }

    @ApiOperation("兑换手续费率")
    @GetMapping("/exchangeRule")
    public ResponseData<HashMap<String, Object>> exchangeRule() {
        return ResponseData.success(new HashMap<String, Object>() {{
            put("fee", sysDictDataService.getCommonConfigAsString(DictConstant.EXCHANGE_FEE));
        }});
    }

    @GetMapping("/getConfig")
    @ApiOperation(value = "获取公共配置")
    public ResponseData<HashMap<String, Object>> getConfig() {
        Boolean needInviteCodeSwitch = sysDictDataService.getCommonConfigAsBoolean(DictConstant.NEED_INVITE_CODE_SWITCH);
        return ResponseData.success(new HashMap<String, Object>() {{
            put("needSmsSwitch", sysDictDataService.getCommonConfigAsBoolean(DictConstant.NEED_SMS_SWITCH));
            put("needInviteCodeSwitch", needInviteCodeSwitch);
        }});
    }

    @ApiOperation("文件上传")
    @PostMapping("/file/upload")
    public ResponseData upload(@RequestPart("file") MultipartFile file) {
        String s3UploadFilePath = ConstantContextHolder.getSysConfigWithDefault("s3_upload_file_path", String.class, "/coin/player");
        return new SuccessResponseData(FileUploadUtils.uploadS3(s3UploadFilePath, file, false));
    }

    @ApiOperation("文件预览")
    @GetMapping("/file/previewFile")
    public void previewFile(@RequestParam String filename, HttpServletResponse response) {
        FileUploadUtils.preview(filename, response);
    }

    @GetMapping("/bank/list")
    @ApiOperation(value = "获取银行列表")
    public ResponseData<List<Bank>> bankList() {
        return new SuccessResponseData<>(bankService.list());
    }

    @PostMapping("/website/report/list")
    @ApiOperation(value = "获取报告列表")
    public ResponseData<List<ReportResp>> reportList() {
        List<PlayerNotice> noticeList = playerNoticeService.lambdaQuery().eq(PlayerNotice::getLanguageCode, Language.getLanguage().name()).eq(PlayerNotice::getStatus, PlayerNoticeStatusEnum.PUBLIC.getCode()).eq(PlayerNotice::getType, "3").orderByDesc(PlayerNotice::getCreateTime).list();
        List<ReportResp> respList = Lists.newArrayList();
        noticeList.stream().forEach(notice -> {
            ReportResp reportResp = new ReportResp();
            BeanUtils.copyProperties(notice, reportResp);
            if (Language.getLanguage().name().equals(Language.zh_CN.name())) {
                reportResp.setReportTime(DateUtils.formatDate(notice.getCreateTime(), "yyyy-MM-dd"));
            } else {
                reportResp.setReportTime(DateUtils.formatDate(notice.getCreateTime(), "dd/MM/yyyy"));
            }
            respList.add(reportResp);
        });
        return SuccessResponseData.success(respList);
    }

    @GetMapping("/website/report/{id}")
    @ApiOperation(value = "获取报告详情")
    public ResponseData<ReportResp> reportDetail(@PathVariable Long id) {
        PlayerNotice notice = playerNoticeService.getById(id);
        ReportResp reportResp = new ReportResp();
        BeanUtils.copyProperties(notice, reportResp);
        if (Language.getLanguage().name().equals(Language.zh_CN.name())) {
            reportResp.setReportTime(DateUtils.formatDate(notice.getCreateTime(), "yyyy-MM-dd"));
        } else {
            reportResp.setReportTime(DateUtils.formatDate(notice.getCreateTime(), "dd/MM/yyyy"));
        }
        return SuccessResponseData.success(reportResp);
    }

    @GetMapping("/spotContractRule/list")
    @ApiOperation(value = "获取合约极速下单规则")
    public ResponseData<HashMap<String, Object>> spotContractRule() {
        List<PortalSpotRuleResult> list = spotContractRuleService.queryAll();
        return ResponseData.success(new HashMap<String, Object>() {{
            put("fee", sysDictDataService.getCommonConfigAsString(DictConstant.SPOT_FEE));
            put("list", list);
        }});
    }
}
