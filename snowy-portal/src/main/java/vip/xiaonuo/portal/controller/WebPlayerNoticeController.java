package vip.xiaonuo.portal.controller;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.business.notice.entity.PlayerNotice;
import vip.xiaonuo.business.notice.entity.PlayerRotation;
import vip.xiaonuo.business.notice.enums.PlayNoticeTypeEnum;
import vip.xiaonuo.business.notice.enums.PlayerRotationStatusEnum;
import vip.xiaonuo.business.notice.param.PlayerNoticeParam;
import vip.xiaonuo.business.notice.param.PlayerRotationParam;
import vip.xiaonuo.business.notice.service.PlayerNoticeService;
import vip.xiaonuo.business.notice.service.PlayerRotationService;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.page.PageTo;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.util.Language;
import vip.xiaonuo.portal.model.resp.PlayerNoticeResp;
import vip.xiaonuo.portal.model.resp.PlayerRotationResp;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@Api(tags = "公告+帮助+轮播")
@RequestMapping("/player")
public class WebPlayerNoticeController {

    @Resource
    private PlayerNoticeService playerNoticeService;

    @Resource
    private PlayerRotationService playerRotationService;

    @ApiOperation("查询首页轮播列表")
    @PostMapping("/rotation/list")
    public ResponseData<List<PlayerRotationResp>> getRotationList() {
        PlayerRotationParam param = new PlayerRotationParam();
        param.setStatus(PlayerRotationStatusEnum.PUBLIC.getCode());
        param.setOrgCode(PrincipalUtils.getOrgCode());
        param.setLanguageCode(Language.getLanguage().name());
        List<PlayerRotation> rotationList = playerRotationService.list(param);
        List<PlayerRotationResp> rotationVoList = rotationList.stream().map(v -> {
            PlayerRotationResp rotationVo = new PlayerRotationResp();
            BeanUtil.copyProperties(v,rotationVo);
            return rotationVo;
        }).collect(Collectors.toList());
        return ResponseData.success(rotationVoList);
    }

    @ApiOperation("查询首页轮播详情")
    @PostMapping("/rotation/detail/{id}")
    public ResponseData<PlayerRotationResp> getRotationDetail(@PathVariable Long id) {
        PlayerRotation playerRotation = playerRotationService.getById(id);
        PlayerRotationResp rotationVo = new PlayerRotationResp();
        BeanUtil.copyProperties(playerRotation,rotationVo);
        return ResponseData.success(rotationVo);
    }

    @ApiOperation("查询首页公告列表")
    @PostMapping("/notice/page")
    public ResponseData<PageResult<PlayerNoticeResp>> getNoticeList(@RequestBody PageTo pageTo) {
        PlayerNoticeParam param = new PlayerNoticeParam();
        param.setStatus(PlayerRotationStatusEnum.PUBLIC.getCode());
        param.setType(PlayNoticeTypeEnum.NOTICE.getCode());
        param.setLanguageCode(Language.getLanguage().name());
        param.setOrgCode(PrincipalUtils.getOrgCode());
        param.setPage(pageTo.getPage());
        param.setLimit(pageTo.getLimit());
        PageResult<PlayerNotice> noticePage = playerNoticeService.page(param);
        List<PlayerNoticeResp> noticeVoList = noticePage.getData().stream().map(v -> {
            PlayerNoticeResp noticeVo = new PlayerNoticeResp();
            BeanUtil.copyProperties(v,noticeVo);
            noticeVo.setUpdateTime(v.getCreateTime());
            return noticeVo;
        }).collect(Collectors.toList());
        PageResult<PlayerNoticeResp> page = new PageResult(noticePage.getPage(),noticePage.getLimit(),noticePage.getCount(),noticePage.getPageCount(),noticeVoList);
        return ResponseData.success(page);
    }

    @ApiOperation("查询首页公告详情")
    @PostMapping("/notice/detail/{id}")
    public ResponseData<PlayerNoticeResp> getNoticeDetail(@PathVariable Long id) {
        PlayerNotice playerNotice = playerNoticeService.getById(id);
        PlayerNoticeResp noticeVo = new PlayerNoticeResp();
        BeanUtil.copyProperties(playerNotice,noticeVo);
        return ResponseData.success(noticeVo);
    }

    @ApiOperation("查询帮助中心列表")
    @PostMapping("/helpCenter/page")
    public ResponseData<PageResult<PlayerNoticeResp>> getHelpCenterList(@RequestBody PageTo pageTo) {
        PlayerNoticeParam param = new PlayerNoticeParam();
        param.setStatus(PlayerRotationStatusEnum.PUBLIC.getCode());
        param.setType(PlayNoticeTypeEnum.HELP_CENTER.getCode());
        param.setLanguageCode(Language.getLanguage().name());
        param.setOrgCode(PrincipalUtils.getOrgCode());
        param.setPage(pageTo.getPage());
        param.setLimit(pageTo.getLimit());
        PageResult<PlayerNotice> noticePage = playerNoticeService.page(param);
        List<PlayerNoticeResp> noticeVoList = noticePage.getData().stream().map(v -> {
            PlayerNoticeResp noticeVo = new PlayerNoticeResp();
            BeanUtil.copyProperties(v,noticeVo);
            return noticeVo;
        }).collect(Collectors.toList());
        PageResult<PlayerNoticeResp> page = new PageResult(noticePage.getPage(),noticePage.getLimit(),noticePage.getCount(),noticePage.getPageCount(),noticeVoList);
        return ResponseData.success(page);
    }

    @ApiOperation("查询帮助中心详情")
    @PostMapping("/helpCenter/detail/{id}")
    public ResponseData<PlayerNoticeResp> getHelpCenterDetail(@PathVariable Long id) {
        PlayerNotice playerNotice = playerNoticeService.getById(id);
        PlayerNoticeResp noticeVo = new PlayerNoticeResp();
        BeanUtil.copyProperties(playerNotice,noticeVo);
        return ResponseData.success(noticeVo);
    }
}
