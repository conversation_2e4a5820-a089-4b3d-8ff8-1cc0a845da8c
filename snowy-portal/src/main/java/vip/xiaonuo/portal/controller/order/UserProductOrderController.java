//package vip.xiaonuo.portal.controller.order;
//
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.apache.commons.lang3.ObjectUtils;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.ResponseBody;
//import vip.xiaonuo.business.userproductorder.param.UserProductOrderParam;
//import vip.xiaonuo.business.userproductorder.pojo.dto.ExecuteUserPurchaseOrderDto;
//import vip.xiaonuo.business.userproductorder.pojo.dto.ExecuteUserRedeemOrderDto;
//import vip.xiaonuo.business.userproductorder.service.UserProductOrderService;
//import vip.xiaonuo.core.annotion.BusinessLog;
//import vip.xiaonuo.core.annotion.Permission;
//import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
//import vip.xiaonuo.core.pojo.response.ResponseData;
//import vip.xiaonuo.core.util.MessageUtils;
//import vip.xiaonuo.session.utils.PrincipalUtils;
//
//import javax.annotation.Resource;
//
///**
// * 理财订单控制器
// *
// * <AUTHOR>
// * @date 2022-04-06 18:42:41
// */
//@Api(tags = "理财订单")
//@Controller
//public class UserProductOrderController {
//
//
//    @Resource
//    private UserProductOrderService userProductOrderService;
//
//    /**
//     * 查询理财订单
//     *
//     * <AUTHOR>
//     * @date 2022-04-06 18:42:41
//     */
//    @ApiOperation("理财订单_查询列表")
//    @ResponseBody
//    @GetMapping("/userProductOrder/page")
//    public ResponseData page(UserProductOrderParam userProductOrderParam) {
//        if(ObjectUtils.isEmpty(userProductOrderParam)){
//            userProductOrderParam = new UserProductOrderParam();
//        }
//        userProductOrderParam.setPlayerId(PrincipalUtils.getPlayerId());
//        return ResponseData.success(userProductOrderService.findOrderForPage(userProductOrderParam));
//    }
//    /**
//     * 查询理财订单
//     *
//     * <AUTHOR>
//     * @date 2022-04-06 18:42:41
//     */
//    @ApiOperation("理财订单_根据id查询详情")
//    @Permission
//    @ResponseBody
//    @GetMapping("/userProductOrder/findOrderById")
//    @BusinessLog(title = "理财订单_根据id查询详情", opType = LogAnnotionOpTypeEnum.QUERY)
//    public ResponseData findOrderById(UserProductOrderParam userProductOrderParam) {
//        if(ObjectUtils.isEmpty(userProductOrderParam) || ObjectUtils.isEmpty(userProductOrderParam.getId())){
//           return ResponseData.error(MessageUtils.get("error"));
//        }
//        return ResponseData.success(userProductOrderService.getById(userProductOrderParam.getId()));
//    }
//    /**
//     * 添加理财订单
//     *
//     * <AUTHOR>
//     * @date 2022-04-06 18:42:41
//     */
//    @ApiOperation("购买理财产品")
//    @Permission
//    @ResponseBody
//    @PostMapping("/userProductOrder/executeUserPurchaseOrder")
//    @BusinessLog(title = "理财订单_增加")
//    public ResponseData executeUserPurchaseOrder(@RequestBody  ExecuteUserPurchaseOrderDto dto) {
//        if(ObjectUtils.isEmpty(dto)){
//            return ResponseData.error(MessageUtils.get("error"));
//        }
//        dto.setPlayerId(PrincipalUtils.getPlayerId());
//        return userProductOrderService.executeUserPurchaseOrder(dto);
//    }
//
//
//    /**
//     * 添加理财订单
//     *
//     * <AUTHOR>
//     * @date 2022-04-06 18:42:41
//     */
//    @ApiOperation("预购买理财产品")
//    @Permission
//    @ResponseBody
//    @PostMapping("/userProductOrder/executeAnticipateOrder")
//    @BusinessLog(title = "理财订单_预增加")
//    public ResponseData executeAnticipateOrder(@RequestBody  ExecuteUserPurchaseOrderDto dto) {
//        if(ObjectUtils.isEmpty(dto)){
//            return ResponseData.error(MessageUtils.get("error"));
//        }
//        dto.setPlayerId(PrincipalUtils.getPlayerId());
//        return   userProductOrderService.executeAnticipateOrder(dto);
//    }
//
//
//    /**
//     * 理财订单_赎回
//     *
//     * <AUTHOR>
//     * @date 2022-04-06 18:42:41
//     */
//    @ApiOperation("理财订单_赎回")
//    @Permission
//    @ResponseBody
//    @PostMapping("/userProductOrder/executeUserRedeemOrder")
//    @BusinessLog(title = "理财订单_赎回")
//    public ResponseData executeUserRedeemOrder(@RequestBody ExecuteUserRedeemOrderDto dto) {
//
//        if(ObjectUtils.isEmpty(dto)){
//            return ResponseData.error(MessageUtils.get("error"));
//        }
//        dto.setPlayerId(PrincipalUtils.getPlayerId());
//        return   userProductOrderService.executeUserRedeemOrder(dto);
//    }
//
//    /**
//     * 理财订单_预赎回
//     *
//     * <AUTHOR>
//     * @date 2022-04-06 18:42:41
//     */
//    @ApiOperation("理财订单_预赎回")
//    @Permission
//    @ResponseBody
//    @PostMapping("/userProductOrder/executeAnticipateRedeem")
//    @BusinessLog(title = "理财订单_预赎回")
//    public ResponseData executeAnticipateRedeem(@RequestBody ExecuteUserRedeemOrderDto dto) {
//
//        if(ObjectUtils.isEmpty(dto)){
//            return ResponseData.error(MessageUtils.get("error"));
//        }
//        dto.setPlayerId(PrincipalUtils.getPlayerId());
//        return   userProductOrderService.executeAnticipateRedeem(dto);
//    }
//
//    /**
//     * 理财订单_赎回
//     *
//     * <AUTHOR>
//     * @date 2022-04-06 18:42:41
//     */
//    @ApiOperation("理财订单_统计")
//    @Permission
//    @ResponseBody
//    @PostMapping("/userProductOrder/executeUserOrder")
//    @BusinessLog(title = "理财订单_赎回")
//    public ResponseData executeUserOrder() {
//        return   userProductOrderService.executeUserOrder(PrincipalUtils.getPlayerId());
//    }
//
//
//
//}
