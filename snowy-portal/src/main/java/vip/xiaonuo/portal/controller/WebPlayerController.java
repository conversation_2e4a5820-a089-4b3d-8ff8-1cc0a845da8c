package vip.xiaonuo.portal.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.business.account.service.AccountService;
import vip.xiaonuo.business.areacode.service.AreaCodeService;
import vip.xiaonuo.business.dict.service.SysDictDataService;
import vip.xiaonuo.business.ipinfo.service.IpInfoService;
import vip.xiaonuo.business.org.entity.SysOrg;
import vip.xiaonuo.business.org.service.SysOrgService;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.enums.PlayerExceptionEnum;
import vip.xiaonuo.business.player.enums.PlayerStatusEnum;
import vip.xiaonuo.business.player.result.AgentOrgInfo;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.playerinfo.entity.PlayerInfo;
import vip.xiaonuo.business.playerinfo.service.PlayerInfoService;
import vip.xiaonuo.core.consts.DictConstant;
import vip.xiaonuo.core.enums.LogWebOpTypeEnum;
import vip.xiaonuo.core.exception.AuthException;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.exception.enums.AuthExceptionEnum;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.redis.RedisHelper;
import vip.xiaonuo.core.util.*;
import vip.xiaonuo.portal.model.LoginModel;
import vip.xiaonuo.portal.model.PlayerInfoModel;
import vip.xiaonuo.portal.model.RegisterModel;
import vip.xiaonuo.portal.model.UpdatePasswordModel;
import vip.xiaonuo.portal.model.resp.PlayerInfoResp;
import vip.xiaonuo.portal.utils.WebLogUtils;
import vip.xiaonuo.session.security.UserSession;
import vip.xiaonuo.session.security.UserSessionStatus;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@Api(tags = "玩家")
public class WebPlayerController {
    @Resource
    private PlayerService playerService;
    @Resource
    private AreaCodeService areaCodeService;
    @Resource
    private RedisHelper redisHelper;
    @Resource
    private IpInfoService ipInfoService;
    @Resource
    private PlayerInfoService playerInfoService;
    @Resource
    private SysDictDataService sysDictDataService;
    @Resource
    private SysOrgService sysOrgService;
    @Resource
    private AccountService accountService;

    @ApiOperation("注册接口")
    @PostMapping("/register")
    @Transactional(rollbackFor = Exception.class)
    public ResponseData<?> register(@RequestBody RegisterModel param, HttpServletRequest request) {
        String orgCode = PrincipalUtils.getOrgCode();
        String ip = ServletUtil.getClientIP(request);
        SysOrg sysOrg = sysOrgService.lambdaQuery().eq(SysOrg::getCode, orgCode).eq(SysOrg::getStatus, 0).one();
        if (sysOrg == null) {
            return ResponseData.error("org not existed");
        }
        //去空格
        param.setInviteCode(StrUtil.trimToEmpty(param.getInviteCode()));
        param.setAccount(StrUtil.trimToEmpty(param.getAccount()));
        param.setEmail(StrUtil.trimToEmpty(param.getEmail()));
        param.setPhone(StrUtil.trimToEmpty(param.getPhone()));
        if (StringUtils.isAllEmpty(param.getAccount(), param.getPhone(), param.getEmail())
                || (StringUtils.isEmpty(param.getAreaCode()) && StringUtils.isNotEmpty(param.getPhone()))
                || StringUtils.isEmpty(param.getPassword())) {
            return ResponseData.error(MessageUtils.get("error"));
        }

//        String regex = "^[a-zA-Z0-9]+$";
//        Pattern pattern = Pattern.compile(regex);
//        Matcher matcher = pattern.matcher(param.getPhone());
//        if(!matcher.matches()){
//            return ResponseData.error(MessageUtils.get("account.pattern.not.correct"));
//        }
//        if(param.getPhone().length() < 6){
//            return ResponseData.error(MessageUtils.get("account.pattern.not.correct"));
//        }
        //验证手机号码格式
//        if (!areaCodeService.checkPhone(orgCode, param.getAreaCode(), param.getPhone())) {
//            return ResponseData.error(MessageUtils.get("sms.format.number.phone"));
//        }
//        if (!UaUtil.isPasswordRule(param.getPassword())) {
//            return ResponseData.error(MessageUtils.get("register.password.incorrect"));
//        }
//        String phoneCode = param.getAreaCode();
//        String redisKey = CommonUtils.getVcLastKey(SmsSendSourceEnum.REGISTER, phoneCode + param.getPhone());
//        Boolean needSmsSwitch = sysDictDataService.getCommonConfigAsBoolean(DictConstant.NEED_SMS_SWITCH);
//        if (needSmsSwitch) {
//            String code = redisHelper.get(redisKey);
//            if (!StringUtils.equals(code, param.getSmsCode())) {
//                return ResponseData.error(MessageUtils.get("sms.code.error"));
//            }
//        }
//        if (playerService.lambdaQuery().eq(Player::getOrgCode, orgCode).eq(Player::getPhoneCode, phoneCode).eq(Player::getPhone, param.getPhone()).count() > 0) {
//            throw new ServiceException(PlayerExceptionEnum.PHONE_EXISTED);
//        }
//        if (playerService.lambdaQuery().eq(Player::getOrgCode, orgCode).eq(Player::getAccount, param.getPhone()).count() > 0) {
//            return ResponseData.error(MessageUtils.get("register.mobile.already.exists"));
//        }
        Player player = new Player();
        player.setOrgCode(orgCode);
        Boolean needInviteCodeSwitch = sysDictDataService.getCommonConfigAsBoolean(DictConstant.NEED_INVITE_CODE_SWITCH);
        if (needInviteCodeSwitch) {
            //必须要邀请码才能注册
            if (StringUtils.isEmpty(param.getInviteCode())) {
                return ResponseData.error(MessageUtils.get("register.invitation.invalid"));
            }
        }
//        if (StringUtils.isNotEmpty(param.getInviteCode())) {
//            //通过推荐码查询线信息
//            AgentOrgInfo agentOrgInfo = this.playerService.getAgentByInviteCode(orgCode, param.getInviteCode());
//            if (ObjectUtil.isEmpty(agentOrgInfo) || ObjectUtil.isEmpty(agentOrgInfo.getAgentId())) {
//                return ResponseData.error(MessageUtils.get("register.invitation.invalid"));
//            }
//            player.setAgentId(agentOrgInfo.getAgentId());
//        }
        // 线索8级分销
        if (StringUtils.isNotEmpty(param.getInviteCode())) {
            Player parent = this.playerService.getOne(
                    Wrappers.lambdaQuery(Player.class).eq(Player::getInvitationCode, param.getInviteCode())
            );
            if (ObjectUtil.isEmpty(parent)) {
                return ResponseData.error(MessageUtils.get("register.invitation.invalid"));
            }
            player.setInvitedBy(parent.getId());

            // 开始构建 invite_seq
            List<Long> inviteSeq = new ArrayList<>();
            inviteSeq.add(parent.getId()); // 先加入直接邀请人的ID

            Long currentParentId = parent.getInvitedBy();  // 上级的上级
            int maxLevel = 8;

            while (currentParentId != null && inviteSeq.size() < maxLevel) {
                Player upper = this.playerService.getById(currentParentId);
                if (upper == null) break;
                inviteSeq.add(upper.getId());
                currentParentId = upper.getInvitedBy();
            }

            // 使用英文逗号拼接成字符串
            String inviteSeqStr = inviteSeq.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            player.setInviteSeq(inviteSeqStr);  // 假设这个字段是 String 类型
        }

        if (StringUtils.isNotEmpty(param.getAreaCode())) {
            player.setPhoneCode(param.getAreaCode());
        }
        player.setPhone(param.getPhone());
        player.setEmail(param.getEmail());
        player.setAccount(StringUtils.isNotEmpty(param.getPhone()) ? param.getAreaCode() + param.getPhone() : (StringUtils.isNotEmpty(param.getEmail()) ? param.getEmail() : param.getAccount()));
        player.setInvitationCode(playerService.genInviteCode());
        player.setSalt(InviteCodeUtil.getRandom(5));
        player.setStatus(PlayerStatusEnum.STATUS1.getCode());
        player.setCreateTime(new Date());
        player.setRegisterIp(ip);
        player.setRole("c");
        String pwd = Hmac.md5EncryptSalt(param.getPassword(), player.getSalt());
        player.setPassword(pwd);
        //注册时，资金密码保持与登录密码一致
        player.setFundsPassword(pwd);
        //昵称
        player.setNickName(CommonUtils.star(player.getAccount()));
        try {
            playerService.save(player);
        } catch (DuplicateKeyException e) {
            throw new ServiceException(MessageUtils.get("account.is.exist"));
        }

        //注册直接创建用户认证信息
        PlayerInfo playerInfo = new PlayerInfo();
        playerInfo.setPlayerId(player.getId());
        playerInfo.setAuthStatus(0);
        playerInfo.setAdvancedStatus(0);
        playerInfoService.save(playerInfo);
//        if (needSmsSwitch) {
//            redisHelper.delete(redisKey);
//        }
        //保存ip信息
        ipInfoService.saveIpInfo(player.getRegisterIp());

        UserSession userSession = new UserSession();
        userSession.setOrgCode(player.getOrgCode());
        userSession.setAccount(player.getAccount());
        userSession.setId(player.getId());
        userSession.setIp(ip);
        userSession.setDevice(UaUtil.getBrowser(request));
        userSession.setPhoneCode(player.getPhoneCode());
        userSession.setPhone(player.getPhone());
        userSession.setStatus(UserSessionStatus.SAVE);
        request.setAttribute(PrincipalUtils.Const.USER_SESSION, userSession);
        String token = IdUtil.randomUUID();
        request.setAttribute(PrincipalUtils.Const.CLIENT_TOKEN_NAME, token);
        playerService.lambdaUpdate()
                .set(Player::getLastLoginIp, ip)
                .set(Player::getLastLoginTime, new Date())
                .set(Player::getOnlineFlag, 1)
                .eq(Player::getId, player.getId()).update();
        //登录成功，记录登录日志
        WebLogUtils.executeLog(player.getAccount(), LogWebOpTypeEnum.LOGIN_SUCCESS, null);

        //赠送美金
//        AccountChangeBalanceParam.AccountChangeBalanceParamBuilder register = AccountChangeBalanceParam.builder()
//                .playerId(player.getId())
//                .type(AccountTypeEnum.TYPE1.getCode())
//                .currencyCode("USDT")
//                .businessType(AccountBillBusinessTypeEnum.DEPOSIT)
//                .optType(AccountBillOptTypeEnum.ADD)
//                .businessNo(UUIDUtil.createOrderId(SerialNoEnum.D))
//                .balance(BigDecimal.valueOf(10));
//        accountService.changeBalance(register.build());
        return ResponseData.success(token);
    }

    @ApiOperation("登录接口")
    @PostMapping("/login")
    public ResponseData<String> login(@RequestBody LoginModel param, HttpServletRequest request) {
        String orgCode = PrincipalUtils.getOrgCode();
        String ip = ServletUtil.getClientIP(request);
        String account = StringUtils.isNotEmpty(param.getPhone()) ? param.getAreaCode() + param.getPhone() : (StringUtils.isNotEmpty(param.getEmail()) ? param.getEmail() : param.getAccount());
        if (StringUtils.isEmpty(account)) {
            throw new ServiceException(MessageUtils.get("account.is.not.null"));
        }
        Player player = playerService.lambdaQuery().eq(Player::getOrgCode, orgCode).eq(Player::getAccount, account).one();
        //用户不存在，账号或密码错误
        if (ObjectUtil.isEmpty(player)) {
            WebLogUtils.executeLog(account, LogWebOpTypeEnum.LOGIN_FAIL, AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR, MessageUtils.get("login.password.incorrect"));
        }
        //验证账号是否被冻结
        if (PlayerStatusEnum.STATUS2.getCode().equals(player.getStatus())) {
            String msg = "The account is frozen";
            WebLogUtils.executeLog(account, LogWebOpTypeEnum.LOGIN_FAIL, msg);
            throw new AuthException(AuthExceptionEnum.ACCOUNT_FREEZE_ERROR, account + ">:" + msg);
        }
        String passwordEncrypt = player.getPassword();
        //验证账号密码是否正确
        if (!StringUtils.equals(Hmac.md5EncryptSalt(param.getPassword(), player.getSalt()), passwordEncrypt)) {
            WebLogUtils.executeLog(account, LogWebOpTypeEnum.LOGIN_FAIL, AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR, MessageUtils.get("login.password.incorrect"));
        }
        if (PlayerStatusEnum.STATUS2.getCode().equals(player.getStatus())) {
            throw new AuthException(AuthExceptionEnum.ACCOUNT_FREEZE_ERROR, MessageUtils.get("account.freeze.error"));
        }
        UserSession userSession = new UserSession();
        userSession.setOrgCode(player.getOrgCode());
        userSession.setAccount(player.getAccount());
        userSession.setId(player.getId());
        userSession.setIp(ip);
        userSession.setDevice(UaUtil.getBrowser(request));
        userSession.setPhoneCode(player.getPhoneCode());
        userSession.setPhone(player.getPhone());
        userSession.setEmail(player.getEmail());
        userSession.setStatus(UserSessionStatus.SAVE);
        request.setAttribute(PrincipalUtils.Const.USER_SESSION, userSession);
        String token = IdUtil.randomUUID();
        request.setAttribute(PrincipalUtils.Const.CLIENT_TOKEN_NAME, token);
        playerService.lambdaUpdate()
                .set(Player::getLastLoginIp, ip)
                .set(Player::getLastLoginTime, new Date())
                .set(Player::getOnlineFlag, 1)
                .eq(Player::getId, player.getId()).update();
        //保存ip信息
        ipInfoService.saveIpInfo(player.getLastLoginIp());
        //登录成功，记录登录日志
        WebLogUtils.executeLog(account, LogWebOpTypeEnum.LOGIN_SUCCESS, null);
        return ResponseData.success(token);
    }

    /**
     * 退出登录
     *
     * <AUTHOR>
     * @date 2020/3/16 15:02
     */
    @ApiOperation("退出登录")
    @GetMapping("/logout")
    public ResponseData<?> logout() {
        Long playerId = PrincipalUtils.getPlayerId();
        PrincipalUtils.getSessionContent().setStatus(UserSessionStatus.DELETE_LOGOUT_INSTANCE);
        playerService.lambdaUpdate()
                .set(Player::getOnlineFlag, 0)
                .eq(Player::getId, playerId).update();
        return ResponseData.success();
    }

    @ApiOperation("获取登录玩家信息")
    @GetMapping("/player/getInfo")
    public ResponseData<PlayerInfoResp> getInfo() {
        Long playerId = PrincipalUtils.getPlayerId();
        Player player = playerService.getById(playerId);
        if (player == null) {
            throw new ServiceException(PlayerExceptionEnum.NOT_EXIST);
        }
        PlayerInfoResp resp = new PlayerInfoResp();
        resp.setOrgCode(player.getOrgCode());
        resp.setNickName(player.getNickName());
        resp.setAvatar(player.getAvatar());
        resp.setAccount(player.getAccount());
        resp.setPhoneCode(player.getPhoneCode());
        resp.setPhone(player.getPhone());
        resp.setFundsPasswordFlag(StringUtils.isEmpty(player.getFundsPassword()) ? 0 : 1);
        PlayerInfo playerInfo = playerInfoService.queryPlayerUserInfo(playerId);
        resp.setAuthStatus(playerInfo.getAuthStatus());
        resp.setAdvancedStatus(playerInfo.getAdvancedStatus());
        return ResponseData.success(resp);
    }

    @ApiOperation("修改玩家信息")
    @PostMapping("/player/updateInfo")
    public ResponseData<?> updateInfo(@RequestBody PlayerInfoModel model) {
        Long playerId = PrincipalUtils.getPlayerId();
        Player player = playerService.getById(playerId);
        if (player == null) {
            throw new ServiceException(PlayerExceptionEnum.NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(model.getNickName())) {
            playerService.lambdaUpdate().set(Player::getNickName, model.getNickName()).eq(Player::getId, player.getId()).update();
        }
        return ResponseData.success();
    }

//    @ApiOperation("重置登录密码")
//    @PostMapping("/player/resetLoginPassword")
//    public ResponseData<?> resetPassword(@Validated @RequestBody ResetPasswordModel resetPasswordModel) {
//        String orgCode = PrincipalUtils.getOrgCode();
//        String phoneCode = resetPasswordModel.getAreaCode();
//        //验证手机号码格式
////        if (!areaCodeService.checkPhone(orgCode, resetPasswordModel.getAreaCode(), resetPasswordModel.getPhone())) {
////            return ResponseData.error(MessageUtils.get("sms.format.number.phone"));
////        }
//        String m = phoneCode + resetPasswordModel.getPhone();
//        String code = redisHelper.get(CommonUtils.getVcLastKey(SmsSendSourceEnum.RESET_PASSWORD, m));
//        boolean flag = resetPasswordModel.getSmsCode().equals(code);
//        if (!flag) {
//            return ResponseData.error(MessageUtils.get("sms.code.error"));
//        }
//        Player player = playerService.lambdaQuery().eq(Player::getPhoneCode, phoneCode).eq(Player::getPhone, resetPasswordModel.getPhone()).one();
//        if (player == null) {
//            throw new ServiceException(PlayerExceptionEnum.NOT_EXIST);
//        }
//        String pwd = Hmac.md5EncryptSalt(resetPasswordModel.getPassword(), player.getSalt());
//        playerService.lambdaUpdate().set(Player::getPassword, pwd).eq(Player::getId, player.getId()).update();
//        redisHelper.delete(CommonUtils.getVcLastKey(SmsSendSourceEnum.RESET_PASSWORD, m));
//        return ResponseData.success();
//    }

    @ApiOperation("修改登录密码")
    @PostMapping("/player/updateLoginPassword")
    public ResponseData<?> updatePassword(@Validated @RequestBody UpdatePasswordModel updatePasswordModel) {
        Long playerId = PrincipalUtils.getPlayerId();
        Player player = playerService.getById(playerId);
        if (player == null) {
            throw new ServiceException(PlayerExceptionEnum.NOT_EXIST);
        }
        if (StringUtils.isEmpty(updatePasswordModel.getOldPassword())) {
            return ResponseData.error(MessageUtils.get("profile.old.password.empty"));
        }
        String pwd = Hmac.md5EncryptSalt(updatePasswordModel.getOldPassword(), player.getSalt());
        if (!player.getPassword().equals(pwd)) {
            return ResponseData.error(MessageUtils.get("profile.old.password.error"));
        }
        if (!UaUtil.isPasswordRule(updatePasswordModel.getPassword())) {
            return ResponseData.error(MessageUtils.get("register.password.incorrect"));
        }
        playerService.lambdaUpdate().set(Player::getPassword, Hmac.md5EncryptSalt(updatePasswordModel.getPassword(), player.getSalt())).eq(Player::getId, player.getId()).update();
        return ResponseData.success();
    }

    @ApiOperation("修改资金密码")
    @PostMapping("/player/updateFundsPassword")
    public ResponseData<?> updateFundsPassword(@Validated @RequestBody UpdatePasswordModel updatePasswordModel) {
        Player player = playerService.getById(PrincipalUtils.getPlayerId());
        if (player == null) {
            throw new ServiceException(PlayerExceptionEnum.NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(player.getFundsPassword())) {
            if (StringUtils.isEmpty(updatePasswordModel.getOldPassword())) {
                return ResponseData.error(MessageUtils.get("profile.old.password.empty"));
            }
            String pwd = Hmac.md5EncryptSalt(updatePasswordModel.getOldPassword(), player.getSalt());
            if (!pwd.equals(player.getFundsPassword())) {
                return ResponseData.error(MessageUtils.get("profile.old.password.error"));
            }
        }
        if (StringUtils.length(updatePasswordModel.getPassword()) < 6) {
            return ResponseData.error(MessageUtils.get("profile.funds.password.incorrect"));
        }
        playerService.lambdaUpdate().set(Player::getFundsPassword, Hmac.md5EncryptSalt(updatePasswordModel.getPassword(), player.getSalt())).eq(Player::getId, player.getId()).update();
        return ResponseData.success();
    }

}
