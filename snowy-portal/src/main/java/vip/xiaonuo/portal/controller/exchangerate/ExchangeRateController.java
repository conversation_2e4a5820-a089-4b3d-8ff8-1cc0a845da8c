package vip.xiaonuo.portal.controller.exchangerate;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.business.exchangerate.service.ExchangeRateService;
import vip.xiaonuo.core.pojo.response.ResponseData;

import javax.annotation.Resource;

/**
 * 用户账户信息
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:39
 */
@Api(tags = "费率")
@RestController
public class ExchangeRateController {


    @Resource
    private ExchangeRateService exchangeRateService;


    @ApiOperation("用户-当日或前一日列表")
    @PostMapping("/exchangeRate/finExchangeRateToDay")
    public ResponseData finExchangeRateToDay() {
        return exchangeRateService.finExchangeRateToDay();
    }
}
