package vip.xiaonuo.portal.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.business.currency.entity.Currency;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.core.context.constant.ConstantContextHolder;
import vip.xiaonuo.core.enums.KLineType;
import vip.xiaonuo.core.pojo.dto.MarketDTO;
import vip.xiaonuo.core.pojo.dto.TradeDTO;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.util.DpDataUtils;
import vip.xiaonuo.portal.model.MarketKLineModel;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@RestController
@Api(tags = "市场")
@RequestMapping("/market")
public class WebMarketController {
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private CurrencyService currencyService;
    @Resource
    private MarketService marketService;

    private HashOperations<String, String, MarketDTO> marketHashOperations;

    @Resource
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
        marketHashOperations = redisTemplate.opsForHash();
    }

    @ApiOperation("k线")
    @PostMapping("/kline")
    public ResponseData<List<List<BigDecimal>>> kline(@RequestBody MarketKLineModel request) {
        request.validate();
//        List<List<BigDecimal>> results = DpDataUtils.getKLineData(stringRedisTemplate, request.getSymbol(),
//                KLineType.of(request.getPeriod()), request.getParsedSize(), request.getEndTime());
        // 写死200条要加上控制K线的逻辑
        request.setParsedSize(200);
        List<List<BigDecimal>> results = DpDataUtils.getKLineData(stringRedisTemplate, request.getSymbol(),
                KLineType.of(request.getPeriod()), request.getParsedSize(), request.getEndTime());
        //[0] 时间戳（openTime）
        //[1] 开盘价（open）
        //[2] 最高价（high）
        //[3] 最低价（low）
        //[4] 收盘价（close） ← 你要修改的部分
        //[5] 成交量（vol）
        if (results != null && !results.isEmpty()) {
            List<BigDecimal> lastKLine = results.get(results.size() - 1);
            BigDecimal price = DpDataUtils.getLatestPrice(redisTemplate, request.getSymbol());
            if (price != null) {
                lastKLine.set(4, price);
            }
        }
        return ResponseData.success(results);
    }

    @ApiOperation("行情")
    @GetMapping("/tickers")
    public ResponseData<List<MarketDTO>> tickers() {
        List<MarketDTO> marketDTOS = DpDataUtils.getTickers(marketHashOperations);
        List<Currency> currencies = currencyService.lambdaQuery().select(Currency::getCode, Currency::getLogo).list();
        for (MarketDTO marketDTO : marketDTOS) {
            currencies.stream().filter(p -> p.getCode().equalsIgnoreCase(marketDTO.getCurrencyCode())).findAny().ifPresent(currency -> marketDTO.setLogo(currency.getLogo()));
        }
        //排序
        marketDTOS.sort(Comparator.comparing(MarketDTO::getSort));
        return ResponseData.success(marketDTOS);
    }

    @ApiOperation("新成交数据")
    @GetMapping("/trade")
    public ResponseData<List<TradeDTO>> trade(@RequestParam String symbol) {
        List<TradeDTO> tradeDTOS = DpDataUtils.getRecentTrades(redisTemplate, symbol);
        return ResponseData.success(tradeDTOS);
    }

    @ApiOperation("最新价格,比如：ETH_USDT")
    @GetMapping("/getLatestPrice")
    public ResponseData<BigDecimal> getLatestPrice(@RequestParam String symbol) {
        return ResponseData.success(marketService.getLatestPrice(symbol));
    }
}
