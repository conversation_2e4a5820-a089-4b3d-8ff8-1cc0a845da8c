package vip.xiaonuo.portal.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.business.tickercollect.entity.TickerCollect;
import vip.xiaonuo.business.tickercollect.service.TickerCollectService;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("tickerCollect")
@Api(tags = "自选")
public class TickerCollectController {
    @Resource
    private TickerCollectService tickerCollectService;

    @ApiOperation("获取自选")
    @GetMapping("/get")
    public ResponseData<List<String>> get() {
        Long playerId = PrincipalUtils.getPlayerId();
        List<TickerCollect> tickerCollects = tickerCollectService.lambdaQuery().eq(TickerCollect::getPlayerId, playerId).orderByDesc(TickerCollect::getCreateTime).list();
        return ResponseData.success(tickerCollects.stream().map(TickerCollect::getSymbol).collect(Collectors.toCollection(ArrayList::new)));
    }

    @ApiOperation("保存自选")
    @GetMapping("/save")
    public ResponseData<Boolean> save(@RequestParam String symbol) {
        Long playerId = PrincipalUtils.getPlayerId();
        TickerCollect tickerCollect = new TickerCollect();
        tickerCollect.setPlayerId(playerId);
        tickerCollect.setSymbol(symbol.toUpperCase());
        tickerCollect.setCreateTime(new Date());
        return ResponseData.success(tickerCollectService.save(tickerCollect));
    }

    @ApiOperation("删除自选")
    @GetMapping("/delete")
    public ResponseData<Boolean> delete(@RequestParam String symbol) {
        Long playerId = PrincipalUtils.getPlayerId();
        return ResponseData.success(tickerCollectService.lambdaUpdate().eq(TickerCollect::getPlayerId, playerId).eq(TickerCollect::getSymbol, symbol).remove());
    }
}
