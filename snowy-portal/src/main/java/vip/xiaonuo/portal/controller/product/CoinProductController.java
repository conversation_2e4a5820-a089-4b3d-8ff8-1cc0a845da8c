package vip.xiaonuo.portal.controller.product;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.business.currency.entity.Currency;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.productintroduce.entity.ProductIntroduce;
import vip.xiaonuo.business.productintroduce.param.ProductIntroduceParam;
import vip.xiaonuo.business.productintroduce.service.ProductIntroduceService;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.portal.model.resp.CoinProductDetailResp;

import javax.annotation.Resource;

@RestController
@Api(tags = "币产品简介信息")
public class CoinProductController {

    @Resource
    private ProductIntroduceService productIntroduceService;

    @ApiOperation("获取币产品简介信息")
    @PostMapping("/coin/product/detail/{code}")
    public ResponseData<CoinProductDetailResp> getCoinProductDetail(@PathVariable String code) {
        ProductIntroduce productIntroduce = productIntroduceService.getByCurrencyCode(code);
        CoinProductDetailResp productResp = new CoinProductDetailResp();
        BeanUtil.copyProperties(productIntroduce, productResp);
        return ResponseData.success(productResp);
    }
}
