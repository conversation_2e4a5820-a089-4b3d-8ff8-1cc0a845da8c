/*
Copyright [2020] [https://www.stylefeng.cn]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Guns采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Guns源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/stylefeng/guns-separation
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/stylefeng/guns-separation
6.若您的项目无法满足以上几点，可申请商业授权，获取Guns商业授权许可，请在官网购买授权，地址为 https://www.stylefeng.cn
 */
package vip.xiaonuo.portal.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.business.areacode.service.AreaCodeService;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.core.enums.SmsSendSourceEnum;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.core.redis.RedisHelper;
import vip.xiaonuo.core.util.CommonUtils;
import vip.xiaonuo.core.util.MessageUtils;
import vip.xiaonuo.portal.model.SmsSendModel;
import vip.xiaonuo.portal.model.VerifyCodeModel;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 短信发送控制器
 *
 * <AUTHOR>
 * @date 2020/6/7 16:07
 */
@RestController
@RequestMapping("sms")
@Api(tags = "短信")
public class SmsSenderController {

    @Resource
    private RedisHelper redisHelper;
    @Resource
    private PlayerService playerService;
    @Resource
    private AreaCodeService areaCodeService;

    /**
     * 发送验证码短信
     */
    @PostMapping("/sendVerifyCode")
    @ApiOperation(value = "发送短信验证码")
    public ResponseData<?> sendVerifyCode(HttpServletRequest request, @RequestBody @Validated SmsSendModel smsSendModel) {
        String orgCode = PrincipalUtils.getOrgCode();
        //获取真实Ip
        String ip = ServletUtil.getClientIP(request);
        //验证手机号码格式
        if (!areaCodeService.checkPhone(orgCode, smsSendModel.getAreaCode(), smsSendModel.getPhone())) {
            return ResponseData.error(MessageUtils.get("sms.format.number.phone"));
        }
        //将国际区号与号码连接
        String phone = smsSendModel.getAreaCode() + smsSendModel.getPhone();
        //账号验证码次数限制校验 59秒1条
        if (!redisHelper.expire(CommonUtils.getVcLtKey(smsSendModel.getSource(), phone), 1, 59)) {
            return ResponseData.error(MessageUtils.get("sms.send.too.frequently"));
        }
        //Ip验证码次数限制校验 1分钟12条
        if (!redisHelper.expire(CommonUtils.getVcIpLtKey(ip), 12, 60)) {
            return ResponseData.error(MessageUtils.get("sms.send.too.frequently"));
        }
        //非注册时，判断手机号码没有注册过，将不进行短信发送
        if (smsSendModel.getSource() != SmsSendSourceEnum.REGISTER && playerService.lambdaQuery().eq(Player::getOrgCode, orgCode).eq(Player::getPhoneCode, smsSendModel.getAreaCode()).eq(Player::getPhone, smsSendModel.getPhone()).count() <= 0) {
            return ResponseData.error(MessageUtils.get("sms.enter.registered.phone"));
        }
        String activeProfile = SpringUtil.getActiveProfile();
        String validateCode = "666666";
        if (StringUtils.startsWithIgnoreCase(activeProfile, "prod")) {
            validateCode = RandomUtil.randomNumbers(6);
        }
        //短信失效时间，默认15分钟
        int invalidateMinutes = 15;
        //todo 需要实际发送
        redisHelper.set(CommonUtils.getVcLastKey(smsSendModel.getSource(), phone), validateCode, invalidateMinutes * 60);
        return ResponseData.success();
    }

    @ApiOperation("验证短信验证码（忘记登录密码、忘记资金密码）")
    @PostMapping("/sms/verifyCode/check")
    public ResponseData<?> checkVerifyCode(@Validated @RequestBody VerifyCodeModel verifyCodeModel) {
        String orgCode = PrincipalUtils.getOrgCode();
        String phoneCode = verifyCodeModel.getAreaCode();
        //验证手机号码格式
        if (!areaCodeService.checkPhone(orgCode, verifyCodeModel.getAreaCode(), verifyCodeModel.getPhone())) {
            return ResponseData.error(MessageUtils.get("sms.format.number.phone"));
        }
        String token = checkVerifyCodeToToken(verifyCodeModel.getSource(), phoneCode, verifyCodeModel.getPhone(), verifyCodeModel.getCode());
        if (StringUtils.isEmpty(token)) {
            return ResponseData.error(ResponseData.DEFAULT_ERROR_CODE, MessageUtils.get("captcha.code.expired"), "");
        }
        return new SuccessResponseData<>(token);
    }

    private String checkVerifyCodeToToken(SmsSendSourceEnum type, String phoneCode, String phone, String verifyCode) {
        String m = phoneCode + phone;
        String code = redisHelper.get(CommonUtils.getVcLastKey(type, m));
        boolean flag = verifyCode.equals(code);
        if (flag) {
            redisHelper.delete(CommonUtils.getVcLastKey(type, m));
            String token = IdUtil.simpleUUID();
            //半个小时过期，如果一直停留在修改密码页面30分钟之后，就无法修改了
            redisHelper.set(CommonUtils.getVctLastKey(type, token), phone, 30 * 60);
            return token;
        }
        return null;
    }
}
