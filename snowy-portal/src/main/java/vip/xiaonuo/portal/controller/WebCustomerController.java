package vip.xiaonuo.portal.controller;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.business.customer.entity.Customer;
import vip.xiaonuo.business.customer.param.CustomerParam;
import vip.xiaonuo.business.customer.service.CustomerService;
import vip.xiaonuo.business.notice.enums.PlayerRotationStatusEnum;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.util.Language;
import vip.xiaonuo.portal.model.resp.CustomerResp;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@Api(tags = "客服信息")
@RequestMapping("/player")
public class WebCustomerController {

    @Resource
    private CustomerService customerService;

    @ApiOperation("获取客服跳转链接")
    @PostMapping("/customer/link")
    public ResponseData<CustomerResp> getCustomerLink() {
        CustomerParam param = new CustomerParam();
        param.setStatus(PlayerRotationStatusEnum.PUBLIC.getCode());
        param.setOrgCode(PrincipalUtils.getOrgCode());
        param.setLanguageCode(Language.getLanguage().name());
        List<Customer> customerList = customerService.list(param);
        List<CustomerResp> customerRespList = customerList.stream().map(v -> {
            CustomerResp customerResp = new CustomerResp();
            BeanUtil.copyProperties(v, customerResp);
            return customerResp;
        }).collect(Collectors.toList());
        return ResponseData.success(CollectionUtils.isEmpty(customerRespList) ? new CustomerResp() : customerRespList.get(0));
    }
}
