package vip.xiaonuo.portal.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.business.follower.entity.Follower;
import vip.xiaonuo.business.follower.service.FollowerService;
import vip.xiaonuo.business.followerapplication.entity.FollowerApplication;
import vip.xiaonuo.business.followerapplication.param.FollowerApplicationParam;
import vip.xiaonuo.business.followerapplication.service.FollowerApplicationService;
import vip.xiaonuo.business.followercontractspotorder.entity.FollowerContractSpotOrder;
import vip.xiaonuo.business.followercontractspotorder.service.FollowerContractSpotOrderService;
import vip.xiaonuo.business.followerrelation.entity.FollowerRelation;
import vip.xiaonuo.business.followerrelation.param.FollowerRelationParam;
import vip.xiaonuo.business.followerrelation.service.FollowerRelationService;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.enums.PlayerExceptionEnum;
import vip.xiaonuo.business.player.pojo.dto.FindFollowerDto;
import vip.xiaonuo.business.player.pojo.vo.FindFollowerVo;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.spotorder.entity.SpotOrder;
import vip.xiaonuo.business.spotorder.service.SpotOrderService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.portal.model.resp.FollowerDashboardResp;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

@RestController
@Api(tags = "跟单")
public class WebFollowerController {
    @Resource
    private PlayerService playerService;

    @Resource
    private FollowerRelationService followerRelationService;

    @Resource
    private FollowerApplicationService followerApplicationService;

    @Resource
    private FollowerService followerService;

    @Resource
    private FollowerContractSpotOrderService followerContractSpotOrderService;

    @Resource
    private SpotOrderService spotOrderService;

    /**
     * 充值提现记录
     *
     * <AUTHOR>
     * @date 2022-04-25 15:48:06
     */
    @ApiOperation("跟单员列表")
    @ResponseBody
    @PostMapping("/follower/page")
    public ResponseData<PageResult<FindFollowerVo>> page(@RequestBody FindFollowerDto currencyTypeParam) {
        currencyTypeParam.setPlayerId(PrincipalUtils.getPlayerId());
        // 需要处理我是否已经跟单了
        return ResponseData.success(playerService.findUserFollower(currencyTypeParam));
    }

    // todo 历史跟单
    @ApiOperation("跟单交易员")
    @PostMapping("/follower/follow")
    public ResponseData follower(@RequestBody FollowerRelationParam followerRelationParam) {
        // todo gjn 可能需要判断用户金额
        Long playerId = PrincipalUtils.getPlayerId();
        Long followerId = followerRelationParam.getFollowerId();

        if (playerId.equals(followerId)) {
            return ResponseData.error("不能跟单自己");
        }

        Follower player = followerService.getOne(Wrappers.lambdaQuery(Follower.class)
                .eq(Follower::getId, followerId));

        if (ObjectUtil.isNull(player)) {
            return ResponseData.error("目标用户不是交易员，无法跟单");
        }

        // 寻找订单
        FollowerContractSpotOrder order = followerContractSpotOrderService.getOne(
                Wrappers.<FollowerContractSpotOrder>lambdaQuery()
                        .eq(FollowerContractSpotOrder::getStatus, 0)
                        .eq(FollowerContractSpotOrder::getFollowerId, followerId)
                        .ge(FollowerContractSpotOrder::getOrderTime, LocalDate.now())
                        .orderByAsc(FollowerContractSpotOrder::getOrderTime)
                        .last("LIMIT 1")
        );

        if (ObjectUtil.isNull(order)) {
            return ResponseData.error("交易员跟单已结束，无法跟单");
        }

        FollowerRelation followerRelation = followerRelationService.getOne(
                Wrappers.lambdaQuery(FollowerRelation.class)
                        .eq(FollowerRelation::getPlayerId, playerId)
                        .eq(FollowerRelation::getFollowerContractSpotOrderId, order.getId())
                        .eq(FollowerRelation::getFollowerId, followerId));

        if (ObjectUtil.isNull(followerRelation)) {
            followerRelation = new FollowerRelation();
            followerRelation.setPlayerId(playerId);
            followerRelation.setFollowerId(followerId);
            followerRelation.setFollowerContractSpotOrderId(order.getId());
            followerRelationService.save(followerRelation);
        }

        return ResponseData.success();
    }

    @ApiOperation("取消跟单交易员")
    @PostMapping("/follower/unfollow")
    public ResponseData unfollower(@RequestBody FollowerRelationParam followerRelationParam) {
        Long playerId = PrincipalUtils.getPlayerId();
        Long followerId = followerRelationParam.getFollowerId();

        FollowerRelation followerRelation = followerRelationService.getOne(
                Wrappers.lambdaQuery(FollowerRelation.class)
                        .eq(FollowerRelation::getPlayerId, playerId)
                        .eq(FollowerRelation::getFollowerId, followerId)
        );

        if (ObjectUtil.isNotNull(followerRelation)) {
            followerRelationService.removeById(followerRelation.getId());
        }

        return ResponseData.success();
    }

    @ApiOperation("申请成为交易员")
    @PostMapping("/follower/apply")
    public ResponseData apply(@RequestBody FollowerApplicationParam param) {
        // 设置当前登录用户ID
        Long playerId = PrincipalUtils.getPlayerId();
        Player player = playerService.getById(playerId);
        if (player == null) {
            throw new ServiceException(PlayerExceptionEnum.NOT_EXIST);
        }

        if (player.getIsFollower().equals(1)) {
            return ResponseData.error("您已经是交易员，不能重复提交申请");
        }

        param.setPlayerId(playerId);

        FollowerApplication exist = followerApplicationService.getOne(
                Wrappers.lambdaQuery(FollowerApplication.class)
                        .eq(FollowerApplication::getPlayerId, playerId)
        );

        if (exist != null) {
            if (exist.getAuditStatus() == 0 || exist.getAuditStatus() == 1) {
                return ResponseData.error("您已提交过申请，不能重复提交");
            } else if (exist.getAuditStatus() == 2) {
                // 被拒绝，重新提交可以覆盖原有申请
                exist.setProfile(param.getProfile());
                exist.setTagList(param.getTagList());
                exist.setExperience(param.getExperience());
                exist.setExpectedAnnualYield(param.getExpectedAnnualYield());
                exist.setRiskPreference(param.getRiskPreference());
                exist.setTradeUrl(param.getTradeUrl());
                exist.setContactInfo(param.getContactInfo());
                exist.setAuditStatus(0);
                followerApplicationService.updateById(exist);
                return ResponseData.success("申请已重新提交，等待审核");
            }
        }

        // 不存在申请，直接新增
        followerApplicationService.add(param);
        return ResponseData.success("申请提交成功，等待审核");
    }

    @ApiOperation("申请成为交易员")
    @GetMapping("/follower/applyInfo")
    public ResponseData apply() {
        FollowerApplication followerApplication = followerApplicationService.getOne(
                Wrappers.lambdaQuery(FollowerApplication.class).eq(FollowerApplication::getPlayerId, PrincipalUtils.getPlayerId())
        );
        return ResponseData.success(followerApplication);
    }

    @ApiOperation("跟单收益明细")
    @GetMapping("/follower/dashboard")
    public ResponseData<FollowerDashboardResp> dashboard() {
        Long playerId = PrincipalUtils.getPlayerId();

        // 初始化响应对象
        FollowerDashboardResp resp = new FollowerDashboardResp();

        // 获取历史总收益 (follower_id != 0)
        QueryWrapper<SpotOrder> totalWrapper = new QueryWrapper<>();
        totalWrapper.select("IFNULL(SUM(profit), 0) as sum")
                .eq("player_id", playerId)
                .ne("follower_id", 0);
        Map<String, Object> totalMap = spotOrderService.getMap(totalWrapper);
        BigDecimal totalProfit = new BigDecimal(totalMap.get("sum").toString());

        // 获取今日收益
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime startOfNextDay = today.plusDays(1).atStartOfDay();

        QueryWrapper<SpotOrder> todayWrapper = new QueryWrapper<>();
        todayWrapper.select("IFNULL(SUM(profit), 0) as sum")
                .eq("player_id", playerId)
                .ne("follower_id", 0)
                .ge("close_time", startOfDay)
                .lt("close_time", startOfNextDay);
        Map<String, Object> todayMap = spotOrderService.getMap(todayWrapper);
        BigDecimal todayProfit = new BigDecimal(todayMap.get("sum").toString());

        // 赋值返回
        resp.setToday(todayProfit);
        resp.setTotal(totalProfit);

        return ResponseData.success(resp);
    }
}
