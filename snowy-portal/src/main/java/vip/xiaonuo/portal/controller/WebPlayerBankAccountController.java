package vip.xiaonuo.portal.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.enums.PlayerExceptionEnum;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.userbankaccount.entity.PlayerBankAccount;
import vip.xiaonuo.business.userbankaccount.service.PlayerBankAccountService;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.util.Hmac;
import vip.xiaonuo.core.util.MessageUtils;
import vip.xiaonuo.portal.model.BankAccountModel;
import vip.xiaonuo.portal.model.PlayerFundPasswdModel;
import vip.xiaonuo.portal.model.resp.PlayerBankAccountResp;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;
import java.util.Date;

@RestController
@Api(tags = "玩家银行账号信息")
@RequestMapping("/player")
public class WebPlayerBankAccountController {

    @Resource
    private PlayerBankAccountService playerBankAccountService;

    @Resource
    private PlayerService playerService;

    @ApiOperation("获取玩家银行账户信息")
    @PostMapping("/bankAccount/detail")
    public ResponseData<PlayerBankAccountResp> getBankAccountDetail() {
        PlayerBankAccount playerBankAccount = playerBankAccountService.getByPlayerId(PrincipalUtils.getPlayerId());
        PlayerBankAccountResp playerBankAccountResp = new PlayerBankAccountResp();
        BeanUtil.copyProperties(playerBankAccount, playerBankAccountResp);
        if (StrUtil.isNotEmpty(playerBankAccountResp.getBankCardNumber())) {
            int len = StrUtil.length(playerBankAccountResp.getBankCardNumber());
            if (len > 6) {
                String str = StrUtil.sub(playerBankAccountResp.getBankCardNumber(), 0, 2) + "*****" + StrUtil.sub(playerBankAccountResp.getBankCardNumber(), len - 4, len);
                playerBankAccountResp.setBankCardNumber(str);
            }
        }
        return ResponseData.success(playerBankAccountResp);
    }

    @ApiOperation("保存or编辑玩家银行账户")
    @PostMapping("/bankAccount/saveOrEdit")
    public ResponseData saveOrEditBankAccount(@Validated @RequestBody BankAccountModel bankAccountModel) {
        Long playerId = PrincipalUtils.getPlayerId();
        playerBankAccountService.getByPlayerId(playerId);
        PlayerBankAccount playerBankAccount = playerBankAccountService.getByPlayerId(PrincipalUtils.getPlayerId());
        if (playerBankAccount == null) {
            playerBankAccount = new PlayerBankAccount();
            BeanUtil.copyProperties(bankAccountModel, playerBankAccount);
            playerBankAccount.setPlayerId(playerId);
            playerBankAccount.setCreateTime(new Date());
            playerBankAccount.setCreateUser(playerId);
            playerBankAccountService.save(playerBankAccount);
        } else {
            BeanUtil.copyProperties(bankAccountModel, playerBankAccount);
            playerBankAccount.setUpdateTime(new Date());
            playerBankAccount.setUpdateUser(playerId);
            if (StrUtil.contains(playerBankAccount.getBankCardNumber(), "*")) {
                //里边包含*号，不修改
                playerBankAccount.setBankCardNumber(null);
            }
            playerBankAccountService.updateById(playerBankAccount);
        }
        return ResponseData.success();
    }

    @ApiOperation("校验玩家资金密码")
    @PostMapping("/bankAccount/checkFundPasswd")
    public ResponseData checkFundPasswd(@Validated @RequestBody PlayerFundPasswdModel playerFundPasswdModel) {
        Player player = playerService.getById(PrincipalUtils.getPlayerId());
        if (player == null) {
            throw new ServiceException(PlayerExceptionEnum.NOT_EXIST.getCode(), MessageUtils.get("player.not.exist"));
        }
        String pwd = Hmac.md5EncryptSalt(playerFundPasswdModel.getFundPasswd(), player.getSalt());
        if (!pwd.equals(player.getFundsPassword())) {
            throw new ServiceException(PlayerExceptionEnum.INCORRECT_FUND_PASSWORD.getCode(), MessageUtils.get("player.fund.password.incorrect"));
        }
        return ResponseData.success();
    }
}
