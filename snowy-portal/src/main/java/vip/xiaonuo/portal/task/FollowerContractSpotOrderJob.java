package vip.xiaonuo.portal.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.business.contractspotorder.controller.service.ContractSpotOrderService;
import vip.xiaonuo.business.followercontractspotorder.entity.FollowerContractSpotOrder;
import vip.xiaonuo.business.followercontractspotorder.service.FollowerContractSpotOrderService;
import vip.xiaonuo.business.followerrelation.entity.FollowerRelation;
import vip.xiaonuo.business.followerrelation.service.FollowerRelationService;
import vip.xiaonuo.business.spotorder.entity.SpotOrder;
import vip.xiaonuo.business.spotorder.param.PlaceSpotOrderModel;
import vip.xiaonuo.business.spotorder.service.SpotOrderService;
import vip.xiaonuo.core.context.constant.ConstantContextHolder;
import vip.xiaonuo.core.util.RedisLock;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

@Component
public class FollowerContractSpotOrderJob {
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private Logger logger = LoggerFactory.getLogger(FollowerContractSpotOrderJob.class);

    @Resource
    FollowerContractSpotOrderService followerContractSpotOrderService;

    @Resource
    FollowerRelationService followerRelationService;

    @Resource
    ContractSpotOrderService contractSpotOrderService;

    @Resource
    SpotOrderService spotOrderService;

    @Scheduled(cron = "0/1 * * * * ?")
    public void control() {
        String redisKey = "follower_spot_order_cron";
        try {
            RedisLock redisLock = new RedisLock(redisTemplate, redisKey, 0, 2 * 60 * 1000);
            try {
                if (redisLock.lock()) {
                    List<FollowerContractSpotOrder> list = followerContractSpotOrderService.getBaseMapper().selectList(
                            Wrappers.<FollowerContractSpotOrder>lambdaQuery()
                                    .eq(FollowerContractSpotOrder::getStatus, 0)
                                    .ge(FollowerContractSpotOrder::getOrderTime, LocalDate.now())
                    );

                    for (FollowerContractSpotOrder order : list) {
                        // 获取交易员跟单
                        List<FollowerRelation> playerList = followerRelationService.getBaseMapper().selectList(
                                Wrappers.<FollowerRelation>lambdaQuery()
                                        .eq(FollowerRelation::getFollowerId, order.getFollowerId())
                        );

                        for (FollowerRelation relation : playerList) {
                            try {
                                followerByPlayerId(relation, order);
                            } catch (Exception e) {
                                logger.error("处理单个跟单失败: followerId={}, playerId={}, orderId={}",
                                        order.getFollowerId(), relation.getPlayerId(), order.getId(), e);
                            }
                        }
                        order.setStatus(1);
                        followerContractSpotOrderService.updateById(order);
                    }
                }
            } catch (Exception e) {
                logger.error("获取跟单极速合约待结束异常", e);
            } finally {
                redisLock.unlock();
            }
        } catch (Exception e) {
            logger.error("获取跟单极速合约待结束异常", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void followerByPlayerId(FollowerRelation relation, FollowerContractSpotOrder order) {
//        PlaceSpotOrderModel model = new PlaceSpotOrderModel();
//        model.setSymbol(order.getSymbol());
//        model.setType(order.getType());
//        model.setRuleId(order.getRuleId().toString());
//        model.setQuantity(order.getQuantity());
//        model.setFollowerId(order.getFollowerId());
//        model.setFollowerContractSpotOrderId(order.getId());
//        model.setFollowerCommissionRate(ConstantContextHolder.getFollowerCommissionSetting());
//        ContractSpotOrder spotOrder = contractSpotOrderService.placeOrder(relation.getPlayerId(), model);

        PlaceSpotOrderModel model = new PlaceSpotOrderModel();
        model.setSymbol(order.getSymbol());
        model.setType(order.getType());
        model.setRuleId(order.getRuleId().toString());
        model.setQuantity(order.getQuantity());
        model.setFollowerId(order.getFollowerId());
        model.setFollowerContractSpotOrderId(order.getId());
        model.setFollowerCommissionRate(ConstantContextHolder.getFollowerCommissionSetting());
        SpotOrder spotOrder = spotOrderService.placeOrder(relation.getPlayerId(), model);
    }
}
