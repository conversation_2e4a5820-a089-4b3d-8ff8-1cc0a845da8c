package vip.xiaonuo.portal.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vip.xiaonuo.business.marketadjustitem.entity.MarketAdjustItem;
import vip.xiaonuo.business.marketadjustitem.mapper.MarketAdjustItemMapper;
import vip.xiaonuo.business.marketadjustitem.service.MarketAdjustItemService;
import vip.xiaonuo.business.marketadjustitemtime.entity.MarketAdjustItemTime;
import vip.xiaonuo.business.marketadjustitemtime.mapper.MarketAdjustItemTimeMapper;
import vip.xiaonuo.business.marketadjustitemtime.service.MarketAdjustItemTimeService;
import vip.xiaonuo.core.consts.DpConstants;
import vip.xiaonuo.core.util.DpDataUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Component
public class KlineControlJob {
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private Logger logger = LoggerFactory.getLogger(KlineControlJob.class);

    @Resource
    MarketAdjustItemTimeService marketAdjustItemTimeService;

    @Resource
    MarketAdjustItemService marketAdjustItemService;

    @Resource
    MarketAdjustItemMapper marketAdjustItemMapper;

    @Resource
    MarketAdjustItemTimeMapper marketAdjustItemTimeMapper;

    @Scheduled(cron = "0/1 * * * * ?")
    public void control() {
        long nowTs = System.currentTimeMillis();
        nowTs = (nowTs / 1000) * 1000; // 舍弃毫秒部分，对齐整秒
        List<MarketAdjustItem> list = marketAdjustItemMapper.selectList(
                Wrappers.<MarketAdjustItem>lambdaQuery()
                        .gt(MarketAdjustItem::getLeftSeconds, 0)
                        .ge(MarketAdjustItem::getEndTs, nowTs)  // 大于等于开始时间
                        .le(MarketAdjustItem::getStartTs, nowTs)    // 小于等于结束时间
        );

        for (MarketAdjustItem item : list) {
            MarketAdjustItemTime itemTime = marketAdjustItemTimeMapper.selectOne(
                    Wrappers.lambdaQuery(MarketAdjustItemTime.class)
                            .eq(MarketAdjustItemTime::getMarketAdjustItemId, item.getId())
                            .eq(MarketAdjustItemTime::getTs, nowTs)
//                            .orderByAsc(MarketAdjustItemTime::getTs)
                            .last("LIMIT 1")
            );
            // 如果当前时间点没有匹配的时间数据，就设置为0
            if (itemTime == null) {
                item.setLeftSeconds(0);
                marketAdjustItemMapper.updateById(item);
            } else {
                Integer leftSeconds = item.getLeftSeconds() - 1;
                String completedKey = String.format(DpConstants.ADJUST_COMPLETED_PRICE, item.getSymbol());
                String adjustKey = String.format(DpConstants.MARKET_ADJUST_PRICE, item.getSymbol());
                BigDecimal adjustPrice = DpDataUtils.getAdjustPrice(redisTemplate, item.getSymbol());
                // 加上控制的逻辑
                if (adjustPrice == null) {
                    // 这个说明是一秒钟执行一次，更新一次价格，假如原本已经控制过的K线的话
                    adjustPrice = itemTime.getUnitValue();
                    redisTemplate.opsForValue().set(adjustKey, adjustPrice);
                } else {
                    // 这个说明是一秒钟执行一次，更新一次价格，假如原本已经控制过的K线的话
                    adjustPrice = itemTime.getUnitValue().add(adjustPrice);
                    redisTemplate.opsForValue().set(adjustKey, itemTime.getUnitValue().add(adjustPrice));
                }
                item.setLeftSeconds(leftSeconds);
                marketAdjustItemMapper.updateById(item);

                if (Integer.valueOf(1).equals(itemTime.getIsOneMinute()) || Integer.valueOf(0).equals(leftSeconds)) {
                    // 覆盖
                    redisTemplate.opsForHash().put(completedKey, "P", adjustPrice);
                    redisTemplate.opsForHash().put(completedKey, "T", itemTime.getTs());
                }
            }
        }
    }
}
