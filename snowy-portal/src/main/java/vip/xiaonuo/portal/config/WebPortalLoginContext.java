package vip.xiaonuo.portal.config;

import org.springframework.stereotype.Component;
import vip.xiaonuo.core.context.login.LoginContext;
import vip.xiaonuo.session.utils.PrincipalUtils;

@Component
public class WebPortalLoginContext implements LoginContext {
    @Override
    public Long getLoginUserId() {
        return PrincipalUtils.getPlayerId();
    }

    @Override
    public boolean hasLogin() {
        return PrincipalUtils.getSessionContent() != null;
    }

    @Override
    public String getLoginUserAccount() {
        return PrincipalUtils.getSessionContent().getAccount();
    }

    @Override
    public String getLoginUserOrgCode() {
        return PrincipalUtils.getPlayerOrgCode();
    }
}
