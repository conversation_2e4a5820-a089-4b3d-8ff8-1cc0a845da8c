/*
Copyright [2020] [https://www.stylefeng.cn]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Guns采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Guns源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/stylefeng/guns-separation
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/stylefeng/guns-separation
6.若您的项目无法满足以上几点，可申请商业授权，获取Guns商业授权许可，请在官网购买授权，地址为 https://www.stylefeng.cn
 */
package vip.xiaonuo.portal.config;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.log.Log;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.NoHandlerFoundException;
import vip.xiaonuo.core.consts.SymbolConstant;
import vip.xiaonuo.core.context.requestno.RequestNoContext;
import vip.xiaonuo.core.exception.AuthException;
import vip.xiaonuo.core.exception.PermissionException;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.exception.enums.*;
import vip.xiaonuo.core.exception.enums.abs.AbstractBaseExceptionEnum;
import vip.xiaonuo.core.pojo.response.ErrorResponseData;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.util.MessageUtils;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * 全局异常处理器
 */
@Order(-120)
@ControllerAdvice
public class GlobalPortalExceptionHandler {

    private static final Log log = Log.get();

    /**
     * 请求参数缺失异常
     *
     * <AUTHOR>
     * @date 2020/6/7 18:03
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseBody
    public ErrorResponseData<?> missParamException(HttpServletRequest request, MissingServletRequestParameterException e) {
        log.error(e, ">>> 请求参数异常，{}，具体信息为：{}", toCommonMsg(request), e.getMessage());
        String parameterType = e.getParameterType();
        String parameterName = e.getParameterName();
        String message = StrUtil.format(">>> 缺少请求的参数{}，类型为{}", parameterName, parameterType);
        return renderJson(500, message);
    }

    /**
     * 拦截参数格式传递异常
     *
     * <AUTHOR>
     * @date 2020/4/2 15:32
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseBody
    public ErrorResponseData<?> httpMessageNotReadable(HttpServletRequest request, HttpMessageNotReadableException e) {
        log.error(e, ">>> 参数格式传递异常，{}，具体信息为：{}", toCommonMsg(request), e.getMessage());
        return renderJson(RequestTypeExceptionEnum.REQUEST_JSON_ERROR);
    }

    /**
     * 拦截不支持媒体类型异常
     *
     * <AUTHOR>
     * @date 2020/4/2 15:38
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseBody
    public ErrorResponseData<?> httpMediaTypeNotSupport(HttpMediaTypeNotSupportedException e) {
        log.error(e, ">>> 参数格式传递异常，请求号为：{}，具体信息为：{}", RequestNoContext.get(), e.getMessage());
        return renderJson(RequestTypeExceptionEnum.REQUEST_TYPE_IS_JSON);
    }

    /**
     * 拦截请求方法的异常
     *
     * <AUTHOR>
     * @date 2020/3/18 19:14
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseBody
    public ErrorResponseData<?> methodNotSupport(HttpServletRequest request) {
        if (ServletUtil.isPostMethod(request)) {
            log.error(">>> 请求方法异常，请求号为：{}，具体信息为：{}", RequestNoContext.get(), RequestMethodExceptionEnum.REQUEST_METHOD_IS_GET.getMessage());
            return renderJson(RequestMethodExceptionEnum.REQUEST_METHOD_IS_GET);
        }
        if (ServletUtil.isGetMethod(request)) {
            log.error(">>> 请求方法异常，请求号为：{}，具体信息为：{}", RequestNoContext.get(), RequestMethodExceptionEnum.REQUEST_METHOD_IS_POST.getMessage());
            return renderJson(RequestMethodExceptionEnum.REQUEST_METHOD_IS_POST);
        }
        return null;
    }

    /**
     * 拦截资源找不到的运行时异常
     *
     * <AUTHOR>
     * @date 2020/4/2 15:38
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    @ResponseBody
    public ErrorResponseData<?> notFound(NoHandlerFoundException e) {
        log.error(e, ">>> 资源不存在异常，请求号为：{}，具体信息为：{}", RequestNoContext.get(), e.getMessage());
        return renderJson(PermissionExceptionEnum.URL_NOT_EXIST);
    }

    /**
     * 拦截参数校验错误异常,JSON传参
     *
     * <AUTHOR>
     * @date 2020/4/2 15:38
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ErrorResponseData<?> methodArgumentNotValidException(HttpServletRequest request, MethodArgumentNotValidException e) {
        String argNotValidMessage = getArgNotValidMessage(e.getBindingResult());
        log.error(e, ">>> 参数校验错误异常，{}，具体信息为：{}", toCommonMsg(request), argNotValidMessage);
        return renderJson(ParamExceptionEnum.PARAM_ERROR.getCode(), argNotValidMessage);
    }

    /**
     * 拦截参数校验错误异常
     *
     * <AUTHOR>
     * @date 2020/3/18 19:41
     */
    @ExceptionHandler(BindException.class)
    @ResponseBody
    public ErrorResponseData<?> paramError(HttpServletRequest request, BindException e) {
        String argNotValidMessage = getArgNotValidMessage(e.getBindingResult());
        log.error(e, ">>> 参数校验错误异常，{}，具体信息为：{}", toCommonMsg(request), argNotValidMessage);
        return renderJson(ParamExceptionEnum.PARAM_ERROR.getCode(), argNotValidMessage);
    }

    /**
     * 拦截认证失败异常
     *
     * <AUTHOR>
     * @date 2020/3/18 19:41
     */
    @ExceptionHandler(AuthException.class)
    @ResponseBody
    public ErrorResponseData<?> authFail(AuthException e) {
        AbstractBaseExceptionEnum exceptionEnum = e.getExceptionNum();
        if (AuthExceptionEnum.ACCOUNT_PWD_ERROR == exceptionEnum || AuthExceptionEnum.ACCOUNT_FREEZE_ERROR == exceptionEnum) {
            log.error(">>> 认证异常，请求号为：{}，具体信息为：{}", RequestNoContext.get(), ExceptionUtil.getMessage(e));
        } else {
            log.error(e, ">>> 认证异常，请求号为：{}，具体信息为：{}", RequestNoContext.get());
        }
        String message = MessageUtils.get(String.valueOf(e.getCode()), e.getErrorMessage());
        return renderJson(e.getCode(), message);
    }

    /**
     * 拦截权限异常
     *
     * <AUTHOR>
     * @date 2020/3/18 19:41
     */
    @ExceptionHandler(PermissionException.class)
    @ResponseBody
    public ErrorResponseData<?> noPermission(PermissionException e) {
        log.error(e, ">>> 权限异常，请求号为：{}", RequestNoContext.get());
        String message = MessageUtils.get(String.valueOf(e.getCode()), e.getErrorMessage());
        return renderJson(e.getCode(), message);
    }

    /**
     * 拦截业务异常
     *
     * <AUTHOR>
     * @date 2020/3/18 19:41
     */
    @ExceptionHandler(ServiceException.class)
    @ResponseBody
    public ErrorResponseData<?> businessError(HttpServletRequest request, ServiceException e) {
        log.error(e, ">>> 业务异常，{}，具体信息为：{}", toCommonMsg(request), RequestNoContext.get());
        String message = MessageUtils.get(String.valueOf(e.getCode()), e.getErrorMessage());
        return renderJson(e.getCode(), message, e);
    }

    /**
     * 拦截mybatis数据库操作的异常
     * <p>
     * 用在demo模式，拦截DemoException
     *
     * <AUTHOR>
     * @date 2020/5/5 15:19
     */
    @ExceptionHandler(MyBatisSystemException.class)
    @ResponseBody
    public ErrorResponseData<?> persistenceException(HttpServletRequest request, MyBatisSystemException e) {
        log.error(e, ">>> mybatis操作出现异常，" + toCommonMsg(request));
        return renderJson(500, "Service Exception");
    }

    /**
     * 拦截未知的运行时异常
     *
     * <AUTHOR>
     * @date 2020/3/18 19:41
     */
    @ExceptionHandler(Throwable.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ResponseBody
    public ErrorResponseData<?> serverError(HttpServletRequest request, Throwable e) {
        log.error(e, ">>>服务器运行异常，" + toCommonMsg(request));
        return renderJson(e);
    }

    private String toCommonMsg(HttpServletRequest request) {
        StringBuilder msg = new StringBuilder();
        msg.append("请求号：").append(RequestNoContext.get());
        msg.append("，URL：").append(request.getRequestURI());
        msg.append("，IP：").append(ServletUtil.getClientIP(request));
        msg.append("，Device：").append(PrincipalUtils.getDevice());
        msg.append("，Account：").append(PrincipalUtils.getPlayerAccount());
        return msg.toString();
    }

    /**
     * 渲染异常json
     *
     * <AUTHOR>
     * @date 2020/5/5 16:22
     */
    private ErrorResponseData<?> renderJson(Integer code, String message) {
        return renderJson(code, message, null);
    }

    /**
     * 渲染异常json
     *
     * <AUTHOR>
     * @date 2020/5/5 16:22
     */
    private ErrorResponseData<?> renderJson(AbstractBaseExceptionEnum baseExceptionEnum) {
        return renderJson(baseExceptionEnum.getCode(), baseExceptionEnum.getMessage(), null);
    }

    /**
     * 渲染异常json
     *
     * <AUTHOR>
     * @date 2020/5/5 16:22
     */
    private ErrorResponseData<?> renderJson(Throwable throwable) {
        AbstractBaseExceptionEnum exceptionEnum = ServerExceptionEnum.SERVER_ERROR;
        String message = MessageUtils.get(String.valueOf(exceptionEnum.getCode()), exceptionEnum.getMessage());
        return renderJson(exceptionEnum.getCode(), message, throwable);
    }

    /**
     * 渲染异常json
     * <p>
     * 根据异常枚举和Throwable异常响应，异常信息响应堆栈第一行
     *
     * <AUTHOR>
     * @date 2020/5/5 16:22
     */
    private ErrorResponseData<?> renderJson(Integer code, String message, Throwable e) {
        return ResponseData.error(code, message);
    }

    /**
     * 获取请求参数不正确的提示信息
     * <p>
     * 多个信息，拼接成用逗号分隔的形式
     *
     * <AUTHOR>
     * @date 2020/5/5 16:50
     */
    private String getArgNotValidMessage(BindingResult bindingResult) {
        if (bindingResult == null) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();

        //多个错误用逗号分隔
        List<ObjectError> allErrorInfos = bindingResult.getAllErrors();
        for (ObjectError error : allErrorInfos) {
            stringBuilder.append(SymbolConstant.COMMA).append(error.getDefaultMessage());
        }

        //最终把首部的逗号去掉
        return StrUtil.removePrefix(stringBuilder.toString(), SymbolConstant.COMMA);
    }

}
