package vip.xiaonuo.portal.config;

import cn.hutool.core.collection.CollectionUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.AllowableListValues;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;
import vip.xiaonuo.core.util.Language;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;

@Configuration
@EnableSwagger2WebMvc
public class Swagger2Config {
    public static final String[] profiles = new String[]{"local", "test"};
    private String basePackage = "vip.xiaonuo.portal.controller";
    @Resource
    private Environment environment;

    @Bean
    public Docket createRestApi() {
        return newDocket("0Default（所有）", PathSelectors.any());
    }

    @Bean
    public Docket createAccountApi() {
        return newDocket("Account（账户）", PathSelectors.ant("/account/**"));
    }

    @Bean
    public Docket createOrderApi() {
        return newDocket("Order（交易）", PathSelectors.ant("/order/**"));
    }

    @Bean
    public Docket createPortalApi() {
        return newDocket("common（公共）", PathSelectors.ant("/common/**"));
    }

    @Bean
    public Docket createMarketApi() {
        return newDocket("Market（行情）", PathSelectors.ant("/market/**"));
    }

    @Bean
    public Docket createUserApi() {
        return newDocket("Player（玩家）", PathSelectors.ant("/player/**"));
    }

    private Docket newDocket(String groupName, Predicate<String> selector) {
        //开发与测试环境开启
        boolean isEnable = environment.acceptsProfiles(profiles);

        Parameter parameter = new ParameterBuilder()
                .name(PrincipalUtils.Const.CLIENT_TOKEN_NAME)
                .description("token令牌")
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(false)
                .build();
        List<Parameter> parameters = CollectionUtil.newArrayList();
        parameters.add(parameter);
        parameters.add(new ParameterBuilder()
                .name(PrincipalUtils.Const.DEVICE_PLATFORM)
                .description("设备标识(1:PC,2:H5,3:ANDROID,4:IOS)")
                .allowableValues(new AllowableListValues(Arrays.asList("1", "2", "3", "4"), "string"))
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(true)
                .build());
        parameters.add(new ParameterBuilder()
                .name(Language.CLIENT_LANGUAGE)
                .allowableValues(new AllowableListValues(Language.toArray(), "string"))
                .defaultValue(Language.zh_CN.name())
                .description("语言")
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(true)
                .build());
        parameters.add(new ParameterBuilder()
                .name(PrincipalUtils.Const.ORG_CODE)
                .defaultValue("xg_cm")
                .description("机构")
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(true)
                .build());

        return new Docket(DocumentationType.SWAGGER_2)
                .groupName(groupName)
                //忽略参数类型
                .ignoredParameterTypes(HttpServletResponse.class, HttpServletRequest.class, HttpSession.class)
                .enable(isEnable)
                .apiInfo(apiInfo())
                .select()
                //扫描包下的接口
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                //过滤请求路径
                .paths(selector)
                .build()
                .globalOperationParameters(parameters);
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("Coin restful APIs")
                .version("1.0")
                .build();
    }
}
