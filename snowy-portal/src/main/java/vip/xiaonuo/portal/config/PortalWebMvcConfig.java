package vip.xiaonuo.portal.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import vip.xiaonuo.business.config.WebMvcConfig;
import vip.xiaonuo.session.security.UserSessionService;
import vip.xiaonuo.session.security.interceptors.IpBlacklistInterceptor;
import vip.xiaonuo.session.security.interceptors.LoginInterceptor;
import vip.xiaonuo.session.security.interceptors.UserSessionInterceptor;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Configuration
public class PortalWebMvcConfig extends WebMvcConfig {
    @Resource
    private UserSessionService userSessionService;
    @Resource
    private IpBlacklistInterceptor ipBlacklistInterceptor;

//    @Value("${logo.path}")
//    private String logoPath;
//
//    @Override
//    public void addResourceHandlers(ResourceHandlerRegistry registry) {
//        registry.addResourceHandler("/static/**").addResourceLocations("file:" + logoPath + "/");
//    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        super.addInterceptors(registry);

        List<String> excludeAllPath = new ArrayList<>();
        excludeAllPath.add("/doc.html");
        excludeAllPath.add("/webjars/**");
        excludeAllPath.add("/designer/**");
        excludeAllPath.add("/assets/**");
        excludeAllPath.add("/swagger-resources/**");
        excludeAllPath.add("/transaction/order/notice");
        registry.addInterceptor(new UserSessionInterceptor(userSessionService)).addPathPatterns("/**")
                .excludePathPatterns(excludeAllPath);

        registry.addInterceptor(ipBlacklistInterceptor).addPathPatterns("/**").excludePathPatterns(excludeAllPath);

        //不登录能访问
        List<String> excludePath = new ArrayList<>(excludeAllPath);
        excludePath.add("/market/**");//市场
        excludePath.add("/login");//登录
        excludePath.add("/register");//注册
        excludePath.add("/common/areaCode/list");//国际代码
        excludePath.add("/common/richText/info");
        excludePath.add("/common/spotRule/list");//获取现货规则
        excludePath.add("/common/contractRule");//获取合约规则
        excludePath.add("/common/getConfig");//获取公共配置
        excludePath.add("/common/telegramBot/set");
        excludePath.add("/common/website/report/**");//获取公共配置
        excludePath.add("/player/resetLoginPassword");//重置登录密码
        excludePath.add("/player/customer/link");//获取客服跳转链接
        excludePath.add("/common/file/previewFile");//图片预览
        excludePath.add("/player/rotation/list");
        excludePath.add("/player/notice/page");
        excludePath.add("/player/notice/detail/**");
        excludePath.add("/player/helpCenter/page");
        excludePath.add("/player/helpCenter/detail/**");
        excludePath.add("/transaction/order/notice");
        excludePath.add("/order/getTransInfo");
        registry.addInterceptor(new LoginInterceptor()).addPathPatterns("/**").excludePathPatterns(excludePath);
    }
}
