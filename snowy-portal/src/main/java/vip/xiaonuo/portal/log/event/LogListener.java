package vip.xiaonuo.portal.log.event;

import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import vip.xiaonuo.business.ipinfo.service.IpInfoService;
import vip.xiaonuo.business.log.entity.WebLog;
import vip.xiaonuo.business.log.service.WebLogService;

import javax.annotation.Resource;

/**
 * 异步监听日志事件
 */
@Component
public class LogListener {

    @Resource
    private WebLogService webLogService;
    @Resource
    private IpInfoService ipInfoService;

    @Async
    @Order
    @EventListener(WebLogEvent.class)
    public void saveSysLog(WebLogEvent event) {
        WebLog webLog = (WebLog) event.getSource();
        webLogService.save(webLog);
        //保存ip信息
        ipInfoService.saveIpInfo(webLog.getIp());
    }

}
