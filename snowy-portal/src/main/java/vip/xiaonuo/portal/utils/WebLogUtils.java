package vip.xiaonuo.portal.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import vip.xiaonuo.business.log.entity.WebLog;
import vip.xiaonuo.core.context.login.LoginContextHolder;
import vip.xiaonuo.core.enums.LogWebOpTypeEnum;
import vip.xiaonuo.core.exception.ServiceException;
import vip.xiaonuo.core.exception.enums.ServerExceptionEnum;
import vip.xiaonuo.core.util.HttpServletUtil;
import vip.xiaonuo.session.utils.PrincipalUtils;
import vip.xiaonuo.core.util.UaUtil;
import vip.xiaonuo.portal.log.event.WebLogEvent;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

public final class WebLogUtils {
    /**
     * 获取日志（已登录）
     *
     * @param typeEnum
     * @param title
     * @return
     */
    public static WebLog getAuthLog(LogWebOpTypeEnum typeEnum, String title) {
        WebLog WebLog = getLog(typeEnum, title);
        WebLog.setAccount(LoginContextHolder.me().getLoginUserAccount());
        return WebLog;
    }

    public static void executeLog(String account, LogWebOpTypeEnum typeEnum, String message) {
        WebLog WebLog = getLog(typeEnum, "");
        WebLog.setAccount(account);
        WebLog.setOpType(typeEnum.getType());
        WebLog.setMessage(message);
        SpringUtil.getApplicationContext().publishEvent(new WebLogEvent(WebLog));
    }

    private static WebLog getLog(LogWebOpTypeEnum typeEnum, String title) {
        HttpServletRequest request = HttpServletUtil.getRequest();
        if (ObjectUtil.isNotNull(request)) {
            String ip = ServletUtil.getClientIP(request);
            String browser = UaUtil.getBrowser(request);
            String os = UaUtil.getOs(request);
            String url = request.getRequestURI();
            String method = request.getMethod();
            WebLog WebLog = genBaseLog(ip, null, browser, os, url, method);
            WebLog.setOpType(typeEnum.getType());
            WebLog.setName(title);
            WebLog.setDeviceType(PrincipalUtils.getDevice());
            return WebLog;
        } else {
            throw new ServiceException(ServerExceptionEnum.REQUEST_EMPTY);
        }
    }

    /**
     * 构建基础操作日志
     */
    private static WebLog genBaseLog(String ip, String location, String browser, String os, String url, String method) {
        WebLog WebLog = new WebLog();
        WebLog.setIp(ip);
        WebLog.setLocation(location);
        WebLog.setBrowser(browser);
        WebLog.setOs(os);
        WebLog.setUrl(url);
        WebLog.setReqMethod(method);
        WebLog.setOpTime(new Date());
        return WebLog;
    }
}
