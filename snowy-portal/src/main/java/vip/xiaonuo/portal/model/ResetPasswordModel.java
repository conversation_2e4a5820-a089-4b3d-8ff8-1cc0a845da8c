package vip.xiaonuo.portal.model;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel(value = "重置密码")
@Data
public class ResetPasswordModel {
    @ApiModelProperty(value = "新密码", required = true)
    @NotBlank
    private String password;

    @NotBlank(message = "国际区号为空")
    @ApiModelProperty(value = "国际区号（不包含+号）", required = true)
    private String areaCode;

    @NotBlank(message = "手机号码为空，请检查phoneNumbers参数")
    @ApiModelProperty(value = "手机号（不包含国际区号）", required = true)
    private String phone;

    @ApiModelProperty(value = "短信验证码", required = true, example = "666666")
    @NotBlank(message = "短信验证码不能为空")
    private String smsCode;
}
