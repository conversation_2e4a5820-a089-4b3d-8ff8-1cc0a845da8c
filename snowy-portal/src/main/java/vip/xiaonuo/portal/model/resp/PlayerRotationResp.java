package vip.xiaonuo.portal.model.resp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "轮播信息")
public class PlayerRotationResp {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "轮播标题")
    private String title;

    @ApiModelProperty(value = "轮播图片url")
    private String imgUrl;

    @ApiModelProperty(value = "轮播图跳转详情页内容")
    private String detail;

    @ApiModelProperty(value = "状态（字典 0草稿 1发布 2撤回 3删除）")
    private Integer status;

    @ApiModelProperty(value = "排序字段")
    private Integer sort;
}
