package vip.xiaonuo.portal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@ApiModel(description = "银行账户信息入参")
public class BankAccountModel {

    @ApiModelProperty(value = "收款人姓名")
    @NotBlank(message = "收款人姓名不能为空")
    private String userName;

    @ApiModelProperty(value = "收款人银行卡号")
    @NotBlank(message = "收款人银行卡号不能为空")
    private String bankCardNumber;

    @ApiModelProperty(value = "收款人银行")
    @NotBlank(message = "收款人银行不能为空")
    private String bankName;

//    @ApiModelProperty(value = "收款人国家")
//    @NotBlank(message = "收款人国家不能为空")
//    private String area;

//    @ApiModelProperty(value = "收款人城市")
//    @NotBlank(message = "收款人城市不能为空")
//    private String city;

//    @ApiModelProperty(value = "收款人地址")
//    @NotBlank(message = "收款人地址不能为空")
//    private String address;

//    @ApiModelProperty(value = "收款人支行信息")
//    private String subBranch;
//
//    @ApiModelProperty(value = "国际电话编码")
//    private String internationalTelephoneNumber;
//
//    @ApiModelProperty(value = "清算号")
//    private String liquidationNumber;

    @ApiModelProperty(value = "银行地址")
    private String bankAddress;
//
//    @ApiModelProperty(value = "创建人")
//    private String createUser;
//
//    @ApiModelProperty(value = "创建时间")
//    private Date createTime;
}
