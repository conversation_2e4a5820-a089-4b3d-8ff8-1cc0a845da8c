package vip.xiaonuo.portal.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("注册参数")
public class RegisterModel {

    private String areaCode;

    private String account;

    private String phone;

    private String email;

    @ApiModelProperty(value = "账号密码", required = true, example = "")
    private String password;

    @ApiModelProperty(value = "邀请码", required = true, example = "u1234")
    private String inviteCode;

    @ApiModelProperty(value = "短信验证码", required = true, example = "666666")
    private String smsCode;

}
