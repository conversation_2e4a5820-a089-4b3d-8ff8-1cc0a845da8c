package vip.xiaonuo.portal.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "玩家银行账户信息")
public class PlayerBankAccountResp {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "玩家id")
    private Long playerId;

    @ApiModelProperty(value = "收款人姓名")
    private String userName;

    @ApiModelProperty(value = "收款人银行卡号")
    private String bankCardNumber;

    @ApiModelProperty(value = "收款人银行")
    private String bankName;

    @ApiModelProperty(value = "收款人国家")
    private String area;

    @ApiModelProperty(value = "收款人城市")
    private String city;

    @ApiModelProperty(value = "收款人地址")
    private String address;

    @ApiModelProperty(value = "分行信息")
    private String subBranch;

    @ApiModelProperty(value = "国际电话编码")
    private String internationalTelephoneNumber;

    @ApiModelProperty(value = "清算号")
    private String liquidationNumber;

    @ApiModelProperty(value = "银行地址")
    private String bankAddress;
}
