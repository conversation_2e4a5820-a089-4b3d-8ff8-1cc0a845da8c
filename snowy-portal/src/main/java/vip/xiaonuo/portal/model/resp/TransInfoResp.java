package vip.xiaonuo.portal.model.resp;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class TransInfoResp {
    private BigDecimal transAmount = BigDecimal.valueOf(353348228);

    private Integer transCount = 210358;

    public TransInfoResp(){}

    public TransInfoResp(BigDecimal transAmount,Integer transCount){
        this.transAmount = transAmount;
        this.transCount = transCount;
    }
}
