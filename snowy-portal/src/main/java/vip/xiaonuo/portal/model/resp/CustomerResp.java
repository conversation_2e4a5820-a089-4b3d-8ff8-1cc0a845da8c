package vip.xiaonuo.portal.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "客服管理信息")
public class CustomerResp {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "嵌入链接")
    private String embedLink;

    @ApiModelProperty(value = "跳转链接")
    private String forwardLink;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态（1-启用；禁用）")
    private Integer status;
}
