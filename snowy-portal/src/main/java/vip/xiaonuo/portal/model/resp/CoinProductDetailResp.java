package vip.xiaonuo.portal.model.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "产品简介信息")
public class CoinProductDetailResp {


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 语言编码
     */
    @ApiModelProperty(value = "语言编码")
    private String languageCode;

    /**
     * 币种编码
     */
    @ApiModelProperty(value = "币种编码")
    private String currencyCode;

    /**
     * 发行时间
     */
    @ApiModelProperty(value = "发行时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date releaseTime;

    /**
     * 发行总量
     */
    @ApiModelProperty(value = "发行总量")
    private String totalIssuance;

    /**
     * 流通总量
     */
    @ApiModelProperty(value = "流通总量")
    private String totalCirculation;

    /**
     * 官方网站
     */
    @ApiModelProperty(value = "官方网站")
    private String officialWebsite;

    /**
     * 白皮书
     */
    @ApiModelProperty(value = "白皮书")
    private String whitePaper;

    /**
     * 区块查询
     */
    @ApiModelProperty(value = "区块查询")
    private String blockQuery;

    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String briefIntroduce;
}
