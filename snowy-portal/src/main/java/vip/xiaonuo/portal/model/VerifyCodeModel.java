package vip.xiaonuo.portal.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import vip.xiaonuo.core.enums.SmsSendSourceEnum;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel("短信验证码验证参数")
@Data
public class VerifyCodeModel {
    @NotBlank(message = "国际区号为空")
    @ApiModelProperty(value = "国际区号（不包含+号）", required = true)
    private String areaCode;
    /**
     * 手机号
     */
    @NotBlank(message = "手机号码为空，请检查phoneNumbers参数")
    @ApiModelProperty(value = "手机号（不包含国际区号）", required = true)
    private String phone;
    /**
     * 发送源
     */
    @NotNull(message = "发送源为空")
    @ApiModelProperty(value = "短信发送源(RESET_PASSWORD:忘记登录密码,FORGOT_WITHDRAWAL_PASSWORD:忘记提款密码)", required = true)
    private SmsSendSourceEnum source;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    @ApiModelProperty(value = "短信验证码", required = true)
    private String code;
}
