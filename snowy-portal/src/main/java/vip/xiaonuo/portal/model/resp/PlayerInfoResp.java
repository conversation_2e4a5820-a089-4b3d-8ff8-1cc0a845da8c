package vip.xiaonuo.portal.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("玩家信息")
public class PlayerInfoResp {
    /**
     * 机构代码
     */
    @ApiModelProperty("机构代码")
    private String orgCode;
    /**
     * 账号
     */
    @ApiModelProperty("账号")
    private String account;
    //国际区号
    @ApiModelProperty("国际区号")
    private String phoneCode;
    /**
     * 手机
     */
    @ApiModelProperty("手机")
    private String phone;
    /**
     * 昵称
     */
    @ApiModelProperty("昵称")
    private String nickName;

    /**
     * 头像
     */
    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("是否有资金密码(0:没有,1:有)")
    private Integer fundsPasswordFlag;


    @ApiModelProperty("初级认证状态 0 待提交 1 审核中 2 审核通过  3 审核不通过 ")
    private Integer authStatus;

    @ApiModelProperty("高级认证状态 0 待提交 1 审核中 2 审核通过  3 审核不通过 ")
    //高级验证状态 0 待提交  1审核中 2审核通过 3审核失败
    private Integer advancedStatus;
}
