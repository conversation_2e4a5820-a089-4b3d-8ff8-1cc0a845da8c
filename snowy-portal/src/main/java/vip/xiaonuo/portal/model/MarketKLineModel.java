package vip.xiaonuo.portal.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@ApiModel("查询K线")
public class MarketKLineModel {
    @ApiModelProperty("交易对")
    private String symbol;
    @ApiModelProperty("1m,5m,15m,30m,1h,2h,4h,6h,12h,1d,1w")
    private String period;
    @ApiModelProperty("当前日期的时间戳")
    private Long endTime;
    @ApiModelProperty("条数")
    private Integer parsedSize;

    public Integer getParsedSize() {
        return parsedSize;
    }

    public void setParsedSize(Integer parsedSize) {
        this.parsedSize = parsedSize;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public void validate() {
        if (!isCorrectSymbol(symbol)) {
            throw new IllegalArgumentException("Invalid market symbol");
        }
        try {
            if (parsedSize < 1) {
                parsedSize = 150;
            }
            if (parsedSize > 1000) {
                parsedSize = 1000;
            }
        } catch (Exception e) {
            parsedSize = 150;
        }
    }

    private static Pattern symbolPattern = Pattern.compile("\\w{2,10}_\\w{2,10}");


    private static boolean isCorrectSymbol(String symbol) {
        if (StringUtils.isBlank(symbol)) {
            return false;
        }
        Matcher m = symbolPattern.matcher(symbol);
        return m.matches();
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }
}
