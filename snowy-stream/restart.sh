#!/bin/sh
APP_DIR=/mnt/snowy-coin-stream
APP_NAME=snowy-coin-stream.jar
SPRING_OPTS='--spring.profiles.active=test'
JAVA_OPTS='-Xms256m -Xmx1024m'
JAVA_BIN=/usr/bin/java
MAX_TIMEOUT=20
echo : [app dir] $APP_DIR
echo : [app name] $APP_NAME

pid=`ps -ef | grep java |grep $APP_NAME | grep -v grep |awk '{print $2}'`
if [ -n "$pid" ]; then
  echo : $APP_NAME is running pid=$pid
  echo : kill -15 $APP_NAME
  kill -15 $pid
fi

for i in `seq $MAX_TIMEOUT`
do
  sleep 1
  pid=`ps -ef | grep java |grep $APP_NAME | grep -v grep |awk '{print $2}'`
  if [ -n "$pid" ]; then
    echo : Stoping...
  else
    break
  fi
done

if [ -n "$pid" ]; then
  echo : $APP_NAME is running pid=$pid
  echo : kill -9 $APP_NAME
  kill -9 $pid
fi

pid=`ps -ef | grep java |grep $APP_NAME | grep -v grep |awk '{print $2}'`
if [ -n "$pid" ]; then
  echo : Running
else
  cp $APP_DIR/target/$APP_NAME $APP_DIR
  echo : Stopped
  echo : cp $APP_DIR/target/$APP_NAME $APP_DIR
fi

# 为了让logback日志记录到APP_DIR
cd $APP_DIR
nohup $JAVA_BIN -jar $JAVA_OPTS $APP_NAME $SPRING_OPTS > $APP_DIR/nohup.out 2>&1 &
echo : start $APP_NAME