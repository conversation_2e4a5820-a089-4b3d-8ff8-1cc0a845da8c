//package vip.xiaonuo.stream.config;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.redis.core.RedisTemplate;
//import vip.xiaonuo.core.pojo.dto.MarketDTO;
//
//@Configuration
//public class RedisConfig {
//
//    @Bean(name = "marketRedisTemplate")
//    public RedisTemplate<String, MarketDTO> redisMarket() {
//        return new RedisTemplate<>();
//    }
//}
