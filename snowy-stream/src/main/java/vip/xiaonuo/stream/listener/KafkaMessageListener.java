package vip.xiaonuo.stream.listener;


import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import vip.xiaonuo.core.pojo.dto.TickerDTO;
import vip.xiaonuo.core.util.KafkaTopics;
import vip.xiaonuo.stream.service.TickerService;

import javax.annotation.Resource;

/**
 * kafka消费队列
 */
@Component
public class KafkaMessageListener {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Resource
    private TickerService tickerService;

    @KafkaListener(topics = KafkaTopics.DP_MARKET_TICKER)
    public void marketTickerListener(ConsumerRecord<?, ?> record) {
        String value = (String) record.value();
        try {
            TickerDTO tickerDTO = JSON.parseObject(value, TickerDTO.class);
            long ct = System.currentTimeMillis() - (5 * 60 * 1000);
            if (tickerDTO.getT() == null || tickerDTO.getT() < ct) {
                //5分钟之后的数据不处理
                return;
            }
//            logger.info("Stream收到信息：" + new DateTime(tickerDTO.getT()));
//            logger.info("Stream收到信息："+JSON.toJSONString(tickerDTO));
            tickerService.writeToRedis(tickerDTO);
        } catch (Exception e) {
            logger.error("写入MarketDTO到redis异常", e);
        }

    }
}
