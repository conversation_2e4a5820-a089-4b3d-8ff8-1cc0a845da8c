package vip.xiaonuo.stream.service;


import aacoin.trade.exchange.CallFailedException;
import aacoin.trade.exchange.rest.ExchangeRestApi;
import aacoin.trade.exchange.rest.ExchangeRestApiManager;
import aacoin.trade.exchange.rest.KLinePeriodMapping;
import aacoin.trade.exchange.rest.binance.BinanceExchangeRestApi;
import com.alibaba.fastjson.JSON;
import com.binance.api.client.domain.general.ExchangeInfo;
import com.binance.api.client.domain.general.FilterType;
import com.binance.api.client.domain.general.SymbolFilter;
import com.binance.api.client.domain.general.SymbolInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import vip.xiaonuo.business.currency.entity.Currency;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.core.consts.DpConstants;
import vip.xiaonuo.core.enums.KLineType;
import vip.xiaonuo.core.pojo.dto.BatchKLineDTO;
import vip.xiaonuo.core.util.Locker;
import vip.xiaonuo.core.util.MarketUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Component
public class MarketDataService implements InitializingBean, DisposableBean {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Resource
    private ExchangeRestApiManager apiManager;
    @Resource
    private TickerService tickerService;
    @Resource
    private Locker locker;
    @Resource
    private MarketService marketService;
    @Resource
    private BatchKLineService batchKLineService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private CurrencyService currencyService;
    private ExecutorService executorService;

    @Override
    public void afterPropertiesSet() {
        executorService = Executors.newFixedThreadPool(5);
    }

    @Override
    public void destroy() throws Exception {
        executorService.shutdownNow();
    }

    public void init(Market market, boolean isReload) {
        logger.info("加载 market info {}", market.getSymbol());
//        initMarketTrade(market);

        // todo k线不订阅
//        executorService.execute(() -> {
//            initMarketKLineCache(market, isReload);
//        });
    }

    private void initMarketKLineCache(Market market, boolean isReload) {
        String symbol = market.getSymbol();
        List<String> disabledKLineTypes = new ArrayList<>();

        for (KLineType kLineType : KLineType.values()) {
            logger.info("load kline {}@{}", kLineType.getCode(), symbol);
            String key = String.format(DpConstants.REDIS_KLINE_KEY, symbol, kLineType.getCode());
            if (!isReload && redisTemplate.opsForZSet().size(key) > 10) {
                continue;
            }
            if (isReload) {
                redisTemplate.opsForZSet().removeRange(key, 0, -1);
            }

            List<BatchKLineDTO> batchKLineDTOS = new ArrayList<>();
            logger.info("get recent [{}] kline data from external {}", market.getSymbol(), market.getPartner());

            List<KLineType> supportedKLinePeriods = KLinePeriodMapping.getSupportedKLinePeriods(market.getPartner());
            if (!supportedKLinePeriods.contains(kLineType)) {
                logger.info("exchange {} 不支持 k线周期{}", market.getPartner(), kLineType.getCode());
                disabledKLineTypes.add(kLineType.getCode());
                continue;
            }

            Integer[] decimals = tickerService.getMarketDecimals(symbol);

            ExchangeRestApi exchangeApi = apiManager.getExchangeApi(market.getPartner());
            try {
                List<List<BigDecimal>> kLineData = exchangeApi.getKLines(market.getCurrencyCode(),
                        market.getBaseCurrencyCode(), kLineType, 1000);
                if (!CollectionUtils.isEmpty(kLineData)) {
                    batchKLineDTOS = kLineData.stream().map(e -> {
                        BatchKLineDTO dto = new BatchKLineDTO();
                        dto.setTime(new Date(e.get(0).longValue()));
                        dto.setOpen(e.get(1).setScale(decimals[0], RoundingMode.FLOOR));
                        dto.setHigh(e.get(2).setScale(decimals[0], RoundingMode.FLOOR));
                        dto.setLow(e.get(3).setScale(decimals[0], RoundingMode.FLOOR));
                        dto.setClose(e.get(4).setScale(decimals[0], RoundingMode.FLOOR));
                        dto.setVol(e.get(5));
                        return dto;
                    }).collect(Collectors.toList());
                }
            } catch (CallFailedException e) {
                //ignore
                disabledKLineTypes.add(kLineType.getCode());
            }
            batchKLineDTOS.forEach(e -> batchKLineService.writeToRedis(symbol, kLineType, e));
        }
        redisTemplate.opsForValue().set(String.format(DpConstants.KLINE_DISABLED_PERIODS, symbol), disabledKLineTypes);
        logger.info("kline@{} cache load done", symbol);
    }

    @Resource
    private BinanceExchangeRestApi binanceExchangeRestApi;

    public void getExchangeInfo() {
        ExchangeInfo exchangeInfo = binanceExchangeRestApi.getExchangeInfo();
//        logger.info("初始化exchangeInfo {}", JSON.toJSONString(exchangeInfo));
        List<Market> markets = marketService.getAllEnabledMarkets();
        List<String> existList = new ArrayList<>();
        for (Market market : markets) {
            String symbol = market.getCurrencyCode() + market.getBaseCurrencyCode();
            logger.info("初始化exchangeInfo {}", symbol);
            SymbolInfo symbolInfo = null;
            for (SymbolInfo symbolInfoTmp : exchangeInfo.getSymbols()) {
                if (symbol.equalsIgnoreCase(symbolInfoTmp.getSymbol())) {
                    symbolInfo = symbolInfoTmp;
                    break;
                }
            }
            if (symbolInfo != null) {
                existList.add(market.getSymbol());
                for (SymbolFilter symbolFilter : symbolInfo.getFilters()) {
                    if (symbolFilter.getFilterType() == FilterType.PRICE_FILTER) {
                        market.setPriceDecimal(MarketUtils.getDigits(symbolFilter.getMinPrice()));
                        market.setMinPriceGroupDecimal(market.getPriceDecimal());
                    }
                    if (symbolFilter.getFilterType() == FilterType.LOT_SIZE) {
                        market.setQuantityDecimal(MarketUtils.getDigits(symbolFilter.getMinQty()));
                    }
                }
                marketService.lambdaUpdate().set(Market::getPriceDecimal, market.getPriceDecimal())
                        .set(Market::getMinPriceGroupDecimal, market.getMinPriceGroupDecimal())
                        .set(Market::getQuantityDecimal, market.getQuantityDecimal())
                        .eq(Market::getId, market.getId()).update();
                logger.info("币安找到市场" + market.getSymbol() + "，进行更新" + market.getPriceDecimal() + "  " + market.getMinPriceGroupDecimal() + "  " + market.getQuantityDecimal());
            }
        }
        markets.removeIf(market -> existList.contains(market.getSymbol()));
        for (Market market : markets) {
            logger.info("币安没有找到，禁用市场" + market.getSymbol());
            marketService.lambdaUpdate().set(Market::getStatus, 0).eq(Market::getId, market.getId()).update();
//            currencyService.lambdaUpdate().set(Currency::getStatus, 0).eq(Currency::getCode, market.getCurrencyCode()).update();
        }
        logger.info("初始化exchangeInfo结束");
    }
//
//    public static void main(String[] args) {
//        BinanceExchangeRestApi api = new BinanceExchangeRestApi(new ExternalConfig() {{
//            setApiConfigs(new ArrayList<ExchangeApiConfig>() {{
//                add(new ExchangeApiConfig() {{
//                    setExchangeCode("binance");
//                }});
//            }});
//            setProxy(true);
//        }});
//        ExchangeInfo exchangeInfo = api.getExchangeInfo();
//        exchangeInfo.getSymbols().forEach(p -> {
//            System.out.println(p.getSymbol());
//            for (SymbolFilter symbolFilter : p.getFilters()) {
//                if (symbolFilter.getFilterType() == FilterType.PRICE_FILTER) {
//                    System.out.println(symbolFilter.getMinPrice() + "   " + MarketUtils.getDigits(symbolFilter.getMinPrice()));
//                }
//                if (symbolFilter.getFilterType() == FilterType.LOT_SIZE) {
//                    System.out.println(symbolFilter.getMinQty() + "  " + MarketUtils.getDigits(symbolFilter.getMinQty()));
//                }
//            }
//        });
//    }
}
