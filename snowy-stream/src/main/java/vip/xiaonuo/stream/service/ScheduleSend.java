package vip.xiaonuo.stream.service;


import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.core.pojo.dto.MarketDTO;
import vip.xiaonuo.stream.dto.KLinePushDTO;
import vip.xiaonuo.stream.orderbook.OrderBookService;
import vip.xiaonuo.stream.orderbook.dto.OrderBookDTO;
import vip.xiaonuo.stream.ws.DataSender;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.locks.ReentrantLock;


@Component
public class ScheduleSend {
    private Set<KLinePushDTO> klineSendTick = new HashSet<>();
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Resource
    private MarketService marketService;
    @Resource
    private MarketDataService marketDataService;
    @Resource
    private TickerService tickerService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private DataSender dataSender;
    @Resource
    private OrderBookService orderBookService;
    private ReentrantLock lock2 = new ReentrantLock();

    // todo gjn k线 发送K线
    @Scheduled(initialDelay = 5000L, fixedDelay = 1500L)
    public void sendKLineData() {
        lock2.lock();
        try {
            for (KLinePushDTO dto : klineSendTick) {
                dataSender.sendKLineData(dto.getSymbol(), dto.getPeriod(), dto.getLines());
            }
            klineSendTick.clear();
        } finally {
            lock2.unlock();
        }
    }

    public void addKLinePushDTO(String symbol, Long period, List<List<BigDecimal>> lines) {
        lock2.lock();
        try {
            klineSendTick.add(new KLinePushDTO(symbol, period, lines));
        } finally {
            lock2.unlock();
        }
    }

    @Scheduled(initialDelay = 5000L, fixedDelay = 1500L)
    public void sendMarketInfo() {
        List<MarketDTO> marketDTOS = tickerService.getMarketTickers();
        if (!CollectionUtils.isEmpty(marketDTOS)) {
            dataSender.sendMarketTicker(marketDTOS);
        }
    }
//
//    @Scheduled(initialDelay = 3000L, fixedDelay = 2000L)
//    void refreshMarket24hData() {
//        tickerService.refreshMarket24hData();
//    }

    @Scheduled(initialDelay = 3000L, fixedDelay = 350L)
    public void sendOrderTask() {
        for (String symbol : orderBookService.getSupportedSymbols()) {
            OrderBookDTO orderHolder = orderBookService.getOrderHolder(symbol);
            if (!orderHolder.shouldPush()) {
                continue;
            }
            for (Integer depth : orderBookService.getOptionalDepth(symbol)) {
                try {
                    dataSender.sendOrderData(symbol, depth,
                            orderHolder.getDataToPush(false, depth, true));
                } catch (Exception e) {
                    logger.error("", e);
                }
            }
        }
    }

    @Scheduled(initialDelay = 2 * 60 * 60 * 1000L, fixedDelay = 2 * 60 * 60 * 1000L)
    public void getExchangeInfo() {
        logger.info("执行getExchangeInfo");
        marketDataService.getExchangeInfo();
    }
}
