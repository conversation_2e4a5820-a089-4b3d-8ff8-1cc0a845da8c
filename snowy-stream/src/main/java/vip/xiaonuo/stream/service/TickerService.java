package vip.xiaonuo.stream.service;


import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.core.consts.DpConstants;
import vip.xiaonuo.core.pojo.dto.MarketDTO;
import vip.xiaonuo.core.pojo.dto.TickerDTO;
import vip.xiaonuo.core.util.DpDataUtils;
import vip.xiaonuo.core.util.MarketUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;


@Service
public class TickerService implements InitializingBean {
    private static Logger logger = LoggerFactory.getLogger(TickerService.class);
    @Resource
    private MarketService marketService;
    @Resource
    private MarketDataService marketDataService;
    //    @Autowired
//    private EstimatedService estimatedService;
//    @Autowired
//    private CurrencyRateService currencyRateService;
//    @Resource
//    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private Environment env;
    private HashOperations<String, String, MarketDTO> marketHashOperations;

    @Resource
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        marketHashOperations = redisTemplate.opsForHash();
        redisTemplateNew = redisTemplate;
    }

    private RedisTemplate<String, Object> redisTemplateNew;

    private static final Map<String, Integer[]> marketDecimal = new ConcurrentHashMap<>();

//    public void refreshMarket24hData() {
//        List<Market> markets = marketService.getAllEnabledMarkets();
//        Map<String, MarketDTO> market24h = (Map<String, MarketDTO>) redisTemplate.opsForValue().get(DpConstants.PREFIX_MARKET_24H);
//        if (market24h == null) {
//            market24h = new LinkedHashMap<>();
//        }
//        if (!CollectionUtils.isEmpty(markets)) {
//            for (Market market : markets) {
//                if (!marketDecimal.containsKey(market.getSymbol())) {
//                    marketDecimal.putIfAbsent(market.getSymbol(), new Integer[]{market.getPriceDecimal(), market.getQuantityDecimal()});
//                }
//                MarketDTO marketDTO = initMarket(market);
//
//                MarketDTO oldMarketDTO = market24h.get(market.getSymbol());
//                if (oldMarketDTO != null) {
//                    marketDTO.setPrevPrice(oldMarketDTO.getC());
//                }
//
//                marketDTO.setChangePercent(getChangePercent(marketDTO.getO(), marketDTO.getC()));
//
//                market24h.put(market.getSymbol(), marketDTO);
//            }
//            for (Iterator<String> itr = market24h.keySet().iterator(); itr.hasNext(); ) {
//                String s = itr.next();
//                Optional<Market> any = markets.stream().filter(e -> e.getSymbol().equalsIgnoreCase(s)).findAny();
//                if (!any.isPresent()) {
//                    itr.remove();
//                }
//            }
//            redisTemplate.opsForValue().set(DpConstants.PREFIX_MARKET_24H, market24h);
//        }

    /// /        estimatedService.init(getMarketTickers());
//    }
    public void clearDecimalCache() {
        marketDecimal.clear();
        allMarkets.clear();
    }

    /**
     * 返回交易对的精度配置
     */
    public Integer[] getMarketDecimals(String symbol) {
        Integer[] decimals = marketDecimal.get(symbol);
        if (decimals == null) {
            Market market = marketService.getMarketBySymbol(symbol);
            if (market != null) {
                decimals = new Integer[]{market.getPriceDecimal(), market.getQuantityDecimal(), market.getMinPriceGroupDecimal()};
                marketDecimal.put(symbol, decimals);
            }
        }
        return decimals == null ? new Integer[]{8, 3, 5} : decimals;
    }

//    private String getFormattedRealValue(BigDecimal realValue) {
//        NumberFormat numberFormat = NumberFormat.getCurrencyInstance(Locale.US);
//        numberFormat.setMaximumFractionDigits(4);
//        return numberFormat.format(realValue);
//    }
//
//    private String getChangePercent(BigDecimal price24HourBefore, BigDecimal current) {
//        BigDecimal change = BigDecimal.ZERO;
//        if (price24HourBefore != null && price24HourBefore.compareTo(BigDecimal.ZERO) > 0) {
//            change = current.subtract(price24HourBefore).multiply(new BigDecimal(100))
//                    .divide(price24HourBefore, 2, BigDecimal.ROUND_HALF_UP);
//        }
//        if (change.compareTo(BigDecimal.ZERO) > 0) {
//            return "+" + new DecimalFormat("0.00").format(change.doubleValue());
//        }
//        return new DecimalFormat("0.00").format(change.doubleValue());
//    }
//
//    public List<MarketDTO> getMarketTickers() {
//        Map<String, MarketDTO> market24h = (Map<String, MarketDTO>) redisTemplate.opsForValue().get(DpConstants.PREFIX_MARKET_24H);
//        if (market24h == null) {
//            market24h = new LinkedHashMap<>();
//        }
//        List<MarketDTO> marketDTOS = new ArrayList<>(market24h.values());
//        marketDTOS.sort((o1, o2) -> {
//            Integer sort1 = o1.getSort() == null ? -1 : o1.getSort();
//            Integer sort2 = o2.getSort() == null ? -1 : o2.getSort();
//            return sort1.compareTo(sort2);
//        });
//        return marketDTOS;
//    }

    public void writeToRedis(TickerDTO tickerDTO) {
        MarketDTO marketDTO = new MarketDTO();
        // 拷贝数据，这里可能要执行价格的增长
        BeanUtils.copyProperties(tickerDTO, marketDTO);
        marketDTO.setSymbolName(MarketUtils.getSymbolName(marketDTO.getSymbol()));
        Market market = getCacheMarket(marketDTO.getSymbol());
        if (market == null) {
            return;
        }
        marketDTO.setHot(market.getHot());
        marketDTO.setSort(market.getSort());
        marketDTO.setPriceDecimal(market.getPriceDecimal());
        marketDTO.setQuantityDecimal(market.getQuantityDecimal());
        marketDTO.setBaseCurrencyCode(market.getBaseCurrencyCode());
        marketDTO.setCurrencyCode(market.getCurrencyCode());
        // todo gjn 另外一个服务redis值，这里只需要获取KEY的值，可能是上涨可能是下跌
        // 写入数据,getSymbol = BTC_USDT
        BigDecimal adjustPrice = DpDataUtils.getAdjustPrice(redisTemplateNew, tickerDTO.getSymbol());
        if (adjustPrice != null) {
            // 加上控制逻辑
            marketDTO.setO(marketDTO.getO().add(adjustPrice));
            marketDTO.setH(marketDTO.getH().add(adjustPrice));
            marketDTO.setL(marketDTO.getL().add(adjustPrice));
            marketDTO.setC(marketDTO.getC().add(adjustPrice));
            marketDTO.setChangePercent(getChangePercent(marketDTO.getChangePercent()));
        } else {
            marketDTO.setChangePercent(getChangePercent(marketDTO.getChangePercent()));
        }
        marketHashOperations.put(DpConstants.PREFIX_MARKET_24H, tickerDTO.getSymbol(), marketDTO);
    }

    private static String getChangePercent(String p) {
        try {
            BigDecimal cp = new BigDecimal(p);
            String changePercent = new DecimalFormat("0.00").format(cp.doubleValue());
            if (cp.compareTo(BigDecimal.ZERO) > 0) {
                changePercent = "+" + changePercent;
            }
            return changePercent;
        } catch (Exception e) {
            return "0";
        }
    }

    public List<MarketDTO> getMarketTickers() {
        return DpDataUtils.getTickers(marketHashOperations);
    }

//    private MarketDTO initMarket(Market market) {
//        MarketDTO marketDTO = new MarketDTO();
//        marketDTO.setHot(market.getHot());
//        marketDTO.setSymbol(market.getSymbol());
//        marketDTO.setBaseCurrencyCode(market.getBaseCurrencyCode());
//        marketDTO.setSymbolName(market.getName());
//        marketDTO.setCurrencyCode(market.getCurrencyCode());
//        marketDTO.setSort(market.getSort());
//        marketDTO.setPriceDecimal(market.getPriceDecimal());
//        marketDTO.setQuantityDecimal(market.getQuantityDecimal());
//        calculate24hourTick(market.getPartner(), marketDTO);
//        return marketDTO;
//    }
//
//    private void calculate24hourTick(String partner, MarketDTO marketDTO) {
//        marketDTO.setH(BigDecimal.ZERO);
//        marketDTO.setL(BigDecimal.ZERO);
//        marketDTO.setO(BigDecimal.ZERO);
//        marketDTO.setObv(BigDecimal.ZERO);
//
//        MarketDTO calculated = null;
//        try {
//            calculated = getExternalTick24h(partner, marketDTO.getCurrencyCode(), marketDTO.getBaseCurrencyCode());
//        } catch (Exception e) {
//            logger.error("", e);
//        }
//        if (calculated != null) {
//            marketDTO.setC(calculated.getC().setScale(marketDTO.getPriceDecimal(), RoundingMode.FLOOR));
//            marketDTO.setH(calculated.getH().setScale(marketDTO.getPriceDecimal(), RoundingMode.FLOOR));
//            marketDTO.setL(calculated.getL().setScale(marketDTO.getPriceDecimal(), RoundingMode.FLOOR));
//            marketDTO.setObv(calculated.getObv());
//            marketDTO.setVol(calculated.getVol());
//            marketDTO.setO(calculated.getO());
//        }
//    }
//
//    private MarketDTO getExternalTick24h(String exchangeCode, String currency, String baseCurrency) {
//        if (Const.EXCHANGE_CODE_HUOBI.equalsIgnoreCase(exchangeCode)) {
//            try {
//                String huobiSymbol = StringUtils.lowerCase(currency + baseCurrency);
//
////                HttpRequest request = HttpUtil.createGet("https://api.huobi.pro/market/detail?symbol=" + huobiSymbol).setSSLProtocol("https");
////                if (Arrays.asList(env.getActiveProfiles()).contains("local")) {
////                    request.setHttpProxy("127.0.0.1", 1080);
////                }
////                String resultJson = request.execute().body();
//
//
//                WebClient.Builder builder = WebClient.builder();
//                if (Arrays.asList(env.getActiveProfiles()).contains("local")) {
//                    HttpClient httpClient = HttpClient.create()
//                            .tcpConfiguration(tcpClient -> tcpClient
//                                    .proxy(proxy -> proxy
//                                            .type(ProxyProvider.Proxy.HTTP)
//                                            .host("127.0.0.1")
//                                            .port(1080)));
//                    ReactorClientHttpConnector connector = new ReactorClientHttpConnector(httpClient);
//                    builder.clientConnector(connector);
//                }
//                Mono<String> resp = builder.build()
//                        .method(HttpMethod.GET)
//                        .uri("https://api.huobi.pro/market/detail?symbol=" + huobiSymbol)
//                        .retrieve()
//                        .bodyToMono(String.class);
//                String resultJson = resp.block();
//
//                JSONObject resultObj = JSON.parseObject(resultJson);
//                JSONObject tickObject = resultObj.getJSONObject("tick");
//                if (tickObject != null) {
//                    MarketDTO result = new MarketDTO();
//                    result.setO(tickObject.getBigDecimal("open"));
//                    result.setH(tickObject.getBigDecimal("high"));
//                    result.setL(tickObject.getBigDecimal("low"));
//                    result.setObv(tickObject.getBigDecimal("vol"));
//                    result.setVol(tickObject.getBigDecimal("amount"));
//                    result.setC(tickObject.getBigDecimal("close"));
//                    return result;
//                }
//            } catch (Exception e) {
//                logger.error("", e);
//            }
//        }
//        if (Const.EXCHANGE_CODE_BINANCE.equalsIgnoreCase(exchangeCode)) {
//            try {
//                currency = "bch".equalsIgnoreCase(currency) ? "bcc" : currency;
//                String binanceSymbol = StringUtils.upperCase(currency + baseCurrency);
//
    /// /                HttpRequest request = HttpUtil.createGet("https://api.binance.com/api/v1/ticker/24hr?symbol=" + binanceSymbol).setSSLProtocol("https");
    /// /                if (Arrays.asList(env.getActiveProfiles()).contains("local")) {
    /// /                    request.setHttpProxy("127.0.0.1", 1080);
    /// /                }
    /// /                String resultJson = request.execute().body();
//
//                WebClient.Builder builder = WebClient.builder();
//                if (Arrays.asList(env.getActiveProfiles()).contains("local")) {
//                    HttpClient httpClient = HttpClient.create()
//                            .tcpConfiguration(tcpClient -> tcpClient
//                                    .proxy(proxy -> proxy
//                                            .type(ProxyProvider.Proxy.HTTP)
//                                            .host("127.0.0.1")
//                                            .port(1080)));
//                    ReactorClientHttpConnector connector = new ReactorClientHttpConnector(httpClient);
//                    builder.clientConnector(connector);
//                }
//                Mono<String> resp = builder.build()
//                        .method(HttpMethod.GET)
//                        .uri("https://api.binance.com/api/v1/ticker/24hr?symbol=" + binanceSymbol)
//                        .retrieve()
//                        .bodyToMono(String.class);
//                String resultJson = resp.block();
//
//                JSONObject resultObj = JSON.parseObject(resultJson);
//                if (resultObj != null) {
//                    MarketDTO result = new MarketDTO();
//                    result.setO(resultObj.getBigDecimal("openPrice"));
//                    result.setH(resultObj.getBigDecimal("highPrice"));
//                    result.setL(resultObj.getBigDecimal("lowPrice"));
//                    result.setObv(resultObj.getBigDecimal("quoteVolume"));
//                    result.setVol(resultObj.getBigDecimal("volume"));
//                    result.setC(resultObj.getBigDecimal("lastPrice"));
//                    return result;
//                }
//            } catch (Exception e) {
//                logger.error("", e);
//            }
//        }
//        return null;
//    }
//
//    /**
//     * 返回用于计算24小时数据的秒、分、时三个时间段的时间点
//     * a-秒-b-分-c-时-d-分-e-秒-f
//     */
//    private Map<String, Object> getTimePoint(Date date) {
//        Map<String, Object> map = new HashMap<>();
//        {
//            //当前时间是2018-09-11 21:18:40
//            date = DateUtils.truncate(date, Calendar.SECOND);
//            map.put("t", date);
//
//            //获取t0 2018-09-10 21:18:40
//            Date t0 = DateUtils.addHours(date, -24);
//            map.put("t0", t0);
//            //获取t1 2018-09-10 22:00:00
//            {
//                Date t1_1 = DateUtils.addHours(t0, 1);
//                Date t1 = DateUtils.truncate(t1_1, Calendar.HOUR_OF_DAY);
//                map.put("t1", t1);
//            }
//
//            //获取t2 2018-09-10 21:19:00
//            {
//                Date t2_1 = DateUtils.addMinutes(t0, 1);
//                Date t2 = DateUtils.truncate(t2_1, Calendar.MINUTE);
//                map.put("t2", t2);
//            }
//
//        }
//        return map;
//    }

    private List<Market> allMarkets = new Vector<>();

    public List<Market> getCacheMarkets() {
        if (allMarkets.isEmpty()) {
            allMarkets.addAll(marketService.getAllEnabledMarkets());
        }
        return allMarkets;
    }

    public Market getCacheMarket(String symbol) {
        return getCacheMarkets().stream().filter(p -> StringUtils.equalsIgnoreCase(symbol, p.getSymbol())).findAny().orElse(null);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            marketDataService.getExchangeInfo();
        } catch (Exception e) {
            logger.error("执行getExchangeInfo异常", e);
        }
        new Thread(() -> reloadMarketKLine(null, false)).start();
    }

    public void reloadMarketKLine(String symbol, boolean isReload) {
        List<Market> markets = new ArrayList<>();
        if (StringUtils.isEmpty(symbol)) {
            markets = getCacheMarkets();
        } else {
            markets.add(getCacheMarket(symbol.toUpperCase()));
        }
        for (Market market : markets) {
            marketDataService.init(market, isReload);
        }
    }
}
