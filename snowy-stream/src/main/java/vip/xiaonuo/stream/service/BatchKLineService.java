package vip.xiaonuo.stream.service;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.UnpooledByteBufAllocator;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scripting.support.ResourceScriptSource;
import org.springframework.stereotype.Service;
import vip.xiaonuo.core.consts.DpConstants;
import vip.xiaonuo.core.enums.KLineType;
import vip.xiaonuo.core.pojo.dto.BatchKLineDTO;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Base64;
import java.util.Collections;

@Service
public class BatchKLineService {
    private DefaultRedisScript<String> updateKLineRedisScript;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @PostConstruct
    public void init() {
        updateKLineRedisScript = new DefaultRedisScript<>();
        updateKLineRedisScript.setResultType(String.class);
        updateKLineRedisScript.setScriptSource(new ResourceScriptSource(new ClassPathResource("scripts/updateKLine.lua")));
    }

    public void writeToRedis(String symbol, KLineType kLineType, BatchKLineDTO batchKLineDTO) {
        //写入redis
        String key = String.format(DpConstants.REDIS_KLINE_KEY, symbol, kLineType.getCode());
        redisTemplate.execute(updateKLineRedisScript, redisTemplate.getStringSerializer(), redisTemplate.getStringSerializer(),
                Collections.singletonList(key), String.valueOf(batchKLineDTO.getTime().getTime()), serializeKLineData(batchKLineDTO));
    }

    private static String serializeKLineData(BatchKLineDTO dto) {
        ByteBuf buffer = UnpooledByteBufAllocator.DEFAULT.buffer(64);
        buffer.writeLong(dto.getTime().getTime());
        buffer.writeDouble(dto.getOpen().stripTrailingZeros().doubleValue());
        buffer.writeDouble(dto.getHigh().stripTrailingZeros().doubleValue());
        buffer.writeDouble(dto.getLow().stripTrailingZeros().doubleValue());
        buffer.writeDouble(dto.getClose().stripTrailingZeros().doubleValue());
        buffer.writeDouble(dto.getVol().stripTrailingZeros().doubleValue());
        int bytes = buffer.readableBytes();
        byte[] b = new byte[bytes];
        buffer.readBytes(b);
        buffer.release();
        return Base64.getEncoder().encodeToString(b);
    }
}
