package vip.xiaonuo.stream.dto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2018/9/11 18:27
 */
public class KLinePushDTO {
    private String symbol;
    private Long period;
    private List<List<BigDecimal>> lines;

    public KLinePushDTO() {
    }

    public KLinePushDTO(String symbol, Long period, List<List<BigDecimal>> lines) {
        this.symbol = symbol;
        this.period = period;
        this.lines = lines;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public Long getPeriod() {
        return period;
    }

    public void setPeriod(Long period) {
        this.period = period;
    }

    public List<List<BigDecimal>> getLines() {
        return lines;
    }

    public void setLines(List<List<BigDecimal>> lines) {
        this.lines = lines;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        KLinePushDTO that = (KLinePushDTO) o;
        return Objects.equals(symbol, that.symbol) &&
                Objects.equals(period, that.period);
    }

    @Override
    public int hashCode() {

        return Objects.hash(symbol, period);
    }
}
