package vip.xiaonuo.stream;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.stream.orderbook.OrderBookClientInitializer;
import vip.xiaonuo.stream.service.TickerService;

import javax.annotation.Resource;

@RestController
public class StreamController {
    @Resource
    private TickerService tickerService;
    @Resource
    private OrderBookClientInitializer orderBookClientInitializer;

    @GetMapping("/check")
    public String check() {
        return "OK";
    }

    @GetMapping("/clearDecimalCache")
    public String clearDecimalCache() {
        tickerService.clearDecimalCache();
        return "OK";
    }

    @GetMapping("/addMarketSymbol")
    public String addMarket(@RequestParam String symbol) {
        orderBookClientInitializer.add(symbol);
        return "OK";
    }

    @GetMapping("/reloadKLine")
    public String reloadKLine(String symbol) {
        tickerService.reloadMarketKLine(symbol, true);
        return "ok";
    }
}
