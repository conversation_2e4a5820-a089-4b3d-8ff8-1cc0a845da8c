package vip.xiaonuo.stream.orderbook;


import aacoin.trade.exchange.websocket.MarketSymbol;
import org.apache.commons.lang3.StringUtils;
//import org.apache.kafka.clients.consumer.ConsumerRecord;
//import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.business.market.service.MarketService;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2018/10/6 17:30
 */
@Component
public class OrderBookClientInitializer {
    @Resource
    private MarketService marketService;
    @Resource
    private List<OrderBookWsClient> wsClients;

//    @KafkaListener(topics = "NEW_SYMBOL_INIT")
//    public void initNewSymbol(ConsumerRecord<String, Object> consumerRecord) {
//        add((String) consumerRecord.value());
//    }

    @PostConstruct
    public void init() {
        List<Market> markets = marketService.getAllEnabledMarkets();
        markets = markets.stream().filter(e -> StringUtils.isNotBlank(e.getPartner()))
                .collect(Collectors.toList());
        Map<String, List<MarketSymbol>> groupedSymbols = new HashMap<>();
        for (Market market : markets) {
            if (!groupedSymbols.containsKey(market.getPartner())) {
                groupedSymbols.put(market.getPartner(), new ArrayList<>());
            }
            groupedSymbols.get(market.getPartner()).add(new MarketSymbol(market.getCurrencyCode(), market.getBaseCurrencyCode()));
        }
        for (Map.Entry<String, List<MarketSymbol>> entry : groupedSymbols.entrySet()) {
            String partner = entry.getKey();
            getWsClient(partner).init(entry.getValue());
        }
    }

    private OrderBookWsClient getWsClient(String partner) {
        OrderBookWsClient wsClient = wsClients.stream().filter(e -> {
                    OrderBookWsClient.ExternalExchange externalExchange = e.getClass().getAnnotation(OrderBookWsClient.ExternalExchange.class);
                    return externalExchange != null && partner.equalsIgnoreCase(externalExchange.value());
                }
        ).findFirst().orElse(null);
        if (wsClient == null) {
            throw new RuntimeException("exchange " + partner + " not supported");
        }
        return wsClient;
    }

    public void add(String symbol) {
        Market market = marketService.getMarketBySymbol(symbol);
        if (StringUtils.isNotBlank(market.getPartner())) {
            getWsClient(market.getPartner()).add(new MarketSymbol(market.getCurrencyCode(), market.getBaseCurrencyCode()));
        }
    }
}
