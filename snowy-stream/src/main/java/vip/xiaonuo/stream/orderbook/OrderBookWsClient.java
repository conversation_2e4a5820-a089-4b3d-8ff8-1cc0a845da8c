package vip.xiaonuo.stream.orderbook;

import aacoin.trade.exchange.websocket.MarketSymbol;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2018/10/6 17:32
 */
public interface OrderBookWsClient {

    void init(List<MarketSymbol> marketSymbols);

    void add(MarketSymbol marketSymbol);

    @Target({ElementType.TYPE})
    @Retention(RetentionPolicy.RUNTIME)
    @interface ExternalExchange{
        String value();
    }

}
