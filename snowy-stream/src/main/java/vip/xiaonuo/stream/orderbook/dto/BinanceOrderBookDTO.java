package vip.xiaonuo.stream.orderbook.dto;

import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.core.pojo.dto.AggOrderDTO;

import java.math.BigDecimal;
import java.util.List;

public class BinanceOrderBookDTO extends OrderBookDTO {
    private long lastUpdateId;

    public BinanceOrderBookDTO(vip.xiaonuo.stream.service.TickerService tickerService, Market market,
                               List<AggOrderDTO> buyOrders,
                               List<AggOrderDTO> sellOrders) {
        super(tickerService, market, buyOrders, sellOrders);
    }

    @Override
    protected BigDecimal getMergedQuantity(BigDecimal originalQty, BigDecimal changedQuantity) {
        return changedQuantity;
    }

    public void merge(Long lastUpdateId, List<OrderChangeDTO> orderChangeDTOS) {
        if (lastUpdateId <= this.lastUpdateId) {
            return;
        }
        merge(false, orderChangeDTOS);
        this.lastUpdateId = lastUpdateId;
    }

    @Override
    public void initOrderData(Long lastUpdateId, List<OrderChangeDTO> orderChangeDTOS) {
        this.lastUpdateId = lastUpdateId;
        super.initOrderData(orderChangeDTOS);
    }
}
