package vip.xiaonuo.stream.orderbook.dto;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2018/6/15 10:17
 */
public class OrderChangeDTO {

    private Boolean created;
    private Integer orderId;
    private Integer userId;
    private String orderType;
    private String currency;
    private String baseCurrency;
    private BigDecimal price;
    private BigDecimal changedQuantity;
    private BigDecimal originalQuantity;
    private Date orderTime;
    private int engineInstanceVersion;

    public int getEngineInstanceVersion() {
        return engineInstanceVersion;
    }

    public void setEngineInstanceVersion(int engineInstanceVersion) {
        this.engineInstanceVersion = engineInstanceVersion;
    }

    public Boolean getCreated() {
        return created;
    }

    public void setCreated(<PERSON><PERSON><PERSON> created) {
        this.created = created;
    }

    public BigDecimal getOriginalQuantity() {
        return originalQuantity;
    }

    public void setOriginalQuantity(BigDecimal originalQuantity) {
        this.originalQuantity = originalQuantity;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getBaseCurrency() {
        return baseCurrency;
    }

    public void setBaseCurrency(String baseCurrency) {
        this.baseCurrency = baseCurrency;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getSymbol() {
        return currency + "_" + baseCurrency;
    }

    public void setSymbol(String symbol) {
        String[] arr = symbol.split("_");
        this.currency = arr[0];
        this.baseCurrency = arr[1];
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getChangedQuantity() {
        return changedQuantity;
    }

    public void setChangedQuantity(BigDecimal changedQuantity) {
        this.changedQuantity = changedQuantity;
    }

}
