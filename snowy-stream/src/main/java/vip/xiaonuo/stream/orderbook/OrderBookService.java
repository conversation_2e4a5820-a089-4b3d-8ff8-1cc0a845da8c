package vip.xiaonuo.stream.orderbook;

import aacoin.trade.exchange.rest.Const;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.core.consts.DpConstants;
import vip.xiaonuo.core.pojo.dto.AggOrderDTO;
import vip.xiaonuo.core.pojo.dto.KLineDepthsDTO;
import vip.xiaonuo.core.util.KafkaTopics;
import vip.xiaonuo.core.util.Locker;
import vip.xiaonuo.stream.orderbook.dto.BinanceOrderBookDTO;
import vip.xiaonuo.stream.orderbook.dto.HuobiOrderBookDTO;
import vip.xiaonuo.stream.orderbook.dto.OrderBookDTO;
import vip.xiaonuo.stream.orderbook.dto.OrderChangeDTO;
import vip.xiaonuo.stream.service.TickerService;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

@Service
public class OrderBookService {
    private Logger logger = LoggerFactory.getLogger(getClass());
    private Map<String, OrderBookDTO> orderSymbolMap = new ConcurrentHashMap<>();
//    @Resource
//    private KafkaTemplate kafkaTemplate;
    @Resource
    private Locker locker;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private TickerService tickerService;
    @Resource
    private MarketService marketService;

    private Set<String> orderInitializing = new HashSet<>();

    public void clearCache(String symbol) {
        if (StringUtils.isBlank(symbol)) {
            orderSymbolMap.clear();
            logger.info("all cache cleared ");
        } else {
            orderSymbolMap.remove(symbol);
            logger.info("{} cache cleared ", symbol);
        }
    }

    public List<Integer> getOptionalDepth(String symbol) {
        return getOrderHolder(symbol).getOptionalDepth();
    }

    public void mergeQuantity(List<OrderChangeDTO> orderChangeList) {
        if (!CollectionUtils.isEmpty(orderChangeList)) {
            String symbol = orderChangeList.get(0).getSymbol();
            getOrderHolder(symbol).merge(false, orderChangeList);
        }
    }

    public void mergeQuantity(Long lastUpdateId, List<OrderChangeDTO> orderChangeList) {
        if (!CollectionUtils.isEmpty(orderChangeList)) {
            String symbol = orderChangeList.get(0).getSymbol();
            if (lastUpdateId != null) {
                OrderBookDTO orderHolder = getOrderHolder(symbol);
                orderHolder.merge(lastUpdateId, orderChangeList);
            }
        }
    }

    public Map<String, List<AggOrderDTO>> getOrderData(String symbol, Integer depth) {
        return getOrderHolder(symbol).getDataToPush(false, depth, false);
    }

    public Integer getMaxDepth(String symbol) {
        return getOrderHolder(symbol).getMaxDepth();
    }

    public KLineDepthsDTO getDepthData(String symbol) {
        Map<String, List<AggOrderDTO>> orderData = getOrderHolder(symbol).getDataToPush(true, null, false);
        KLineDepthsDTO depthsDTO = new KLineDepthsDTO();
        List<AggOrderDTO> buyOrders = orderData.get("buy");
        List<AggOrderDTO> sellOrders = orderData.get("sell");

        buyOrders.forEach(e -> depthsDTO.getBids().add(new BigDecimal[]{e.getPrice(), e.getQuantity()}));
        sellOrders.forEach(e -> depthsDTO.getAsks().add(new BigDecimal[]{e.getPrice(), e.getQuantity()}));
        if (!CollectionUtils.isEmpty(buyOrders)) {
            depthsDTO.setBuyOne(buyOrders.get(0).getPrice());
        }
        if (!CollectionUtils.isEmpty(sellOrders)) {
            depthsDTO.setSellOne(sellOrders.get(sellOrders.size() - 1).getPrice());
        }
        return depthsDTO;
    }

    public OrderBookDTO getOrderHolder(String symbol) {
        if (!orderSymbolMap.containsKey(symbol)) {
            Market market = marketService.getMarketBySymbol(symbol);
            if (market == null) {
                throw new RuntimeException("invalid symbol : " + symbol);
            }
            if (!orderInitializing.contains(symbol)) {
                init(market);
            }
        }
        return orderSymbolMap.get(symbol);
    }

    public Set<String> getSupportedSymbols() {
        return orderSymbolMap.keySet();
    }

    private void init(Market market) {
        ReentrantLock symbolLock = locker.getLock(market.getSymbol());
        symbolLock.lock();
        try {
            if (!orderSymbolMap.containsKey(market.getSymbol())) {
                OrderBookDTO orderBookDTO = null;
                if (isExternalExchange(market)) {
                    if (Const.EXCHANGE_CODE_BINANCE.equalsIgnoreCase(market.getPartner())) {
                        orderBookDTO = new BinanceOrderBookDTO(tickerService, market, new ArrayList<>(), new ArrayList<>());
                    } else if (Const.EXCHANGE_CODE_HUOBI.equalsIgnoreCase(market.getPartner())) {
                        orderBookDTO = new HuobiOrderBookDTO(tickerService, market, new ArrayList<>(), new ArrayList<>());
                    } else {
                        throw new RuntimeException("unknown exchange code: " + market.getPartner());
                    }
                } else {
                    orderBookDTO = new OrderBookDTO(tickerService, market, new ArrayList<>(), new ArrayList<>());
                }
                orderSymbolMap.put(market.getSymbol(), orderBookDTO);
                redisTemplate.opsForValue().set(String.format(DpConstants.ORDER_DEPTH_GROUP, market.getSymbol()), orderBookDTO.getOptionalDepth());
                if (!isExternalExchange(market)) {
                    logger.info("load {} order data from matching", market.getSymbol());
//                    kafkaTemplate.send(KafkaTopics.DP_REQUEST_ORDER_DATA, market.getSymbol());
                }
            }
        } finally {
            symbolLock.unlock();
        }
    }

    private boolean isExternalExchange(Market market) {
        return StringUtils.isNotBlank(market.getPartner());
    }

    @PostConstruct
    public void afterPropertiesSet() {
        List<Market> allMarkets = marketService.getAllEnabledMarkets();
        for (Market market : allMarkets) {
            init(market);
        }
    }

    public void initOrderData(Long lastUpdateId, String symbol, List<OrderChangeDTO> orderChangeDTOS) {
        orderSymbolMap.remove(symbol);
        getOrderHolder(symbol).initOrderData(lastUpdateId, orderChangeDTOS);
        orderInitializing.remove(symbol);
    }

    public void initOrderData(String symbol, List<OrderChangeDTO> orderChangeDTOS) {
        getOrderHolder(symbol).initOrderData(orderChangeDTOS);
        orderInitializing.remove(symbol);
    }
}
