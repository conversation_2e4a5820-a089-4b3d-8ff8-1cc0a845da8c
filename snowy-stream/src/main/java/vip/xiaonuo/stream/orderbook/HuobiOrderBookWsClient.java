package vip.xiaonuo.stream.orderbook;


import aacoin.trade.exchange.rest.Const;
import aacoin.trade.exchange.util.ZipUtil;
import aacoin.trade.exchange.websocket.MarketSymbol;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.java_websocket.WebSocket;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.java_websocket.framing.Framedata;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vip.xiaonuo.stream.orderbook.dto.OrderChangeDTO;
import vip.xiaonuo.stream.service.TickerService;

import java.io.Closeable;
import java.math.BigDecimal;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URI;
import java.nio.ByteBuffer;
import java.text.MessageFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Component
@OrderBookWsClient.ExternalExchange(Const.EXCHANGE_CODE_HUOBI)
public class HuobiOrderBookWsClient implements OrderBookWsClient {
    private Logger logger = LoggerFactory.getLogger(getClass());
    private List<MarketSymbol> marketSymbols = new ArrayList<>();
    private Pattern HUOBI_CHANNEL_PATTERN = Pattern.compile("market.(\\w*).(\\w*).(\\w*)");
    @Autowired
    private TickerService tickerService;
    @Autowired
    private OrderBookService orderBookService;
    private HuobiWsClientHandler wsClientHandler;

    @Scheduled(initialDelay = 10000L, fixedDelay = 3000L)
    public void reconnectIfClosed() {
        if (wsClientHandler != null && wsClientHandler.isClosed()) {
            initWebsocket();
        }
    }

    @Override
    public void add(MarketSymbol marketSymbol) {
        if (marketSymbols.contains(marketSymbol)) {
            return;
        }
        String symbol = (marketSymbol.getCurrency() + marketSymbol.getBaseCurrency()).toLowerCase();
        if (wsClientHandler == null) {
            init(new ArrayList<>());
        }
        wsClientHandler.subscribe(symbol, marketSymbols.size());
        marketSymbols.add(marketSymbol);
    }

    public void init(List<MarketSymbol> marketSymbols) {
        this.marketSymbols.addAll(marketSymbols);
        initWebsocket();
    }

    private void initWebsocket() {
        if (wsClientHandler != null) {
            IOUtils.closeQuietly(wsClientHandler);
        }
        try {
            logger.info("init huobi websocket client: {}", Const.HUOBI_MARKET_DATA_WS_URL);
            URI uri = new URI(Const.HUOBI_MARKET_DATA_WS_URL);
            wsClientHandler = new HuobiWsClientHandler(uri);
            wsClientHandler.connectBlocking();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private String getMappingLocalSymbol(String huobiSymbol) {
        MarketSymbol localSymbol = marketSymbols.stream()
                .filter(e -> huobiSymbol.equals(StringUtils.lowerCase(e.getCurrency() + e.getBaseCurrency())))
                .findAny().orElse(null);
        if (localSymbol != null) {
            return localSymbol.getCurrency() + "_" + localSymbol.getBaseCurrency();
        }
        return null;
    }

    class HuobiWsClientHandler extends WebSocketClient implements Closeable {
        private boolean closed;
        private long lastPingTime = System.currentTimeMillis();

        HuobiWsClientHandler(URI serverURI) {
            super(serverURI, new Draft_6455(), null, 5000);
            if (Const.SET_PROXY) {
                this.setProxy(new Proxy(Proxy.Type.HTTP, InetSocketAddress.createUnresolved("127.0.0.1", 7890)));
            }
        }

        @Override
        public boolean isClosed() {
            return closed || System.currentTimeMillis() - lastPingTime > 10000;
        }

        @Override
        public void onWebsocketPing(WebSocket conn, Framedata f) {
            lastPingTime = System.currentTimeMillis();
            super.onWebsocketPing(conn, f);
        }

        @Override
        public void onWebsocketPong(WebSocket conn, Framedata f) {
            lastPingTime = System.currentTimeMillis();
            super.onWebsocketPong(conn, f);
        }

        @Override
        public void onMessage(ByteBuffer bytes) {
            try {
                lastPingTime = System.currentTimeMillis();
                String message = new String(ZipUtil.decompress(bytes.array()), "UTF-8");
                JSONObject json = JSONObject.parseObject(message);

                if (json.containsKey("ping")) {
                    sendPong();
                    return;
                }
                if ("error".equalsIgnoreCase(json.getString("status"))) {
                    logger.warn("error resp: {}", json.toJSONString());
                    return;
                }

                String resCh = (String) json.get("ch");
                if (StringUtils.isBlank(resCh)) {
                    if (!"ok".equalsIgnoreCase((String) json.get("status"))) {
                        logger.info("invalid data: {}", json);
                    }
                    return;
                }
                Matcher matcher = HUOBI_CHANNEL_PATTERN.matcher(resCh);
                if (!matcher.matches()) {
                    logger.info("invalid channel : {}", resCh);
                    return;
                }
                String symbol = matcher.group(1);
                String dataType = matcher.group(2);
                if ("depth".equalsIgnoreCase(dataType)) {
                    JSONObject tick = json.getJSONObject("tick");
                    if (tick == null || tick.isEmpty()) {
                        return;
                    }
                    depthDataUpdated(symbol, tick);
                }
            } catch (Exception e) {
                logger.error("", e);
            }
        }

        private void depthDataUpdated(String huobisymbol, JSONObject ticks) {
            String localSymbol = getMappingLocalSymbol(huobisymbol);
            if (localSymbol != null) {
                JSONArray bids = (JSONArray) ticks.get("bids");
                JSONArray asks = (JSONArray) ticks.get("asks");
                Collections.reverse(asks);
                Integer[] decimals = tickerService.getMarketDecimals(localSymbol);
                List<Map<String, Object>> buyOrders = convertToMap(decimals, bids);
                List<Map<String, Object>> sellOrders = convertToMap(decimals, asks);
                orderBookService.initOrderData(localSymbol, convertDTO(localSymbol, buyOrders, sellOrders));
            }
        }

        List<OrderChangeDTO> convertDTO(String localSymbol,
                                        List<Map<String, Object>> buyOrders,
                                        List<Map<String, Object>> sellOrders) {
            List<OrderChangeDTO> orderChangeDTOList = buyOrders.stream().map(e -> {
                OrderChangeDTO orderChangeDTO = new OrderChangeDTO();
                orderChangeDTO.setSymbol(localSymbol);
                orderChangeDTO.setOrderType("buy");
                orderChangeDTO.setPrice((BigDecimal) e.get("price"));
                orderChangeDTO.setChangedQuantity((BigDecimal) e.get("quantity"));
                return orderChangeDTO;
            }).collect(Collectors.toList());
            orderChangeDTOList.addAll(sellOrders.stream().map(e -> {
                OrderChangeDTO orderChangeDTO = new OrderChangeDTO();
                orderChangeDTO.setSymbol(localSymbol);
                orderChangeDTO.setOrderType("sell");
                orderChangeDTO.setPrice((BigDecimal) e.get("price"));
                orderChangeDTO.setChangedQuantity((BigDecimal) e.get("quantity"));
                return orderChangeDTO;
            }).collect(Collectors.toList()));
            return orderChangeDTOList;
        }

        private List<Map<String, Object>> convertToMap(Integer[] decimals, JSONArray jsonArray) {
            List<Map<String, Object>> orderDTOS = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONArray sell = jsonArray.getJSONArray(i);
                Map<String, Object> aggOrderDTO = new HashMap<>();
                aggOrderDTO.put("price", sell.getBigDecimal(0));
                aggOrderDTO.put("quantity", sell.getBigDecimal(1));
                aggOrderDTO.put("priceDecimal", decimals[0]);
                aggOrderDTO.put("quantityDecimal", decimals[1]);
                orderDTOS.add(aggOrderDTO);
            }
            return orderDTOS;
        }

        private void sendPong() {
            JSONObject pongMessage = new JSONObject();
            pongMessage.put("pong", System.currentTimeMillis());
            send(pongMessage.toJSONString());
        }

        @Override
        public void onOpen(ServerHandshake handshakedata) {
            logger.info("HuobiWsClient onOpen");
            lastPingTime = System.currentTimeMillis();
            List<String> huobiSymbols = marketSymbols.stream()
                    .map(e -> (e.getCurrency() + e.getBaseCurrency()).toLowerCase()).collect(Collectors.toList());
            for (int i = 0; i < huobiSymbols.size(); i++) {
                String huobiSymbol = huobiSymbols.get(i);
                subscribe(huobiSymbol, i);
            }
        }

        void subscribe(String huobiSymbol, int seq) {
            String subscribeCommand = MessageFormat.format("market.{0}.depth.step0", huobiSymbol);
            String subscribeId = MessageFormat.format("depth_sub_id_{0}", seq + "");
            Map<String, String> map = new HashMap<>(2);
            map.put("sub", subscribeCommand);
            map.put("id", subscribeId);
            logger.info("huobi websocket client<<<< subscribe: {}", map);
            this.send(JSON.toJSONString(map));
        }

        @Override
        public void onMessage(String message) {
            lastPingTime = System.currentTimeMillis();
            logger.info("HuobiWsClient onMessage: {}", message);
        }

        @Override
        public void onClose(int code, String reason, boolean remote) {
            logger.warn("HuobiWsClient onClose: {} {}", code, reason);
            closed = true;
        }

        @Override
        public void onError(Exception ex) {
            logger.error("HuobiWsClient onError:", ex);
        }
    }
}
