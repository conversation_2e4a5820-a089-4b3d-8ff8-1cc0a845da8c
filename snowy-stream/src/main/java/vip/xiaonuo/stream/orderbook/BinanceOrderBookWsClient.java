package vip.xiaonuo.stream.orderbook;

import aacoin.trade.exchange.rest.Const;
import aacoin.trade.exchange.websocket.MarketSymbol;
import com.binance.api.client.BinanceApiClientFactory;
import com.binance.api.client.BinanceApiRestClient;
import com.binance.api.client.BinanceApiWebSocketClient;
import com.binance.api.client.domain.market.OrderBook;
import com.binance.api.client.domain.market.OrderBookEntry;
import com.binance.api.client.impl.BinanceWebSocketHandle;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import vip.xiaonuo.stream.orderbook.dto.OrderChangeDTO;
import vip.xiaonuo.stream.service.TickerService;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2018/10/6 17:21
 */
@Component
@OrderBookWsClient.ExternalExchange(Const.EXCHANGE_CODE_BINANCE)
public class BinanceOrderBookWsClient implements OrderBookWsClient {
    private Logger logger = LoggerFactory.getLogger(getClass());
    private List<MarketSymbol> marketSymbols = new ArrayList<>();
    @Resource
    private OrderBookService orderBookService;
    private BinanceApiClientFactory clientFactory = BinanceApiClientFactory.newInstance();
    private BinanceWebSocketHandle orderbookWebsocket;
    private boolean orderbookWsConnecting;

    @Autowired
    private TickerService tickerService;

    @Scheduled(initialDelay = 5000L, fixedDelay = 12 * 3600 * 1000L)
    public void reConnectWebsockets() {
        if (orderbookWebsocket != null) {
            IOUtils.closeQuietly(orderbookWebsocket);
        }
        init(null);
    }

    @Scheduled(initialDelay = 40000L, fixedDelay = 2000L)
    void reset() {
        if (orderbookWebsocket != null && orderbookWebsocket.isClosed() && !orderbookWsConnecting) {
            logger.info("market-data ws reconnecting...");
            init(null);
        }
    }

    @Override
    public void add(MarketSymbol marketSymbol) {
        if (marketSymbols.contains(marketSymbol)) {
            return;
        }
        _add(marketSymbol);
        marketSymbols.add(marketSymbol);
    }

    public void init(List<MarketSymbol> marketSymbols) {
        try {
            orderbookWsConnecting = true;
            if (!CollectionUtils.isEmpty(marketSymbols)) {
                this.marketSymbols.addAll(marketSymbols);
            }
            this.marketSymbols.forEach(this::_add);
        } finally {
            orderbookWsConnecting = false;
        }
    }

    private void _add(MarketSymbol marketSymbol) {
        BinanceApiWebSocketClient webSocketClient = clientFactory.newWebSocketClient();
        BinanceApiRestClient restClient = clientFactory.newRestClient();

        String localSymbol = marketSymbol.getCurrency() + "_" + marketSymbol.getBaseCurrency();
        logger.info("init binance websocket client for [{}]", localSymbol);
        String binanceSymbol = "bch".equalsIgnoreCase(marketSymbol.getCurrency()) ? ("bcc" + marketSymbol.getBaseCurrency()).toUpperCase()
                : (marketSymbol.getCurrency() + marketSymbol.getBaseCurrency()).toUpperCase();

        OrderBook orderBook = restClient.getOrderBook(binanceSymbol, 500);

        List<OrderChangeDTO> orderChangeDTOList = convert(localSymbol, "sell", orderBook.getAsks());
        orderChangeDTOList.addAll(convert(localSymbol, "buy", orderBook.getBids()));

        orderBookService.initOrderData(orderBook.getLastUpdateId(), localSymbol, orderChangeDTOList);

        orderbookWebsocket = webSocketClient.onDepthEvent(binanceSymbol.toLowerCase(), response -> {
            String localSymbol_ = getMappingLocalSymbol(binanceSymbol);
            if (localSymbol_ != null) {
                List<OrderChangeDTO> orderChangeDTOList_ = convert(localSymbol, "sell", response.getAsks());
                orderChangeDTOList_.addAll(convert(localSymbol, "buy", response.getBids()));

                orderBookService.mergeQuantity(response.getFinalUpdateId(), orderChangeDTOList_);
            }
        });
    }

    private List<OrderChangeDTO> convert(String localSymbol, String orderSide, List<OrderBookEntry> orderBookEntries) {
        Integer[] decimals = tickerService.getMarketDecimals(localSymbol);
        if (CollectionUtils.isEmpty(orderBookEntries)) {
            return new ArrayList<>();
        }
        return orderBookEntries.stream().map(e -> {
            OrderChangeDTO orderChangeDTO = new OrderChangeDTO();
            orderChangeDTO.setSymbol(localSymbol);
            orderChangeDTO.setOrderType(orderSide);
//            orderChangeDTO.setPrice(NumberUtils.createBigDecimal(e.getPrice()));
//            orderChangeDTO.setChangedQuantity(NumberUtils.createBigDecimal(e.getQty()));
            orderChangeDTO.setPrice(NumberUtils.createBigDecimal(e.getPrice()).setScale(decimals[0], RoundingMode.DOWN));
            orderChangeDTO.setChangedQuantity(NumberUtils.createBigDecimal(e.getQty()).setScale(decimals[1], RoundingMode.DOWN));
            return orderChangeDTO;
        }).collect(Collectors.toList());
    }

    private String getMappingLocalSymbol(String binanceSymbol) {
        MarketSymbol localSymbol = marketSymbols.stream()
                .filter(e -> {
                    String c = "bch".equalsIgnoreCase(e.getCurrency()) ?
                            "bcc" + e.getBaseCurrency() : e.getCurrency() + e.getBaseCurrency();
                    return binanceSymbol.equalsIgnoreCase(c);
                })
                .findAny().orElse(null);
        if (localSymbol != null) {
            return localSymbol.getCurrency() + "_" + localSymbol.getBaseCurrency();
        }
        return null;
    }

}
