package vip.xiaonuo.stream.orderbook.dto;


import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.core.pojo.dto.AggOrderDTO;
import vip.xiaonuo.stream.service.TickerService;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2018/10/7 10:05
 */
public class HuobiOrderBookDTO extends OrderBookDTO {
    public HuobiOrderBookDTO(TickerService tickerService, Market market,
                             List<AggOrderDTO> buyOrders,
                             List<AggOrderDTO> sellOrders) {
        super(tickerService, market, buyOrders, sellOrders);
    }
}
