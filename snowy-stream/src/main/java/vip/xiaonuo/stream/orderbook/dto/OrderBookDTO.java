package vip.xiaonuo.stream.orderbook.dto;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.core.pojo.dto.AggOrderDTO;
import vip.xiaonuo.stream.service.TickerService;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;

public class OrderBookDTO {
    private Logger logger = LoggerFactory.getLogger(getClass());

    private TreeSet<AggOrderDTO> originalBuyOrderSet = new TreeSet<>();
    private TreeSet<AggOrderDTO> originalSellOrderSet = new TreeSet<>();
    private Map<Integer, TreeSet<AggOrderDTO>> buyOrderDepthMap = new HashMap<>();
    private Map<Integer, TreeSet<AggOrderDTO>> sellOrderDepthMap = new HashMap<>();
    private volatile boolean dirty;
    private volatile long lastPushTime;
    private volatile List<Integer> availableDepth;
    private ReentrantLock lock = new ReentrantLock();
    private TickerService tickerService;
    private String symbol;

    public OrderBookDTO(TickerService tickerService, Market market, List<AggOrderDTO> buyOrders, List<AggOrderDTO> sellOrders) {
        this.tickerService = tickerService;
        this.symbol = market.getSymbol();
        for (AggOrderDTO orderDTO : buyOrders) {
            orderDTO.setPriceDecimal(market.getPriceDecimal());
            orderDTO.setQuantityDecimal(market.getQuantityDecimal());
            this.originalBuyOrderSet.add(orderDTO);
        }
        for (AggOrderDTO orderDTO : sellOrders) {
            orderDTO.setPriceDecimal(market.getPriceDecimal());
            orderDTO.setQuantityDecimal(market.getQuantityDecimal());
            this.originalSellOrderSet.add(orderDTO);
        }
        this.availableDepth = reCalcGroupDepth();
        reCreatePriceGroupIfNecessary();
    }

    private void reCreatePriceGroupIfNecessary() {
        buyOrderDepthMap.clear();
        sellOrderDepthMap.clear();
        if (CollectionUtils.isEmpty(originalBuyOrderSet) && CollectionUtils.isEmpty(originalSellOrderSet)) {
            return;
        }
        for (Integer scale : availableDepth) {
            //最大深度的直接从原始数据 originalBuyOrderSet 或者 sellOrderSet获取，无需group
            if (scale.equals(getMaxDepth())) {
                continue;
            }
            TreeSet<AggOrderDTO> buyOrderMap_ = new TreeSet<>();
            TreeSet<AggOrderDTO> sellOrderMap_ = new TreeSet<>();
            for (AggOrderDTO orderDTO : originalBuyOrderSet) {
                mergeOriginalOrders(buyOrderMap_, orderDTO.cloneByPriceScale(scale), true);
            }
            for (AggOrderDTO orderDTO : originalSellOrderSet) {
                mergeOriginalOrders(sellOrderMap_, orderDTO.cloneByPriceScale(scale), true);
            }
            buyOrderDepthMap.put(scale, buyOrderMap_);
            sellOrderDepthMap.put(scale, sellOrderMap_);
        }
    }

    public boolean shouldPush() {
        return dirty && (System.currentTimeMillis() - lastPushTime) > 300;
    }

    public List<Integer> getOptionalDepth() {
        return availableDepth;
    }

    public Integer getMaxDepth() {
        return tickerService.getMarketDecimals(symbol)[0];
    }

    private List<Integer> reCalcGroupDepth() {
        Integer[] marketDecimals = tickerService.getMarketDecimals(symbol);
        int max = marketDecimals[0];
        List<Integer> result = new ArrayList<>();
        int min = marketDecimals[2];
        for (int i = min; i <= max; i++) {
            result.add(i);
        }
        return result;
    }

    public void merge(boolean override, List<OrderChangeDTO> orderChangeList) {
        try {
            lock.lock();
            if (override) {
                clear();
            }
            boolean dirty_ = false;
            for (OrderChangeDTO orderChangeDTO : orderChangeList) {
                dirty_ = merge(orderChangeDTO.getOrderType(), orderChangeDTO.getPrice(), orderChangeDTO.getChangedQuantity()) || dirty_;
            }
            this.dirty = dirty_;
            reCreatePriceGroupIfNecessary();
        } finally {
            lock.unlock();
        }
    }

    private boolean merge(String orderType, BigDecimal price, BigDecimal deltaQty) {
        TreeSet<AggOrderDTO> orderSet = null;
        boolean merged = false;
        Map<Integer, TreeSet<AggOrderDTO>> groupedOrderMap = null;
        if ("sell".equalsIgnoreCase(orderType)) {
            orderSet = originalSellOrderSet;
            groupedOrderMap = sellOrderDepthMap;
        } else if ("buy".equalsIgnoreCase(orderType)) {
            orderSet = originalBuyOrderSet;
            groupedOrderMap = buyOrderDepthMap;
        }

        if (orderSet != null) {
            Integer[] marketDecimals = tickerService.getMarketDecimals(symbol);
            AggOrderDTO orderDTO = new AggOrderDTO();
            orderDTO.setPriceDecimal(marketDecimals[0]);
            orderDTO.setQuantityDecimal(marketDecimals[1]);
            orderDTO.setQuantity(deltaQty);
            orderDTO.setPrice(price);
            mergeOriginalOrders(orderSet, orderDTO, false);

            orderDTO = new AggOrderDTO();
            orderDTO.setPriceDecimal(marketDecimals[0]);
            orderDTO.setQuantityDecimal(marketDecimals[1]);
            orderDTO.setQuantity(deltaQty);
            orderDTO.setPrice(price);
            mergeGroupedOrders(groupedOrderMap, orderDTO);
            merged = true;
        }
        return merged;
    }

    private void mergeGroupedOrders(Map<Integer, TreeSet<AggOrderDTO>> groupedOrderMap, AggOrderDTO originalOrderDTO) {
        for (Map.Entry<Integer, TreeSet<AggOrderDTO>> entry : groupedOrderMap.entrySet()) {
            AggOrderDTO newOrderDTO = originalOrderDTO.cloneByPriceScale(entry.getKey());
            mergeOriginalOrders(entry.getValue(), newOrderDTO, true);
        }
    }

    private void mergeOriginalOrders(TreeSet<AggOrderDTO> targetOrderSet, AggOrderDTO orderDTO, boolean isGroup) {
        AggOrderDTO existedDTO = targetOrderSet.floor(orderDTO);
        if (existedDTO == null || !existedDTO.equals(orderDTO)) {
            if (orderDTO.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                targetOrderSet.add(orderDTO);
            }
            return;
        }
        BigDecimal afterQty = isGroup ? existedDTO.getQuantity().add(orderDTO.getQuantity())
                : getMergedQuantity(existedDTO.getQuantity(), orderDTO.getQuantity());
        if (afterQty.compareTo(BigDecimal.ZERO) == 0) {
            targetOrderSet.remove(existedDTO);
            return;
        } else if (afterQty.compareTo(BigDecimal.ZERO) < 0) {
            logger.warn("[{}] {}@{} qty is less than zero", symbol, existedDTO.getPrice().toPlainString(), existedDTO.getQuantity().toPlainString());
        }
        existedDTO.setQuantity(afterQty);
    }

    /**
     * 增量数据合并逻辑，币安是直接覆盖，而不是相加，这里弄个方法出来以供重载
     */
    protected BigDecimal getMergedQuantity(BigDecimal originalQty, BigDecimal changedQuantity) {
        return originalQty.add(changedQuantity);
    }

    public Map<String, List<AggOrderDTO>> getDataToPush(boolean fullData, Integer depth, boolean refreshSentTime) {
        try {
            lock.lock();
            Map<String, List<AggOrderDTO>> results = new HashMap<>();
            List<Integer> optionalDepth = getOptionalDepth();
            if (depth == null || !optionalDepth.contains(depth)) {
                depth = getMaxDepth();
            }
            TreeSet<AggOrderDTO> buyOrderSet = depth.equals(getMaxDepth()) ? originalBuyOrderSet : buyOrderDepthMap.get(depth);
            TreeSet<AggOrderDTO> sellOrderSet = depth.equals(getMaxDepth()) ? originalSellOrderSet : sellOrderDepthMap.get(depth);
            List<AggOrderDTO> buyOrders = new ArrayList<>();
            List<AggOrderDTO> sellOrders = new ArrayList<>();
            if (!CollectionUtils.isEmpty(buyOrderSet)) {
                buyOrders = new ArrayList<>(buyOrderSet);
            }
            if (!CollectionUtils.isEmpty(sellOrderSet)) {
                sellOrders = new ArrayList<>(sellOrderSet);
            }
            if (!fullData) {
                if (buyOrders.size() > 15) {
                    buyOrders = buyOrders.subList(0, 15);
                }
                if (sellOrders.size() > 15) {
                    sellOrders = sellOrders.subList(sellOrders.size() - 15, sellOrders.size());
                }
            }
            AggOrderDTO.calcMinMax(buyOrders);
            AggOrderDTO.calcMinMax(sellOrders);
            if (refreshSentTime) {
                dirty = false;
                lastPushTime = System.currentTimeMillis();
            }

            /*卖单数量累加*/
            BigDecimal sellOrderQuantity = BigDecimal.ZERO;
            for (int i = sellOrders.size() - 1; i >= 0; i--) {
                AggOrderDTO o = sellOrders.get(i);
                sellOrderQuantity = sellOrderQuantity.add(o.getQuantity());
                o.setSum(sellOrderQuantity);
            }

            /*买单数量累加*/
            BigDecimal buyOrderQuantity = BigDecimal.ZERO;
            for (int i = 0; i < buyOrders.size(); i++) {
                AggOrderDTO o = buyOrders.get(i);
                buyOrderQuantity = buyOrderQuantity.add(o.getQuantity());
                o.setSum(buyOrderQuantity);
            }

            results.put("buy", buyOrders);
            results.put("sell", sellOrders);
            return results;
        } finally {
            lock.unlock();
        }
    }

    private void clear() {
        originalBuyOrderSet.clear();
        originalSellOrderSet.clear();
        reCreatePriceGroupIfNecessary();
    }

    public void initOrderData(List<OrderChangeDTO> orderChangeDTOS) {
        merge(true, orderChangeDTOS);
    }

    public void merge(Long lastUpdateId, List<OrderChangeDTO> orderChangeDTOS) {
        throw new UnsupportedOperationException();
    }

    public void initOrderData(Long lastUpdateId, List<OrderChangeDTO> orderChangeDTOS) {
        throw new UnsupportedOperationException();
    }
}
