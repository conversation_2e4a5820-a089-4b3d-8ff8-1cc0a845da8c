package vip.xiaonuo.stream.external;

import aacoin.trade.exchange.websocket.MarketSymbol;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public abstract class ExternalWsClient {
    public abstract void init(List<MarketSymbol> marketSymbols);
    public abstract boolean supports(String exchangeCode);
    public abstract void add(MarketSymbol marketSymbol);

    List<MarketSymbol> marketSymbols = new ArrayList<>();

    MarketSymbol getMappingLocalMarketSymbol(String externalSymbol) {
        return marketSymbols.stream()
                .filter(e -> externalSymbol.equals(StringUtils.lowerCase(e.getCurrency() + e.getBaseCurrency())))
                .findAny().orElse(null);
    }

    String getMappingLocalSymbol(String externalSymbol) {
        MarketSymbol mappingLocalMarketSymbol = getMappingLocalMarketSymbol(externalSymbol);
        if (mappingLocalMarketSymbol != null) {
            return mappingLocalMarketSymbol.getCurrency() + "_" + mappingLocalMarketSymbol.getBaseCurrency();
        }
        return null;
    }
}
