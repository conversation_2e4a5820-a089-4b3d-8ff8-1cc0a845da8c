package vip.xiaonuo.stream.external;


import aacoin.trade.exchange.rest.Const;
import aacoin.trade.exchange.rest.ExternalConfig;
import aacoin.trade.exchange.rest.KLinePeriodMapping;
import aacoin.trade.exchange.util.ZipUtil;
import aacoin.trade.exchange.websocket.CrossNetWorkOrderDTO;
import aacoin.trade.exchange.websocket.MarketSymbol;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import org.java_websocket.WebSocket;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.java_websocket.framing.Framedata;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import vip.xiaonuo.core.enums.KLineType;
import vip.xiaonuo.core.pojo.dto.BatchKLineDTO;
import vip.xiaonuo.core.pojo.dto.KLineUpdateDTO;
import vip.xiaonuo.core.pojo.dto.TradeDTO;
import vip.xiaonuo.stream.external.utils.HuobiApiSignature;
import vip.xiaonuo.stream.service.BatchKLineService;
import vip.xiaonuo.stream.service.ScheduleSend;
import vip.xiaonuo.stream.service.TickerService;
import vip.xiaonuo.stream.ws.DataSender;

import javax.annotation.Resource;
import java.io.Closeable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URI;
import java.nio.ByteBuffer;
import java.text.MessageFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2018/9/29 10:47
 */
@Component
public class HuobiWsClient extends ExternalWsClient implements DisposableBean {
    private Logger logger = LoggerFactory.getLogger(getClass());
//    private UserDataWebSocket userDataWebSocket;
    private MarketDataWebSocket marketDataWebSocket;

    /**
     * 用于提取websocket返回的channel中的信息
     * market.$symbol.trade.detail
     * market.$symbol.depth.$type
     * market.$symbol.kline.$period
     */
    private Pattern HUOBI_CHANNEL_PATTERN = Pattern.compile("market.(\\w*).(\\w*).(\\w*)");

//    @Resource
//    private KafkaTemplate<String, Object> kafkaTemplate;
    @Resource
    private ExternalConfig externalConfig;
    @Resource
    private TickerService tickerService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private BatchKLineService batchKLineService;

    private Timer timer = new Timer();
//    private boolean userDataWebSocketConnecting;
    private boolean marketDataWebSocketConnecting;

    @Scheduled(initialDelay = 30000L, fixedDelay = 2000L)
    void reset() {
        if (marketDataWebSocket != null && marketDataWebSocket.isClosed()) {
            logger.info("market-data ws reconnecting...");
            initMarketDataWebsocket();
        }
//        if (userDataWebSocket != null && userDataWebSocket.isClosed()) {
//            logger.info("user-data ws reconnecting...");
//            initUserDataWebsocket();
//        }
    }

    @Override
    public void init(List<MarketSymbol> marketSymbols) {
        this.marketSymbols.addAll(marketSymbols);
//        initUserDataWebsocket();
        initMarketDataWebsocket();
    }

    private void initMarketDataWebsocket() {
        if(marketDataWebSocketConnecting) {
            return;
        }
        if(marketDataWebSocket != null) {
            IOUtils.closeQuietly(marketDataWebSocket);
        }
        marketDataWebSocketConnecting = true;
        try {
            logger.info("init huobi market-data websocket client...");
            URI uri = new URI(Const.HUOBI_MARKET_DATA_WS_URL);
            marketDataWebSocket = new MarketDataWebSocket(uri);
            marketDataWebSocket.connectBlocking();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            marketDataWebSocketConnecting = false;
        }
    }

//    private void initUserDataWebsocket() {
//        if(userDataWebSocketConnecting) {
//            return;
//        }
//        if(userDataWebSocket != null) {
//            IOUtils.closeStream(userDataWebSocket);
//        }
//        userDataWebSocketConnecting = true;
//        try {
//            ExternalConfig.ExchangeApiConfig apiConfig = externalConfig.getApiConfig(Const.EXCHANGE_CODE_HUOBI);
//            if (StringUtils.isBlank(apiConfig.getAccessKey()) || StringUtils.isBlank(apiConfig.getSecretKey())) {
//                logger.warn("access key 没有配置");
//                return;
//            }
//            URI uri = new URI(Const.HUOBI_USER_DATA_WS_URL);
//            userDataWebSocket = new UserDataWebSocket(uri, apiConfig.getAccessKey(), apiConfig.getSecretKey());
//            userDataWebSocket.connectBlocking();
//        } catch (Exception e) {
//            logger.error("", e);
//        } finally {
//            userDataWebSocketConnecting = false;
//        }
//    }

    private String buildSubscribeMessage(String sub, String id) {
        Map<String, String> map = new HashMap<>(2);
        map.put("sub", sub);
        map.put("id", id);
        return JSON.toJSONString(map);
    }

    @Override
    public void add(MarketSymbol marketSymbol) {
        if (this.marketSymbols.contains(marketSymbol)) {
            return;
        }
        String huobiSymbol = StringUtils.lowerCase(marketSymbol.getCurrency() + marketSymbol.getBaseCurrency());
//        getUserDataWebSocket().send("{\"op\":\"sub\", \"topic\": \"orders." + huobiSymbol + "\"}");
        getMarketDataWebSocket().subscribeKLineAndTrade(marketSymbol);
        this.marketSymbols.add(marketSymbol);
    }

    private MarketDataWebSocket getMarketDataWebSocket() {
        if (marketDataWebSocket == null || marketDataWebSocket.isClosed()) {
            initMarketDataWebsocket();
        }
        return marketDataWebSocket;
    }
//
//    private UserDataWebSocket getUserDataWebSocket() {
//        if (userDataWebSocket == null || userDataWebSocket.isClosed()) {
//            initUserDataWebsocket();
//        }
//        return userDataWebSocket;
//    }

    @Override
    public boolean supports(String exchangeCode) {
        return Const.EXCHANGE_CODE_HUOBI.equalsIgnoreCase(exchangeCode);
    }

    class UserDataWebSocket extends WebSocketClient implements Closeable{
        private Logger logger = LoggerFactory.getLogger(getClass());

        private String accessKey;
        private String secretKey;
        private volatile long lastPingTime;

        UserDataWebSocket(URI uri, String accessKey, String secretKey) {
            super(uri, new Draft_6455(), null, 5000);
            if (Const.SET_PROXY) {
                this.setProxy(new Proxy(Proxy.Type.HTTP, InetSocketAddress.createUnresolved("127.0.0.1", 7890)));
            }
            this.accessKey = accessKey;
            this.secretKey = secretKey;
        }

        @Override
        public void onOpen(ServerHandshake shake) {
            lastPingTime = System.currentTimeMillis();
            logger.info("UserDataWebSocket connected... add auth");
            addAuth();
        }

        @Override
        public void onWebsocketPing(WebSocket conn, Framedata f) {
            lastPingTime = System.currentTimeMillis();
            super.onWebsocketPing(conn, f);
        }

        @Override
        public void onMessage(String arg0) {
            lastPingTime = System.currentTimeMillis();
            if (arg0 != null) {
                logger.info("receive message " + arg0);
            }
        }

        @Override
        public void onError(Exception arg0) {
            try {
                String message = new String(arg0.getMessage().getBytes(), "UTF-8");
                logger.info("websocket error: " + message);
            } catch (Exception e) {
                logger.error("", e);
            }
        }

        @Override
        public void onClose(int arg0, String arg1, boolean arg2) {
            logger.info("UserDataWebSocket connection closed, {} {} {}", arg0, arg1, arg2);
        }

        @Override
        public void onMessage(ByteBuffer bytes) {
            String message = "";
            try {
                lastPingTime = System.currentTimeMillis();
                message = new String(ZipUtil.decompress(bytes.array()), "UTF-8");
                JSONObject jsonObject = JSONObject.parseObject(message);
                String op = jsonObject.getString("op");
                Integer errCode = jsonObject.getInteger("err-code");
                if ("ping".equals(op)) {
                    String pong = jsonObject.toString();
                    send(pong.replace("ping", "pong"));
                } else if ("auth".equals(op)) {
                    if (errCode == 0) {
                        logger.info("websocket auth success!");
                        subscribeOrderChange();
                    } else {
                        logger.info(message);
                    }
                } else if ("notify".equals(op)) {
                    logger.info("notify onMessage: {}", message);
                    String topic = jsonObject.getString("topic");
                    if (topic.contains("orders")) {
                        JSONObject orderDetail = (JSONObject) jsonObject.get("data");
                        String seq_id = orderDetail.getString("seq-id");
                        String order_id = orderDetail.getString("order-id");
                        String symbol = orderDetail.getString("symbol");//交易对
                        Integer account_id = orderDetail.getInteger("account-id");//账户 id
                        String order_amount = (String) orderDetail.get("order-amount");//订单数量
                        String order_price = (String) orderDetail.get("order-price");//订单价格
                        Long created_at = orderDetail.getLong("created-at");//订单创建时间
                        String order_type = (String) orderDetail.get("order-type");//订单类型，请参考订单类型说明
                        String order_source = (String) orderDetail.get("order-source");//订单来源，请参考订单来源说明
                        String order_state = (String) orderDetail.get("order-state");//订单状态，请参考订单状态说明
                        String role = (String) orderDetail.get("role");//maker, taker
                        String price = (String) orderDetail.get("price");//成交价格
                        String filled_amount = (String) orderDetail.get("filled-amount");//单次成交数量
                        String unfilled_amount = (String) orderDetail.get("unfilled-amount");//单次未成交数量
                        String filled_cash_amount = (String) orderDetail.get("filled-cash-amount");//单次成交金额
                        String filled_fees = (String) orderDetail.get("filled-fees");//单次成交手续费（买入为币，卖出

                        MarketSymbol mappingLocalMarketSymbol = getMappingLocalMarketSymbol(symbol);
                        if (mappingLocalMarketSymbol == null) {
                            logger.info("huobiSymbol {} not mapped", symbol);
                            return;
                        }
                        CrossNetWorkOrderDTO cno = new CrossNetWorkOrderDTO();
                        cno.setCurrency(mappingLocalMarketSymbol.getCurrency());
                        cno.setBaseCurrency(mappingLocalMarketSymbol.getBaseCurrency());
                        cno.setExternalOrderId(order_id);
                        cno.setSource(Const.EXCHANGE_CODE_HUOBI);
                        cno.setStatus(WsClientService.MSG_STATUS_SENT);
                        cno.setExternalMessageId(Const.EXCHANGE_CODE_HUOBI + "_" + seq_id);
                        cno.setCreatedBy("websocket-client");
                        cno.setOrderType(StringUtils.startsWithIgnoreCase(order_type, "buy") ? "buy" : "sell");

                        if ("partial-canceled".equalsIgnoreCase(order_state) || "canceled".equalsIgnoreCase(order_state)) {
                            //撤单
                            cno.setMessageType(CrossNetWorkOrderDTO.TYPE_CANCEL);
                        } else if ("partial-filled".equalsIgnoreCase(order_state) || "filled".equalsIgnoreCase(order_state)) {
                            //成交
                            cno.setMessageType(CrossNetWorkOrderDTO.TYPE_EXCHANGE);
                            cno.setLastFilledPrice(price);
                            cno.setLastFilledQuantity(filled_amount);
                            cno.setOrderQuantity(order_amount);
                            cno.setOrderPrice(order_price);
                            cno.setRemainQuantity(unfilled_amount);

                            //买单的情况下，如果是taker，则是买方市场；如果是卖单，maker，也是买方市场
                            String side = ("buy".equals(cno.getOrderType()) && "taker".equalsIgnoreCase(role))
                                    || ("sell".equals(cno.getOrderType()) && "maker".equalsIgnoreCase(role)) ? "buy" : "sell";

                            cno.setTransactionSide(side);
                        }
                        if (StringUtils.isNotBlank(cno.getMessageType())) {
//                            timer.schedule(new TimerTask() {
//                                @Override
//                                public void run() {
//                                    kafkaTemplate.send(KafkaTopics.COROSS_NETWORK_EXCHANGE_RESULT, JSON.toJSONString(cno));
//                                }
//                            }, 1000);
//                            saveMessage(cno);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("message: " + message, e);
            }
        }

        private void addAuth() {
            Map<String, String> map = new HashMap<>();
            HuobiApiSignature as = new HuobiApiSignature();
            try {
                String theHost = uri.getHost() + ":" + uri.getPort();
                as.createSignature(accessKey, secretKey, "GET", theHost, uri.getPath(), map);
            } catch (Exception e) {
                logger.error("", e);
                return;
            }
            map.put(HuobiApiSignature.op, HuobiApiSignature.opValue);
            map.put("cid", "auth_id_1");
            send(JSON.toJSONString(map));
        }

        private void subscribeOrderChange() {
            for (MarketSymbol marketSymbol : marketSymbols) {
                String huobiSymbol = StringUtils.lowerCase(marketSymbol.getCurrency() + marketSymbol.getBaseCurrency());
                send("{\"op\":\"sub\", \"topic\": \"orders." + huobiSymbol + "\"}");
            }
        }

    }

    class MarketDataWebSocket extends WebSocketClient implements Closeable{
        private Logger logger = LoggerFactory.getLogger(getClass());

        MarketDataWebSocket(URI uri) {
            super(uri, new Draft_6455(), null, 5000);
        }

        @Override
        public void onOpen(ServerHandshake shake) {
            logger.info("market-data ws connected...");
            marketSymbols.forEach(this::subscribeKLineAndTrade);
        }

        void subscribeKLineAndTrade(MarketSymbol marketSymbol) {
            String huobiSymbol = (marketSymbol.getCurrency() + marketSymbol.getBaseCurrency()).toLowerCase();
            for (Map.Entry<String, KLineType> entry : KLinePeriodMapping.getHuobiPeriodMapping().entrySet()) {
                marketDataWebSocket.send(buildSubscribeMessage(MessageFormat.format("market.{0}.kline.{1}", huobiSymbol, entry.getKey()),
                        MessageFormat.format("kline_sub_id_{0}_1", entry.getValue().getCode())));
            }

            marketDataWebSocket.send(buildSubscribeMessage(MessageFormat.format("market.{0}.trade.detail", huobiSymbol),
                    "trade_sub_id_1"));
        }

        @Override
        public void send(String text) {
            if (!StringUtils.contains(text, "pong")) {
                logger.info("send data: {}", text);
            }
            super.send(text);
        }

        @Override
        public void onMessage(String arg0) {
            if (arg0 != null) {
                logger.info("receive message " + arg0);
            }
        }

        @Override
        public void onError(Exception arg0) {
            try {
                String message = new String(arg0.getMessage().getBytes(), "UTF-8");
                logger.info("websocket error: " + message);
            } catch (Exception e) {
                logger.error("", e);
            }
        }

        @Override
        public void onClose(int arg0, String arg1, boolean arg2) {
            logger.info("MarketDataWebSocket connection closed, {} {} {}", arg0, arg1, arg2);
        }

        @Override
        public void onMessage(ByteBuffer bytes) {
            String message = "";
            try {
                message = new String(ZipUtil.decompress(bytes.array()), "UTF-8");
                JSONObject json = JSON.parseObject(message);
                if (json.containsKey("ping")) {
                    sendPong();
                    return;
                }
                if ("error".equalsIgnoreCase(json.getString("status"))) {
                    logger.warn("error resp: {}", json.toJSONString());
                    return;
                }
                String resCh = (String) json.get("ch");
                if (StringUtils.isBlank(resCh)) {
                    if (!"ok".equalsIgnoreCase((String) json.get("status"))) {
                        logger.info("invalid data: {}", json);
                    }
                    return;
                }
                Matcher matcher = HUOBI_CHANNEL_PATTERN.matcher(resCh);
                if (!matcher.matches()) {
                    logger.info("invalid channel : {}", resCh);
                    return;
                }
                String symbol = matcher.group(1);
                String dataType = matcher.group(2);
                JSONObject tick = json.getJSONObject("tick");
                if (tick == null || tick.isEmpty()) {
                    return;
                }
                if ("trade".equals(dataType)) {
                    tradeDataUpdated(symbol, tick);
                } else if ("kline".equals(dataType)) {
                    String period = matcher.group(3);
                    kLineDataUpdated(symbol, period, tick);
                }
            } catch (Exception e) {
                logger.info("火币数据处理异常:" + message, e);
            }
        }

        void sendPong() {
            JSONObject pongMessage = new JSONObject();
            pongMessage.put("pong", System.currentTimeMillis());
            send(pongMessage.toJSONString());
        }

        void kLineDataUpdated(String huobisymbol, String huobiPeriod, JSONObject ticks) {
            BigDecimal ts = new BigDecimal(new Date(new Long((ticks.get("id")) + "000")).getTime());
            BigDecimal open = ticks.getBigDecimal("open");
            BigDecimal close = ticks.getBigDecimal("close");
            BigDecimal low = ticks.getBigDecimal("low");
            BigDecimal high = ticks.getBigDecimal("high");
            BigDecimal vol = ticks.getBigDecimal("amount");

            String localSymbol = getMappingLocalSymbol(huobisymbol);

            Integer[] marketDecimal = tickerService.getMarketDecimals(localSymbol);
            int priceDecimal = marketDecimal[0];

            List<BigDecimal> line = Arrays.asList(ts,
                    open.setScale(priceDecimal, RoundingMode.FLOOR).stripTrailingZeros(),
                    high.setScale(priceDecimal, RoundingMode.FLOOR).stripTrailingZeros(),
                    low.setScale(priceDecimal, RoundingMode.FLOOR).stripTrailingZeros(),
                    close.setScale(priceDecimal, RoundingMode.FLOOR).stripTrailingZeros(),
                    vol.stripTrailingZeros());
            List<List<BigDecimal>> lines = new ArrayList<>();
            lines.add(line);

            KLineType localKLineType = KLinePeriodMapping.getLocalKLinePeriod(huobiPeriod);
            if (localKLineType != null && localSymbol != null) {

                if (!CollectionUtils.isEmpty(lines)) {
                    List<BigDecimal> aLines = lines.get(0);
                    BatchKLineDTO batchKLineDTO = BatchKLineDTO.zero();
                    batchKLineDTO.setTime(new Date(aLines.get(0).longValue()));
                    batchKLineDTO.setOpen(aLines.get(1));
                    batchKLineDTO.setHigh(aLines.get(2));
                    batchKLineDTO.setLow(aLines.get(3));
                    batchKLineDTO.setClose(aLines.get(4));
                    batchKLineDTO.setVol(aLines.get(5));
                    batchKLineService.writeToRedis(localSymbol, localKLineType, batchKLineDTO);
                }

                KLineUpdateDTO dto = new KLineUpdateDTO();
                dto.setPeriod(localKLineType.getPeriod());
                dto.setSymbol(localSymbol);
                dto.setLines(lines);
//                kafkaTemplate.send(KafkaTopics.DP_NEW_KLINE, JSON.toJSONString(dto));

                //推送到前端
                SpringUtil.getBean(ScheduleSend.class).addKLinePushDTO(dto.getSymbol(), dto.getPeriod(), dto.getLines());
            }
        }

        void tradeDataUpdated(String huobisymbol, JSONObject tick) {

            JSONArray data = (JSONArray) tick.get("data");
            List<TradeDTO> tradeList = new ArrayList<>();
            if (data != null) {
                for (Object aData : data) {
                    JSONObject trad = (JSONObject) aData;
                    TradeDTO tradeDTO = new TradeDTO();
                    tradeDTO.setQuantity(trad.getBigDecimal("amount"));
                    tradeDTO.setPrice(trad.getBigDecimal("price"));
                    tradeDTO.setTransTime(trad.getLong("ts"));
                    tradeDTO.setMainMarket((String) trad.get("direction"));
                    tradeList.add(tradeDTO);
                }
            }

            String localSymbol = getMappingLocalSymbol(huobisymbol);
            if (localSymbol != null && !CollectionUtils.isEmpty(tradeList)) {
                Integer[] decimals = tickerService.getMarketDecimals(localSymbol);
                tradeList.forEach(e -> {
                    e.setPriceDecimal(decimals[0]);
                    e.setQuantityDecimal(decimals[1]);
                    e.setSymbol(localSymbol);
                });

//                List<String> serializedTradeData = tradeList.stream().map(SerializeUtils::serializeTradeDTO).collect(Collectors.toList());
//                kafkaTemplate.send(KafkaTopics.DP_NEW_TRADE, localSymbol, JSON.toJSONString(serializedTradeData));
                //推送给前端
                SpringUtil.getBean(DataSender.class).sendTradeData(localSymbol,  tradeList);

//                redisTemplate.opsForValue().set(String.format(DpConstants.MARKET_LATEST_PRICE, localSymbol),
//                        tradeList.get(tradeList.size() - 1).getPrice());

//                String tradeKey = String.format(DpConstants.MARKET_RECENT_TRADES, localSymbol);
//                serializedTradeData.forEach(e -> {
//                    Long aLong = redisTemplate.opsForList().leftPush(tradeKey, e);
//                    if (aLong > 50) {
//                        redisTemplate.opsForList().rightPop(tradeKey);
//                    }
//                });
            }
        }

    }

    @Override
    public void destroy() {
        if (marketDataWebSocket != null && !marketDataWebSocket.isClosed()) {
            marketDataWebSocket.close();
        }
//        if (userDataWebSocket != null && !userDataWebSocket.isClosed() ) {
//            userDataWebSocket.close();
//        }
    }
}
