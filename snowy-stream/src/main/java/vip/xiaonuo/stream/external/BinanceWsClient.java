package vip.xiaonuo.stream.external;

import aacoin.trade.exchange.rest.Const;
import aacoin.trade.exchange.rest.ExternalConfig;
import aacoin.trade.exchange.rest.KLinePeriodMapping;
import aacoin.trade.exchange.websocket.MarketSymbol;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binance.api.client.BinanceApiClientFactory;
import com.binance.api.client.BinanceApiWebSocketClient;
import com.binance.api.client.domain.event.AggTradeEvent;
import com.binance.api.client.domain.event.CandlestickEvent;
import com.binance.api.client.domain.event.TickerEvent;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.exception.BinanceApiException;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import vip.xiaonuo.core.consts.DpConstants;
import vip.xiaonuo.core.enums.KLineType;
import vip.xiaonuo.core.pojo.dto.BatchKLineDTO;
import vip.xiaonuo.core.pojo.dto.KLineUpdateDTO;
import vip.xiaonuo.core.pojo.dto.TickerDTO;
import vip.xiaonuo.core.pojo.dto.TradeDTO;
import vip.xiaonuo.core.util.DpDataUtils;
import vip.xiaonuo.core.util.KafkaTopics;
import vip.xiaonuo.core.util.SerializeUtils;
import vip.xiaonuo.stream.service.BatchKLineService;
import vip.xiaonuo.stream.service.ScheduleSend;
import vip.xiaonuo.stream.service.TickerService;
import vip.xiaonuo.stream.ws.DataSender;

import javax.annotation.Resource;
import java.io.Closeable;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class BinanceWsClient extends ExternalWsClient implements DisposableBean {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Resource
    private KafkaTemplate<String, Object> kafkaTemplate;
    @Resource
    private ExternalConfig externalConfig;
    @Resource
    private TickerService tickerService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private BatchKLineService batchKLineService;

    private BinanceApiWebSocketClient webSocketClient;
    private BinanceApiClientFactory clientFactory;
    private String listenerKey;
    private Timer timer = new Timer();
    private MarketDataWebSocket marketDataWebSocket;
    //    private BinanceWebSocketHandle userDataWebSocket;
//    private boolean userDataWebSocketConnecting;
    private boolean marketDataWebSocketConnecting;

    @Scheduled(initialDelay = 40 * 1000L, fixedDelay = 4 * 1000L)
    void reset() {
        if (marketDataWebSocket != null && marketDataWebSocket.isClosed() && !marketDataWebSocketConnecting) {
            logger.info("market-data ws reconnecting...");
            subscribeMarketDataStream();
        }
//        if (userDataWebSocket != null && userDataWebSocket.isClosed() && !userDataWebSocketConnecting) {
//            logger.info("user-data ws reconnecting...");
//            subscribeUserDataStream();
//        }
    }

    @Scheduled(initialDelay = 60 * 60 * 1000L, fixedDelay = 60 * 60 * 1000L)
    public void reconnectMarketWebsockets() {
        logger.info("reconnect market-data websocket.....");
        if (marketDataWebSocket != null) {
            IOUtils.closeQuietly(marketDataWebSocket);
            try {
                Thread.sleep(2000L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        subscribeMarketDataStream();
    }

    @Scheduled(initialDelay = 30000L, fixedDelay = 60000L)
    void keepUserDataStreamAlive() {
        if (clientFactory != null && StringUtils.isNotBlank(listenerKey)) {
            clientFactory.newRestClient().keepAliveUserDataStream(listenerKey);
        }
    }


    @Override
    public void init(List<MarketSymbol> marketSymbols) {
        if (!CollectionUtils.isEmpty(marketSymbols)) {
            this.marketSymbols.addAll(marketSymbols);
        }
        ExternalConfig.ExchangeApiConfig apiConfig = externalConfig.getApiConfig(Const.EXCHANGE_CODE_BINANCE);
        clientFactory = BinanceApiClientFactory.newInstance(apiConfig.getAccessKey(), apiConfig.getSecretKey());
        initWebsocketClient();
    }

    @Override
    public void add(MarketSymbol marketSymbol) {
        if (this.marketSymbols.contains(marketSymbol)) {
            return;
        }
        this.marketSymbols.add(marketSymbol);
        subscribeMarketDataStream();
    }

    private void initWebsocketClient() {
        webSocketClient = clientFactory.newWebSocketClient();
        subscribeMarketDataStream();
    }


    private synchronized void subscribeMarketDataStream() {
        try {
            marketDataWebSocketConnecting = true;
            if (marketDataWebSocket != null) {
                IOUtils.closeQuietly(marketDataWebSocket);
            }
            marketDataWebSocket = new MarketDataWebSocket();
            marketDataWebSocket.connect();
        } catch (Exception e) {
            logger.error("", e);
        } finally {
            marketDataWebSocketConnecting = false;
        }
    }

    class MarketDataWebSocket extends WebSocketListener implements Closeable {
        private Logger logger = LoggerFactory.getLogger(getClass());
        private OkHttpClient client;
        private WebSocket webSocket;
        private boolean closed;

        MarketDataWebSocket() {
            Dispatcher d = new Dispatcher();
            d.setMaxRequestsPerHost(100);
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            if (Const.SET_PROXY) {
                builder.proxy(new Proxy(Proxy.Type.HTTP,
                        InetSocketAddress.createUnresolved("127.0.0.1", 7890)));
            }
            this.client = builder.dispatcher(d).retryOnConnectionFailure(true).build();
        }

        void connect() {
            List<String> binanceStreams = marketSymbols.stream()
                    .map(e -> {
                        if ("bch".equalsIgnoreCase(e.getCurrency())) {
                            return ("bcc" + e.getBaseCurrency()).toLowerCase();
                        }
                        return (e.getCurrency() + e.getBaseCurrency()).toLowerCase();
                    }).map(e -> {
                        List<String> streams_ = new ArrayList<>();
                        //todo k线不订阅
                        // klineAct
                        streams_.addAll(KLinePeriodMapping.getBinancePeriodMapping().keySet().stream().map(kLineType -> String.format("%s@kline_%s", e, kLineType.getIntervalId())).collect(Collectors.toList()));
                        streams_.add(String.format("%s@aggTrade", e));
                        streams_.add(String.format("%s@ticker", e));
                        return streams_;
                    })
                    .reduce(new ArrayList<>(), (a, b) -> {
                        a.addAll(b);
                        return a;
                    });
            String subscribeUrl = String.format(Const.BINANCE_MARKET_DATA_WS_URL, StringUtils.join(binanceStreams, "/"));
            logger.info("subscribe MarketDataStream data:\n{}", StringUtils.join(binanceStreams, "\n"));
            Request request = new Request.Builder().url(subscribeUrl).build();
            webSocket = client.newWebSocket(request, MarketDataWebSocket.this);
        }

        public void close() {
            if (webSocket != null) {
                webSocket.close(3105, "");
            }
        }

        @Override
        public void onOpen(WebSocket webSocket, Response response) {
            logger.info("market data ws open...");
        }

        boolean isClosed() {
            return closed;
        }

        @Override
        public void onFailure(WebSocket webSocket, Throwable t, Response response) {
            logger.warn("market data ws closed!!! ", t);
            closed = true;
        }

        @Override
        public void onMessage(WebSocket webSocket, String message) {
//            logger.info("订阅收到的信息: " + message);
            if (message != null) {
                JSONObject jsonObject = JSONObject.parseObject(message);
                String stream = jsonObject.getString("stream");
                String data = jsonObject.getString("data");
                try {
                    if (StringUtils.contains(stream, "@kline")) {
                        CandlestickEvent candlestickEvent = convert(data, CandlestickEvent.class);
                        onKLineUpdated(candlestickEvent);
                    } else if (StringUtils.contains(stream, "aggTrade")) {
                        AggTradeEvent tradeEvent = convert(data, AggTradeEvent.class);
                        onNewTradeUpdated(tradeEvent);
                    } else if (StringUtils.contains(stream, "ticker")) {
                        TickerEvent tickerEvent = convert(data, TickerEvent.class);
//                        System.out.println(new DateTime(tickerEvent.getEventTime()) + "ticker>>:" + message + "");
                        onTickerEvent(tickerEvent);
                    } else {
                        logger.warn("unrecognized message: {}", message);
                    }
                } catch (Exception e) {
                    logger.error("error process msg:" + jsonObject.toJSONString(), e);
                }
            }
        }

        @Override
        public void onClosed(WebSocket webSocket, int code, String reason) {
            logger.warn("market data ws closed!!! {} {} ", code, reason);
            closed = true;
        }

        private void onTickerEvent(TickerEvent tickerEvent) {
            String symbol = tickerEvent.getSymbol();
            String localSymbol_ = getMappingLocalSymbol(symbol);

            TickerDTO ticker = new TickerDTO();
            ticker.setT(tickerEvent.getEventTime());
            ticker.setSymbol(localSymbol_);
            ticker.setL(NumberUtils.createBigDecimal(tickerEvent.getL()));
            ticker.setH(NumberUtils.createBigDecimal(tickerEvent.getH()));
            ticker.setC(NumberUtils.createBigDecimal(tickerEvent.getC()));
            ticker.setO(NumberUtils.createBigDecimal(tickerEvent.getO()));
            ticker.setObv(NumberUtils.createBigDecimal(tickerEvent.getQ()));
            ticker.setVol(NumberUtils.createBigDecimal(tickerEvent.getV()));
            ticker.setPrevPrice(NumberUtils.createBigDecimal(tickerEvent.getX()));
            ticker.setChangePercent(tickerEvent.getCp());
            kafkaTemplate.send(KafkaTopics.DP_MARKET_TICKER, JSON.toJSONString(ticker));
        }

        private void onKLineUpdated(CandlestickEvent candlestickEvent) {
            // SOLUSDT
            String symbol = candlestickEvent.getSymbol();
            // SOL_USDT
            String localSymbol_ = getMappingLocalSymbol(symbol);
            KLineType localKLineType = getLocalKLineType(candlestickEvent.getIntervalId());
            if (localSymbol_ == null || localKLineType == null) {
                logger.warn("binance {} intervalId: {} not found", candlestickEvent.getSymbol(), candlestickEvent.getIntervalId());
                return;
            }
//            System.out.println(candlestickEvent.getEventType()+" "+candlestickEvent.getIntervalId()+"xxuuuuuuuuuuuuuuuuu>:"
//                    +new DateTime(candlestickEvent.getEventTime()) +"   "
//                    +new DateTime(candlestickEvent.getOpenTime())+"    "
//                    +new DateTime(candlestickEvent.getCloseTime()));
            BatchKLineDTO batchKLineDTO = BatchKLineDTO.zero();
            batchKLineDTO.setTime(new Date(candlestickEvent.getOpenTime()));
            batchKLineDTO.setOpen(NumberUtils.createBigDecimal(candlestickEvent.getOpen()));
            batchKLineDTO.setHigh(NumberUtils.createBigDecimal(candlestickEvent.getHigh()));
            batchKLineDTO.setLow(NumberUtils.createBigDecimal(candlestickEvent.getLow()));
            batchKLineDTO.setClose(NumberUtils.createBigDecimal(candlestickEvent.getClose()));
            batchKLineDTO.setVol(NumberUtils.createBigDecimal(candlestickEvent.getVolume()));
            // todo gjn 写入，Kline推送的情况下，这个是这根K线的开始时间
//            long ts = batchKLineDTO.getTime().getTime();
//            // 1m,5m,15m,30m,1h,2h,4h,6h,12h,1d,1w只要有值就说明是控制过的K线
//            if (candlestickEvent.getIntervalId().equals("1m")) {
//
//            }
//            if (candlestickEvent.getIntervalId().equals("5m")) {
//
//            }
//            if (candlestickEvent.getIntervalId().equals("15m")) {
//
//            }
//            if (candlestickEvent.getIntervalId().equals("30m")) {
//
//            }
//            if (candlestickEvent.getIntervalId().equals("1h")) {
//
//            }
//            if (candlestickEvent.getIntervalId().equals("2h")) {
//
//            }
//            if (candlestickEvent.getIntervalId().equals("4h")) {
//
//            }
//            if (candlestickEvent.getIntervalId().equals("6h")) {
//
//            }
//            if (candlestickEvent.getIntervalId().equals("12h")) {
//
//            }
//            if (candlestickEvent.getIntervalId().equals("1d")) {
//
//            }
//            if (candlestickEvent.getIntervalId().equals("1w")) {
//
//            }
            // 实时推送的如果系统有价格控制要执行的
            BigDecimal adjustPrice = DpDataUtils.getAdjustPrice(redisTemplate, localSymbol_);
            if (adjustPrice != null) {
                // todo gjn 开盘价不能一直变的那样会有问题
                // 多个情况1、正在控线中，控线已经完毕
                BigDecimal completedAdjustPrice = DpDataUtils.getCompletedAdjustPrice(redisTemplate, localSymbol_);
                Long ts = DpDataUtils.getCompletedAdjustTs(redisTemplate, localSymbol_);
                if (ts != null) {
//                    if (ts <= candlestickEvent.getOpenTime()) {
                    if (ts < candlestickEvent.getOpenTime()) {
                        if (batchKLineDTO.getOpen() != null) {
                            batchKLineDTO.setOpen(batchKLineDTO.getOpen().add(completedAdjustPrice));
                        }
                    }
                }
                if (batchKLineDTO.getClose() != null) {
                    BigDecimal price = DpDataUtils.getLatestPrice(redisTemplate, localSymbol_);
                    if (price != null) {
                        batchKLineDTO.setClose(price);
                    } else {
                        batchKLineDTO.setClose(batchKLineDTO.getClose().add(adjustPrice));
                    }
                }
                if (batchKLineDTO.getHigh() != null) {
                    batchKLineDTO.setHigh(batchKLineDTO.getHigh().add(adjustPrice));
                }
                if (batchKLineDTO.getLow() != null) {
                    batchKLineDTO.setLow(batchKLineDTO.getLow().add(adjustPrice));
                }
            }
            // 如果是2025-07-15 15:00的K线，大于15:00这根K线才是确定下来的，如果是小于的就是没有确定下来的
            // 2025-07-15 15:00:00 ~ 15:00:59 直到 15:01:00，这根 K 线的所有数据（开盘价、最高价、最低价、收盘价、成交量等）才确定完成。
            batchKLineService.writeToRedis(localSymbol_, localKLineType, batchKLineDTO);

            List<BigDecimal> lines = batchKLineDTO.toList();
            KLineUpdateDTO dto = new KLineUpdateDTO();
            dto.setPeriod(localKLineType.getPeriod());
            dto.setSymbol(localSymbol_);
            dto.setLines(Collections.singletonList(lines));
//            System.out.println(candlestickEvent);
            kafkaTemplate.send(KafkaTopics.DP_NEW_KLINE, JSON.toJSONString(dto));
            //推送到前端
            SpringUtil.getBean(ScheduleSend.class).addKLinePushDTO(dto.getSymbol(), dto.getPeriod(), dto.getLines());
        }

        private void onNewTradeUpdated(AggTradeEvent tradeEvent) {
            String symbol = tradeEvent.getSymbol();
            String localSymbol_ = getMappingLocalSymbol(symbol);
            Integer[] decimals = tickerService.getMarketDecimals(localSymbol_);
            if (localSymbol_ != null) {
                TradeDTO tradeDTO = new TradeDTO();
                tradeDTO.setSymbol(localSymbol_);
                tradeDTO.setTransTime(tradeEvent.getTradeTime());
                tradeDTO.setPrice(NumberUtils.createBigDecimal(tradeEvent.getPrice()));
                tradeDTO.setQuantity(NumberUtils.createBigDecimal(tradeEvent.getQuantity()));

                tradeDTO.setPriceDecimal(decimals[0]);
                tradeDTO.setQuantityDecimal(decimals[1]);

                tradeDTO.setMainMarket(tradeEvent.isBuyerMaker() ? "sell" : "buy");

                try {
                    List<String> serializedTradeData = Collections.singletonList(SerializeUtils.serializeTradeDTO(tradeDTO));
//                    kafkaTemplate.send(KafkaTopics.DP_NEW_TRADE, localSymbol_, JSON.toJSONString(serializedTradeData));
                    //推送给前端
                    SpringUtil.getBean(DataSender.class).sendTradeData(localSymbol_, Collections.singletonList(tradeDTO));

//                    redisTemplate.opsForValue().set(String.format(DpConstants.MARKET_LATEST_PRICE, localSymbol_), tradeDTO.getPrice());

                    String tradeKey = String.format(DpConstants.MARKET_RECENT_TRADES, localSymbol_);
                    serializedTradeData.forEach(e -> {
                        Long aLong = redisTemplate.opsForList().leftPush(tradeKey, e);
                        if (aLong != null && aLong > 50) {
                            redisTemplate.opsForList().rightPop(tradeKey);
                        }
                    });
                } catch (Exception e) {
                    logger.error("", e);
                }
            }
        }

        private <T> T convert(String text, Class<T> eventClass) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                return mapper.readValue(text, eventClass);
            } catch (IOException e) {
                throw new BinanceApiException(e);
            }
        }
    }

    @Override
    public boolean supports(String exchangeCode) {
        return Const.EXCHANGE_CODE_BINANCE.equalsIgnoreCase(exchangeCode);
    }

    @Override
    MarketSymbol getMappingLocalMarketSymbol(String externalSymbol) {
        return marketSymbols.stream()
                .filter(e -> {
                    String c = "bch".equalsIgnoreCase(e.getCurrency()) ?
                            "bcc" + e.getBaseCurrency() : e.getCurrency() + e.getBaseCurrency();
                    return externalSymbol.equalsIgnoreCase(c);
                })
                .findAny().orElse(null);
    }

    private KLineType getLocalKLineType(String binancePeriod) {
        CandlestickInterval interval = CandlestickInterval.fromId(binancePeriod);
        if (interval == null) {
            return null;
        }
        return KLinePeriodMapping.getLocalKLinePeriod(interval);
    }

    @Override
    public void destroy() {
        if (marketDataWebSocket != null && !marketDataWebSocket.isClosed()) {
            marketDataWebSocket.close();
        }
//        if (userDataWebSocket != null && !userDataWebSocket.isClosed()) {
//            try {
//                userDataWebSocket.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
    }
}
