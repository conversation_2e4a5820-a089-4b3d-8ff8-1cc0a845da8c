package vip.xiaonuo.stream.external;


import aacoin.trade.exchange.rest.Const;
import aacoin.trade.exchange.rest.ExchangeRestApi;
import aacoin.trade.exchange.rest.ExchangeRestApiManager;
import aacoin.trade.exchange.rest.dto.OrderDetailDTO;
import aacoin.trade.exchange.rest.dto.OrderTradeDTO;
import aacoin.trade.exchange.websocket.MarketSymbol;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.stream.service.MarketDataService;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

//import org.springframework.kafka.annotation.KafkaListener;
//import org.springframework.kafka.core.KafkaTemplate;
//import org.springframework.messaging.handler.annotation.Payload;

@Component
public class WsClientService implements CommandLineRunner {
    public static final String MSG_STATUS_CREATED = "created";
    public static final String MSG_STATUS_SENT = "sent";


    private Logger logger = LoggerFactory.getLogger(getClass());
    @Resource
    private MarketService marketService;
    @Resource
    private List<ExternalWsClient> externalApiClients;
    @Resource
    private MarketDataService marketDataService;
    //    @Resource
//    private KafkaTemplate<String, Object> kafkaTemplate;
    @Resource
    private ExchangeRestApiManager restApiManager;

    @Override
    public void run(String... args) throws Exception {
        initExternalExchangeClient();
    }

    protected void initExternalExchangeClient() {
        List<Market> markets = marketService.getAllEnabledMarkets();
        markets = markets.stream().filter(e -> StringUtils.isNotBlank(e.getPartner()))
                .collect(Collectors.toList());
        Map<String, List<MarketSymbol>> groupedSymbols = new HashMap<>();
        for (Market market : markets) {
            if (!groupedSymbols.containsKey(market.getPartner())) {
                groupedSymbols.put(market.getPartner(), new ArrayList<>());
            }
            groupedSymbols.get(market.getPartner())
                    .add(new MarketSymbol(market.getCurrencyCode(), market.getBaseCurrencyCode()));
        }
        for (Map.Entry<String, List<MarketSymbol>> entry : groupedSymbols.entrySet()) {
            ExternalWsClient client = getClient(entry.getKey());
            logger.info("init external exchanges: {}@{}", StringUtils.join(entry.getValue(), ","), entry.getKey());
            client.init(entry.getValue());
        }
    }

    private ExternalWsClient getClient(String exchangeCode) {
        ExternalWsClient externalApiClient = externalApiClients.stream()
                .filter(e -> e.supports(exchangeCode)).findFirst().orElse(null);
        if (externalApiClient == null) {
            throw new RuntimeException(exchangeCode + " not supported");
        }
        return externalApiClient;
    }

    public void initMarket(String symbol) {
        Market market = marketService.getMarketBySymbol(symbol);
        if (StringUtils.isNotBlank(market.getPartner())) {
            ExternalWsClient client = getClient(market.getPartner());
            client.add(new MarketSymbol(market.getCurrencyCode(), market.getBaseCurrencyCode()));
        }
        marketDataService.init(market, true);
    }

    /**
     * 同步外部交易所的最新成交
     *
     * @param symbol 如果不传，则同步所有的
     */
    public void syncTrade(String symbol, Date start, Date end) {
        if (StringUtils.isNotBlank(symbol)) {
            Market market = marketService.getMarketBySymbol(symbol);
            if (market != null && StringUtils.isNotBlank(market.getPartner())) {
                if (Const.EXCHANGE_CODE_HUOBI.equalsIgnoreCase(market.getPartner())) {
                    //火币接口有bug  换种方式
//                    syncMarketTradeByOrder(market);
                } else {
                    syncMarketTrade(market, start, end);
                }
            }
            return;
        }
        List<Market> markets = marketService.getAllEnabledMarkets();
        markets.stream().filter(e -> StringUtils.isNotBlank(e.getPartner()))
                .forEach(e -> this.syncMarketTrade(e, start, end));
    }

//    private void syncMarketTradeByOrder(Market market) {
//        List<LocalOrder> localOrders = streamMapper.getCurrentOrder(market.getCurrencyCode(), market.getBaseCurrencyCode());
//        if (CollectionUtils.isEmpty(localOrders)) {
//            return;
//        }
//        ExchangeRestApi exchangeApi = restApiManager.getExchangeApi(market.getPartner());
//        ExternalWsClient wsClient = getClient(market.getPartner());
//        localOrders.stream().filter(e -> StringUtils.isNotBlank(e.getPartnerOrderNo())).forEach(e -> {
//            OrderDetailDTO orderDetail = exchangeApi.getOrderDetail(
//                    market.getCurrencyCode(), market.getBaseCurrencyCode(), e.getPartnerOrderNo());
//            BigDecimal filledQuantity = orderDetail.getFilledQuantity();
//            if (filledQuantity.compareTo(e.getFilledQuantity()) != 0) {
//                List<OrderTradeDTO> orderTradeDetail = exchangeApi.getOrderTradeDetail(e.getPartnerOrderNo());
//                if (!CollectionUtils.isEmpty(orderTradeDetail)) {
//                    for (OrderTradeDTO orderTradeDTO : orderTradeDetail) {
//                        process(orderDetail, orderTradeDTO, exchangeApi, wsClient);
//                    }
//                }
//            }
//        });
//    }

    private void syncMarketTrade(Market market, Date start, Date end) {
        ExchangeRestApi exchangeApi = restApiManager.getExchangeApi(market.getPartner());
        ExternalWsClient wsClient = getClient(market.getPartner());
        List<OrderTradeDTO> marketTradeDetail = exchangeApi.getMarketTradeDetail(market.getCurrencyCode(), market.getBaseCurrencyCode(), start, end);
        if (!CollectionUtils.isEmpty(marketTradeDetail)) {
            for (OrderTradeDTO orderTradeDTO : marketTradeDetail) {
                process(null, orderTradeDTO, exchangeApi, wsClient);
            }
        }
    }

    private void process(OrderDetailDTO orderDetail, OrderTradeDTO orderTradeDTO, ExchangeRestApi exchangeApi, ExternalWsClient wsClient) {
        String tradeId = exchangeApi.getExchangeCode() + "_" + orderTradeDTO.getId();


        logger.info("trade-id: {} 同步成功:\n{}", tradeId, JSON.toJSONString(orderTradeDTO));
    }


//    @KafkaListener(topics = "NEW_SYMBOL_INIT")
//    public void initNewSymbol(@Payload List<String> messages) {
//        if (CollectionUtils.isEmpty(messages)) {
//            return;
//        }
//        messages.forEach(this::initMarket);
//    }


}
