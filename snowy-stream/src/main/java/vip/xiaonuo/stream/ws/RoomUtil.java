package vip.xiaonuo.stream.ws;

public class RoomUtil {

    public static String ticker() {
        return "all_ticker";
    }

    public static String kline(String symbol, Long period) {
        return period != null ? "kline_" + symbol + "_" + period : "kline_" + symbol;
    }

    public static String trade(String symbol) {
        return "trade_" + symbol;
    }

    public static String trade() {
        return "trade_all";
    }

    public static String order(String symbol, Integer groupDecimal) {
        return groupDecimal != null ? "order_" + symbol + "_" + groupDecimal : "order_" + symbol;
    }

//    public static String depth(String symbol) {
//        return "depth_" + symbol;
//    }
//
//    public static String assetchange(String userId, String symbol) {
//        return "assetchange_" + userId + "_" + symbol;
//    }

}
