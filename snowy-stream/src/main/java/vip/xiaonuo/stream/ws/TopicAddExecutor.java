package vip.xiaonuo.stream.ws;

import com.alibaba.fastjson.JSON;
import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.annotation.OnConnect;
import com.corundumstudio.socketio.annotation.OnDisconnect;
import com.corundumstudio.socketio.annotation.OnEvent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import vip.xiaonuo.core.enums.KLineType;
import vip.xiaonuo.core.pojo.dto.AggOrderDTO;
import vip.xiaonuo.core.pojo.dto.MarketDTO;
import vip.xiaonuo.stream.orderbook.OrderBookService;
import vip.xiaonuo.stream.service.TickerService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class TopicAddExecutor {

    private static final Logger logger = LoggerFactory.getLogger(TopicAddExecutor.class);
    @Resource
    private OrderBookService orderBookService;
    @Resource
    private TickerService tickerService;

    private AtomicInteger count = new AtomicInteger();

    @OnConnect
    public void onConnect(SocketIOClient client) {
        int i = count.incrementAndGet();
        logger.info("增加 当前在线人数:{}", i);
    }

    @OnDisconnect
    public void onDisconnect(SocketIOClient client) {
        int i = count.decrementAndGet();
        logger.info("减少 当前在线人数:{}", i);
        client.disconnect();
    }

    @OnEvent("ticker_add")
    public void allTicker(SocketIOClient client) {
        client.joinRoom(RoomUtil.ticker());
        List<MarketDTO> marketDTOS = tickerService.getMarketTickers();
        client.sendEvent("ticker", marketDTOS);
    }

    @OnEvent("kline_add")
    public void kline(SocketIOClient client, String message) {
        RequestBean.KlineBean bean = JSON.parseObject(message, RequestBean.KlineBean.class);
        if (bean == null || StringUtils.isBlank(bean.getSymbol())) {
            client.sendEvent("error", "invalid parameter");
            client.disconnect();
            return;
        }
        client.joinRoom(RoomUtil.kline(bean.getSymbol(), KLineType.of(bean.getPeriod()).getPeriod()));
    }

    @OnEvent("trade_add")
    public void trade(SocketIOClient client, String message) {
        RequestBean.ClientBean bean = JSON.parseObject(message, RequestBean.ClientBean.class);
        if (bean == null || StringUtils.isBlank(bean.getSymbol())) {
            client.sendEvent("error", "invalid parameter");
            client.disconnect();
            return;
        }
        client.joinRoom(RoomUtil.trade(bean.getSymbol()));
    }

    @OnEvent("order_add")
    public void order(SocketIOClient client, String message) {
        long time = System.currentTimeMillis();
        RequestBean.ClientBean bean = JSON.parseObject(message, RequestBean.ClientBean.class);
        if (bean == null || StringUtils.isBlank(bean.getSymbol())) {
            client.sendEvent("error", "invalid parameter");
            client.disconnect();
            return;
        }
        if (bean.getDepth() == null) {
            bean.setDepth(orderBookService.getMaxDepth(bean.getSymbol()));
        }
        String roomId = RoomUtil.order(bean.getSymbol(), bean.getDepth());
        client.joinRoom(roomId);
        Map<String, List<AggOrderDTO>> orderData = orderBookService.getOrderData(bean.getSymbol(), bean.getDepth());
        client.sendEvent("order", orderData, bean.getSymbol(), bean.getDepth());
    }

//    @OnEvent("depth_add")
//    public void depth(SocketIOClient client, String message) {
//        RequestBean.ClientBean bean = JSON.parseObject(message, RequestBean.ClientBean.class);
//        if (bean == null || StringUtils.isBlank(bean.getSymbol())) {
//            client.sendEvent("error", "invalid parameter");
//            client.disconnect();
//            return;
//        }
//        client.joinRoom(RoomUtil.depth(bean.getSymbol()));
//        KLineDepthsDTO depthsDTO = orderBookService.getDepthData(bean.getSymbol());
//        if (depthsDTO != null) {
//            client.sendEvent("depth", depthsDTO, bean.getSymbol());
//        }
//    }

//    @OnEvent("assetchange_add")
//    public void assetchange(SocketIOClient client, String message) {
//        RequestBean.OrderBean bean = JSON.parseObject(message, RequestBean.OrderBean.class);
//        if (bean == null || StringUtils.isBlank(bean.getSymbol()) || bean.getUserId() == null) {
//            client.sendEvent("error", "invalid parameter");
//            client.disconnect();
//            return;
//        }
//        client.joinRoom(RoomUtil.assetchange(bean.getUserId(), bean.getSymbol()));
//    }
}
