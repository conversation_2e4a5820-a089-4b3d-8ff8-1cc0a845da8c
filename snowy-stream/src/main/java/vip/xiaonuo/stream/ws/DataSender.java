package vip.xiaonuo.stream.ws;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import vip.xiaonuo.core.pojo.dto.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DataSender {

    @Autowired
    private ObjectMapper objectMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    public void sendMarketTicker(List<MarketDTO> marketList) {
        publish("ticker:ALL", marketList); // 所有 ticker 在一个通道也可以
    }

    public void sendKLineData(String symbol, long period, List<List<BigDecimal>> resultList) {
        KLineUpdateDTO kline = new KLineUpdateDTO();
        kline.setLines(resultList);
        kline.setPeriod(period);
        kline.setSymbol(symbol);
        publish("kline:" + symbol + ':' + period, kline);
    }

    public void sendTradeData(String symbol, List<TradeDTO> tradeDTO) {
        publish("trade:" + symbol, new TradePayload(symbol, tradeDTO));
    }

    public void sendOrderData(String symbol, Integer groupDecimal, Map<String, List<AggOrderDTO>> orderDTOS) {
        publish("order:" + symbol, new OrderPayload(symbol, orderDTOS));
    }

    private void publish(String channel, Object payload) {
//        String json = JSONUtil.toJsonStr(payload);
        try {
            String json = objectMapper.writeValueAsString(payload);
            redisTemplate.convertAndSend(channel, json);
        } catch (Exception e) {
            log.info("序列化 JSON 失败", e);
        }
    }
}
