//package vip.xiaonuo.stream.ws;
//
//
//import com.corundumstudio.socketio.SocketConfig;
//import com.corundumstudio.socketio.SocketIOClient;
//import com.corundumstudio.socketio.SocketIOServer;
//import com.corundumstudio.socketio.Transport;
//import com.corundumstudio.socketio.annotation.SpringAnnotationScanner;
//import com.corundumstudio.socketio.listener.ExceptionListener;
//import io.netty.channel.ChannelHandlerContext;
//import io.netty.handler.codec.http.HttpHeaderNames;
//import io.netty.handler.codec.http.HttpHeaders;
//import io.netty.handler.codec.http.cookie.Cookie;
//import io.netty.handler.codec.http.cookie.ServerCookieDecoder;
//import io.netty.handler.codec.http.cookie.ServerCookieEncoder;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import java.util.List;
//import java.util.Set;
//
//@Configuration
//public class WsServerConfig {
//    private Logger logger = LoggerFactory.getLogger(getClass());
//
//    @Value("${netty.websocket.port:9789}")
//    private Integer serverPort;
//
//    @Bean
//    public SocketIOServer socketIOServer() {
//
//        com.corundumstudio.socketio.Configuration configuration = new com.corundumstudio.socketio.Configuration();
//        configuration.setBossThreads(Runtime.getRuntime().availableProcessors() << 1);
//        configuration.setWorkerThreads(Runtime.getRuntime().availableProcessors() << 2);
//        logger.info("worker threads: {}", configuration.getWorkerThreads());
//        configuration.setExceptionListener(new ExceptionListener() {
//            @Override
//            public void onEventException(Exception e, List<Object> args, SocketIOClient client) {
//                logger.error("onEventException: " + client.getSessionId(), e);
//            }
//
//            @Override
//            public void onDisconnectException(Exception e, SocketIOClient client) {
//                logger.error("onDisconnectException: " + client.getSessionId(), e);
//            }
//
//            @Override
//            public void onConnectException(Exception e, SocketIOClient client) {
//                logger.error("onConnectException:  " + client.getSessionId(), e);
//            }
//
//            @Override
//            public void onPingException(Exception e, SocketIOClient client) {
//                logger.error("onPingException:  " +  client.getSessionId(), e);
//            }
//
//            @Override
//            public boolean exceptionCaught(ChannelHandlerContext ctx, Throwable e) throws Exception {
//                logger.error("", e);
//                return false;
//            }
//        });
//        configuration.setPort(serverPort);
//        configuration.setHttpCompression(true);
//        configuration.setTransports(Transport.WEBSOCKET);
//        configuration.setContext("/stream");
//
//        configuration.setAuthorizationListener((data) -> {
//            //删除cookie中的io字段
//            HttpHeaders httpHeaders = data.getHttpHeaders();
//            for (String cookieHeader: httpHeaders.getAll(HttpHeaderNames.COOKIE)) {
//                Set<Cookie> cookies = ServerCookieDecoder.LAX.decode(cookieHeader);
//                for (Cookie cookie : cookies) {
//                    if (cookie.name().equals("io")) {
//                        cookies.remove(cookie);
//                        break;
//                    }
//                }
//                httpHeaders.set(HttpHeaderNames.COOKIE, ServerCookieEncoder.LAX.encode(cookies));
//            }
//            return true;
//        });
//        configuration.setAllowCustomRequests(true);
//        SocketConfig socketConfig = new SocketConfig();
//        socketConfig.setReuseAddress(true);
//        socketConfig.setTcpKeepAlive(true);
//        configuration.setSocketConfig(socketConfig);
//        return new SocketIOServer(configuration);
//    }
//
//    @Bean
//    public SpringAnnotationScanner springAnnotationScanner(SocketIOServer socketServer) {
//        return new SpringAnnotationScanner(socketServer);
//    }
//}
