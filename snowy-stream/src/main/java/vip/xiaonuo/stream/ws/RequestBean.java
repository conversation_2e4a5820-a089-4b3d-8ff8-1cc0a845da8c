package vip.xiaonuo.stream.ws;

public interface RequestBean {

    class ClientBean {
        public ClientBean() {
        }

        public ClientBean(String symbol) {
            this.symbol = symbol;
        }

        private Integer depth;
        private String symbol;

        public Integer getDepth() {
            return depth;
        }

        public void setDepth(Integer depth) {
            this.depth = depth;
        }

        public String getSymbol() {
            return symbol;
        }

        public void setSymbol(String symbol) {
            this.symbol = symbol;
        }

    }

    class KlineBean extends ClientBean {
        private String period;

        public String getPeriod() {
            return period;
        }

        public void setPeriod(String period) {
            this.period = period;
        }
    }

    class OrderBean extends ClientBean {
        private String userId;
        private String clientId;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getClientId() {
            return clientId;
        }

        public void setClientId(String clientId) {
            this.clientId = clientId;
        }
    }

}
