#服务配置
server:
  port: 9003
  max-http-header-size: 10240

#spring相关配置
spring:
  profiles:
    active: @spring.active@
  servlet:
    multipart:
      #文件最大上传大小配置 最大上传100M
      max-request-size: 100MB
      #文件最大上传大小配置 最大上传100M
      max-file-size: 100MB
  jackson:
    time-zone: GMT+7
    date-format: yyyy-MM-dd HH:mm:ss.SSS
    locale: zh_CN
    serialization:
      # 格式化输出
      indent_output: false
  messages:
    basename: i18n/messages
  kafka:
    producer:
      retries: 3
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all
    consumer:
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: true
      auto-commit-interval: 100ms
      auto-offset-reset: earliest
      group-id: dp-stream-1

#mybaits相关配置
mybatis-plus:
  mapper-locations: classpath*:vip/xiaonuo/**/mapping/*.xml, classpath:/META-INF/modeler-mybatis-mappings/*.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    #不打印sql
    #log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    #打印sql
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    banner: false
    db-config:
      id-type: assign_id
      table-underline: true
    enable-sql-runner: true
  configuration-properties:
    prefix:
    #如果数据库为postgresql，则需要配置为blobType: BINARY
    blobType: BLOB
    #如果数据库为oracle或mssql，则需要配置为boolValue: 1
    boolValue: true

external:
  api-configs:
    - exchange-code: huobi
      accessKey: ""
      secretKey: ""
    - exchange-code: binance
      accessKey: ""
      secretKey: ""