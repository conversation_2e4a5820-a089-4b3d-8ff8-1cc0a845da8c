kafka运维地址
http://3.0.103.30:7766/

jenkins
http://3.0.103.30:8090
admin/111111

测试环境：
http://3.0.103.30:8081/coin-h5/index.html

chown -R 用户名称 目录名称


空号检查
http://3.0.103.30:8666   phone/123456
http://3.35.95.117:7000

<EMAIL>
!i123456

https://cn.dreamstime.com/
<EMAIL>/gJg*89382s

+447451201358
fb密码：!i1234
推特密码：!i123456

-- 别人的欧美盘
http://************:6003
这是总后台，登录密码：admin
963369

网页
https://bit.ly/3sqXPK0
https://bit.ly/38fy1d0

下载
https://bit.ly/3wn4BkS
https://bit.ly/3N3l0lo
这是前端，需要注册

账号：日本 89436542 密码 12345678
账号：日本 12345678 密码 12345678
账号：日本 89436543 密码 12345678


************  内网IP：************
root/data_jrckJ@12138$_
http://************:16988/92f90e82
username: t3whsgsl
password: a6ed71fc

scp jdk-8u151-linux-x64.tar.gz root@************:/opt

rpm -qa|grep jdk

-- 生成分析文件
jmap -dump:live,format=b,file=heap2.hprof 2194
-- jvm分析
https://memory.console.heapdump.cn/

-- cpu占用高分析
top -H -p 2194
ps -mp 2194 -o THREAD,tid,time
jstack 2194 | grep 2198

使用jstat 命令查看gc日志
jstat -gc 18100 3000

-- 越南时区
ln -sf /usr/share/zoneinfo/Asia/Ho_Chi_Minh /etc/localtime
修正java获取的时区不对
修改/etc/timezone,将第一行改为:
Asia/Ho_Chi_Minh


<EMAIL>
tik_tok0003


-- 别人的越南盘
官网地址：https://www.guiyon.vip
手机网页：https://www.byteme.vip
下载页面：https://www.guiyon.vip/download
下载备用：https://www.guiyon.vip/download/down.html
旧版下载：https://www.guiyon.vip/download.aspx
后台管理：https://kmadmin.ba1298.vip/sign_026c3f20.aspx
mmark99q/110120...

