#!/bin/sh
dd=`date +%Y-%m-%d-%H-%M-%S`
APP_DIR=/mnt/snowy-admin
APP_NAME=snowy-admin.jar
SPRING_OPTS='--spring.profiles.active=test'
JAVA_OPTS='-Xms512m -Xmx1024m'
PID_FILE='app_service.pid'
JAVA_BIN=java
MAX_TIMEOUT=15
echo : [dir] $APP_DIR
echo : [name] $APP_NAME

if [ -f $APP_DIR/$PID_FILE ]; then
  echo : Pid文件$APP_DIR/$PID_FILE存在
  tmpPid=`cat $APP_DIR/$PID_FILE`
  pid=`ps aux | awk '{print $2}'| grep -w $tmpPid`

  if [ -n "$pid" ]; then
    echo : $APP_NAME is running pid=$pid
    echo : kill -15 $APP_NAME
    kill -15 $pid
  fi
  for i in `seq $MAX_TIMEOUT`
  do
    sleep 1
    pid=`ps aux | awk '{print $2}'| grep -w $tmpPid`
    if [ -n "$pid" ]; then
      echo : Stoping...
    else
      break
    fi
  done

  if [ -n "$pid" ]; then
    echo : $APP_NAME is running pid=$pid
    echo : kill -9 $APP_NAME
    kill -9 $pid
  fi
  pid=`ps aux | awk '{print $2}'| grep -w $tmpPid`
fi

if [ -n "$pid" ]; then
  echo : Running
else
  cp $APP_DIR/temp/$APP_NAME $APP_DIR
  echo : Stopped
  echo : cp $APP_DIR/temp/$APP_NAME $APP_DIR
fi

cd $APP_DIR
nohup $JAVA_BIN -jar $JAVA_OPTS $APP_NAME $SPRING_OPTS > $APP_DIR/nohup.out 2>&1 & echo $! > service.pid
echo : start $APP_NAME