/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.area.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.admin.modular.area.entity.SysArea;
import vip.xiaonuo.admin.modular.area.mapper.SysAreaMapper;
import vip.xiaonuo.admin.modular.area.param.SysAreaParam;
import vip.xiaonuo.admin.modular.area.service.SysAreaService;

/**
 * 系统区域service接口实现类
 *
 * <AUTHOR>
 * @date 2020/3/13 16:11
 */
@Service
public class SysAreaServiceImpl extends ServiceImpl<SysAreaMapper, SysArea> implements SysAreaService {

    @Override
    public PageResult<SysArea> page(SysAreaParam sysAreaParam) {
        QueryWrapper<SysArea> queryWrapper = new QueryWrapper<>();
        if(ObjectUtil.isNotNull(sysAreaParam)) {
            if(ObjectUtil.isNotEmpty(sysAreaParam.getParentCode())) {
                queryWrapper.lambda().eq(SysArea::getParentCode, sysAreaParam.getParentCode());
            } else {
                queryWrapper.lambda().eq(SysArea::getParentCode, "0");
            }
        }
        //处理结果
        PageResult<SysArea> pageResult = new PageResult<>();
        pageResult.setData(this.baseMapper.list(queryWrapper));
        return pageResult;
    }
}
