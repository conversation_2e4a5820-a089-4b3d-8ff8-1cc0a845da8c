/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.userproductorder.controller;

import vip.xiaonuo.business.userproductorder.entity.UserProductOrder;
import vip.xiaonuo.business.userproductorder.param.UserProductOrderParam;
import vip.xiaonuo.business.userproductorder.pojo.vo.FindOrderForPageVo;
import vip.xiaonuo.business.userproductorder.service.UserProductOrderService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.DataScope;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.List;

/**
 * 理财订单控制器
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:41
 */
@Controller
public class UserProductOrderController {

    private String PATH_PREFIX = "userProductOrder/";

    @Resource
    private UserProductOrderService userProductOrderService;

    /**
     * 理财订单页面
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    @Permission
    @GetMapping("/userProductOrder/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 理财订单表单页面
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    @GetMapping("/userProductOrder/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询理财订单
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    @Permission
    @ResponseBody
    @GetMapping("/userProductOrder/page")
    @BusinessLog(title = "理财订单_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    @DataScope
    public PageResult<FindOrderForPageVo> page(UserProductOrderParam userProductOrderParam) {
        return userProductOrderService.findOrderForPage(userProductOrderParam);
    }

    /**
     * 添加理财订单
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    @Permission
    @ResponseBody
    @PostMapping("/userProductOrder/add")
    @BusinessLog(title = "理财订单_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(UserProductOrderParam.add.class) UserProductOrderParam userProductOrderParam) {
        userProductOrderService.add(userProductOrderParam);
        return new SuccessResponseData();
    }

    /**
     * 删除理财订单
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    @Permission
    @ResponseBody
    @PostMapping("/userProductOrder/delete")
    @BusinessLog(title = "理财订单_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(UserProductOrderParam.delete.class) List<UserProductOrderParam> userProductOrderParamList) {
        userProductOrderService.delete(userProductOrderParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑理财订单
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    @Permission
    @ResponseBody
    @PostMapping("/userProductOrder/edit")
    @BusinessLog(title = "理财订单_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(UserProductOrderParam.edit.class) UserProductOrderParam userProductOrderParam) {
        userProductOrderService.edit(userProductOrderParam);
        return new SuccessResponseData();
    }

    /**
     * 查看理财订单
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    @Permission
    @ResponseBody
    @GetMapping("/userProductOrder/detail")
    @BusinessLog(title = "理财订单_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(UserProductOrderParam.detail.class) UserProductOrderParam userProductOrderParam) {
        return new SuccessResponseData(userProductOrderService.detail(userProductOrderParam));
    }

    /**
     * 理财订单列表
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:41
     */
    @Permission
    @ResponseBody
    @GetMapping("/userProductOrder/list")
    @BusinessLog(title = "理财订单_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(UserProductOrderParam userProductOrderParam) {
        return new SuccessResponseData(userProductOrderService.list(userProductOrderParam));
    }

}
