package vip.xiaonuo.admin.modular.timer.tasks;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import vip.xiaonuo.business.userproductorder.entity.UserProductOrder;
import vip.xiaonuo.business.userproductorder.service.UserProductOrderService;
import vip.xiaonuo.core.timer.TimerTaskRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 计算收益
 *
 * <AUTHOR>
 * @date 2020/6/30 22:09
 */
@Slf4j
@Component
public class UserFinancialIncomeTaskRunner implements TimerTaskRunner {
    @Resource
    UserProductOrderService userProductOrderService;

    private Lock lock = new ReentrantLock();

    @Override
    public void action() {
        log.info("计算理财订单收益开始");
        lock.lock();
        try {
            List<UserProductOrder> orderList =  userProductOrderService.findUserFinancialIncome();
            if (ObjectUtils.isEmpty(orderList)) {
                return;
            }
            for (UserProductOrder userProductOrder : orderList) {
                //单个处理，不把所有的捆绑在一起开事务
                try {
                    userProductOrderService.executeUserFinancialIncomeTask(userProductOrder);
                } catch (Exception e) {
                    log.error("计算玩家理财收益异常{}", userProductOrder.getId(), e);
                }
            }
        } catch (Exception e) {
            log.error("计算理财订单收益异常", e);
        } finally {
            lock.unlock();
        }
        log.info("计算理财订单收益结束");
    }


}
