/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.areacode.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.areacode.entity.AreaCode;
import vip.xiaonuo.business.areacode.param.AreaCodeParam;
import vip.xiaonuo.business.areacode.service.AreaCodeService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;

/**
 * 国际区号控制器
 *
 * <AUTHOR>
 * @date 2022-04-07 15:37:38
 */
@Controller
public class AreaCodeController {

    private String PATH_PREFIX = "areaCode/";

    @Resource
    private AreaCodeService areaCodeService;

    /**
     * 国际区号页面
     *
     * <AUTHOR>
     * @date 2022-04-07 15:37:38
     */
    @Permission
    @GetMapping("/areaCode/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 国际区号表单页面
     *
     * <AUTHOR>
     * @date 2022-04-07 15:37:38
     */
    @GetMapping("/areaCode/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询国际区号
     *
     * <AUTHOR>
     * @date 2022-04-07 15:37:38
     */
    @Permission
    @ResponseBody
    @GetMapping("/areaCode/page")
    @BusinessLog(title = "国际区号_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<AreaCode> page(AreaCodeParam areaCodeParam) {
        return areaCodeService.page(areaCodeParam);
    }

    /**
     * 添加国际区号
     *
     * <AUTHOR>
     * @date 2022-04-07 15:37:38
     */
    @Permission
    @ResponseBody
    @PostMapping("/areaCode/add")
    @BusinessLog(title = "国际区号_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(AreaCodeParam.add.class) AreaCodeParam areaCodeParam) {
        areaCodeService.add(areaCodeParam);
        return new SuccessResponseData();
    }

    /**
     * 删除国际区号
     *
     * <AUTHOR>
     * @date 2022-04-07 15:37:38
     */
    @Permission
    @ResponseBody
    @PostMapping("/areaCode/delete")
    @BusinessLog(title = "国际区号_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(AreaCodeParam.delete.class) List<AreaCodeParam> areaCodeParamList) {
        areaCodeService.delete(areaCodeParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑国际区号
     *
     * <AUTHOR>
     * @date 2022-04-07 15:37:38
     */
    @Permission
    @ResponseBody
    @PostMapping("/areaCode/edit")
    @BusinessLog(title = "国际区号_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(AreaCodeParam.edit.class) AreaCodeParam areaCodeParam) {
        areaCodeService.edit(areaCodeParam);
        return new SuccessResponseData();
    }

    /**
     * 查看国际区号
     *
     * <AUTHOR>
     * @date 2022-04-07 15:37:38
     */
    @Permission
    @ResponseBody
    @GetMapping("/areaCode/detail")
    @BusinessLog(title = "国际区号_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(AreaCodeParam.detail.class) AreaCodeParam areaCodeParam) {
        return new SuccessResponseData(areaCodeService.detail(areaCodeParam));
    }

    /**
     * 国际区号列表
     *
     * <AUTHOR>
     * @date 2022-04-07 15:37:38
     */
    @ResponseBody
    @GetMapping("/areaCode/list")
    public ResponseData list(AreaCodeParam areaCodeParam) {
        return new SuccessResponseData(areaCodeService.list(areaCodeParam));
    }

}
