package vip.xiaonuo.admin.modular.timer.tasks;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.core.timer.TimerTaskRunner;
import vip.xiaonuo.session.security.UserSession;
import vip.xiaonuo.session.security.UserSessionService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 定时扫描玩家登录过期,30分钟执行一次
 */
@Component
public class PlayerLoginExpirationTaskRunner implements TimerTaskRunner {
    private Logger logger = LoggerFactory.getLogger(PlayerLoginExpirationTaskRunner.class);
    @Resource
    private PlayerService playerService;
    @Resource
    private UserSessionService userSessionService;

    @Override
    public void action() {
        try {
            List<Player> playerList = playerService.lambdaQuery().eq(Player::getOnlineFlag, 1).list();
            for (Player player : playerList) {
                try {
                    boolean isLogout = userSessionService.checkLogout(player.getId());
                    if (isLogout) {
                        playerService.lambdaUpdate().set(Player::getOnlineFlag, 0).eq(Player::getId, player.getId()).update();
                    }
                } catch (Exception e) {
                    logger.error(player.getId() + "玩家登录过期任务异常", e);
                }
            }
        } catch (Exception e) {
            logger.error("定时扫描玩家登录过期异常", e);
        }

        try {
            List<UserSession> userSessionList = userSessionService.getOnlineUserSessionList();
            for (UserSession userSession : userSessionList) {
                playerService.lambdaUpdate().set(Player::getOnlineFlag, 1).eq(Player::getId, userSession.getId()).update();
            }
        } catch (Exception e) {
            logger.error("定时扫描玩家登录在线异常", e);
        }
    }

}
