package vip.xiaonuo.admin.modular.exchangerate.controller;

import vip.xiaonuo.business.exchangerate.entity.ExchangeRate;
import vip.xiaonuo.business.exchangerate.param.ExchangeRateParam;
import vip.xiaonuo.business.exchangerate.service.ExchangeRateService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.List;

/**
 * 汇率（同步）控制器
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:32
 */
@Controller
public class ExchangeRateController {

    private String PATH_PREFIX = "exchangeRate/";

    @Resource
    private ExchangeRateService exchangeRateService;

    /**
     * 汇率（同步）页面
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    @Permission
    @GetMapping("/exchangeRate/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 汇率（同步）表单页面
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    @GetMapping("/exchangeRate/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询汇率（同步）
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    @Permission
    @ResponseBody
    @GetMapping("/exchangeRate/page")
    @BusinessLog(title = "汇率（同步）_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<ExchangeRate> page(ExchangeRateParam exchangeRateParam) {
        return exchangeRateService.page(exchangeRateParam);
    }

    /**
     * 添加汇率（同步）
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    @Permission
    @ResponseBody
    @PostMapping("/exchangeRate/add")
    @BusinessLog(title = "汇率（同步）_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ExchangeRateParam.add.class) ExchangeRateParam exchangeRateParam) {
        exchangeRateService.add(exchangeRateParam);
        return new SuccessResponseData();
    }



    /**
     * 删除汇率（同步）_手动同步
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    @Permission
    @ResponseBody
    @PostMapping("/exchangeRate/executeExchangeRateTask")
    @BusinessLog(title = "汇率（同步）_手动同步")
    public ResponseData executeExchangeRateTask() {
        exchangeRateService.executeExchangeRateTask();
        return ResponseData.success("数据处理中，请稍后查看");
    }
    /**
     * 删除汇率（同步）
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    @Permission
    @ResponseBody
    @PostMapping("/exchangeRate/delete")
    @BusinessLog(title = "汇率（同步）_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ExchangeRateParam.delete.class) List<ExchangeRateParam> exchangeRateParamList) {
        exchangeRateService.delete(exchangeRateParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑汇率（同步）
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    @Permission
    @ResponseBody
    @PostMapping("/exchangeRate/edit")
    @BusinessLog(title = "汇率（同步）_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ExchangeRateParam.edit.class) ExchangeRateParam exchangeRateParam) {
        exchangeRateService.edit(exchangeRateParam);
        return new SuccessResponseData();
    }

    /**
     * 查看汇率（同步）
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    @Permission
    @ResponseBody
    @GetMapping("/exchangeRate/detail")
    @BusinessLog(title = "汇率（同步）_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(ExchangeRateParam.detail.class) ExchangeRateParam exchangeRateParam) {
        return new SuccessResponseData(exchangeRateService.detail(exchangeRateParam));
    }

    /**
     * 汇率（同步）列表
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:32
     */
    @Permission
    @ResponseBody
    @GetMapping("/exchangeRate/list")
    @BusinessLog(title = "汇率（同步）_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(ExchangeRateParam exchangeRateParam) {
        return new SuccessResponseData(exchangeRateService.list(exchangeRateParam));
    }

}
