/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.follower.controller;

import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.business.follower.entity.Follower;
import vip.xiaonuo.business.follower.param.FollowerParam;
import vip.xiaonuo.business.follower.service.FollowerService;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.List;

/**
 * 交易员控制器
 *
 * <AUTHOR>
 * @date 2025-07-20 13:22:48
 */
@Controller
public class FollowerController {

    private String PATH_PREFIX = "follower/";

    @Resource
    private FollowerService followerService;

    /**
     * 交易员页面
     *
     * <AUTHOR>
     * @date 2025-07-20 13:22:48
     */
    @Permission
    @GetMapping("/follower/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 交易员表单页面
     *
     * <AUTHOR>
     * @date 2025-07-20 13:22:48
     */
    @GetMapping("/follower/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询交易员
     *
     * <AUTHOR>
     * @date 2025-07-20 13:22:48
     */
    @Permission
    @ResponseBody
    @GetMapping("/follower/page")
    @BusinessLog(title = "交易员_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<Follower> page(FollowerParam followerParam) {
        return followerService.page(followerParam);
    }

    /**
     * 添加交易员
     *
     * <AUTHOR>
     * @date 2025-07-20 13:22:48
     */
    @Permission
    @ResponseBody
    @PostMapping("/follower/add")
    @BusinessLog(title = "交易员_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(FollowerParam.add.class) FollowerParam followerParam) {
        followerService.add(followerParam);
        return new SuccessResponseData();
    }

    /**
     * 删除交易员
     *
     * <AUTHOR>
     * @date 2025-07-20 13:22:48
     */
    @Permission
    @ResponseBody
    @PostMapping("/follower/delete")
    @BusinessLog(title = "交易员_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(FollowerParam.delete.class) List<FollowerParam> followerParamList) {
        followerService.delete(followerParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑交易员
     *
     * <AUTHOR>
     * @date 2025-07-20 13:22:48
     */
    @Permission
    @ResponseBody
    @PostMapping("/follower/edit")
    @BusinessLog(title = "交易员_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(FollowerParam.edit.class) FollowerParam followerParam) {
        followerService.edit(followerParam);
        return new SuccessResponseData();
    }

    /**
     * 查看交易员
     *
     * <AUTHOR>
     * @date 2025-07-20 13:22:48
     */
    @Permission
    @ResponseBody
    @GetMapping("/follower/detail")
    @BusinessLog(title = "交易员_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(FollowerParam.detail.class) FollowerParam followerParam) {
        return new SuccessResponseData(followerService.detail(followerParam));
    }

    /**
     * 交易员列表
     *
     * <AUTHOR>
     * @date 2025-07-20 13:22:48
     */
    @Permission
    @ResponseBody
    @GetMapping("/follower/list")
    @BusinessLog(title = "交易员_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(FollowerParam followerParam) {
        return new SuccessResponseData(followerService.list(followerParam));
    }

}
