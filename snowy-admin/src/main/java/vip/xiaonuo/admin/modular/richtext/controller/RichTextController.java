/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.richtext.controller;

import vip.xiaonuo.business.richtext.entity.RichText;
import vip.xiaonuo.business.richtext.param.RichTextParam;
import vip.xiaonuo.business.richtext.service.RichTextService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.List;

/**
 * 富文本信息控制器
 *
 * <AUTHOR>
 * @date 2024-12-23 10:45:38
 */
@Controller
public class RichTextController {

    private String PATH_PREFIX = "richText/";

    @Resource
    private RichTextService richTextService;

    /**
     * 富文本信息页面
     *
     * <AUTHOR>
     * @date 2024-12-23 10:45:38
     */
    @Permission
    @GetMapping("/richText/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 富文本信息表单页面
     *
     * <AUTHOR>
     * @date 2024-12-23 10:45:38
     */
    @GetMapping("/richText/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询富文本信息
     *
     * <AUTHOR>
     * @date 2024-12-23 10:45:38
     */
    @Permission
    @ResponseBody
    @GetMapping("/richText/page")
    @BusinessLog(title = "富文本信息_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<RichText> page(RichTextParam richTextParam) {
        return richTextService.page(richTextParam);
    }

    /**
     * 添加富文本信息
     *
     * <AUTHOR>
     * @date 2024-12-23 10:45:38
     */
    @Permission
    @ResponseBody
    @PostMapping("/richText/add")
    @BusinessLog(title = "富文本信息_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(RichTextParam.add.class) RichTextParam richTextParam) {
        richTextService.add(richTextParam);
        return new SuccessResponseData();
    }

    /**
     * 删除富文本信息
     *
     * <AUTHOR>
     * @date 2024-12-23 10:45:38
     */
    @Permission
    @ResponseBody
    @PostMapping("/richText/delete")
    @BusinessLog(title = "富文本信息_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(RichTextParam.delete.class) List<RichTextParam> richTextParamList) {
        richTextService.delete(richTextParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑富文本信息
     *
     * <AUTHOR>
     * @date 2024-12-23 10:45:38
     */
    @Permission
    @ResponseBody
    @PostMapping("/richText/edit")
    @BusinessLog(title = "富文本信息_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(RichTextParam.edit.class) RichTextParam richTextParam) {
        richTextService.edit(richTextParam);
        return new SuccessResponseData();
    }

    /**
     * 查看富文本信息
     *
     * <AUTHOR>
     * @date 2024-12-23 10:45:38
     */
    @Permission
    @ResponseBody
    @GetMapping("/richText/detail")
    @BusinessLog(title = "富文本信息_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(RichTextParam.detail.class) RichTextParam richTextParam) {
        return new SuccessResponseData(richTextService.detail(richTextParam));
    }

    /**
     * 富文本信息列表
     *
     * <AUTHOR>
     * @date 2024-12-23 10:45:38
     */
    @Permission
    @ResponseBody
    @GetMapping("/richText/list")
    @BusinessLog(title = "富文本信息_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(RichTextParam richTextParam) {
        return new SuccessResponseData(richTextService.list(richTextParam));
    }

}
