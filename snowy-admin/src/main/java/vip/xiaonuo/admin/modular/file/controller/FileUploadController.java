package vip.xiaonuo.admin.modular.file.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.core.util.FileUploadUtils;

@RestController
@RequestMapping("/file")
@Api(tags = "文件")
public class FileUploadController {

    @ApiOperation("文件上传")
    @PostMapping("/upload")
    public ResponseData upload(@RequestPart("file") MultipartFile file) {
        return new SuccessResponseData(FileUploadUtils.uploadS3(file));
    }
}
