package vip.xiaonuo.admin.modular.product.controller;

import vip.xiaonuo.business.context.login.AdminLoginContext;
import vip.xiaonuo.business.product.entity.Product;
import vip.xiaonuo.business.product.param.ProductParam;
import vip.xiaonuo.business.product.service.ProductService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.context.login.LoginContextHolder;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 理财产品控制器
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:39
 */
@Controller
public class ProductController {

    private String PATH_PREFIX = "product/";

    @Resource
    private ProductService productService;

    /**
     * 理财产品页面
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    @Permission
    @GetMapping("/product/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 理财产品表单页面
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    @GetMapping("/product/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询理财产品
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    @Permission
    @ResponseBody
    @GetMapping("/product/page")
    @BusinessLog(title = "理财产品_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<Product> page(ProductParam productParam) {
        return productService.page(productParam);
    }

    /**
     * 添加理财产品
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    @Permission
    @ResponseBody
    @PostMapping("/product/add")
    @BusinessLog(title = "理财产品_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ProductParam.add.class) ProductParam productParam) {
        productParam.setCreateId(LoginContextHolder.me(AdminLoginContext.class).getLoginUserId());
        productParam.setUpdateId(LoginContextHolder.me(AdminLoginContext.class).getLoginUserId());
        productParam.setOrgCode(LoginContextHolder.me(AdminLoginContext.class).getLoginUserOrgCode());
        productService.add(productParam);
        return new SuccessResponseData();
    }

    /**
     * 删除理财产品
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    @Permission
    @ResponseBody
    @PostMapping("/product/delete")
    @BusinessLog(title = "理财产品_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ProductParam.delete.class) List<ProductParam> productParamList) {
        productService.delete(productParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑理财产品
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    @Permission
    @ResponseBody
    @PostMapping("/product/edit")
    @BusinessLog(title = "理财产品_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ProductParam.edit.class) ProductParam productParam) {
        productParam.setUpdateId(LoginContextHolder.me(AdminLoginContext.class).getLoginUserId());
        productService.edit(productParam);
        return new SuccessResponseData();
    }

    /**
     * 查看理财产品
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    @Permission
    @ResponseBody
    @GetMapping("/product/detail")
    @BusinessLog(title = "理财产品_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(ProductParam.detail.class) ProductParam productParam) {
        return new SuccessResponseData(productService.detail(productParam));
    }

    /**
     * 理财产品列表
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:39
     */
    @Permission
    @ResponseBody
    @GetMapping("/product/list")
    @BusinessLog(title = "理财产品_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(ProductParam productParam) {
        return new SuccessResponseData(productService.list(productParam));
    }

}
