/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.notice.controller;

import vip.xiaonuo.business.notice.entity.PlayerRotation;
import vip.xiaonuo.business.notice.param.PlayerRotationParam;
import vip.xiaonuo.business.notice.service.PlayerRotationService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.List;

/**
 * 轮播图控制器
 *
 * <AUTHOR>
 * @date 2022-04-07 21:51:43
 */
@Controller
public class PlayerRotationController {

    private String PATH_PREFIX = "playerRotation/";

    @Resource
    private PlayerRotationService playerRotationService;

    /**
     * 轮播图页面
     *
     * <AUTHOR>
     * @date 2022-04-07 21:51:43
     */
    @Permission
    @GetMapping("/playerRotation/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 轮播图表单页面
     *
     * <AUTHOR>
     * @date 2022-04-07 21:51:43
     */
    @GetMapping("/playerRotation/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询轮播图
     *
     * <AUTHOR>
     * @date 2022-04-07 21:51:43
     */
    @Permission
    @ResponseBody
    @GetMapping("/playerRotation/page")
    @BusinessLog(title = "轮播图_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<PlayerRotation> page(PlayerRotationParam playerRotationParam) {
        return playerRotationService.page(playerRotationParam);
    }

    /**
     * 添加轮播图
     *
     * <AUTHOR>
     * @date 2022-04-07 21:51:43
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerRotation/add")
    @BusinessLog(title = "轮播图_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(PlayerRotationParam.add.class) PlayerRotationParam playerRotationParam) {
        playerRotationService.add(playerRotationParam);
        return new SuccessResponseData();
    }

    /**
     * 删除轮播图
     *
     * <AUTHOR>
     * @date 2022-04-07 21:51:43
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerRotation/delete")
    @BusinessLog(title = "轮播图_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(PlayerRotationParam.delete.class) List<PlayerRotationParam> playerRotationParamList) {
        playerRotationService.delete(playerRotationParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑轮播图
     *
     * <AUTHOR>
     * @date 2022-04-07 21:51:43
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerRotation/edit")
    @BusinessLog(title = "轮播图_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(PlayerRotationParam.edit.class) PlayerRotationParam playerRotationParam) {
        playerRotationService.edit(playerRotationParam);
        return new SuccessResponseData();
    }

    /**
     * 查看轮播图
     *
     * <AUTHOR>
     * @date 2022-04-07 21:51:43
     */
    @Permission
    @ResponseBody
    @GetMapping("/playerRotation/detail")
    @BusinessLog(title = "轮播图_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(PlayerRotationParam.detail.class) PlayerRotationParam playerRotationParam) {
        return new SuccessResponseData(playerRotationService.detail(playerRotationParam));
    }

    /**
     * 轮播图列表
     *
     * <AUTHOR>
     * @date 2022-04-07 21:51:43
     */
    @Permission
    @ResponseBody
    @GetMapping("/playerRotation/list")
    @BusinessLog(title = "轮播图_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(PlayerRotationParam playerRotationParam) {
        return new SuccessResponseData(playerRotationService.list(playerRotationParam));
    }

    /**
     * 轮播图修改状态
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerRotation/changeStatus")
    @BusinessLog(title = "轮播图_修改状态", opType = LogAnnotionOpTypeEnum.CHANGE_STATUS)
    public ResponseData changeStatus(@RequestBody @Validated(PlayerRotationParam.changeStatus.class) PlayerRotationParam playerRotationParam) {
        playerRotationService.changeStatus(playerRotationParam);
        return new SuccessResponseData();
    }

}
