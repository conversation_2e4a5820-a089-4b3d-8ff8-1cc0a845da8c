/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.ipblacklist.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.ipblacklist.entity.IpBlacklist;
import vip.xiaonuo.business.ipblacklist.param.IpBlacklistParam;
import vip.xiaonuo.business.ipblacklist.service.IpBlacklistService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ErrorResponseData;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;

/**
 * IP黑名单控制器
 *
 * <AUTHOR>
 * @date 2024-03-27 18:52:06
 */
@Controller
public class IpBlacklistController {

    private String PATH_PREFIX = "ipBlacklist/";

    @Resource
    private IpBlacklistService ipBlacklistService;

    /**
     * IP黑名单页面
     *
     * <AUTHOR>
     * @date 2024-03-27 18:52:06
     */
    @Permission
    @GetMapping("/ipBlacklist/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * IP黑名单表单页面
     *
     * <AUTHOR>
     * @date 2024-03-27 18:52:06
     */
    @GetMapping("/ipBlacklist/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询IP黑名单
     *
     * <AUTHOR>
     * @date 2024-03-27 18:52:06
     */
    @Permission
    @ResponseBody
    @GetMapping("/ipBlacklist/page")
    @BusinessLog(title = "IP黑名单_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<IpBlacklist> page(IpBlacklistParam ipBlacklistParam) {
        return ipBlacklistService.page(ipBlacklistParam);
    }

    /**
     * 添加IP黑名单
     *
     * <AUTHOR>
     * @date 2024-03-27 18:52:06
     */
    @Permission
    @ResponseBody
    @PostMapping("/ipBlacklist/add")
    @BusinessLog(title = "IP黑名单_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(IpBlacklistParam.add.class) IpBlacklistParam ipBlacklistParam) {
        if (ipBlacklistService.lambdaQuery().eq(IpBlacklist::getIp, ipBlacklistParam.getIp()).count() >= 1) {
            return new ErrorResponseData(ErrorResponseData.DEFAULT_ERROR_CODE, "IP已存在");
        }
        ipBlacklistService.add(ipBlacklistParam);
        return new SuccessResponseData();
    }

    /**
     * 删除IP黑名单
     *
     * <AUTHOR>
     * @date 2024-03-27 18:52:06
     */
    @Permission
    @ResponseBody
    @PostMapping("/ipBlacklist/delete")
    @BusinessLog(title = "IP黑名单_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(IpBlacklistParam.delete.class) List<IpBlacklistParam> ipBlacklistParamList) {
        ipBlacklistService.delete(ipBlacklistParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑IP黑名单
     *
     * <AUTHOR>
     * @date 2024-03-27 18:52:06
     */
    @Permission
    @ResponseBody
    @PostMapping("/ipBlacklist/edit")
    @BusinessLog(title = "IP黑名单_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(IpBlacklistParam.edit.class) IpBlacklistParam ipBlacklistParam) {
        if (ipBlacklistService.lambdaQuery().eq(IpBlacklist::getIp, ipBlacklistParam.getIp()).ne(IpBlacklist::getId, ipBlacklistParam.getId()).count() >= 1) {
            return new ErrorResponseData(ErrorResponseData.DEFAULT_ERROR_CODE, "IP已存在");
        }
        ipBlacklistService.edit(ipBlacklistParam);
        return new SuccessResponseData();
    }

    /**
     * 查看IP黑名单
     *
     * <AUTHOR>
     * @date 2024-03-27 18:52:06
     */
    @Permission
    @ResponseBody
    @GetMapping("/ipBlacklist/detail")
    @BusinessLog(title = "IP黑名单_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(IpBlacklistParam.detail.class) IpBlacklistParam ipBlacklistParam) {
        return new SuccessResponseData(ipBlacklistService.detail(ipBlacklistParam));
    }

    /**
     * IP黑名单列表
     *
     * <AUTHOR>
     * @date 2024-03-27 18:52:06
     */
    @Permission
    @ResponseBody
    @GetMapping("/ipBlacklist/list")
    @BusinessLog(title = "IP黑名单_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(IpBlacklistParam ipBlacklistParam) {
        return new SuccessResponseData(ipBlacklistService.list(ipBlacklistParam));
    }

}
