/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.contractorder.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.business.contractorder.entity.ContractOrder;
import vip.xiaonuo.business.contractorder.param.ContractOrderParam;
import vip.xiaonuo.business.contractorder.service.ContractOrderService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.DataScope;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;

/**
 * 合约订单控制器
 *
 * <AUTHOR>
 * @date 2022-04-24 13:36:59
 */
@Controller
public class ContractOrderController {

    private String PATH_PREFIX = "contractOrder/";

    @Resource
    private ContractOrderService contractOrderService;

    /**
     * 合约订单页面
     *
     * <AUTHOR>
     * @date 2022-04-24 13:36:59
     */
    @Permission
    @GetMapping("/contractOrder/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 合约订单表单页面
     *
     * <AUTHOR>
     * @date 2022-04-24 13:36:59
     */
    @GetMapping("/contractOrder/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询合约订单
     *
     * <AUTHOR>
     * @date 2022-04-24 13:36:59
     */
    @Permission
    @ResponseBody
    @GetMapping("/contractOrder/page")
    @BusinessLog(title = "合约订单_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    @DataScope
    public PageResult<ContractOrder> page(ContractOrderParam contractOrderParam) {
        return contractOrderService.page(contractOrderParam);
    }

    /**
     * 添加合约订单
     *
     * <AUTHOR>
     * @date 2022-04-24 13:36:59
     */
    @Permission
    @ResponseBody
    @PostMapping("/contractOrder/add")
    @BusinessLog(title = "合约订单_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ContractOrderParam.add.class) ContractOrderParam contractOrderParam) {
        contractOrderService.add(contractOrderParam);
        return new SuccessResponseData();
    }

    @Permission
    @ResponseBody
    @PostMapping("/contractOrder/revoke")
    @BusinessLog(title = "合约订单_撤单")
    public ResponseData revoke(@RequestParam String orderNo, @RequestParam Long playerId) {
        contractOrderService.revokeEntrust(playerId, orderNo, CommonConstant.SYS_ID);
        return new SuccessResponseData();
    }

    /**
     * 删除合约订单
     *
     * <AUTHOR>
     * @date 2022-04-24 13:36:59
     */
    @Permission
    @ResponseBody
    @PostMapping("/contractOrder/delete")
    @BusinessLog(title = "合约订单_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ContractOrderParam.delete.class) List<ContractOrderParam> contractOrderParamList) {
        contractOrderService.delete(contractOrderParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑合约订单
     *
     * <AUTHOR>
     * @date 2022-04-24 13:36:59
     */
    @Permission
    @ResponseBody
    @PostMapping("/contractOrder/edit")
    @BusinessLog(title = "合约订单_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ContractOrderParam.edit.class) ContractOrderParam contractOrderParam) {
        contractOrderService.edit(contractOrderParam);
        return new SuccessResponseData();
    }

    /**
     * 查看合约订单
     *
     * <AUTHOR>
     * @date 2022-04-24 13:36:59
     */
    @Permission
    @ResponseBody
    @GetMapping("/contractOrder/detail")
    @BusinessLog(title = "合约订单_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(ContractOrderParam.detail.class) ContractOrderParam contractOrderParam) {
        return new SuccessResponseData(contractOrderService.detail(contractOrderParam));
    }

    /**
     * 合约订单列表
     *
     * <AUTHOR>
     * @date 2022-04-24 13:36:59
     */
    @Permission
    @ResponseBody
    @GetMapping("/contractOrder/list")
    @BusinessLog(title = "合约订单_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(ContractOrderParam contractOrderParam) {
        return new SuccessResponseData(contractOrderService.list(contractOrderParam));
    }

}
