/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.role.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.context.login.AdminLoginContext;
import vip.xiaonuo.business.pojo.sysuser.SysLoginUser;
import vip.xiaonuo.business.role.entity.SysRole;
import vip.xiaonuo.business.role.param.SysRoleParam;
import vip.xiaonuo.business.role.service.SysRoleService;
import vip.xiaonuo.business.user.entity.SysUser;
import vip.xiaonuo.business.user.service.SysUserService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.DataScope;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.context.login.LoginContextHolder;
import vip.xiaonuo.core.enums.AdminRoleTypeEnum;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;


import javax.annotation.Resource;
import java.util.List;

/**
 * 系统角色控制器
 *
 * <AUTHOR>
 * @date 2020/3/20 19:42
 */
@Controller
public class SysRoleController {

    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private SysUserService sysUserService;
    /**
     * 系统角色页面
     *
     * <AUTHOR>
     * @date 2020/11/17 16:40
     */
    @Permission
    @GetMapping("/sysRole/index")
    public String index() {
        return "system/sysRole/index.html";
    }

    /**
     * 系统角色表单页面
     *
     * <AUTHOR>
     * @date 2020/11/17 16:40
     */
    @GetMapping("/sysRole/form")
    public String form() {
        return "system/sysRole/form.html";
    }

    /**
     * 系统角色授权菜单页面
     *
     * <AUTHOR>
     * @date 2020/11/17 16:40
     */
    @GetMapping("/sysRole/grantMenu")
    public String grantMenu() {
        return "system/sysRole/grantMenu.html";
    }

    /**
     * 系统角色授权数据页面
     *
     * <AUTHOR>
     * @date 2020/11/17 16:40
     */
    @GetMapping("/sysRole/grantData")
    public String grantData() {
        return "system/sysRole/grantData.html";
    }

    /**
     * 查询系统角色
     *
     * <AUTHOR>
     * @date 2020/3/28 14:45
     */
    @Permission
    @ResponseBody
    @GetMapping("/sysRole/page")
    @BusinessLog(title = "系统角色_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<SysRole> page(SysRoleParam sysRoleParam) {
        return sysRoleService.page(sysRoleParam);
    }

    /**
     * 系统角色下拉（用于授权角色时选择）
     *
     * <AUTHOR>
     * @date 2020/4/5 16:45
     */
    @Permission
    @ResponseBody
    @GetMapping("/sysRole/dropDown")
    @BusinessLog(title = "系统角色_下拉", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData dropDown() {
        SysUser loginSysUser = sysUserService.getUserById(LoginContextHolder.me(AdminLoginContext.class).getLoginUserId());
        return new SuccessResponseData(sysRoleService.dropDown(loginSysUser));
    }

    /**
     * 添加系统角色
     *
     * <AUTHOR>
     * @date 2020/3/28 14:45
     */
    @Permission
    @ResponseBody
    @PostMapping("/sysRole/add")
    @BusinessLog(title = "系统角色_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(SysRoleParam.add.class) SysRoleParam sysRoleParam) {
        sysRoleService.add(sysRoleParam);
        return new SuccessResponseData();
    }

    /**
     * 删除系统角色
     *
     * <AUTHOR>
     * @date 2020/3/28 14:45
     */
    @Permission
    @ResponseBody
    @PostMapping("/sysRole/delete")
    @BusinessLog(title = "系统角色_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(SysRoleParam.delete.class) List<SysRoleParam> sysRoleParamList) {
        sysRoleService.delete(sysRoleParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑系统角色
     *
     * <AUTHOR>
     * @date 2020/3/28 14:46
     */
    @Permission
    @ResponseBody
    @PostMapping("/sysRole/edit")
    @BusinessLog(title = "系统角色_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(SysRoleParam.edit.class) SysRoleParam sysRoleParam) {
        sysRoleService.edit(sysRoleParam);
        return new SuccessResponseData();
    }

    /**
     * 查看系统角色
     *
     * <AUTHOR>
     * @date 2020/3/28 14:46
     */
    @Permission
    @ResponseBody
    @GetMapping("/sysRole/detail")
    @BusinessLog(title = "系统角色_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(SysRoleParam.detail.class) SysRoleParam sysRoleParam) {
        return new SuccessResponseData(sysRoleService.detail(sysRoleParam));
    }

    /**
     * 授权菜单
     *
     * <AUTHOR>
     * @date 2020/3/28 16:05
     */
    @Permission
    @ResponseBody
    @PostMapping("/sysRole/grantMenu")
    @BusinessLog(title = "系统角色_授权菜单", opType = LogAnnotionOpTypeEnum.GRANT)
    public ResponseData grantMenu(@RequestBody @Validated(SysRoleParam.grantMenu.class) SysRoleParam sysRoleParam) {
        sysRoleService.grantMenu(sysRoleParam);
        return new SuccessResponseData();
    }

    /**
     * 授权数据
     *
     * <AUTHOR>
     * @date 2020/3/28 16:05
     */
    @Permission
    @DataScope
    @ResponseBody
    @PostMapping("/sysRole/grantData")
    @BusinessLog(title = "系统角色_授权数据", opType = LogAnnotionOpTypeEnum.GRANT)
    public ResponseData grantData(@RequestBody @Validated(SysRoleParam.grantData.class) SysRoleParam sysRoleParam) {
        sysRoleService.grantData(sysRoleParam);
        return new SuccessResponseData();
    }

    /**
     * 拥有菜单
     *
     * <AUTHOR>
     * @date 2020/3/28 14:46
     */
    @Permission
    @ResponseBody
    @GetMapping("/sysRole/ownMenu")
    @BusinessLog(title = "系统角色_拥有菜单", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData ownMenu(@Validated(SysRoleParam.detail.class) SysRoleParam sysRoleParam) {
        return new SuccessResponseData(sysRoleService.ownMenu(sysRoleParam));
    }

    /**
     * 拥有数据
     *
     * <AUTHOR>
     * @date 2020/3/28 14:46
     */
    @Permission
    @ResponseBody
    @GetMapping("/sysRole/ownData")
    @BusinessLog(title = "系统角色_拥有数据", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData ownData(@Validated(SysRoleParam.detail.class) SysRoleParam sysRoleParam) {
        return new SuccessResponseData(sysRoleService.ownData(sysRoleParam));
    }

}
