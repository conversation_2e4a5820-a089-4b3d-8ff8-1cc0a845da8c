/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.depositbankaccount.controller;

import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.business.depositbankaccount.entity.DepositBankAccount;
import vip.xiaonuo.business.depositbankaccount.param.DepositBankAccountParam;
import vip.xiaonuo.business.depositbankaccount.service.DepositBankAccountService;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.List;

/**
 * 存款银行账号控制器
 *
 * <AUTHOR>
 * @date 2022-10-24 15:49:10
 */
@Controller
public class DepositBankAccountController {

    private String PATH_PREFIX = "depositBankAccount/";

    @Resource
    private DepositBankAccountService depositBankAccountService;

    /**
     * 存款银行账号页面
     *
     * <AUTHOR>
     * @date 2022-10-24 15:49:10
     */
    @Permission
    @GetMapping("/depositBankAccount/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 存款银行账号表单页面
     *
     * <AUTHOR>
     * @date 2022-10-24 15:49:10
     */
    @GetMapping("/depositBankAccount/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询存款银行账号
     *
     * <AUTHOR>
     * @date 2022-10-24 15:49:10
     */
    @Permission
    @ResponseBody
    @GetMapping("/depositBankAccount/page")
    @BusinessLog(title = "存款银行账号_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<DepositBankAccount> page(DepositBankAccountParam depositBankAccountParam) {
        return depositBankAccountService.page(depositBankAccountParam);
    }

    /**
     * 添加存款银行账号
     *
     * <AUTHOR>
     * @date 2022-10-24 15:49:10
     */
    @Permission
    @ResponseBody
    @PostMapping("/depositBankAccount/add")
    @BusinessLog(title = "存款银行账号_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(DepositBankAccountParam.add.class) DepositBankAccountParam depositBankAccountParam) {
        depositBankAccountService.add(depositBankAccountParam);
        return new SuccessResponseData();
    }

    /**
     * 删除存款银行账号
     *
     * <AUTHOR>
     * @date 2022-10-24 15:49:10
     */
    @Permission
    @ResponseBody
    @PostMapping("/depositBankAccount/delete")
    @BusinessLog(title = "存款银行账号_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(DepositBankAccountParam.delete.class) List<DepositBankAccountParam> depositBankAccountParamList) {
        depositBankAccountService.delete(depositBankAccountParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑存款银行账号
     *
     * <AUTHOR>
     * @date 2022-10-24 15:49:10
     */
    @Permission
    @ResponseBody
    @PostMapping("/depositBankAccount/edit")
    @BusinessLog(title = "存款银行账号_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(DepositBankAccountParam.edit.class) DepositBankAccountParam depositBankAccountParam) {
        depositBankAccountService.edit(depositBankAccountParam);
        return new SuccessResponseData();
    }

    /**
     * 查看存款银行账号
     *
     * <AUTHOR>
     * @date 2022-10-24 15:49:10
     */
    @Permission
    @ResponseBody
    @GetMapping("/depositBankAccount/detail")
    @BusinessLog(title = "存款银行账号_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(DepositBankAccountParam.detail.class) DepositBankAccountParam depositBankAccountParam) {
        return new SuccessResponseData(depositBankAccountService.detail(depositBankAccountParam));
    }

    /**
     * 存款银行账号列表
     *
     * <AUTHOR>
     * @date 2022-10-24 15:49:10
     */
    @Permission
    @ResponseBody
    @GetMapping("/depositBankAccount/list")
    @BusinessLog(title = "存款银行账号_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(DepositBankAccountParam depositBankAccountParam) {
        return new SuccessResponseData(depositBankAccountService.list(depositBankAccountParam));
    }

}
