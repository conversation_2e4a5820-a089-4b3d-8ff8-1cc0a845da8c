package vip.xiaonuo.admin.modular.timer.tasks;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vip.xiaonuo.business.exchangerate.service.ExchangeRateService;
import vip.xiaonuo.business.userproductorder.service.UserProductOrderService;
import vip.xiaonuo.core.timer.TimerTaskRunner;

import javax.annotation.Resource;

/**
 * 同步费率
 *
 * <AUTHOR>
 * @date 2020/6/30 22:09
 */
@Slf4j
@Component
public class ExchangeRateTaskRunner implements TimerTaskRunner {
    @Resource
    ExchangeRateService exchangeRateService;

    @Override
    public void action() {
        log.info("同步费率开始");
        exchangeRateService.executeExchangeRateTask();
        log.info("同步费率结束");
    }

}
