/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.followercontractspotorder.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.follower.entity.Follower;
import vip.xiaonuo.business.follower.service.FollowerService;
import vip.xiaonuo.business.followercontractspotorder.entity.FollowerContractSpotOrder;
import vip.xiaonuo.business.followercontractspotorder.param.FollowerContractSpotOrderParam;
import vip.xiaonuo.business.followercontractspotorder.service.FollowerContractSpotOrderService;
import vip.xiaonuo.business.spotcontractrule.entity.SpotContractRule;
import vip.xiaonuo.business.spotcontractrule.service.SpotContractRuleService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ErrorResponseData;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;

/**
 * 跟单合约极速订单预设控制器
 *
 * <AUTHOR>
 * @date 2025-07-22 14:38:50
 */
@Controller
public class FollowerContractSpotOrderController {

    private String PATH_PREFIX = "followerContractSpotOrder/";

    @Resource
    private FollowerContractSpotOrderService followerContractSpotOrderService;

    @Resource
    private SpotContractRuleService spotContractRuleService;

    @Resource
    private FollowerService followerService;

    /**
     * 跟单合约极速订单预设页面
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    @Permission
    @GetMapping("/followerContractSpotOrder/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 跟单合约极速订单预设表单页面
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    @GetMapping("/followerContractSpotOrder/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询跟单合约极速订单预设
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    @Permission
    @ResponseBody
    @GetMapping("/followerContractSpotOrder/page")
    @BusinessLog(title = "跟单合约极速订单预设_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<FollowerContractSpotOrder> page(FollowerContractSpotOrderParam followerContractSpotOrderParam) {
        return followerContractSpotOrderService.page(followerContractSpotOrderParam);
    }

    /**
     * 添加跟单合约极速订单预设
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    @Permission
    @ResponseBody
    @PostMapping("/followerContractSpotOrder/add")
    @BusinessLog(title = "跟单合约极速订单预设_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(FollowerContractSpotOrderParam.add.class) FollowerContractSpotOrderParam followerContractSpotOrderParam) {
        SpotContractRule spotContractRule = spotContractRuleService.getOne(Wrappers.lambdaQuery(SpotContractRule.class).eq(SpotContractRule::getId, followerContractSpotOrderParam.getRuleId()));
        Follower follower = followerService.getOne(Wrappers.lambdaQuery(Follower.class).eq(Follower::getId, followerContractSpotOrderParam.getFollowerId()));

        if (spotContractRule == null) {
            return new ErrorResponseData(ErrorResponseData.DEFAULT_ERROR_CODE, "下单规则不存在");
        }

        if (follower == null) {
            return new ErrorResponseData(ErrorResponseData.DEFAULT_ERROR_CODE, "交易员不存在");
        }

        followerContractSpotOrderParam.setSeconds(spotContractRule.getSeconds());
        followerContractSpotOrderService.add(followerContractSpotOrderParam);
        return new SuccessResponseData();
    }

    /**
     * 删除跟单合约极速订单预设
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    @Permission
    @ResponseBody
    @PostMapping("/followerContractSpotOrder/delete")
    @BusinessLog(title = "跟单合约极速订单预设_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(FollowerContractSpotOrderParam.delete.class) List<FollowerContractSpotOrderParam> followerContractSpotOrderParamList) {
        followerContractSpotOrderService.delete(followerContractSpotOrderParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑跟单合约极速订单预设
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    @Permission
    @ResponseBody
    @PostMapping("/followerContractSpotOrder/edit")
    @BusinessLog(title = "跟单合约极速订单预设_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(FollowerContractSpotOrderParam.edit.class) FollowerContractSpotOrderParam followerContractSpotOrderParam) {
        SpotContractRule spotContractRule = spotContractRuleService.getOne(Wrappers.lambdaQuery(SpotContractRule.class).eq(SpotContractRule::getId, followerContractSpotOrderParam.getRuleId()));
        Follower follower = followerService.getOne(Wrappers.lambdaQuery(Follower.class).eq(Follower::getId, followerContractSpotOrderParam.getFollowerId()));

        if (spotContractRule == null) {
            return new ErrorResponseData(ErrorResponseData.DEFAULT_ERROR_CODE, "下单规则不存在");
        }

        if (follower == null) {
            return new ErrorResponseData(ErrorResponseData.DEFAULT_ERROR_CODE, "交易员不存在");
        }

        followerContractSpotOrderParam.setSeconds(spotContractRule.getSeconds());
        followerContractSpotOrderService.edit(followerContractSpotOrderParam);
        return new SuccessResponseData();
    }

    /**
     * 查看跟单合约极速订单预设
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    @Permission
    @ResponseBody
    @GetMapping("/followerContractSpotOrder/detail")
    @BusinessLog(title = "跟单合约极速订单预设_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(FollowerContractSpotOrderParam.detail.class) FollowerContractSpotOrderParam followerContractSpotOrderParam) {
        return new SuccessResponseData(followerContractSpotOrderService.detail(followerContractSpotOrderParam));
    }

    /**
     * 跟单合约极速订单预设列表
     *
     * <AUTHOR>
     * @date 2025-07-22 14:38:50
     */
    @Permission
    @ResponseBody
    @GetMapping("/followerContractSpotOrder/list")
    @BusinessLog(title = "跟单合约极速订单预设_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(FollowerContractSpotOrderParam followerContractSpotOrderParam) {
        return new SuccessResponseData(followerContractSpotOrderService.list(followerContractSpotOrderParam));
    }

}
