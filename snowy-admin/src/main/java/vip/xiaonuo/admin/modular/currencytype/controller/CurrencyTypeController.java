/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.currencytype.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.currencytype.entity.CurrencyType;
import vip.xiaonuo.business.currencytype.param.CurrencyTypeParam;
import vip.xiaonuo.business.currencytype.service.CurrencyTypeService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;

/**
 * 充币类型控制器
 *
 * <AUTHOR>
 * @date 2022-04-25 15:48:06
 */
@Controller
public class CurrencyTypeController {

    private String PATH_PREFIX = "currencyType/";

    @Resource
    private CurrencyTypeService currencyTypeService;

    /**
     * 充币类型页面
     *
     * <AUTHOR>
     * @date 2022-04-25 15:48:06
     */
    @Permission
    @GetMapping("/currencyType/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 充币类型表单页面
     *
     * <AUTHOR>
     * @date 2022-04-25 15:48:06
     */
    @GetMapping("/currencyType/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询充币类型
     *
     * <AUTHOR>
     * @date 2022-04-25 15:48:06
     */
    @Permission
    @ResponseBody
    @GetMapping("/currencyType/page")
    @BusinessLog(title = "充币类型_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<CurrencyType> page(CurrencyTypeParam currencyTypeParam) {
        return currencyTypeService.page(currencyTypeParam);
    }

    /**
     * 添加充币类型
     *
     * <AUTHOR>
     * @date 2022-04-25 15:48:06
     */
    @Permission
    @ResponseBody
    @PostMapping("/currencyType/add")
    @BusinessLog(title = "充币类型_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(CurrencyTypeParam.add.class) CurrencyTypeParam currencyTypeParam) {
        currencyTypeService.add(currencyTypeParam);
        return new SuccessResponseData();
    }

    /**
     * 删除充币类型
     *
     * <AUTHOR>
     * @date 2022-04-25 15:48:06
     */
    @Permission
    @ResponseBody
    @PostMapping("/currencyType/delete")
    @BusinessLog(title = "充币类型_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(CurrencyTypeParam.delete.class) List<CurrencyTypeParam> currencyTypeParamList) {
        currencyTypeService.delete(currencyTypeParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑充币类型
     *
     * <AUTHOR>
     * @date 2022-04-25 15:48:06
     */
    @Permission
    @ResponseBody
    @PostMapping("/currencyType/edit")
    @BusinessLog(title = "充币类型_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(CurrencyTypeParam.edit.class) CurrencyTypeParam currencyTypeParam) {
        currencyTypeService.edit(currencyTypeParam);
        return new SuccessResponseData();
    }

    /**
     * 查看充币类型
     *
     * <AUTHOR>
     * @date 2022-04-25 15:48:06
     */
    @Permission
    @ResponseBody
    @GetMapping("/currencyType/detail")
    @BusinessLog(title = "充币类型_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(CurrencyTypeParam.detail.class) CurrencyTypeParam currencyTypeParam) {
        return new SuccessResponseData(currencyTypeService.detail(currencyTypeParam));
    }

    /**
     * 充币类型列表
     *
     * <AUTHOR>
     * @date 2022-04-25 15:48:06
     */
    @Permission
    @ResponseBody
    @GetMapping("/currencyType/list")
    @BusinessLog(title = "充币类型_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(CurrencyTypeParam currencyTypeParam) {
        return new SuccessResponseData(currencyTypeService.list(currencyTypeParam));
    }

}
