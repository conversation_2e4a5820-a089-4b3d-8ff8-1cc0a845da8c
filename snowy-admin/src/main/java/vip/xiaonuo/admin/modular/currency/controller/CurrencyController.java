/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.currency.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.currency.entity.Currency;
import vip.xiaonuo.business.currency.param.CurrencyParam;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.currencytype.entity.CurrencyType;
import vip.xiaonuo.business.currencytype.param.CurrencyTypeParam;
import vip.xiaonuo.business.currencytype.service.CurrencyTypeService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;

/**
 * 币种控制器
 *
 * <AUTHOR>
 * @date 2022-04-06 12:56:08
 */
@Controller
public class CurrencyController {

    private String PATH_PREFIX = "currency/";
    @Resource
    private CurrencyTypeService currencyTypeService;
    @Resource
    private CurrencyService currencyService;

    /**
     * 币种页面
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    @Permission
    @GetMapping("/currency/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 币种表单页面
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    @GetMapping("/currency/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询币种
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    @Permission
    @ResponseBody
    @GetMapping("/currency/page")
    @BusinessLog(title = "币种_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<Currency> page(CurrencyParam currencyParam) {
        return currencyService.page(currencyParam);
    }

    /**
     * 添加币种
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    @Permission
    @ResponseBody
    @PostMapping("/currency/add")
    @BusinessLog(title = "币种_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(CurrencyParam.add.class) CurrencyParam currencyParam) {
        currencyService.add(currencyParam);
        return new SuccessResponseData();
    }

    /**
     * 删除币种
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    @Permission
    @ResponseBody
    @PostMapping("/currency/delete")
    @BusinessLog(title = "币种_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(CurrencyParam.delete.class) List<CurrencyParam> currencyParamList) {
        currencyService.delete(currencyParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑币种
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    @Permission
    @ResponseBody
    @PostMapping("/currency/edit")
    @BusinessLog(title = "币种_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody CurrencyParam currencyParam) {
        currencyService.edit(currencyParam);
        return new SuccessResponseData();
    }

    /**
     * 查看币种
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    @Permission
    @ResponseBody
    @GetMapping("/currency/detail")
    @BusinessLog(title = "币种_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(CurrencyParam.detail.class) CurrencyParam currencyParam) {
        return new SuccessResponseData(currencyService.detail(currencyParam));
    }

    /**
     * 币种列表
     *
     * <AUTHOR>
     * @date 2022-04-06 12:56:08
     */
    @Permission
    @ResponseBody
    @GetMapping("/currency/list")
    @BusinessLog(title = "币种_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(CurrencyParam currencyParam) {
        return new SuccessResponseData(currencyService.list(currencyParam));
    }

    @ResponseBody
    @GetMapping("/currency/withdrawCurrencys")
    public ResponseData<List<Currency>> withdrawCurrencys() {
        return new SuccessResponseData(currencyService.getWithdrawCurrencyList());
    }

    @ResponseBody
    @GetMapping("/currency/chargeCurrencys")
    public ResponseData<List<CurrencyType>> chargeCurrencys() {
        CurrencyTypeParam currencyTypeParam = new CurrencyTypeParam();
        List<CurrencyType> list = currencyTypeService.queryList(currencyTypeParam);
        return new SuccessResponseData(list);
    }
}
