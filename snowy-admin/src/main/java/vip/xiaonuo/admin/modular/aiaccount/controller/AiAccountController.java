/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.aiaccount.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.aiaccount.entity.AiAccount;
import vip.xiaonuo.business.aiaccount.param.AiAccountParam;
import vip.xiaonuo.business.aiaccount.service.AiAccountService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;

/**
 * AI银行账户表控制器
 *
 * <AUTHOR>
 * @date 2025-08-04 11:32:42
 */
@Controller
public class AiAccountController {

    private String PATH_PREFIX = "aiAccount/";

    @Resource
    private AiAccountService aiAccountService;

    /**
     * AI银行账户表页面
     *
     * <AUTHOR>
     * @date 2025-08-04 11:32:42
     */
    @Permission
    @GetMapping("/aiAccount/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * AI银行账户表表单页面
     *
     * <AUTHOR>
     * @date 2025-08-04 11:32:42
     */
    @GetMapping("/aiAccount/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询AI银行账户表
     *
     * <AUTHOR>
     * @date 2025-08-04 11:32:42
     */
    @Permission
    @ResponseBody
    @GetMapping("/aiAccount/page")
    @BusinessLog(title = "AI银行账户表_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<AiAccount> page(AiAccountParam aiAccountParam) {
        return aiAccountService.page(aiAccountParam);
    }

    /**
     * 添加AI银行账户表
     *
     * <AUTHOR>
     * @date 2025-08-04 11:32:42
     */
    @Permission
    @ResponseBody
    @PostMapping("/aiAccount/add")
    @BusinessLog(title = "AI银行账户表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(AiAccountParam.add.class) AiAccountParam aiAccountParam) {
        aiAccountService.add(aiAccountParam);
        return new SuccessResponseData();
    }

    /**
     * 删除AI银行账户表
     *
     * <AUTHOR>
     * @date 2025-08-04 11:32:42
     */
    @Permission
    @ResponseBody
    @PostMapping("/aiAccount/delete")
    @BusinessLog(title = "AI银行账户表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(AiAccountParam.delete.class) List<AiAccountParam> aiAccountParamList) {
        aiAccountService.delete(aiAccountParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑AI银行账户表
     *
     * <AUTHOR>
     * @date 2025-08-04 11:32:42
     */
    @Permission
    @ResponseBody
    @PostMapping("/aiAccount/edit")
    @BusinessLog(title = "AI银行账户表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(AiAccountParam.edit.class) AiAccountParam aiAccountParam) {
        aiAccountService.edit(aiAccountParam);
        return new SuccessResponseData();
    }

    /**
     * 查看AI银行账户表
     *
     * <AUTHOR>
     * @date 2025-08-04 11:32:42
     */
    @Permission
    @ResponseBody
    @GetMapping("/aiAccount/detail")
    @BusinessLog(title = "AI银行账户表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(AiAccountParam.detail.class) AiAccountParam aiAccountParam) {
        return new SuccessResponseData(aiAccountService.detail(aiAccountParam));
    }

    /**
     * AI银行账户表列表
     *
     * <AUTHOR>
     * @date 2025-08-04 11:32:42
     */
    @Permission
    @ResponseBody
    @GetMapping("/aiAccount/list")
    @BusinessLog(title = "AI银行账户表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(AiAccountParam aiAccountParam) {
        return new SuccessResponseData(aiAccountService.list(aiAccountParam));
    }

}
