/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.customer.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.context.login.AdminLoginContext;
import vip.xiaonuo.business.customer.entity.Customer;
import vip.xiaonuo.business.customer.param.CustomerParam;
import vip.xiaonuo.business.customer.service.CustomerService;
import vip.xiaonuo.business.notice.param.PlayerNoticeParam;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.context.login.LoginContext;
import vip.xiaonuo.core.context.login.LoginContextHolder;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.base.param.BaseParam;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.session.utils.PrincipalUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 客服管理控制器
 *
 * <AUTHOR>
 * @date 2022-04-12 13:32:27
 */
@Controller
public class CustomerController {

    private String PATH_PREFIX = "customer/";

    @Resource
    private CustomerService customerService;

    /**
     * 客服管理页面
     *
     * <AUTHOR>
     * @date 2022-04-12 13:32:27
     */
    @Permission
    @GetMapping("/customer/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 客服管理表单页面
     *
     * <AUTHOR>
     * @date 2022-04-12 13:32:27
     */
    @GetMapping("/customer/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询客服管理
     *
     * <AUTHOR>
     * @date 2022-04-12 13:32:27
     */
    @Permission
    @ResponseBody
    @GetMapping("/customer/page")
    @BusinessLog(title = "客服管理_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<Customer> page(CustomerParam customerParam) {
        return customerService.page(customerParam);
    }

    /**
     * 添加客服管理
     *
     * <AUTHOR>
     * @date 2022-04-12 13:32:27
     */
    @Permission
    @ResponseBody
    @PostMapping("/customer/add")
    @BusinessLog(title = "客服管理_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(CustomerParam.add.class) CustomerParam customerParam) {
        customerParam.setOrgCode(LoginContextHolder.me(AdminLoginContext.class).getLoginUserOrgCode());
        customerService.add(customerParam);
        return new SuccessResponseData();
    }

    /**
     * 删除客服管理
     *
     * <AUTHOR>
     * @date 2022-04-12 13:32:27
     */
    @Permission
    @ResponseBody
    @PostMapping("/customer/delete")
    @BusinessLog(title = "客服管理_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(CustomerParam.delete.class) List<CustomerParam> customerParamList) {
        customerService.delete(customerParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑客服管理
     *
     * <AUTHOR>
     * @date 2022-04-12 13:32:27
     */
    @Permission
    @ResponseBody
    @PostMapping("/customer/edit")
    @BusinessLog(title = "客服管理_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(CustomerParam.edit.class) CustomerParam customerParam) {
        customerService.edit(customerParam);
        return new SuccessResponseData();
    }

    /**
     * 查看客服管理
     *
     * <AUTHOR>
     * @date 2022-04-12 13:32:27
     */
    @Permission
    @ResponseBody
    @GetMapping("/customer/detail")
    @BusinessLog(title = "客服管理_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(CustomerParam.detail.class) CustomerParam customerParam) {
        return new SuccessResponseData(customerService.detail(customerParam));
    }

    /**
     * 客服管理列表
     *
     * <AUTHOR>
     * @date 2022-04-12 13:32:27
     */
    @Permission
    @ResponseBody
    @GetMapping("/customer/list")
    @BusinessLog(title = "客服管理_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(CustomerParam customerParam) {
        return new SuccessResponseData(customerService.list(customerParam));
    }

    /**
     * 客服管理修改状态
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    @Permission
    @ResponseBody
    @PostMapping("/customer/changeStatus")
    @BusinessLog(title = "客服管理_修改状态", opType = LogAnnotionOpTypeEnum.CHANGE_STATUS)
    public ResponseData changeStatus(@RequestBody @Validated(CustomerParam.changeStatus.class) CustomerParam customerParam) {
        customerService.changeStatus(customerParam);
        return new SuccessResponseData();
    }

}
