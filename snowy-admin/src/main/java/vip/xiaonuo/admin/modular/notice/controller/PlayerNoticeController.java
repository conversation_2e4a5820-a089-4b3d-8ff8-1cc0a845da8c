/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.notice.controller;

import vip.xiaonuo.business.notice.entity.PlayerNotice;
import vip.xiaonuo.business.notice.param.PlayerNoticeParam;
import vip.xiaonuo.business.notice.service.PlayerNoticeService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.List;

/**
 * 玩家公告表控制器
 *
 * <AUTHOR>
 * @date 2022-04-07 17:14:13
 */
@Controller
public class PlayerNoticeController {

    private String PATH_PREFIX = "playerNotice/";

    @Resource
    private PlayerNoticeService playerNoticeService;

    /**
     * 玩家公告表页面
     *
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    @Permission
    @GetMapping("/playerNotice/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 玩家公告表表单页面
     *
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    @GetMapping("/playerNotice/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询玩家公告表
     *
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    @Permission
    @ResponseBody
    @GetMapping("/playerNotice/page")
    @BusinessLog(title = "玩家公告表_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<PlayerNotice> page(PlayerNoticeParam playerNoticeParam) {
        return playerNoticeService.page(playerNoticeParam);
    }

    /**
     * 添加玩家公告表
     *
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerNotice/add")
    @BusinessLog(title = "玩家公告表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(PlayerNoticeParam.add.class) PlayerNoticeParam playerNoticeParam) {
        playerNoticeService.add(playerNoticeParam);
        return new SuccessResponseData();
    }

    /**
     * 删除玩家公告表
     *
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerNotice/delete")
    @BusinessLog(title = "玩家公告表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(PlayerNoticeParam.delete.class) List<PlayerNoticeParam> playerNoticeParamList) {
        playerNoticeService.delete(playerNoticeParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑玩家公告表
     *
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerNotice/edit")
    @BusinessLog(title = "玩家公告表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(PlayerNoticeParam.edit.class) PlayerNoticeParam playerNoticeParam) {
        playerNoticeService.edit(playerNoticeParam);
        return new SuccessResponseData();
    }

    /**
     * 查看玩家公告表
     *
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    @Permission
    @ResponseBody
    @GetMapping("/playerNotice/detail")
    @BusinessLog(title = "玩家公告表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(PlayerNoticeParam.detail.class) PlayerNoticeParam playerNoticeParam) {
        return new SuccessResponseData(playerNoticeService.detail(playerNoticeParam));
    }

    /**
     * 玩家公告表列表
     *
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    @Permission
    @ResponseBody
    @GetMapping("/playerNotice/list")
    @BusinessLog(title = "玩家公告表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(PlayerNoticeParam playerNoticeParam) {
        return new SuccessResponseData(playerNoticeService.list(playerNoticeParam));
    }

    /**
     * 玩家公告修改状态
     *
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerNotice/changeStatus")
    @BusinessLog(title = "玩家公告_修改状态", opType = LogAnnotionOpTypeEnum.CHANGE_STATUS)
    public ResponseData changeStatus(@RequestBody @Validated(PlayerNoticeParam.changeStatus.class) PlayerNoticeParam playerNoticeParam) {
        playerNoticeService.changeStatus(playerNoticeParam);
        return new SuccessResponseData();
    }

    /**
     * 玩家公告置顶/取消置顶
     *
     * <AUTHOR>
     * @date 2022-04-07 17:14:13
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerNotice/changeTop")
    @BusinessLog(title = "玩家公告_置顶/取消置顶", opType = LogAnnotionOpTypeEnum.CHANGE_TOP)
    public ResponseData changeTop(@RequestBody @Validated(PlayerNoticeParam.changeTop.class) PlayerNoticeParam playerNoticeParam) {
        playerNoticeService.changeTop(playerNoticeParam);
        return new SuccessResponseData();
    }
}
