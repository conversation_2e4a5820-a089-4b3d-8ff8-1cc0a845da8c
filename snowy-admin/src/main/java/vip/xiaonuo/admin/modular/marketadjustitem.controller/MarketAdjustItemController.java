/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.marketadjustitem.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.business.market.mapper.MarketMapper;
import vip.xiaonuo.business.market.service.MarketService;
import vip.xiaonuo.business.market.service.impl.MarketServiceImpl;
import vip.xiaonuo.business.marketadjustitem.entity.MarketAdjustItem;
import vip.xiaonuo.business.marketadjustitem.param.MarketAdjustItemParam;
import vip.xiaonuo.business.marketadjustitem.service.MarketAdjustItemService;
import vip.xiaonuo.business.marketadjustitemtime.entity.MarketAdjustItemTime;
import vip.xiaonuo.business.marketadjustitemtime.service.MarketAdjustItemTimeService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 市场调整控制器
 *
 * <AUTHOR>
 * @date 2025-07-09 21:54:45
 */
@Controller
public class MarketAdjustItemController {

    private String PATH_PREFIX = "marketAdjustItem/";

    @Resource
    private MarketAdjustItemService marketAdjustItemService;

    @Resource
    private MarketAdjustItemTimeService marketAdjustItemTimeService;

    @Resource
    private MarketMapper marketMapper;

    /**
     * 市场调整页面
     *
     * <AUTHOR>
     * @date 2025-07-09 21:54:45
     */
    @Permission
    @GetMapping("/marketAdjustItem/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 市场调整表单页面
     *
     * <AUTHOR>
     * @date 2025-07-09 21:54:45
     */
    @GetMapping("/marketAdjustItem/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询市场调整
     *
     * <AUTHOR>
     * @date 2025-07-09 21:54:45
     */
    @Permission
    @ResponseBody
    @GetMapping("/marketAdjustItem/page")
    @BusinessLog(title = "市场调整_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<MarketAdjustItem> page(MarketAdjustItemParam marketAdjustItemParam) {
        return marketAdjustItemService.page(marketAdjustItemParam);
    }

    /**
     * 添加市场调整
     *
     * <AUTHOR>
     * @date 2025-07-09 21:54:45
     */
    @Permission
    @ResponseBody
    @PostMapping("/marketAdjustItem/add")
    @BusinessLog(title = "市场调整_增加", opType = LogAnnotionOpTypeEnum.ADD)
    @Transactional(rollbackFor = Exception.class)
    public ResponseData add(@RequestBody @Validated(MarketAdjustItemParam.add.class) MarketAdjustItemParam marketAdjustItemParam) {
        long nowTs = System.currentTimeMillis();
        nowTs = (nowTs / 1000) * 1000 + 1000; // 舍弃毫秒部分，对齐整秒
//        marketAdjustItemService.add(marketAdjustItemParam);
        MarketAdjustItem marketAdjustItem = new MarketAdjustItem();
        BeanUtil.copyProperties(marketAdjustItemParam, marketAdjustItem);
        marketAdjustItem.setStartTs(nowTs);
        marketAdjustItem.setEndTs(nowTs + marketAdjustItemParam.getSeconds() * 1000);
        marketAdjustItem.setLeftSeconds(marketAdjustItem.getSeconds());
        marketAdjustItemService.save(marketAdjustItem);
        // 预设
        List<MarketAdjustItemTime> detailList = new ArrayList<>();

        BigDecimal totalValue = marketAdjustItemParam.getValue();
        int seconds = marketAdjustItemParam.getSeconds();
        // 按照market的精度
        Market market = marketMapper.selectOne(Wrappers.lambdaQuery(Market.class).eq(Market::getSymbol, marketAdjustItemParam.getSymbol()));
        BigDecimal perSecondValue = totalValue.divide(BigDecimal.valueOf(seconds), market.getPriceDecimal(), RoundingMode.HALF_UP);
        BigDecimal cumulative = BigDecimal.ZERO;
        for (int i = 0; i < marketAdjustItemParam.getSeconds(); i++) {
            MarketAdjustItemTime detail = new MarketAdjustItemTime();
            detail.setSymbol(marketAdjustItemParam.getSymbol());
            detail.setMarketAdjustItemId(marketAdjustItem.getId());
            detail.setTs(marketAdjustItem.getStartTs() + i * 1000); // 每秒间隔 -> 毫秒
            detail.setUnitValue(perSecondValue);
//            detail.setIsOneMinute((marketAdjustItem.getStartTs() + i * 1000) % 60000 == 0 ? 1 : 0); // ✅ 判断是否为整分钟点（1m）
            detail.setIsOneMinute(((marketAdjustItem.getStartTs() + i * 1000) % 60000 == 59000) ? 1 : 0);
            // 累加
            cumulative = cumulative.add(perSecondValue);
            detail.setValue(cumulative); // 设置为当前时刻的累计值
            detailList.add(detail);
        }

        marketAdjustItemTimeService.saveBatch(detailList);
        return new SuccessResponseData();
    }

    /**
     * 删除市场调整
     *
     * <AUTHOR>
     * @date 2025-07-09 21:54:45
     */
    @Permission
    @ResponseBody
    @PostMapping("/marketAdjustItem/delete")
    @BusinessLog(title = "市场调整_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(MarketAdjustItemParam.delete.class) List<MarketAdjustItemParam> marketAdjustItemParamList) {
        marketAdjustItemService.delete(marketAdjustItemParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑市场调整
     *
     * <AUTHOR>
     * @date 2025-07-09 21:54:45
     */
    @Permission
    @ResponseBody
    @PostMapping("/marketAdjustItem/edit")
    @BusinessLog(title = "市场调整_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(MarketAdjustItemParam.edit.class) MarketAdjustItemParam marketAdjustItemParam) {
        marketAdjustItemService.edit(marketAdjustItemParam);
        return new SuccessResponseData();
    }

    /**
     * 查看市场调整
     *
     * <AUTHOR>
     * @date 2025-07-09 21:54:45
     */
    @Permission
    @ResponseBody
    @GetMapping("/marketAdjustItem/detail")
    @BusinessLog(title = "市场调整_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(MarketAdjustItemParam.detail.class) MarketAdjustItemParam marketAdjustItemParam) {
        return new SuccessResponseData(marketAdjustItemService.detail(marketAdjustItemParam));
    }

    /**
     * 市场调整列表
     *
     * <AUTHOR>
     * @date 2025-07-09 21:54:45
     */
    @Permission
    @ResponseBody
    @GetMapping("/marketAdjustItem/list")
    @BusinessLog(title = "市场调整_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(MarketAdjustItemParam marketAdjustItemParam) {
        return new SuccessResponseData(marketAdjustItemService.list(marketAdjustItemParam));
    }

}
