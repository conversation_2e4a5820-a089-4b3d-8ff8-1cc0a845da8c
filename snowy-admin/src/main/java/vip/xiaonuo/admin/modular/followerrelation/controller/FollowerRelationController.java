/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.followerrelation.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.followerrelation.entity.FollowerRelation;
import vip.xiaonuo.business.followerrelation.param.FollowerRelationParam;
import vip.xiaonuo.business.followerrelation.service.FollowerRelationService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;

/**
 * 玩家跟单控制器
 *
 * <AUTHOR>
 * @date 2025-07-18 14:22:02
 */
@Controller
public class FollowerRelationController {

    private String PATH_PREFIX = "followerRelation/";

    @Resource
    private FollowerRelationService followerRelationService;

    /**
     * 玩家跟单页面
     *
     * <AUTHOR>
     * @date 2025-07-18 14:22:02
     */
    @Permission
    @GetMapping("/followerRelation/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 玩家跟单表单页面
     *
     * <AUTHOR>
     * @date 2025-07-18 14:22:02
     */
    @GetMapping("/followerRelation/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询玩家跟单
     *
     * <AUTHOR>
     * @date 2025-07-18 14:22:02
     */
    @Permission
    @ResponseBody
    @GetMapping("/followerRelation/page")
    @BusinessLog(title = "玩家跟单_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<FollowerRelation> page(FollowerRelationParam followerRelationParam) {
        return followerRelationService.page(followerRelationParam);
    }

    /**
     * 添加玩家跟单
     *
     * <AUTHOR>
     * @date 2025-07-18 14:22:02
     */
    @Permission
    @ResponseBody
    @PostMapping("/followerRelation/add")
    @BusinessLog(title = "玩家跟单_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(FollowerRelationParam.add.class) FollowerRelationParam followerRelationParam) {
        followerRelationService.add(followerRelationParam);
        return new SuccessResponseData();
    }

    /**
     * 删除玩家跟单
     *
     * <AUTHOR>
     * @date 2025-07-18 14:22:02
     */
    @Permission
    @ResponseBody
    @PostMapping("/followerRelation/delete")
    @BusinessLog(title = "玩家跟单_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(FollowerRelationParam.delete.class) List<FollowerRelationParam> followerRelationParamList) {
        followerRelationService.delete(followerRelationParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑玩家跟单
     *
     * <AUTHOR>
     * @date 2025-07-18 14:22:02
     */
    @Permission
    @ResponseBody
    @PostMapping("/followerRelation/edit")
    @BusinessLog(title = "玩家跟单_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(FollowerRelationParam.edit.class) FollowerRelationParam followerRelationParam) {
        followerRelationService.edit(followerRelationParam);
        return new SuccessResponseData();
    }

    /**
     * 查看玩家跟单
     *
     * <AUTHOR>
     * @date 2025-07-18 14:22:02
     */
    @Permission
    @ResponseBody
    @GetMapping("/followerRelation/detail")
    @BusinessLog(title = "玩家跟单_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(FollowerRelationParam.detail.class) FollowerRelationParam followerRelationParam) {
        return new SuccessResponseData(followerRelationService.detail(followerRelationParam));
    }

    /**
     * 玩家跟单列表
     *
     * <AUTHOR>
     * @date 2025-07-18 14:22:02
     */
    @Permission
    @ResponseBody
    @GetMapping("/followerRelation/list")
    @BusinessLog(title = "玩家跟单_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(FollowerRelationParam followerRelationParam) {
        return new SuccessResponseData(followerRelationService.list(followerRelationParam));
    }

}
