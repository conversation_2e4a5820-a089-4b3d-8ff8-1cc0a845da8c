/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.accoutbill.controller;

import org.springframework.context.i18n.LocaleContextHolder;
import vip.xiaonuo.business.accountbill.entity.AccountBill;
import vip.xiaonuo.business.accountbill.param.AccountBillParam;
import vip.xiaonuo.business.accountbill.service.AccountBillService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;

/**
 * 账户日志表控制器
 *
 * <AUTHOR>
 * @date 2023-01-02 08:46:20
 */
@Controller
public class AccountBillController {

    private String PATH_PREFIX = "accountBill/";

    @Resource
    private AccountBillService accountBillService;

    /**
     * 账户日志表页面
     *
     * <AUTHOR>
     * @date 2023-01-02 08:46:20
     */
    @Permission
    @GetMapping("/accountBill/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 账户日志表表单页面
     *
     * <AUTHOR>
     * @date 2023-01-02 08:46:20
     */
    @GetMapping("/accountBill/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询账户日志表
     *
     * <AUTHOR>
     * @date 2023-01-02 08:46:20
     */
    @Permission
    @ResponseBody
    @GetMapping("/accountBill/page")
    @BusinessLog(title = "账户日志表_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<AccountBill> page(AccountBillParam accountBillParam) {
        LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);
        return accountBillService.page(accountBillParam);
    }

    /**
     * 添加账户日志表
     *
     * <AUTHOR>
     * @date 2023-01-02 08:46:20
     */
    @Permission
    @ResponseBody
    @PostMapping("/accountBill/add")
    @BusinessLog(title = "账户日志表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(AccountBillParam.add.class) AccountBillParam accountBillParam) {
        accountBillService.add(accountBillParam);
        return new SuccessResponseData();
    }

    /**
     * 删除账户日志表
     *
     * <AUTHOR>
     * @date 2023-01-02 08:46:20
     */
    @Permission
    @ResponseBody
    @PostMapping("/accountBill/delete")
    @BusinessLog(title = "账户日志表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(AccountBillParam.delete.class) List<AccountBillParam> accountBillParamList) {
        accountBillService.delete(accountBillParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑账户日志表
     *
     * <AUTHOR>
     * @date 2023-01-02 08:46:20
     */
    @Permission
    @ResponseBody
    @PostMapping("/accountBill/edit")
    @BusinessLog(title = "账户日志表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(AccountBillParam.edit.class) AccountBillParam accountBillParam) {
        accountBillService.edit(accountBillParam);
        return new SuccessResponseData();
    }

    /**
     * 查看账户日志表
     *
     * <AUTHOR>
     * @date 2023-01-02 08:46:20
     */
    @Permission
    @ResponseBody
    @GetMapping("/accountBill/detail")
    @BusinessLog(title = "账户日志表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(AccountBillParam.detail.class) AccountBillParam accountBillParam) {
        return new SuccessResponseData(accountBillService.detail(accountBillParam));
    }

    /**
     * 账户日志表列表
     *
     * <AUTHOR>
     * @date 2023-01-02 08:46:20
     */
    @Permission
    @ResponseBody
    @GetMapping("/accountBill/list")
    @BusinessLog(title = "账户日志表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(AccountBillParam accountBillParam) {
        return new SuccessResponseData(accountBillService.list(accountBillParam));
    }

}
