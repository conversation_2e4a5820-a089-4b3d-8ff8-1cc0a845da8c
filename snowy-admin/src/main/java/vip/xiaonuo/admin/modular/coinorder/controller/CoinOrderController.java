/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.coinorder.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.business.coinorder.entity.CoinOrder;
import vip.xiaonuo.business.coinorder.param.CoinOrderParam;
import vip.xiaonuo.business.coinorder.service.CoinOrderService;
import vip.xiaonuo.business.context.login.AdminLoginContext;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.DataScope;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.context.login.LoginContextHolder;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 币币订单控制器
 *
 * <AUTHOR>
 * @date 2022-09-14 11:02:20
 */
@Controller
public class CoinOrderController {

    private String PATH_PREFIX = "coinOrder/";

    @Resource
    private CoinOrderService coinOrderService;

    /**
     * 币币订单页面
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    @Permission
    @GetMapping("/coinOrder/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 币币订单表单页面
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    @GetMapping("/coinOrder/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    //订单成交
    @GetMapping("/coinOrder/closeForm")
    public String closeForm() {
        return PATH_PREFIX + "closeForm.html";
    }

    /**
     * 查询币币订单
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    @Permission
    @ResponseBody
    @GetMapping("/coinOrder/page")
    @BusinessLog(title = "币币订单_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    @DataScope
    public PageResult<CoinOrder> page(CoinOrderParam coinOrderParam) {
        return coinOrderService.page(coinOrderParam);
    }

    /**
     * 添加币币订单
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    @Permission
    @ResponseBody
    @PostMapping("/coinOrder/add")
    @BusinessLog(title = "币币订单_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(CoinOrderParam.add.class) CoinOrderParam coinOrderParam) {
        coinOrderService.add(coinOrderParam);
        return new SuccessResponseData();
    }

    @Permission
    @ResponseBody
    @PostMapping("/coinOrder/revoke")
    @BusinessLog(title = "币币订单_撤单")
    public ResponseData revoke(@RequestParam String orderNo, @RequestParam Long playerId) {
        coinOrderService.revokeEntrust(playerId, orderNo, CommonConstant.SYS_ID);
        return new SuccessResponseData();
    }

    @Permission(value = "coinOrder:close")
    @ResponseBody
    @PostMapping("/coinOrder/endCoinOrder")
    @BusinessLog(title = "币币订单_成交")
    public ResponseData endCoinOrder(@RequestParam String orderNo, @RequestParam BigDecimal c) {
        Long userId = LoginContextHolder.me(AdminLoginContext.class).getLoginUserId();
        coinOrderService.endCoinOrder(orderNo, c, userId);
        return new SuccessResponseData();
    }

    /**
     * 编辑币币订单
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    @Permission
    @ResponseBody
    @PostMapping("/coinOrder/edit")
    @BusinessLog(title = "币币订单_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(CoinOrderParam.edit.class) CoinOrderParam coinOrderParam) {
        coinOrderService.edit(coinOrderParam);
        return new SuccessResponseData();
    }

    /**
     * 查看币币订单
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    @Permission
    @ResponseBody
    @GetMapping("/coinOrder/detail")
    @BusinessLog(title = "币币订单_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(CoinOrderParam.detail.class) CoinOrderParam coinOrderParam) {
        return new SuccessResponseData(coinOrderService.detail(coinOrderParam));
    }

    /**
     * 币币订单列表
     *
     * <AUTHOR>
     * @date 2022-09-14 11:02:20
     */
    @Permission
    @ResponseBody
    @GetMapping("/coinOrder/list")
    @BusinessLog(title = "币币订单_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(CoinOrderParam coinOrderParam) {
        return new SuccessResponseData(coinOrderService.list(coinOrderParam));
    }

}
