/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.spotorder.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.business.spotorder.entity.SpotOrder;
import vip.xiaonuo.business.spotorder.param.SpotOrderParam;
import vip.xiaonuo.business.spotorder.service.SpotOrderService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.DataScope;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;

/**
 * 现货订单控制器
 *
 * <AUTHOR>
 * @date 2022-04-24 10:55:37
 */
@Controller
public class SpotOrderController {

    private String PATH_PREFIX = "spotOrder/";

    @Resource
    private SpotOrderService spotOrderService;

    /**
     * 现货订单页面
     *
     * <AUTHOR>
     * @date 2022-04-24 10:55:37
     */
    @Permission
    @GetMapping("/spotOrder/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 现货订单表单页面
     *
     * <AUTHOR>
     * @date 2022-04-24 10:55:37
     */
    @GetMapping("/spotOrder/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询现货订单
     *
     * <AUTHOR>
     * @date 2022-04-24 10:55:37
     */
    @Permission
    @ResponseBody
    @GetMapping("/spotOrder/page")
    @BusinessLog(title = "现货订单_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    @DataScope
    public PageResult<SpotOrder> page(SpotOrderParam spotOrderParam) {
        return spotOrderService.page(spotOrderParam);
    }

    /**
     * 添加现货订单
     *
     * <AUTHOR>
     * @date 2022-04-24 10:55:37
     */
    @Permission
    @ResponseBody
    @PostMapping("/spotOrder/add")
    @BusinessLog(title = "现货订单_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(SpotOrderParam.add.class) SpotOrderParam spotOrderParam) {
        spotOrderService.add(spotOrderParam);
        return new SuccessResponseData();
    }

    /**
     * 删除现货订单
     *
     * <AUTHOR>
     * @date 2022-04-24 10:55:37
     */
    @Permission
    @ResponseBody
    @PostMapping("/spotOrder/delete")
    @BusinessLog(title = "现货订单_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(SpotOrderParam.delete.class) List<SpotOrderParam> spotOrderParamList) {
        spotOrderService.delete(spotOrderParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑现货订单
     *
     * <AUTHOR>
     * @date 2022-04-24 10:55:37
     */
    @Permission
    @ResponseBody
    @PostMapping("/spotOrder/edit")
    @BusinessLog(title = "现货订单_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(SpotOrderParam.edit.class) SpotOrderParam spotOrderParam) {
        spotOrderService.edit(spotOrderParam);
        return new SuccessResponseData();
    }

    @Permission(value = "spotOrder:editControl")
    @ResponseBody
    @GetMapping("/spotOrder/editControl")
    @BusinessLog(title = "现货订单_编辑control", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData editControl(@RequestParam Long id, @RequestParam Integer control) {
        spotOrderService.editControl(id, control);
        return new SuccessResponseData();
    }

    /**
     * 查看现货订单
     *
     * <AUTHOR>
     * @date 2022-04-24 10:55:37
     */
    @Permission
    @ResponseBody
    @GetMapping("/spotOrder/detail")
    @BusinessLog(title = "现货订单_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(SpotOrderParam.detail.class) SpotOrderParam spotOrderParam) {
        return new SuccessResponseData(spotOrderService.detail(spotOrderParam));
    }

    /**
     * 现货订单列表
     *
     * <AUTHOR>
     * @date 2022-04-24 10:55:37
     */
    @Permission
    @ResponseBody
    @GetMapping("/spotOrder/list")
    @BusinessLog(title = "现货订单_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(SpotOrderParam spotOrderParam) {
        return new SuccessResponseData(spotOrderService.list(spotOrderParam));
    }

}
