package vip.xiaonuo.admin.modular.playerinfo.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.business.context.login.AdminLoginContext;
import vip.xiaonuo.business.playerinfo.param.PlayerInfoAuditParam;
import vip.xiaonuo.business.playerinfo.param.PlayerInfoParam;
import vip.xiaonuo.business.playerinfo.pojo.dto.FindPlayerInfoForPageDto;
import vip.xiaonuo.business.playerinfo.pojo.vo.FindPlayerInfoForPageVo;
import vip.xiaonuo.business.playerinfo.service.PlayerInfoService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.DataScope;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.context.login.LoginContextHolder;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 认证审核控制器
 *
 * <AUTHOR>
 * @date 2022-04-06 18:42:38
 */
@Controller
public class PlayerInfoController {

    private String PATH_PREFIX = "playerInfo/";

    @Resource
    private PlayerInfoService playerInfoService;

    /**
     * 认证审核页面
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    @Permission
    @GetMapping("/playerInfo/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 认证审核表单页面
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    @GetMapping("/playerInfo/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
//    @Permission
//    @ResponseBody
//    @GetMapping("/playerInfo/page")
//    @BusinessLog(title = "认证审核_查询", opType = LogAnnotionOpTypeEnum.QUERY)
//    public PageResult<PlayerInfo> page(PlayerInfoParam playerInfoParam) {
//        return playerInfoService.page(playerInfoParam);
//    }

    /**
     * 查询认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    @Permission
    @ResponseBody
    @GetMapping("/playerInfo/page")
//    @BusinessLog(title = "认证审核_查询关联用户信息", opType = LogAnnotionOpTypeEnum.QUERY)
    @DataScope
    public PageResult<FindPlayerInfoForPageVo> findPlayerInfoForPage(FindPlayerInfoForPageDto playerInfoParam) {
        return playerInfoService.findPlayerInfoForPage(playerInfoParam);
    }

    /**
     * 添加认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerInfo/add")
    @BusinessLog(title = "认证审核_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(PlayerInfoParam.add.class) PlayerInfoParam playerInfoParam) {
        playerInfoService.add(playerInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 删除认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerInfo/delete")
    @BusinessLog(title = "认证审核_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(PlayerInfoParam.delete.class) List<PlayerInfoParam> playerInfoParamList) {
        playerInfoService.delete(playerInfoParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerInfo/edit")
    @BusinessLog(title = "认证审核_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(PlayerInfoParam.edit.class) PlayerInfoParam playerInfoParam) {
        playerInfoService.edit(playerInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerInfo/updateUserAuthStatus")
    @BusinessLog(title = "认证审核", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData updateUserAuthStatus(@RequestBody PlayerInfoParam playerInfoParam) {
        playerInfoParam.setCheckTime(new Date());
        playerInfoParam.setCheckId(LoginContextHolder.me(AdminLoginContext.class).getLoginUserId());
        playerInfoService.edit(playerInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 查看认证审核
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
//    @Permission
    @ResponseBody
    @GetMapping("/playerInfo/detail")
//    @BusinessLog(title = "认证审核_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(PlayerInfoParam.detail.class) PlayerInfoParam playerInfoParam) {
        return new SuccessResponseData(playerInfoService.detail(playerInfoParam));
    }

    @ResponseBody
    @GetMapping("/playerInfo/detailByPlayerId")
    public ResponseData detailByPlayerId(@RequestParam Long playerId) {
        return new SuccessResponseData(playerInfoService.queryPlayerUserInfo(playerId));
    }

    @Permission(value = {"playerInfo:auditStatus"})
    @ResponseBody
    @PostMapping("/playerInfo/auditStatus")
    @BusinessLog(title = "认证审核", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData auditStatus(@RequestBody PlayerInfoAuditParam playerInfoParam) {
        Long loginUserId = LoginContextHolder.me(AdminLoginContext.class).getLoginUserId();
        playerInfoService.auditStatus(loginUserId, playerInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 认证审核列表
     *
     * <AUTHOR>
     * @date 2022-04-06 18:42:38
     */
    @Permission
    @ResponseBody
    @GetMapping("/playerInfo/list")
    @BusinessLog(title = "认证审核_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(PlayerInfoParam playerInfoParam) {
        return new SuccessResponseData(playerInfoService.list(playerInfoParam));
    }

}
