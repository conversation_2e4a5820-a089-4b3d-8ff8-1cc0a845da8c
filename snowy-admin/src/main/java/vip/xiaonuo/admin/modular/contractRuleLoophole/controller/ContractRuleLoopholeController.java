/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.contractRuleLoophole.controller;

import vip.xiaonuo.business.contractRuleLoophole.entity.ContractRuleLoophole;
import vip.xiaonuo.business.contractRuleLoophole.param.ContractRuleLoopholeParam;
import vip.xiaonuo.business.contractRuleLoophole.service.ContractRuleLoopholeService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.List;

/**
 * 合约漏洞表控制器
 *
 * <AUTHOR>
 * @date 2024-04-08 20:04:41
 */
@Controller
public class ContractRuleLoopholeController {

    private String PATH_PREFIX = "contractRuleLoophole/";

    @Resource
    private ContractRuleLoopholeService contractRuleLoopholeService;

    /**
     * 合约漏洞表页面
     *
     * <AUTHOR>
     * @date 2024-04-08 20:04:41
     */
    @Permission
    @GetMapping("/contractRuleLoophole/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 合约漏洞表表单页面
     *
     * <AUTHOR>
     * @date 2024-04-08 20:04:41
     */
    @GetMapping("/contractRuleLoophole/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询合约漏洞表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:04:41
     */
    @Permission
    @ResponseBody
    @GetMapping("/contractRuleLoophole/page")
    @BusinessLog(title = "合约漏洞表_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<ContractRuleLoophole> page(ContractRuleLoopholeParam contractRuleLoopholeParam) {
        return contractRuleLoopholeService.page(contractRuleLoopholeParam);
    }

    /**
     * 添加合约漏洞表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:04:41
     */
    @Permission
    @ResponseBody
    @PostMapping("/contractRuleLoophole/add")
    @BusinessLog(title = "合约漏洞表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ContractRuleLoopholeParam.add.class) ContractRuleLoopholeParam contractRuleLoopholeParam) {
        contractRuleLoopholeService.add(contractRuleLoopholeParam);
        return new SuccessResponseData();
    }

    /**
     * 删除合约漏洞表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:04:41
     */
    @Permission
    @ResponseBody
    @PostMapping("/contractRuleLoophole/delete")
    @BusinessLog(title = "合约漏洞表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ContractRuleLoopholeParam.delete.class) List<ContractRuleLoopholeParam> contractRuleLoopholeParamList) {
        contractRuleLoopholeService.delete(contractRuleLoopholeParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑合约漏洞表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:04:41
     */
    @Permission
    @ResponseBody
    @PostMapping("/contractRuleLoophole/edit")
    @BusinessLog(title = "合约漏洞表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ContractRuleLoopholeParam.edit.class) ContractRuleLoopholeParam contractRuleLoopholeParam) {
        contractRuleLoopholeService.edit(contractRuleLoopholeParam);
        return new SuccessResponseData();
    }

    /**
     * 查看合约漏洞表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:04:41
     */
    @Permission
    @ResponseBody
    @GetMapping("/contractRuleLoophole/detail")
    @BusinessLog(title = "合约漏洞表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(ContractRuleLoopholeParam.detail.class) ContractRuleLoopholeParam contractRuleLoopholeParam) {
        return new SuccessResponseData(contractRuleLoopholeService.detail(contractRuleLoopholeParam));
    }

    /**
     * 合约漏洞表列表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:04:41
     */
    @Permission
    @ResponseBody
    @GetMapping("/contractRuleLoophole/list")
    @BusinessLog(title = "合约漏洞表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(ContractRuleLoopholeParam contractRuleLoopholeParam) {
        return new SuccessResponseData(contractRuleLoopholeService.list(contractRuleLoopholeParam));
    }

}
