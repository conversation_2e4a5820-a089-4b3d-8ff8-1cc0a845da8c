/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.followerapplication.controller;

import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.follower.param.FollowerParam;
import vip.xiaonuo.business.follower.service.FollowerService;
import vip.xiaonuo.business.followerapplication.entity.FollowerApplication;
import vip.xiaonuo.business.followerapplication.param.FollowerApplicationParam;
import vip.xiaonuo.business.followerapplication.service.FollowerApplicationService;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;

/**
 * 交易员申请控制器
 *
 * <AUTHOR>
 * @date 2025-07-18 15:55:41
 */
@Controller
public class FollowerApplicationController {

    private String PATH_PREFIX = "followerApplication/";

    @Resource
    private FollowerApplicationService followerApplicationService;

    @Resource
    private PlayerService playerService;

    @Resource
    private FollowerService followerService;

    /**
     * 交易员申请页面
     *
     * <AUTHOR>
     * @date 2025-07-18 15:55:41
     */
    @Permission
    @GetMapping("/followerApplication/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 交易员申请表单页面
     *
     * <AUTHOR>
     * @date 2025-07-18 15:55:41
     */
    @GetMapping("/followerApplication/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询交易员申请
     *
     * <AUTHOR>
     * @date 2025-07-18 15:55:41
     */
    @Permission
    @ResponseBody
    @GetMapping("/followerApplication/page")
    @BusinessLog(title = "交易员申请_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<FollowerApplication> page(FollowerApplicationParam followerApplicationParam) {
        return followerApplicationService.page(followerApplicationParam);
    }

    /**
     * 添加交易员申请
     *
     * <AUTHOR>
     * @date 2025-07-18 15:55:41
     */
    @Permission
    @ResponseBody
    @PostMapping("/followerApplication/add")
    @BusinessLog(title = "交易员申请_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(FollowerApplicationParam.add.class) FollowerApplicationParam followerApplicationParam) {
        followerApplicationService.add(followerApplicationParam);
        return new SuccessResponseData();
    }

    /**
     * 删除交易员申请
     *
     * <AUTHOR>
     * @date 2025-07-18 15:55:41
     */
    @Permission
    @ResponseBody
    @PostMapping("/followerApplication/delete")
    @BusinessLog(title = "交易员申请_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(FollowerApplicationParam.delete.class) List<FollowerApplicationParam> followerApplicationParamList) {
        followerApplicationService.delete(followerApplicationParamList);
        return new SuccessResponseData();
    }

    @PostMapping("/followerApplication/edit")
    @Permission
    @ResponseBody
    @BusinessLog(title = "交易员申请_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    @Transactional(rollbackFor = Exception.class)
    public ResponseData edit(@RequestBody @Validated(FollowerApplicationParam.edit.class) FollowerApplicationParam followerApplicationParam) {
        // 根据 ID 查询当前记录
        FollowerApplication old = followerApplicationService.getById(followerApplicationParam.getId());
        if (old == null) {
            return ResponseData.error("记录不存在");
        }

        // 仅允许审核状态为 0（待审核）时进行编辑
        if (old.getAuditStatus() != 0) {
            return ResponseData.error("仅允许编辑待审核状态的申请");
        }

        // 通过校验后，执行编辑操作
        followerApplicationService.edit(followerApplicationParam);
        if (followerApplicationParam.getAuditStatus() == 1) {
            Player player = playerService.getById(followerApplicationParam.getPlayerId());
            if (player != null) {
                player.setIsFollower(1);
                playerService.updateById(player);
                FollowerParam followerParam = new FollowerParam();
                followerParam.setPlayerId(old.getId());
                followerParam.setProfile(old.getProfile());
                followerParam.setTagList(old.getTagList());
                followerService.add(followerParam);
            }
        }
        return new SuccessResponseData();
    }


    /**
     * 查看交易员申请
     *
     * <AUTHOR>
     * @date 2025-07-18 15:55:41
     */
    @Permission
    @ResponseBody
    @GetMapping("/followerApplication/detail")
    @BusinessLog(title = "交易员申请_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(FollowerApplicationParam.detail.class) FollowerApplicationParam followerApplicationParam) {
        return new SuccessResponseData(followerApplicationService.detail(followerApplicationParam));
    }

    /**
     * 交易员申请列表
     *
     * <AUTHOR>
     * @date 2025-07-18 15:55:41
     */
    @Permission
    @ResponseBody
    @GetMapping("/followerApplication/list")
    @BusinessLog(title = "交易员申请_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(FollowerApplicationParam followerApplicationParam) {
        return new SuccessResponseData(followerApplicationService.list(followerApplicationParam));
    }

}
