package vip.xiaonuo.admin.modular.timer.tasks;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.stereotype.Component;
import vip.xiaonuo.business.exchangerate.service.ExchangeRateService;
import vip.xiaonuo.core.redis.RedisHelper;
import vip.xiaonuo.core.timer.TimerTaskRunner;
import vip.xiaonuo.core.util.DateUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Random;

/**
 * 同步费率
 *
 * <AUTHOR>
 * @date 2020/6/30 22:09
 */
@Slf4j
@Component
public class CalTransCountTaskRunner implements TimerTaskRunner {
    @Resource
    private RedisHelper redisHelper;

    @Override
    public void action() {
        log.info("计算成交量开始");
        String transAmountKey = "TOTAL_TRANS_AMOUNT1";
        String transOutKey = "TOTAL_TRANS_COUNT1";
        Date date = new Date();
        String hour = DateUtils.formatDate(date,"HH");
        String time = DateUtils.formatDate(date,"HH:mm");
        int hourNum = Integer.valueOf(hour);
        BigDecimal transAmount = BigDecimal.valueOf(353348228);
        Integer transCount = Integer.valueOf(210358);
        if(time.equals("23:59")){
            redisHelper.set(transAmountKey,transAmount);
            redisHelper.set(transOutKey,transCount);
        }
        if(hourNum >= 8 && hourNum <= 23){
            if(redisHelper.getRedisTemplate().hasKey(transAmountKey)){
                String transAmountStr = redisHelper.get(transAmountKey);
                transAmount = new BigDecimal(transAmountStr);
            }
            Random num = new Random();
            transAmount = transAmount.add(BigDecimal.valueOf(1000000)).add(BigDecimal.valueOf(num.nextInt(10000)));
            redisHelper.set(transAmountKey,transAmount);

            if(redisHelper.getRedisTemplate().hasKey(transOutKey)){
                transCount = redisHelper.getInteger(transOutKey);
            }
            Random transNum = new Random();
            transCount = transCount + (100+transNum.nextInt(100));
            redisHelper.set(transOutKey,transCount);
        }else if(hourNum >= 0 && hourNum <= 5){
            if(redisHelper.getRedisTemplate().hasKey(transAmountKey)){
                String transAmountStr = redisHelper.get(transAmountKey);
                transAmount = new BigDecimal(transAmountStr);
            }
            Random num = new Random();
            if(transAmount.compareTo(BigDecimal.valueOf(353348228)) > 0){
                transAmount = transAmount.subtract(BigDecimal.valueOf(100000)).add(BigDecimal.valueOf(num.nextInt(10000)));
                redisHelper.set(transAmountKey,transAmount);
            }
            if(redisHelper.getRedisTemplate().hasKey(transOutKey)){
                transCount = redisHelper.getInteger(transOutKey);
            }
            Random transNum = new Random();
            transCount = transCount - (100+transNum.nextInt(100));
            redisHelper.set(transOutKey,transCount);
        }else{
            if(redisHelper.getRedisTemplate().hasKey(transAmountKey)){
                String transAmountStr = redisHelper.get(transAmountKey);
                transAmount = new BigDecimal(transAmountStr);
            }
            Random num = new Random(10000);
            transAmount = transAmount.add(BigDecimal.valueOf(300000)).add(BigDecimal.valueOf(num.nextInt(10000)));
            redisHelper.set(transAmountKey,transAmount);

            if(redisHelper.getRedisTemplate().hasKey(transOutKey)){
                transCount = redisHelper.getInteger(transOutKey);
            }
            Random transNum = new Random();
            transCount = transCount + (30+transNum.nextInt(100));
            redisHelper.set(transOutKey,transCount);
        }
        log.info("计算成交量结束");
        log.info("成交额："+redisHelper.get(transAmountKey));
        log.info("成交量："+redisHelper.get(transOutKey));
    }
}
