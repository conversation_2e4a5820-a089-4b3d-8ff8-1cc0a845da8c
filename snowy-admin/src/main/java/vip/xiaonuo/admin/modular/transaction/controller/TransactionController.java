package vip.xiaonuo.admin.modular.transaction.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.account.enums.AccountTypeEnum;
import vip.xiaonuo.business.context.login.AdminLoginContext;
import vip.xiaonuo.business.dict.service.SysDictDataService;
import vip.xiaonuo.business.transaction.param.TransactionParam;
import vip.xiaonuo.business.transaction.pojo.dto.*;
import vip.xiaonuo.business.transaction.pojo.vo.FindRechargeUserForPageVo;
import vip.xiaonuo.business.transaction.service.TransactionService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.DataScope;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.context.login.LoginContextHolder;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户充值，提现记录表控制器
 *
 * <AUTHOR>
 * @date 2022-04-08 10:08:56
 */
@Controller
public class TransactionController {

    private String PATH_PREFIX = "transaction/";

    private String PATH_WITHDRAWAL_PREFIX = "withdrawal/";

    @Resource
    private TransactionService transactionService;
    @Autowired
    SysDictDataService sysDictDataService;

    /**
     * 用户充值，提现记录表页面
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @Permission
    @GetMapping("/transaction/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 用户充值，提现记录表表单页面
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @GetMapping("/transaction/form")
    public String form(Model model) {
        model.addAttribute("defaultCurrentList", sysDictDataService.getDictvalueByDictTypeCode("default_current"));
        return PATH_PREFIX + "form.html";
    }

    /**
     * 用户充值，提现记录表页面
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @Permission
    @GetMapping("/withdrawal/index")
    public String indexList(Model model) {
        return PATH_WITHDRAWAL_PREFIX + "index.html";
    }

    /**
     * 用户充值，提现记录表表单页面
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @GetMapping("/withdrawal/form")
    public String formAdd(Model model) {
        model.addAttribute("defaultCurrentList", sysDictDataService.getDictvalueByDictTypeCode("default_current"));
        return PATH_WITHDRAWAL_PREFIX + "form.html";
    }

    /**
     * 用户填
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @GetMapping("/withdrawal/addwithdrawal")
    public String addwithdrawal(Model model) {
        model.addAttribute("defaultCurrentList", sysDictDataService.getDictvalueByDictTypeCode("default_current"));
        return PATH_WITHDRAWAL_PREFIX + "addwithdrawal.html";
    }

    /**
     * 用户充值
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @GetMapping("/withdrawal/addRechage")
    public String addRechage() {
        return PATH_PREFIX + "addRechage.html";
    }


    /**
     * 查询用户充值，提现记录表
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @Permission
    @ResponseBody
    @GetMapping("/transaction/page")
    @BusinessLog(title = "用户充值记录表_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    @DataScope
    public PageResult<FindRechargeUserForPageVo> findRechargeUserForPage(FindRechargeUserForPageDto transactionParam) {
        return transactionService.findRechargeUserForPage(transactionParam);
    }

    /**
     * 查询用户充值，提现记录表
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @Permission
    @ResponseBody
    @GetMapping("/withdrawal/page")
    @BusinessLog(title = "用户提现记录表_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    @DataScope
    public PageResult<FindRechargeUserForPageVo> findWithdrawalUserForPage(FindWithdrawalUserForPageDto transactionParam) {
        return transactionService.findWithdrawalUserForPage(transactionParam);
    }


    /**
     * 添加用户充值
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @Permission
    @ResponseBody
    @PostMapping("/transaction/add")
    @BusinessLog(title = "用户充值，提现记录表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(TransactionParam.add.class) TransactionParam transactionParam) {
        transactionService.add(transactionParam);
        return new SuccessResponseData();
    }

    /**
     * 删除用户充值，提现记录表
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @Permission
    @ResponseBody
    @PostMapping("/transaction/delete")
    @BusinessLog(title = "用户充值，提现记录表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(TransactionParam.delete.class) List<TransactionParam> transactionParamList) {
        transactionService.delete(transactionParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑用户充值，提现记录表
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @Permission
    @ResponseBody
    @PostMapping("/transaction/edit")
    @BusinessLog(title = "用户充值，提现记录表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(TransactionParam.edit.class) TransactionParam transactionParam) {
        transactionService.edit(transactionParam);
        return new SuccessResponseData();
    }

    /**
     * 查看用户充值，提现记录表
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @Permission
    @ResponseBody
    @GetMapping("/transaction/detail")
    @BusinessLog(title = "用户充值，提现记录表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(TransactionParam.detail.class) TransactionParam transactionParam) {
        return new SuccessResponseData(transactionService.detail(transactionParam));
    }

    /**
     * 用户充值，提现记录表列表
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @Permission
    @ResponseBody
    @GetMapping("/transaction/list")
    @BusinessLog(title = "用户充值，提现记录表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(TransactionParam transactionParam) {
        return new SuccessResponseData(transactionService.list(transactionParam));
    }


    /**
     * 用户充值
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @Permission(value = "player:manualDeposit")
    @ResponseBody
    @PostMapping("/transaction/executeRechargeUser")
    @BusinessLog(title = "给玩家加额度_手工充值")
    public ResponseData executeRechargeUser(@RequestBody ExecuteRechargeUserDto transactionParam) {
        Long loginUserId = LoginContextHolder.me(AdminLoginContext.class).getLoginUserId();
        transactionParam.setOperationType(1);
        transactionParam.setStatus(2);
        transactionParam.setAccountType(AccountTypeEnum.TYPE1.getCode());
        transactionParam.setChenckId(loginUserId);
        return transactionService.executeRechargeUser(transactionParam);
    }

    /**
     * 用户提现
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @ResponseBody
    @PostMapping("/transaction/executeWithdrawalUser")
    @BusinessLog(title = "给玩家减额度_手工提现")
    public ResponseData executeWithdrawalUser(@RequestBody ExecuteWithdrawalUserDto transactionParam) {
        Long loginUserId = LoginContextHolder.me(AdminLoginContext.class).getLoginUserId();
        transactionParam.setStatus(2);
        transactionParam.setChenckId(loginUserId);
        return transactionService.executeWithdrawalUser(transactionParam,false);
    }

    /**
     * 审核
     *
     * <AUTHOR>
     * @date 2022-04-08 10:08:56
     */
    @Permission
    @ResponseBody
    @PostMapping("/transaction/checkTransaction")
    @BusinessLog(title = "玩家充值，提现记录表_审核", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData checkTransaction(@RequestBody CheckTransactionDto dto) {
        Long loginUserId = LoginContextHolder.me(AdminLoginContext.class).getLoginUserId();
        return transactionService.checkTransaction(loginUserId, dto);
    }

}
