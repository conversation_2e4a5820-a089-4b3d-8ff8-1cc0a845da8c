/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.playerbankaccount.controller;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.userbankaccount.entity.PlayerBankAccount;
import vip.xiaonuo.business.userbankaccount.param.PlayerBankAccountParam;
import vip.xiaonuo.business.userbankaccount.service.PlayerBankAccountService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.DataScope;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户账号信息控制器
 *
 * <AUTHOR>
 * @date 2022-04-12 19:32:16
 */
@Controller
public class PlayerBankAccountController {

    private String PATH_PREFIX = "playerBankAccount/";

    @Resource
    private PlayerBankAccountService playerBankAccountService;

    /**
     * 用户账号信息页面
     *
     * <AUTHOR>
     * @date 2022-04-12 19:32:16
     */
    @Permission
    @GetMapping("/playerBankAccount/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 用户账号信息表单页面
     *
     * <AUTHOR>
     * @date 2022-04-12 19:32:16
     */
    @GetMapping("/playerBankAccount/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询用户账号信息
     *
     * <AUTHOR>
     * @date 2022-04-12 19:32:16
     */
    @Permission
    @ResponseBody
    @GetMapping("/playerBankAccount/page")
//    @BusinessLog(title = "用户账号信息_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    @DataScope
    public PageResult<PlayerBankAccount> page(PlayerBankAccountParam playerBankAccountParam) {
        return playerBankAccountService.page(playerBankAccountParam);
    }

    /**
     * 添加用户账号信息
     *
     * <AUTHOR>
     * @date 2022-04-12 19:32:16
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerBankAccount/add")
    @BusinessLog(title = "玩家银行账号信息_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(PlayerBankAccountParam.add.class) PlayerBankAccountParam playerBankAccountParam) {
        playerBankAccountService.add(playerBankAccountParam);
        return new SuccessResponseData();
    }

    /**
     * 删除用户账号信息
     *
     * <AUTHOR>
     * @date 2022-04-12 19:32:16
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerBankAccount/delete")
    @BusinessLog(title = "玩家银行账号信息_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(PlayerBankAccountParam.delete.class) List<PlayerBankAccountParam> playerBankAccountParamList) {
        playerBankAccountService.delete(playerBankAccountParamList);
        return new SuccessResponseData();
    }

    @Permission("playerBankAccount:delete")
    @ResponseBody
    @PostMapping("/playerBankAccount/deleteOne")
    @BusinessLog(title = "玩家银行账号信息_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData deleteOne(@RequestBody @Validated(PlayerBankAccountParam.delete.class) PlayerBankAccountParam playerBankAccountParam) {
        playerBankAccountService.delete(playerBankAccountParam);
        return new SuccessResponseData();
    }


    /**
     * 编辑用户账号信息
     *
     * <AUTHOR>
     * @date 2022-04-12 19:32:16
     */
    @Permission
    @ResponseBody
    @PostMapping("/playerBankAccount/edit")
    @BusinessLog(title = "玩家银行账号信息_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(PlayerBankAccountParam.edit.class) PlayerBankAccountParam playerBankAccountParam) {
        if (StringUtils.isEmpty(playerBankAccountParam.getId())) {
            PlayerBankAccount playerBankAccount = new PlayerBankAccount();
            BeanUtil.copyProperties(playerBankAccountParam, playerBankAccount);
            playerBankAccountService.save(playerBankAccount);
        } else {
            playerBankAccountService.edit(playerBankAccountParam);
        }
        return new SuccessResponseData();
    }

    /**
     * 查看用户账号信息
     *
     * <AUTHOR>
     * @date 2022-04-12 19:32:16
     */
    @Permission
    @ResponseBody
    @GetMapping("/playerBankAccount/detail")
//    @BusinessLog(title = "用户账号信息_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(PlayerBankAccountParam.detail.class) PlayerBankAccountParam playerBankAccountParam) {
        return new SuccessResponseData(playerBankAccountService.detail(playerBankAccountParam));
    }

    /**
     * 用户账号信息列表
     *
     * <AUTHOR>
     * @date 2022-04-12 19:32:16
     */
    @Permission
    @ResponseBody
    @GetMapping("/playerBankAccount/list")
//    @BusinessLog(title = "用户账号信息_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(PlayerBankAccountParam playerBankAccountParam) {
        return new SuccessResponseData(playerBankAccountService.list(playerBankAccountParam));
    }

}
