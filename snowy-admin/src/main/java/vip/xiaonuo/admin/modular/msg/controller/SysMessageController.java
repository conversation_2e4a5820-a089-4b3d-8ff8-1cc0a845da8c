/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.msg.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.admin.modular.msg.param.SysMessageParam;
import vip.xiaonuo.admin.modular.msg.service.SysMessageService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统消息控制器
 *
 * <AUTHOR>
 * @date 2021-01-21 17:50:51
 */
@Controller
public class SysMessageController {

    private String PATH_PREFIX = "sysMessage/";

    @Resource
    private SysMessageService sysMessageService;

    /**
     * 查询系统消息列表
     *
     * @param sysMessageParam 查询参数
     * <AUTHOR>
     * @date 2021/1/21 17:55
     */
    @ResponseBody
    @GetMapping("/sysMessage/list")
    @BusinessLog(title = "系统消息_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(SysMessageParam sysMessageParam) {
        return new SuccessResponseData(sysMessageService.list(sysMessageParam));
    }

    /**
     * 读取系统消息
     *
     * <AUTHOR>
     * @date 2020/6/28 17:25
     */
    @ResponseBody
    @PostMapping("/sysMessage/read")
    @BusinessLog(title = "系统消息_读取", opType = LogAnnotionOpTypeEnum.DETAIL)
    public void read(@RequestBody @Validated(SysMessageParam.detail.class) List<SysMessageParam> sysMessageParamList) {
       sysMessageService.detail(sysMessageParamList);
    }
}
