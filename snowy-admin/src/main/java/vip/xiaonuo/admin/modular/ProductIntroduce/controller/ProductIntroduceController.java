/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.ProductIntroduce.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.productintroduce.entity.ProductIntroduce;
import vip.xiaonuo.business.productintroduce.param.ProductIntroduceParam;
import vip.xiaonuo.business.productintroduce.result.ProductIntroduceInfo;
import vip.xiaonuo.business.productintroduce.service.ProductIntroduceService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品简介控制器
 *
 * <AUTHOR>
 * @date 2022-04-25 15:27:42
 */
@Controller
public class ProductIntroduceController {

    private String PATH_PREFIX = "productIntroduce/";

    @Resource
    private ProductIntroduceService productIntroduceService;

    /**
     * 产品简介页面
     *
     * <AUTHOR>
     * @date 2022-04-25 15:27:42
     */
    @Permission
    @GetMapping("/productIntroduce/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 产品简介表单页面
     *
     * <AUTHOR>
     * @date 2022-04-25 15:27:42
     */
    @GetMapping("/productIntroduce/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询产品简介
     *
     * <AUTHOR>
     * @date 2022-04-25 15:27:42
     */
    @Permission
    @ResponseBody
    @GetMapping("/productIntroduce/page")
    @BusinessLog(title = "产品简介_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<ProductIntroduce> page(ProductIntroduceParam productIntroduceParam) {
        return productIntroduceService.page(productIntroduceParam);
    }

    /**
     * 添加产品简介
     *
     * <AUTHOR>
     * @date 2022-04-25 15:27:42
     */
    @Permission
    @ResponseBody
    @PostMapping("/productIntroduce/add")
    @BusinessLog(title = "产品简介_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ProductIntroduceParam.add.class) ProductIntroduceParam productIntroduceParam) {
        productIntroduceService.add(productIntroduceParam);
        return new SuccessResponseData();
    }

    /**
     * 删除产品简介
     *
     * <AUTHOR>
     * @date 2022-04-25 15:27:42
     */
    @Permission
    @ResponseBody
    @PostMapping("/productIntroduce/delete")
    @BusinessLog(title = "产品简介_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ProductIntroduceParam.delete.class) List<ProductIntroduceParam> productIntroduceParamList) {
        productIntroduceService.delete(productIntroduceParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑产品简介
     *
     * <AUTHOR>
     * @date 2022-04-25 15:27:42
     */
    @Permission
    @ResponseBody
    @PostMapping("/productIntroduce/edit")
    @BusinessLog(title = "产品简介_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ProductIntroduceParam.edit.class) ProductIntroduceParam productIntroduceParam) {
        productIntroduceService.edit(productIntroduceParam);
        return new SuccessResponseData();
    }

    /**
     * 查看产品简介
     *
     * <AUTHOR>
     * @date 2022-04-25 15:27:42
     */
    @Permission
    @ResponseBody
    @GetMapping("/productIntroduce/detail")
    @BusinessLog(title = "产品简介_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(ProductIntroduceParam.detail.class) ProductIntroduceParam productIntroduceParam) {
        return new SuccessResponseData(productIntroduceService.detail(productIntroduceParam));
    }

    /**
     * 产品简介列表
     *
     * <AUTHOR>
     * @date 2022-04-25 15:27:42
     */
    @Permission
    @ResponseBody
    @GetMapping("/productIntroduce/list")
    @BusinessLog(title = "产品简介_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(ProductIntroduceParam productIntroduceParam) {
        return new SuccessResponseData(productIntroduceService.list(productIntroduceParam));
    }

}
