/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.spotrule.controller;

import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.business.spotrule.entity.SpotRule;
import vip.xiaonuo.business.spotrule.param.SpotRuleParam;
import vip.xiaonuo.business.spotrule.service.SpotRuleService;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.List;

/**
 * 极速下单规则控制器
 *
 * <AUTHOR>
 * @date 2022-12-28 11:21:34
 */
@Controller
public class SpotRuleController {

    private String PATH_PREFIX = "spotRule/";

    @Resource
    private SpotRuleService spotRuleService;

    /**
     * 极速下单规则页面
     *
     * <AUTHOR>
     * @date 2022-12-28 11:21:34
     */
    @Permission
    @GetMapping("/spotRule/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 极速下单规则表单页面
     *
     * <AUTHOR>
     * @date 2022-12-28 11:21:34
     */
    @GetMapping("/spotRule/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询极速下单规则
     *
     * <AUTHOR>
     * @date 2022-12-28 11:21:34
     */
    @Permission
    @ResponseBody
    @GetMapping("/spotRule/page")
    @BusinessLog(title = "极速下单规则_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<SpotRule> page(SpotRuleParam spotRuleParam) {
        return spotRuleService.page(spotRuleParam);
    }

    /**
     * 添加极速下单规则
     *
     * <AUTHOR>
     * @date 2022-12-28 11:21:34
     */
    @Permission
    @ResponseBody
    @PostMapping("/spotRule/add")
    @BusinessLog(title = "极速下单规则_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(SpotRuleParam.add.class) SpotRuleParam spotRuleParam) {
        spotRuleService.add(spotRuleParam);
        return new SuccessResponseData();
    }

    /**
     * 删除极速下单规则
     *
     * <AUTHOR>
     * @date 2022-12-28 11:21:34
     */
    @Permission
    @ResponseBody
    @PostMapping("/spotRule/delete")
    @BusinessLog(title = "极速下单规则_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(SpotRuleParam.delete.class) List<SpotRuleParam> spotRuleParamList) {
        spotRuleService.delete(spotRuleParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑极速下单规则
     *
     * <AUTHOR>
     * @date 2022-12-28 11:21:34
     */
    @Permission
    @ResponseBody
    @PostMapping("/spotRule/edit")
    @BusinessLog(title = "极速下单规则_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(SpotRuleParam.edit.class) SpotRuleParam spotRuleParam) {
        spotRuleService.edit(spotRuleParam);
        return new SuccessResponseData();
    }

    /**
     * 查看极速下单规则
     *
     * <AUTHOR>
     * @date 2022-12-28 11:21:34
     */
    @Permission
    @ResponseBody
    @GetMapping("/spotRule/detail")
    @BusinessLog(title = "极速下单规则_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(SpotRuleParam.detail.class) SpotRuleParam spotRuleParam) {
        return new SuccessResponseData(spotRuleService.detail(spotRuleParam));
    }

    /**
     * 极速下单规则列表
     *
     * <AUTHOR>
     * @date 2022-12-28 11:21:34
     */
    @Permission
    @ResponseBody
    @GetMapping("/spotRule/list")
    @BusinessLog(title = "极速下单规则_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(SpotRuleParam spotRuleParam) {
        return new SuccessResponseData(spotRuleService.list(spotRuleParam));
    }

}
