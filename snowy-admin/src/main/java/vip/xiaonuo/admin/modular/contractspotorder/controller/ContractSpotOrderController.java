/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.contractspotorder.controller;

import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.business.contractspotorder.controller.entity.ContractSpotOrder;
import vip.xiaonuo.business.contractspotorder.controller.param.ContractSpotOrderParam;
import vip.xiaonuo.business.contractspotorder.controller.service.ContractSpotOrderService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.DataScope;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 合约极速订单表控制器
 *
 * <AUTHOR>
 * @date 2024-04-08 20:24:21
 */
@Controller
public class ContractSpotOrderController {

    private String PATH_PREFIX = "contractSpotOrder/";

    @Resource
    private ContractSpotOrderService contractSpotOrderService;

    /**
     * 合约极速订单表页面
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    @Permission
    @GetMapping("/contractSpotOrder/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 合约极速订单表表单页面
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    @GetMapping("/contractSpotOrder/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询合约极速订单表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    @Permission
    @ResponseBody
    @GetMapping("/contractSpotOrder/page")
    @BusinessLog(title = "合约极速订单表_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    @DataScope
    public PageResult<ContractSpotOrder> page(ContractSpotOrderParam contractSpotOrderParam) {
        return contractSpotOrderService.page(contractSpotOrderParam);
    }

    /**
     * 添加合约极速订单表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    @Permission
    @ResponseBody
    @PostMapping("/contractSpotOrder/add")
    @BusinessLog(title = "合约极速订单表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ContractSpotOrderParam.add.class) ContractSpotOrderParam contractSpotOrderParam) {
        contractSpotOrderService.add(contractSpotOrderParam);
        return new SuccessResponseData();
    }

    /**
     * 删除合约极速订单表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    @Permission
    @ResponseBody
    @PostMapping("/contractSpotOrder/delete")
    @BusinessLog(title = "合约极速订单表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ContractSpotOrderParam.delete.class) List<ContractSpotOrderParam> contractSpotOrderParamList) {
        contractSpotOrderService.delete(contractSpotOrderParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑合约极速订单表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    @Permission
    @ResponseBody
    @PostMapping("/contractSpotOrder/edit")
    @BusinessLog(title = "合约极速订单表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ContractSpotOrderParam.edit.class) ContractSpotOrderParam contractSpotOrderParam) {
        contractSpotOrderService.edit(contractSpotOrderParam);
        return new SuccessResponseData();
    }

    /**
     * 查看合约极速订单表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    @Permission
    @ResponseBody
    @GetMapping("/contractSpotOrder/detail")
    @BusinessLog(title = "合约极速订单表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(ContractSpotOrderParam.detail.class) ContractSpotOrderParam contractSpotOrderParam) {
        return new SuccessResponseData(contractSpotOrderService.detail(contractSpotOrderParam));
    }

    /**
     * 合约极速订单表列表
     *
     * <AUTHOR>
     * @date 2024-04-08 20:24:21
     */
    @Permission
    @ResponseBody
    @GetMapping("/contractSpotOrder/list")
    @BusinessLog(title = "合约极速订单表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(ContractSpotOrderParam contractSpotOrderParam) {
        return new SuccessResponseData(contractSpotOrderService.list(contractSpotOrderParam));
    }

    @Permission(value = "contractSpotOrder:editControl")
    @ResponseBody
    @GetMapping("/contractSpotOrder/editControl")
    @BusinessLog(title = "现货订单_编辑control", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData editControl(@RequestParam Long id, @RequestParam Integer control) {
        contractSpotOrderService.editControl(id, control);
        return new SuccessResponseData();
    }


}
