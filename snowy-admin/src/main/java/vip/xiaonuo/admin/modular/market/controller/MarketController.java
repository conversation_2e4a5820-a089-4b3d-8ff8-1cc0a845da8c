/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.market.controller;

import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.business.productintroduce.param.ProductIntroduceParam;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.business.market.entity.Market;
import vip.xiaonuo.business.market.param.MarketParam;
import vip.xiaonuo.business.market.service.MarketService;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 市场控制器
 *
 * <AUTHOR>
 * @date 2022-04-11 10:55:42
 */
@Controller
public class MarketController {

    private String PATH_PREFIX = "market/";

    @Resource
    private MarketService marketService;

    /**
     * 市场页面
     *
     * <AUTHOR>
     * @date 2022-04-11 10:55:42
     */
    @Permission
    @GetMapping("/market/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 市场表单页面
     *
     * <AUTHOR>
     * @date 2022-04-11 10:55:42
     */
    @GetMapping("/market/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }


    /**
     * 市场调整页面
     *
     * <AUTHOR>
     * @date 2022-04-11 10:55:42
     */
    @GetMapping("/market/adjustForm")
    public String adjustForm() {
        return PATH_PREFIX + "adjustForm.html";
    }

    /**
     * 查询市场
     *
     * <AUTHOR>
     * @date 2022-04-11 10:55:42
     */
    @Permission
    @ResponseBody
    @GetMapping("/market/page")
    @BusinessLog(title = "市场_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<Market> page(MarketParam marketParam) {
        return marketService.page(marketParam);
    }

    /**
     * 添加市场
     *
     * <AUTHOR>
     * @date 2022-04-11 10:55:42
     */
    @Permission
    @ResponseBody
    @PostMapping("/market/add")
    @BusinessLog(title = "市场_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(MarketParam.add.class) MarketParam marketParam) {
        marketService.add(marketParam);
        return new SuccessResponseData();
    }

    /**
     * 删除市场
     *
     * <AUTHOR>
     * @date 2022-04-11 10:55:42
     */
    @Permission
    @ResponseBody
    @PostMapping("/market/delete")
    @BusinessLog(title = "市场_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(MarketParam.delete.class) List<MarketParam> marketParamList) {
        marketService.delete(marketParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑市场
     *
     * <AUTHOR>
     * @date 2022-04-11 10:55:42
     */
    @Permission
    @ResponseBody
    @PostMapping("/market/edit")
    @BusinessLog(title = "市场_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(MarketParam.edit.class) MarketParam marketParam) {
        marketService.edit(marketParam);
        return new SuccessResponseData();
    }

    @ResponseBody
    @PostMapping("/market/editStatus")
    public ResponseData editStatus(@RequestBody MarketParam marketParam) {
        marketService.edit(marketParam);
        return new SuccessResponseData();
    }

    /**
     * 查看市场
     *
     * <AUTHOR>
     * @date 2022-04-11 10:55:42
     */
    @Permission
    @ResponseBody
    @GetMapping("/market/detail")
    @BusinessLog(title = "市场_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(MarketParam.detail.class) MarketParam marketParam) {
        return new SuccessResponseData(marketService.detail(marketParam));
    }

    /**
     * 市场列表
     *
     * <AUTHOR>
     * @date 2022-04-11 10:55:42
     */
    @Permission
    @ResponseBody
    @GetMapping("/market/list")
    @BusinessLog(title = "市场_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(MarketParam marketParam) {
        return new SuccessResponseData(marketService.list(marketParam));
    }

    @ResponseBody
    @GetMapping("/market/getLatestPrice")
    public ResponseData getLatestPrice(@RequestParam String symbol) {
        return new SuccessResponseData(marketService.getLatestPrice(symbol));
    }


}
