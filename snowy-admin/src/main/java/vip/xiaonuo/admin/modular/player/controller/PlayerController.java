/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.player.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.business.context.login.AdminLoginContext;
import vip.xiaonuo.business.currency.service.CurrencyService;
import vip.xiaonuo.business.player.entity.Player;
import vip.xiaonuo.business.player.param.PlayerParam;
import vip.xiaonuo.business.player.result.PlayerResult;
import vip.xiaonuo.business.player.service.PlayerService;
import vip.xiaonuo.business.pojo.sysuser.SysLoginUser;
import vip.xiaonuo.business.spotorder.service.SpotScaleHelper;
import vip.xiaonuo.business.sysconfig.entity.SysConfig;
import vip.xiaonuo.business.sysconfig.service.SysConfigService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.DataScope;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.consts.CommonConstant;
import vip.xiaonuo.core.context.constant.ConstantContext;
import vip.xiaonuo.core.context.constant.ConstantContextHolder;
import vip.xiaonuo.core.context.login.LoginContextHolder;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.core.util.Hmac;
import vip.xiaonuo.session.security.UserSessionService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 玩家控制器
 *
 * <AUTHOR>
 * @date 2022-04-04 14:31:45
 */
@Controller
public class PlayerController {
    private Logger logger = LoggerFactory.getLogger(PlayerController.class);
    private String PATH_PREFIX = "player/";

    @Resource
    private PlayerService playerService;
    @Resource
    private SysConfigService sysConfigService;
    @Resource
    private SpotScaleHelper spotScaleHelper;
    @Resource
    private CurrencyService currencyService;
    @Resource
    private UserSessionService userSessionService;

    /**
     * 玩家页面
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    @Permission
    @GetMapping("/player/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    @GetMapping("/player/risk_control")
    public String risk_control(Model model) {
        model.addAttribute(CommonConstant.RISK_CONTROL_SPOT_SCALE, ConstantContextHolder.getRiskControlSpotScale());
        return PATH_PREFIX + "risk_control.html";
    }

    /**
     * 玩家表单页面
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    @GetMapping("/player/form")
    public String form(HttpServletRequest request, Model model) {
        model.addAttribute("isAdd", request.getParameter("isAdd"));
        return PATH_PREFIX + "form.html";
    }

    @GetMapping("/player/toAuthStatus")
    public String toAuthStatus(HttpServletRequest request, Model model) {
        return PATH_PREFIX + "authStatus.html";
    }


    @GetMapping("/player/editPwdForm")
    public String editPwdForm(HttpServletRequest request, Model model) {
        return PATH_PREFIX + "editPwdForm.html";
    }

    @GetMapping("/player/editFundsPwdForm")
    public String editFundsPwdForm(HttpServletRequest request, Model model) {
        return PATH_PREFIX + "editFundsPwdForm.html";
    }

    @GetMapping("/player/changeAgentForm")
    public String changeAgentForm(HttpServletRequest request, Model model) {
        return PATH_PREFIX + "changeAgentForm.html";
    }

    @GetMapping("/player/queryAccount")
    public String queryAccount(HttpServletRequest request, Model model) {
        model.addAttribute("playerId", request.getParameter("playerId"));
        return PATH_PREFIX + "queryAccount.html";
    }

    @GetMapping("/player/manualDeposit")
    public String manualDeposit(HttpServletRequest request, Model model) {
        model.addAttribute("playerId", request.getParameter("playerId"));
        model.addAttribute("userAccount", request.getParameter("userAccount"));
        model.addAttribute("chargeCurrencyList", currencyService.getChargeCurrencyList());
        return PATH_PREFIX + "manualDeposit.html";
    }

    @ResponseBody
    @GetMapping("/player/changeRole")
    public ResponseData changeRole(@RequestParam Long id, @RequestParam String role) {
        playerService.lambdaUpdate().set(Player::getRole, role).eq(Player::getId, id).update();
        return new SuccessResponseData();
    }


    /**
     * 查询玩家
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    @Permission
    @ResponseBody
    @GetMapping("/player/page")
//    @BusinessLog(title = "玩家_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    @DataScope
    public PageResult<PlayerResult> page(PlayerParam playerParam) {
        return playerService.pageResult(playerParam);
    }

    /**
     * 添加玩家
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    @Permission
    @ResponseBody
    @PostMapping("/player/add")
    @BusinessLog(title = "玩家_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(PlayerParam.add.class) PlayerParam playerParam, HttpServletRequest request) {
        String ip = ServletUtil.getClientIP(request);
        //登录的管理员账号
        SysLoginUser loginUser = LoginContextHolder.me(AdminLoginContext.class).getSysLoginUser();
        String orgCode = LoginContextHolder.me(AdminLoginContext.class).getLoginUserOrgCode();
        if (StrUtil.isEmpty(orgCode)) {
            return ResponseData.error("没找到机构代码");
        }
        playerParam.setAgentId(loginUser.getId());
        playerParam.setRegisterIp(ip);
        playerParam.setOrgCode(orgCode);
        playerParam.setPhoneCode("84");
        playerService.add(playerParam);
        return new SuccessResponseData();
    }

    /**
     * 删除玩家
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    @Permission
    @ResponseBody
    @PostMapping("/player/delete")
    @BusinessLog(title = "玩家_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody List<PlayerParam> playerParamList) {
        playerService.delete(playerParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑玩家
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    @Permission
    @ResponseBody
    @PostMapping("/player/edit")
    @BusinessLog(title = "玩家_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(PlayerParam.edit.class) PlayerParam playerParam) {
        playerService.edit(playerParam);
        return new SuccessResponseData();
    }

    @Permission(value = "player:spotScaleEdit")
    @ResponseBody
    @GetMapping("/player/editSpotScale")
    @BusinessLog(title = "玩家_编辑SpotScale", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData editSpotScale(@RequestParam Long id, @RequestParam Integer spotScale) {
        playerService.editSpotScale(id, spotScale);
        return new SuccessResponseData();
    }

    @Permission(value = "risk_control:spotScaleEdit")
    @ResponseBody
    @PostMapping("/player/editRiskControlSpotScale")
    @Transactional
    @BusinessLog(title = "风控-极速比例", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData editRiskControlSpotScale(@RequestBody String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        logger.info("风控-极速比例{}", jsonObject);
        String spotScale = jsonObject.getString("spotScale");
        if (StringUtils.isNotEmpty(spotScale)) {
            SysConfig sysConfig = sysConfigService.lambdaQuery().eq(SysConfig::getCode, CommonConstant.RISK_CONTROL_SPOT_SCALE).one();
            if (sysConfig == null) {
                sysConfig = new SysConfig();
                sysConfig.setName("现货风控概率");
                sysConfig.setCode(CommonConstant.RISK_CONTROL_SPOT_SCALE);
                sysConfig.setSysFlag("Y");
                sysConfig.setStatus(0);
                sysConfig.setGroupCode("DEFAULT");
            }
            sysConfig.setValue(spotScale);
            sysConfigService.saveOrUpdate(sysConfig);
            ConstantContext.me().put(CommonConstant.RISK_CONTROL_SPOT_SCALE, spotScale);
            spotScaleHelper.initRiskControl(spotScale);
        }
        return new SuccessResponseData();
    }


    @Permission
    @ResponseBody
    @PostMapping("/player/editPwd")
    @BusinessLog(title = "玩家_重置登录密码", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData editPwd(@RequestBody @Validated PlayerParam playerParam) {
        Player player = playerService.getById(playerParam.getId());
        String pwd = Hmac.md5EncryptSalt(playerParam.getPassword(), player.getSalt());
        playerService.lambdaUpdate().set(Player::getPassword, pwd).eq(Player::getId, player.getId()).update();
        return new SuccessResponseData();
    }

    @Permission
    @ResponseBody
    @PostMapping("/player/editFundsPwd")
    @BusinessLog(title = "玩家_重置提款密码", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData editFundsPwd(@RequestBody @Validated PlayerParam playerParam) {
        Player player = playerService.getById(playerParam.getId());
        String pwd = Hmac.md5EncryptSalt(playerParam.getFundsPassword(), player.getSalt());
        playerService.lambdaUpdate().set(Player::getFundsPassword, pwd).eq(Player::getId, player.getId()).update();
        return new SuccessResponseData();
    }

    @Permission
    @ResponseBody
    @PostMapping("/player/changeAgent")
    @BusinessLog(title = "玩家_变更代理人", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData changeAgent(@RequestBody PlayerParam playerParam) {
        Player player = playerService.getById(playerParam.getId());
        if (playerParam.getAgentId() == null) {
            return ResponseData.error("代理人必选");
        }
        playerService.lambdaUpdate().set(Player::getAgentId, playerParam.getAgentId()).eq(Player::getId, player.getId()).update();
        return new SuccessResponseData();
    }

    @Permission
    @ResponseBody
    @PostMapping("/player/forbidTrade")
    @BusinessLog(title = "玩家_允许/禁止交易", opType = LogAnnotionOpTypeEnum.FORBID_TRADE)
    public ResponseData forbidTrade(@RequestBody PlayerParam playerParam) {
        Player player = playerService.getById(playerParam.getId());
        playerService.lambdaUpdate().set(Player::getIsForbidTrade, playerParam.getIsForbidTrade()).eq(Player::getId, player.getId()).update();
        return new SuccessResponseData();
    }

    @ResponseBody
    @PostMapping("/player/logout")
    @BusinessLog(title = "玩家_强制用户退出登录", opType = LogAnnotionOpTypeEnum.LOGOUT)
    public ResponseData logout(@RequestBody PlayerParam playerParam) {
        if (ObjectUtils.isNotEmpty(playerParam.getId())) {
            userSessionService.deleteUserSessionByUserId(playerParam.getId());
        }
        return new SuccessResponseData();
    }

    /**
     * 查看玩家
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    @Permission
    @ResponseBody
    @GetMapping("/player/detail")
//    @BusinessLog(title = "玩家_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(PlayerParam.detail.class) PlayerParam playerParam) {
        return new SuccessResponseData(playerService.detail(playerParam));
    }

    /**
     * 玩家列表
     *
     * <AUTHOR>
     * @date 2022-04-04 14:31:45
     */
    @Permission
    @ResponseBody
    @GetMapping("/player/list")
//    @BusinessLog(title = "玩家_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(PlayerParam playerParam) {
        return new SuccessResponseData(playerService.list(playerParam));
    }

}
