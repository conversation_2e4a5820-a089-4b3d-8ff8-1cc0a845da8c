/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.bank.controller;

import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;
import vip.xiaonuo.business.bank.entity.Bank;
import vip.xiaonuo.business.bank.param.BankParam;
import vip.xiaonuo.business.bank.service.BankService;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.List;

/**
 * 银行管理控制器
 *
 * <AUTHOR>
 * @date 2022-10-24 16:58:05
 */
@Controller
public class BankController {

    private String PATH_PREFIX = "bank/";

    @Resource
    private BankService bankService;

    /**
     * 银行管理页面
     *
     * <AUTHOR>
     * @date 2022-10-24 16:58:05
     */
    @Permission
    @GetMapping("/bank/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 银行管理表单页面
     *
     * <AUTHOR>
     * @date 2022-10-24 16:58:05
     */
    @GetMapping("/bank/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询银行管理
     *
     * <AUTHOR>
     * @date 2022-10-24 16:58:05
     */
    @Permission
    @ResponseBody
    @GetMapping("/bank/page")
    @BusinessLog(title = "银行管理_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<Bank> page(BankParam bankParam) {
        return bankService.page(bankParam);
    }

    /**
     * 添加银行管理
     *
     * <AUTHOR>
     * @date 2022-10-24 16:58:05
     */
    @Permission
    @ResponseBody
    @PostMapping("/bank/add")
    @BusinessLog(title = "银行管理_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(BankParam.add.class) BankParam bankParam) {
        bankService.add(bankParam);
        return new SuccessResponseData();
    }

    /**
     * 删除银行管理
     *
     * <AUTHOR>
     * @date 2022-10-24 16:58:05
     */
    @Permission
    @ResponseBody
    @PostMapping("/bank/delete")
    @BusinessLog(title = "银行管理_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(BankParam.delete.class) List<BankParam> bankParamList) {
        bankService.delete(bankParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑银行管理
     *
     * <AUTHOR>
     * @date 2022-10-24 16:58:05
     */
    @Permission
    @ResponseBody
    @PostMapping("/bank/edit")
    @BusinessLog(title = "银行管理_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(BankParam.edit.class) BankParam bankParam) {
        bankService.edit(bankParam);
        return new SuccessResponseData();
    }

    /**
     * 查看银行管理
     *
     * <AUTHOR>
     * @date 2022-10-24 16:58:05
     */
    @Permission
    @ResponseBody
    @GetMapping("/bank/detail")
    @BusinessLog(title = "银行管理_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(BankParam.detail.class) BankParam bankParam) {
        return new SuccessResponseData(bankService.detail(bankParam));
    }

    /**
     * 银行管理列表
     *
     * <AUTHOR>
     * @date 2022-10-24 16:58:05
     */
    @Permission
    @ResponseBody
    @GetMapping("/bank/list")
    @BusinessLog(title = "银行管理_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(BankParam bankParam) {
        return new SuccessResponseData(bankService.list(bankParam));
    }

}
