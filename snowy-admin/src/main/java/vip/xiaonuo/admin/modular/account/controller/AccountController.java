/*
Copyright [2020] [https://www.xiaonuo.vip]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：

1.请不要删除和修改根目录下的LICENSE文件。
2.请不要删除和修改Snowy源码头部的版权声明。
3.请保留源码和相关描述文件的项目出处，作者声明等。
4.分发源码时候，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/xiaonuobase/snowy-layui
6.若您的项目无法满足以上几点，可申请商业授权，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.admin.modular.account.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import vip.xiaonuo.business.account.entity.Account;
import vip.xiaonuo.business.account.param.AccountParam;
import vip.xiaonuo.business.account.param.EditBalanceParam;
import vip.xiaonuo.business.account.service.AccountService;
import vip.xiaonuo.core.annotion.BusinessLog;
import vip.xiaonuo.core.annotion.Permission;
import vip.xiaonuo.core.enums.LogAnnotionOpTypeEnum;
import vip.xiaonuo.core.pojo.page.PageResult;
import vip.xiaonuo.core.pojo.response.ResponseData;
import vip.xiaonuo.core.pojo.response.SuccessResponseData;

import javax.annotation.Resource;
import java.util.List;

/**
 * 账户表控制器
 *
 * <AUTHOR>
 * @date 2022-04-06 13:49:40
 */
@Controller
public class AccountController {

    private String PATH_PREFIX = "account/";

    @Resource
    private AccountService accountService;

    /**
     * 账户表页面
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    @Permission
    @GetMapping("/account/index")
    public String index() {
        return PATH_PREFIX + "index.html";
    }

    /**
     * 账户表表单页面
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    @GetMapping("/account/form")
    public String form() {
        return PATH_PREFIX + "form.html";
    }

    /**
     * 查询账户表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    @Permission
    @ResponseBody
    @GetMapping("/account/page")
    @BusinessLog(title = "账户表_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public PageResult<Account> page(AccountParam accountParam) {
        return accountService.page(accountParam);
    }

    /**
     * 添加账户表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    @Permission
    @ResponseBody
    @PostMapping("/account/add")
    @BusinessLog(title = "账户表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(AccountParam.add.class) AccountParam accountParam) {
        accountService.add(accountParam);
        return new SuccessResponseData();
    }

    /**
     * 删除账户表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    @Permission
    @ResponseBody
    @PostMapping("/account/delete")
    @BusinessLog(title = "账户表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(AccountParam.delete.class) List<AccountParam> accountParamList) {
        accountService.delete(accountParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑账户表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    @Permission
    @ResponseBody
    @PostMapping("/account/edit")
    @BusinessLog(title = "账户表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(AccountParam.edit.class) AccountParam accountParam) {
        accountService.edit(accountParam);
        return new SuccessResponseData();
    }

    /**
     * 查看账户表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    @Permission
    @ResponseBody
    @GetMapping("/account/detail")
    @BusinessLog(title = "账户表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(AccountParam.detail.class) AccountParam accountParam) {
        return new SuccessResponseData(accountService.detail(accountParam));
    }

    /**
     * 账户表列表
     *
     * <AUTHOR>
     * @date 2022-04-06 13:49:40
     */
    @Permission
    @ResponseBody
    @GetMapping("/account/list")
    @BusinessLog(title = "账户表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(AccountParam accountParam) {
        return new SuccessResponseData(accountService.list(accountParam));
    }

    @Permission(value = "player:manualDeposit")
    @ResponseBody
    @PostMapping("/account/editBalance")
    @BusinessLog(title = "账户_人为变更余额", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData editBalance(@RequestBody EditBalanceParam editBalanceParam) {
        QueryWrapper<Account> accountWrapper = new QueryWrapper<>();
        accountWrapper.lambda().eq(Account::getPlayerId, editBalanceParam.getPlayerId());
        accountWrapper.lambda().eq(Account::getType, editBalanceParam.getType());
        accountWrapper.lambda().eq(Account::getCurrencyCode, editBalanceParam.getCurrencyCode());
        Account account = accountService.getOne(accountWrapper);
        if (account == null) {
            return ResponseData.error(500, "账户不存在");
        }

        if ("balance".equalsIgnoreCase(editBalanceParam.getField())) {
            if (editBalanceParam.getValue().compareTo(account.getBalance()) > 0) {
                return ResponseData.error(500, account.getCurrencyCode() + "账户余额不能大于" + account.getBalance().toPlainString());
            }
            //edit
            accountService.lambdaUpdate().set(Account::getBalance, editBalanceParam.getValue()).eq(Account::getId, account.getId()).update();
        }
        if ("frozenBalance".equalsIgnoreCase(editBalanceParam.getField())) {
            //冻结账户余额不能动，去取消订单，冻结账户余额自动减少，会回到余额中，再从余额中扣除
        }
        return new SuccessResponseData(true);
    }

}
