package vip.xiaonuo.admin.core.config;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;
import vip.xiaonuo.business.config.WebMvcConfig;
import vip.xiaonuo.business.core.error.SnowyErrorAttributes;
import vip.xiaonuo.business.core.error.SnowyErrorView;
import vip.xiaonuo.business.core.filter.xss.XssFilter;
import vip.xiaonuo.business.core.validator.SnowyValidator;
import vip.xiaonuo.core.web.SnowyRequestResponseBodyMethodProcessor;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Configuration
public class AdminWebMvcConfig extends WebMvcConfig {
    /**
     * 错误信息提示重写
     *
     * <AUTHOR>
     * @date 2020/4/14 22:27
     */
    @Bean
    public SnowyErrorAttributes snowyErrorAttributes() {
        return new SnowyErrorAttributes();
    }

    /**
     * 默认错误页面
     *
     * <AUTHOR>
     * @date 2020/4/14 22:27
     */
    @Bean("error")
    public SnowyErrorView snowyErrorView() {
        return new SnowyErrorView();
    }

    /**
     * xss过滤器
     *
     * <AUTHOR>
     * @date 2020/6/21 10:30
     */
    @Bean
    public FilterRegistrationBean<XssFilter> xssFilterFilterRegistrationBean() {
        FilterRegistrationBean<XssFilter> registration = new FilterRegistrationBean<>(new XssFilter());
        registration.addUrlPatterns("/*");
        return registration;
    }

    /**
     * 自定义的spring参数校验器，重写主要为了保存一些在自定义validator中读不到的属性
     *
     * <AUTHOR>
     * @date 2020/8/12 20:18
     */
    @Bean
    public SnowyValidator snowyValidator() {
        return new SnowyValidator();
    }


    /**
     * 自定义的SnowyRequestResponseBodyMethodProcessor，放在所有resolvers之前
     *
     * <AUTHOR>
     * @date 2020/8/21 21:09
     */
    @Configuration
    public static class MethodArgumentResolver {

        @Resource
        private RequestMappingHandlerAdapter adapter;

        @PostConstruct
        public void injectSelfMethodArgumentResolver() {
            List<HandlerMethodArgumentResolver> argumentResolvers = new ArrayList<>();
            argumentResolvers.add(new SnowyRequestResponseBodyMethodProcessor(adapter.getMessageConverters()));
            argumentResolvers.addAll(Objects.requireNonNull(adapter.getArgumentResolvers()));
            adapter.setArgumentResolvers(argumentResolvers);
        }
    }
}
