package vip.xiaonuo;

import cn.hutool.log.Log;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.ApplicationPidFileWriter;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import vip.xiaonuo.admin.core.config.SpringSecurityConfig;
import vip.xiaonuo.business.auth.context.LoginContextSpringSecurityImpl;
import vip.xiaonuo.business.core.filter.security.JwtAuthenticationTokenFilter;
import vip.xiaonuo.business.core.filter.security.entrypoint.JwtAuthenticationEntryPoint;
import vip.xiaonuo.business.core.listener.ResourceCollectListener;
import vip.xiaonuo.core.util.PathUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * SpringBoot方式启动类
 *
 * <AUTHOR>
 * @date 2017/5/21 12:06
 */
@SpringBootApplication
@EnableAsync
public class SnowyAdminApplication {

    private static final Log log = Log.get();

    public static void main(String[] args) throws UnknownHostException {
        SpringApplication springApplication = new SpringApplication(SnowyAdminApplication.class);
        springApplication.addListeners(new ApplicationPidFileWriter(PathUtils.getPidPath(SnowyAdminApplication.class)));
        ConfigurableApplicationContext application = springApplication.run(args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        log.info("[{}:{}]启动成功", ip, port);
    }

    @Bean
    public LoginContextSpringSecurityImpl loginContextSpringSecurity() {
        return new LoginContextSpringSecurityImpl();
    }

    @Bean
    public SpringSecurityConfig springSecurityConfig() {
        return new SpringSecurityConfig();
    }

    @Bean
    public ResourceCollectListener resourceCollectListener() {
        return new ResourceCollectListener();
    }

    @Bean
    public JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint() {
        return new JwtAuthenticationEntryPoint();
    }

    @Bean
    public JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter() {
        return new JwtAuthenticationTokenFilter();
    }
}
