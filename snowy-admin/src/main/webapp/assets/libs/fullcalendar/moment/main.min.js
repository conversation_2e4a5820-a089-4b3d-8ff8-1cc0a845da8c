/*!
FullCalendar Moment Plugin v4.3.0
Docs & License: https://fullcalendar.io/
(c) 2019 <PERSON>
*/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("moment"),require("@fullcalendar/core")):"function"==typeof define&&define.amd?define(["exports","moment","@fullcalendar/core"],t):t((e=e||self).FullCalendarMoment={},e.moment,e.FullCalendar)}(this,function(e,t,n){"use strict";var r=t;var a=n.createPlugin({cmdFormatter:function(e,t){var n=function e(t){var n=t.match(/^(.*?)\{(.*)\}(.*)$/);if(n){var r=e(n[2]);return{head:n[1],middle:r,tail:n[3],whole:n[1]+r.whole+n[3]}}return{head:null,middle:null,tail:null,whole:t}}(e);if(t.end){var r=l(t.start.array,t.timeZone,t.start.timeZoneOffset,t.localeCodes[0]),a=l(t.end.array,t.timeZone,t.end.timeZoneOffset,t.localeCodes[0]);return function e(t,n,r,a){if(t.middle){var o=n(t.head),l=e(t.middle,n,r,a),u=n(t.tail),i=r(t.head),d=e(t.middle,n,r,a),f=r(t.tail);if(o===i&&u===f)return o+(l===d?l:l+a+d)+u}var c=n(t.whole),m=r(t.whole);return c===m?c:c+a+m}(n,o(r),o(a),t.separator)}return l(t.date.array,t.timeZone,t.date.timeZoneOffset,t.localeCodes[0]).format(n.whole)}});function o(e){return function(t){return t?e.format(t):""}}function l(e,t,n,a){var o;return"local"===t?o=r(e):"UTC"===t?o=r.utc(e):r.tz?o=r.tz(e,t):(o=r.utc(e),null!=n&&o.utcOffset(n)),o.locale(a),o}e.default=a,e.toDuration=function(e){return r.duration(e)},e.toMoment=function(e,t){if(!(t instanceof n.Calendar))throw new Error("must supply a Calendar instance");return l(e,t.dateEnv.timeZone,null,t.dateEnv.locale.codes[0])},Object.defineProperty(e,"__esModule",{value:!0})});