(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
    typeof define === 'function' && define.amd ? define(factory) :
    (global = global || self, (global.FullCalendarLocales = global.FullCalendarLocales || {}, global.FullCalendarLocales.hr = factory()));
}(this, function () { 'use strict';

    var hr = {
        code: "hr",
        week: {
            dow: 1,
            doy: 7 // The week that contains Jan 1st is the first week of the year.
        },
        buttonText: {
            prev: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            next: "<PERSON>ljedeć<PERSON>",
            today: "<PERSON><PERSON>",
            month: "<PERSON>jese<PERSON>",
            week: "Tje<PERSON>",
            day: "Dan",
            list: "Raspored"
        },
        weekLabel: "Tje",
        allDayText: "Ci<PERSON>li dan",
        eventLimitText: function (n) {
            return "+ još " + n;
        },
        noEventsMessage: "Nema događaja za prikaz"
    };

    return hr;

}));
