# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Trang trÆ°á»c
previous_label=TrÆ°á»c
next.title=Trang Sau
next_label=Tiáº¿p

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Trang
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=trÃªn {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} trÃªn {{pagesCount}})

zoom_out.title=Thu nhá»
zoom_out_label=Thu nhá»
zoom_in.title=PhÃ³ng to
zoom_in_label=PhÃ³ng to
zoom.title=Thu phÃ³ng
presentation_mode.title=Chuyá»n sang cháº¿ Äá» trÃ¬nh chiáº¿u
presentation_mode_label=Cháº¿ Äá» trÃ¬nh chiáº¿u
open_file.title=Má» táº­p tin
open_file_label=Má» táº­p tin
print.title=In
print_label=In
download.title=Táº£i xuá»ng
download_label=Táº£i xuá»ng
bookmark.title=Cháº¿ Äá» xem hiá»n táº¡i (sao chÃ©p hoáº·c má» trong cá»­a sá» má»i)
bookmark_label=Cháº¿ Äá» xem hiá»n táº¡i

# Secondary toolbar and context menu
tools.title=CÃ´ng cá»¥
tools_label=CÃ´ng cá»¥
first_page.title=Vá» trang Äáº§u
first_page.label=Vá» trang Äáº§u
first_page_label=Vá» trang Äáº§u
last_page.title=Äáº¿n trang cuá»i
last_page.label=Äáº¿n trang cuá»i
last_page_label=Äáº¿n trang cuá»i
page_rotate_cw.title=Xoay theo chiá»u kim Äá»ng há»
page_rotate_cw.label=Xoay theo chiá»u kim Äá»ng há»
page_rotate_cw_label=Xoay theo chiá»u kim Äá»ng há»
page_rotate_ccw.title=Xoay ngÆ°á»£c chiá»u kim Äá»ng há»
page_rotate_ccw.label=Xoay ngÆ°á»£c chiá»u kim Äá»ng há»
page_rotate_ccw_label=Xoay ngÆ°á»£c chiá»u kim Äá»ng há»

cursor_text_select_tool.title=KÃ­ch hoáº¡t cÃ´ng cá»¥ chá»n vÃ¹ng vÄn báº£n
cursor_text_select_tool_label=CÃ´ng cá»¥ chá»n vÃ¹ng vÄn báº£n
cursor_hand_tool.title=KÃ­ch hoáº¡t cÃ´ng cá»¥ con trá»
cursor_hand_tool_label=CÃ´ng cá»¥ con trá»

scroll_vertical.title=Sá»­ dá»¥ng cuá»n dá»c
scroll_vertical_label=Cuá»n dá»c
scroll_horizontal.title=Sá»­ dá»¥ng cuá»n ngang
scroll_horizontal_label=Cuá»n ngang
scroll_wrapped.title=Sá»­ dá»¥ng cuá»n ngáº¯t dÃ²ng
scroll_wrapped_label=Cuá»n ngáº¯t dÃ²ng

spread_none.title=KhÃ´ng ná»i rá»ng trang
spread_none_label=KhÃ´ng cÃ³ phÃ¢n cÃ¡ch
spread_odd.title=Ná»i trang bÃ i báº¯t Äáº§u vá»i cÃ¡c trang ÄÆ°á»£c ÄÃ¡nh sá» láº»
spread_odd_label=PhÃ¢n cÃ¡ch theo sá» láº»
spread_even.title=Ná»i trang bÃ i báº¯t Äáº§u vá»i cÃ¡c trang ÄÆ°á»£c ÄÃ¡nh sá» cháºµn
spread_even_label=PhÃ¢n cÃ¡ch theo sá» cháºµn

# Document properties dialog box
document_properties.title=Thuá»c tÃ­nh cá»§a tÃ i liá»uâ¦
document_properties_label=Thuá»c tÃ­nh cá»§a tÃ i liá»uâ¦
document_properties_file_name=TÃªn táº­p tin:
document_properties_file_size=KÃ­ch thÆ°á»c:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} byte)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} byte)
document_properties_title=TiÃªu Äá»:
document_properties_author=TÃ¡c giáº£:
document_properties_subject=Chá»§ Äá»:
document_properties_keywords=Tá»« khÃ³a:
document_properties_creation_date=NgÃ y táº¡o:
document_properties_modification_date=NgÃ y sá»­a Äá»i:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=NgÆ°á»i táº¡o:
document_properties_producer=Pháº§n má»m táº¡o PDF:
document_properties_version=PhiÃªn báº£n PDF:
document_properties_page_count=Tá»ng sá» trang:
document_properties_page_size=KÃ­ch thÆ°á»c trang:
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=khá» dá»c
document_properties_page_size_orientation_landscape=khá» ngang
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=ThÆ°
document_properties_page_size_name_legal=PhÃ¡p lÃ½
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Xem nhanh trÃªn web:
document_properties_linearized_yes=CÃ³
document_properties_linearized_no=KhÃ´ng
document_properties_close=ÃÃ³ng

print_progress_message=Chuáº©n bá» trang Äá» inâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Há»§y bá»

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Báº­t/Táº¯t thanh lá»
toggle_sidebar_notification.title=Báº­t táº¯t thanh lá» (tÃ i liá»u bao gá»m báº£n phÃ¡c tháº£o/táº­p tin ÄÃ­nh kÃ¨m)
toggle_sidebar_label=Báº­t/Táº¯t thanh lá»
document_outline.title=Hiá»n tÃ i liá»u phÃ¡c tháº£o (nháº¥p ÄÃºp vÃ o Äá» má» rá»ng/thu gá»n táº¥t cáº£ cÃ¡c má»¥c)
document_outline_label=Báº£n phÃ¡c tÃ i liá»u
attachments.title=Hiá»n ná»i dung ÄÃ­nh kÃ¨m
attachments_label=Ná»i dung ÄÃ­nh kÃ¨m
thumbs.title=Hiá»n thá» áº£nh thu nhá»
thumbs_label=áº¢nh thu nhá»
findbar.title=TÃ¬m trong tÃ i liá»u
findbar_label=TÃ¬m

# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=Trang {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Trang {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=áº¢nh thu nhá» cá»§a trang {{page}}

# Find panel button title and messages
find_input.title=TÃ¬m
find_input.placeholder=TÃ¬m trong tÃ i liá»uâ¦
find_previous.title=TÃ¬m cá»¥m tá»« á» pháº§n trÆ°á»c
find_previous_label=TrÆ°á»c
find_next.title=TÃ¬m cá»¥m tá»« á» pháº§n sau
find_next_label=Tiáº¿p
find_highlight=TÃ´ sÃ¡ng táº¥t cáº£
find_match_case_label=PhÃ¢n biá»t hoa, thÆ°á»ng
find_entire_word_label=ToÃ n bá» tá»«
find_reached_top=ÄÃ£ Äáº¿n pháº§n Äáº§u tÃ i liá»u, quay trá» láº¡i tá»« cuá»i
find_reached_bottom=ÄÃ£ Äáº¿n pháº§n cuá»i cá»§a tÃ i liá»u, quay trá» láº¡i tá»« Äáº§u
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} cá»§a {{total}} ÄÃ£ trÃ¹ng
find_match_count[two]={{current}} cá»§a {{total}} ÄÃ£ trÃ¹ng
find_match_count[few]={{current}} cá»§a {{total}} ÄÃ£ trÃ¹ng
find_match_count[many]={{current}} cá»§a {{total}} ÄÃ£ trÃ¹ng
find_match_count[other]={{current}} cá»§a {{total}} ÄÃ£ trÃ¹ng
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Nhiá»u hÆ¡n {{limit}} ÄÃ£ trÃ¹ng
find_match_count_limit[one]=Nhiá»u hÆ¡n {{limit}} ÄÃ£ trÃ¹ng
find_match_count_limit[two]=Nhiá»u hÆ¡n {{limit}} ÄÃ£ trÃ¹ng
find_match_count_limit[few]=Nhiá»u hÆ¡n {{limit}} ÄÃ£ trÃ¹ng
find_match_count_limit[many]=Nhiá»u hÆ¡n {{limit}} ÄÃ£ trÃ¹ng
find_match_count_limit[other]=Nhiá»u hÆ¡n {{limit}} ÄÃ£ trÃ¹ng
find_not_found=KhÃ´ng tÃ¬m tháº¥y cá»¥m tá»« nÃ y

# Error panel labels
error_more_info=ThÃ´ng tin thÃªm
error_less_info=Hiá»n thá» Ã­t thÃ´ng tin hÆ¡n
error_close=ÄÃ³ng
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ThÃ´ng Äiá»p: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stack: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Táº­p tin: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=DÃ²ng: {{line}}
rendering_error=Lá»i khi hiá»n thá» trang.

# Predefined zoom values
page_scale_width=Vá»«a chiá»u rá»ng
page_scale_fit=Vá»«a chiá»u cao
page_scale_auto=Tá»± Äá»ng chá»n kÃ­ch thÆ°á»c
page_scale_actual=KÃ­ch thÆ°á»c thá»±c
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Lá»i
loading_error=Lá»i khi táº£i tÃ i liá»u PDF.
invalid_file_error=Táº­p tin PDF há»ng hoáº·c khÃ´ng há»£p lá».
missing_file_error=Thiáº¿u táº­p tin PDF.
unexpected_response_error=MÃ¡y chá»§ cÃ³ pháº£n há»i láº¡.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} ChÃº thÃ­ch]
password_label=Nháº­p máº­t kháº©u Äá» má» táº­p tin PDF nÃ y.
password_invalid=Máº­t kháº©u khÃ´ng ÄÃºng. Vui lÃ²ng thá»­ láº¡i.
password_ok=OK
password_cancel=Há»§y bá»

printing_not_supported=Cáº£nh bÃ¡o: In áº¥n khÃ´ng ÄÆ°á»£c há» trá»£ Äáº§y Äá»§ á» trÃ¬nh duyá»t nÃ y.
printing_not_ready=Cáº£nh bÃ¡o: PDF chÆ°a ÄÆ°á»£c táº£i háº¿t Äá» in.
web_fonts_disabled=PhÃ´ng chá»¯ Web bá» vÃ´ hiá»u hÃ³a: khÃ´ng thá» sá»­ dá»¥ng cÃ¡c phÃ´ng chá»¯ PDF ÄÆ°á»£c nhÃºng.
