<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">交易员</label>
        <div class="layui-input-block">
            <div id="followerBox1"></div>
        </div>
    </div>
    <!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">交易员ID</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="followerId" class="layui-input" placeholder="请输入交易员ID" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">订单号</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="orderNo" class="layui-input" placeholder="请输入订单号" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">类型(1:买涨;2:买跌;)</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="type" class="layui-input" placeholder="请输入类型(1:买涨;2:买跌;)" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">类型</label>
        <div class="layui-input-block">
            <select name="type" lay-verType="tips" lay-verify="required" required>
                <option value="">请选择</option>
                <option value="1">买涨</option>
                <option value="2">买跌</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">控制</label>
        <div class="layui-input-block">
            <select name="control" lay-verType="tips" lay-verify="required" required>
                <option value="">请选择</option>
                <option value="-1">默认</option>
                <option value="1">控赢</option>
                <option value="0">控亏</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">交易币种</label>
        <div class="layui-input-block">
            <div id="currentBox1"></div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">下单规则</label>
        <div class="layui-input-block">
            <div id="ruleBox1"></div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">盈亏率%</label>
        <div class="layui-input-block">
            <input name="profitability" class="layui-input" placeholder="请输入盈亏率" lay-verType="tips"
                   lay-verify="required" required/>
        </div>
    </div>

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">秒</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="seconds" class="layui-input" placeholder="请输入秒" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">下单时间</label>
        <div class="layui-input-block">
            <input type="text" name="orderTime" id="orderTimeId" placeholder="请选择下单时间" autocomplete="off" class="layui-input">
        </div>
    </div>

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">下单价格</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="orderPrice" class="layui-input" placeholder="请输入下单价格" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">下单数量</label>
        <div class="layui-input-block">
            <input name="quantity" class="layui-input" placeholder="请输入下单数量" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">平仓价格</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="closePrice" class="layui-input" placeholder="请输入平仓价格" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">平仓时间</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input type="text" name="closeTime" id="closeTimeId" placeholder="请选择平仓时间" autocomplete="off" class="layui-input">-->
<!--        </div>-->
<!--    </div>-->

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">盈亏率</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="profitability" class="layui-input" placeholder="请输入盈亏率" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">手续费</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="orderFee" class="layui-input" placeholder="请输入手续费" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">盈亏</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="profit" class="layui-input" placeholder="请输入盈亏" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">0:持仓,1:平仓</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="status" class="layui-input" placeholder="请输入0:持仓,1:平仓" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">控制(-1:默认,0:亏,1:赢)</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="control" class="layui-input" placeholder="请输入控制(-1:默认,0:亏,1:赢)" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;

        laydate.render({
            elem: '#orderTimeId',     // 绑定元素
            type: 'datetime',         // 启用年月日时分秒
            format: 'yyyy-MM-dd HH:mm:ss', // 时间格式（可选）
            trigger: 'click'          // 点击触发
        });
        // laydate.render({
        //     elem: '#closeTimeId'
        // });

        // 渲染币种
        var currentRenderIns = {};
        admin.req(getProjectUrl() + 'market/list', function(res){
            currentRenderIns = xmSelect.render({
                el: '#currentBox1',
                name: 'symbol',
                data: res.data,
                layVerify: 'required',
                layVerType: 'tips',
                radio: true,
                clickClose: true,
                model: { icon:'hidden', label: { type: 'text' }},
                prop: {
                    name: 'symbol',
                    value: 'symbol'
                },
                tips: '请选择交易币种'
            });
        }, {async: false});

        var followerRenderIns = {};
        admin.req(getProjectUrl() + 'follower/list', function(res){
            followerRenderIns = xmSelect.render({
                el: '#followerBox1',
                name: 'followerId',
                data: res.data,
                layVerify: 'required',
                layVerType: 'tips',
                radio: true,
                clickClose: true,
                model: { icon:'hidden', label: { type: 'text' }},
                prop: {
                    name: 'nickname',
                    value: 'id'
                },
                tips: '请选择交易员'
            });
        }, {async: false});

        // 渲染币种
        var ruleRenderIns = {};
        admin.req(getProjectUrl() + 'spotContractRule/list', function(res){
            ruleRenderIns = xmSelect.render({
                el: '#ruleBox1',
                name: 'ruleId',
                data: res.data,
                layVerify: 'required',
                layVerType: 'tips',
                radio: true,
                clickClose: true,
                model: { icon:'hidden', label: { type: 'text' }},
                prop: {
                    name: 'seconds',
                    value: 'id'
                },
                tips: '请选择下单规则'
            });
        }, {async: false});

        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
            // console.log(JSON.stringify(editData))
            currentRenderIns.setValue([editData.symbol]);
            followerRenderIns.setValue([editData.followerId]);
            ruleRenderIns.setValue([editData.ruleId]);
        }

        // 🚨 加上这个，确保 select 渲染！
        form.render('select');

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'followerContractSpotOrder/edit' : getProjectUrl() + 'followerContractSpotOrder/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
