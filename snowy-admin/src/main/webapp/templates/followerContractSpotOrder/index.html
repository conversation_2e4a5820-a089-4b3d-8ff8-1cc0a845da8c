<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>跟单合约极速订单预设</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">交易员ID:</label>
                        <div class="layui-input-inline">
                            <input name="followerId" class="layui-input" placeholder="请输入交易员ID"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">订单号:</label>
                        <div class="layui-input-inline">
                            <input name="orderNo" class="layui-input" placeholder="请输入订单号"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">类型:</label>
                        <div class="layui-input-inline">
                            <select name="type">
                                <option value="">所有</option>
                                <option value="1">买涨</option>
                                <option value="2">买跌</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态:</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">所有</option>
                                <option value="0">待处理</option>
                                <option value="1">已处理</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">交易类型:</label>
                        <div class="layui-input-inline">
                            <input name="symbol" class="layui-input" placeholder="交易类型,如ETH_USDT"/>
                        </div>
                    </div>
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="tableSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" perm-show="followerContractSpotOrder:edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" perm-show="followerContractSpotOrder:delete" lay-event="delete">删除</a>
</script>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var laydate = layui.laydate;

        //日期时间选择器
        laydate.render({
            elem: 'orderTimeId'
            ,type: 'datetime'
        });
        //日期时间选择器
        laydate.render({
            elem: 'closeTimeId'
            ,type: 'datetime'
        });

        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'followerContractSpotOrder/page',
            page: true,
            toolbar: ['<p>',
                '<button lay-event="add" perm-show="followerContractSpotOrder:add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>&nbsp;',
                '<button lay-event="delete" perm-show="followerContractSpotOrder:delete" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>删除</button>',
                '</p>'].join(''),
            cellMinWidth: 100,
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'followerId', title: '交易员ID', sort: true},
                    {field: 'type', title: '类型', templet: function (d) {
                            if (d.type === 1) {
                                return '<span style="color: green;">买涨</span>'
                            }
                            if (d.type === 2) {
                                return '<span style="color: red;">买跌</span>'
                            }
                        }, width: 70},
                    {field: 'symbol', title: '交易类型', sort: true},
                    {field: 'status', title: '状态', templet: function (d) {
                            if (d.status === 0) {
                                return '<span style="color: red;">待处理</span>'
                            }
                            if (d.status === 1) {
                                return '<span style="color: #1e9fff;">已处理</span>'
                            }
                        }, width: 70},
                    {field: 'seconds', title: '秒', sort: true},
                    {field: 'orderTime', title: '下单时间', sort: true, width: 200},
                    {field: 'quantity', title: '下单数量', sort: true},
                    {field: 'profitability', title: '盈亏率', sort: true},
                    // {field: 'control', title: '控制(-1:默认,0:亏,1:赢)', sort: true},
                    {field: 'ruleId', title: '规则ID', sort: true},
                    {field: 'control', title: '控制', templet: function (d) {
                            if (d.control === 1) {
                                return '<span style="color: green;">赢</span>';
                            }
                            if (d.control === 0) {
                                return '<span style="color: red;">亏</span>';
                            }
                            if (d.control === -1) {
                                return '<span style="color: gray;">默认</span>';
                            }
                            return '';
                        }, width: 70},
                    {title: '操作', toolbar: '#tableBar', align: 'center', width: 200, minWidth: 200}
                ]
            ],
            done: function(res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);
            }
        });

        /* 表格搜索 */
        form.on('submit(tableSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 表格工具条点击事件 */
        table.on('tool(dataTable)', function (obj) {
            /* 删除 */
            if (obj.event === 'delete') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'followerContractSpotOrder/delete', JSON.stringify([{'id': obj.data.id}]), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            /* 编辑 */
            if (obj.event === 'edit') {
                showAddOrUpdateModel(obj.data);
            }
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showAddOrUpdateModel();
            }
            if (obj.event === 'delete') { // 删除
                var checkRows = table.checkStatus('dataTable');
                if (checkRows.data.length === 0) {
                    notice.msg('请选择要操作的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    var ids = checkRows.data.map(function (d) {
                        return {"id": d.id};
                    });
                    admin.req(getProjectUrl() + 'followerContractSpotOrder/delete', JSON.stringify(ids), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        // 显示表单弹窗
        function showAddOrUpdateModel(data) {
            var layIndex = admin.open({
                title: (data ? '修改' : '添加') + '跟单合约极速订单预设',
                url: getProjectUrl() + 'followerContractSpotOrder/form',
                area: ['800px',''],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }
    });
</script>
</body>
</html>
