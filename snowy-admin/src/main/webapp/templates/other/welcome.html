<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>欢迎</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
<style>
    /** 应用快捷块样式 */
    .console-app-group {
        padding: 16px;
        border-radius: 4px;
        text-align: center;
        background-color: #fff;
        cursor: pointer;
        display: block;
    }

    .console-app-group .console-app-icon {
        width: 32px;
        height: 32px;
        line-height: 32px;
        margin-bottom: 6px;
        display: inline-block;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        font-size: 32px;
        color: #69c0ff;
    }

    .console-app-group:hover {
        box-shadow: 0 0 15px rgba(0, 0, 0, .08);
    }

    /** //应用快捷块样式 */

    /** 小组成员 */
    .console-user-group {
        position: relative;
        padding: 10px 0 10px 60px;
    }

    .console-user-group .console-user-group-head {
        width: 32px;
        height: 32px;
        position: absolute;
        top: 50%;
        left: 12px;
        margin-top: -16px;
        border-radius: 50%;
    }

    .console-user-group .layui-badge {
        position: absolute;
        top: 50%;
        right: 8px;
        margin-top: -10px;
    }

    .console-user-group .console-user-group-name {
        line-height: 1.2;
    }

    .console-user-group .console-user-group-desc {
        color: #8c8c8c;
        line-height: 1;
        font-size: 12px;
        margin-top: 5px;
    }

    /** 卡片轮播图样式 */
    .admin-carousel .layui-carousel-ind {
        position: absolute;
        top: -41px;
        text-align: right;
    }

    .admin-carousel .layui-carousel-ind ul {
        background: 0 0;
    }

    .admin-carousel .layui-carousel-ind li {
        background-color: #e2e2e2;
    }

    .admin-carousel .layui-carousel-ind li.layui-this {
        background-color: #999;
    }

    /** 广告位轮播图 */
    .admin-news .layui-carousel-ind {
        height: 45px;
    }

    .admin-news a {
        display: block;
        line-height: 70px;
        text-align: center;
    }

    /** 最新动态时间线 */
    .layui-timeline-dynamic .layui-timeline-item {
        padding-bottom: 0;
    }

    .layui-timeline-dynamic .layui-timeline-item:before {
        top: 16px;
    }

    .layui-timeline-dynamic .layui-timeline-axis {
        width: 9px;
        height: 9px;
        left: 1px;
        top: 7px;
        background-color: #cbd0db;
    }

    .layui-timeline-dynamic .layui-timeline-axis.active {
        background-color: #0c64eb;
        box-shadow: 0 0 0 2px rgba(12, 100, 235, .3);
    }

    .dynamic-card-body {
        box-sizing: border-box;
        overflow: hidden;
    }

    .dynamic-card-body:hover {
        overflow-y: auto;
        padding-right: 9px;
    }

    /** 优先级徽章 */
    .layui-badge-priority {
        border-radius: 50%;
        width: 20px;
        height: 20px;
        padding: 0;
        line-height: 18px;
        border-width: 2px;
        font-weight: 600;
    }
</style>
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid ew-console-wrapper">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body">
                    当日注册用户 <p class="lay-big-font" data-id="registerUserDayCount"></p>
                    <p>注册总用户<span class="pull-right" data-id="registerUserCount"></span></p>
                </div>
            </div>
        </div>
        <% if(adminRoleTypeEnum == 'AGENT_LINE') { %>
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-header">
                    总代理
                </div>
                <div class="layui-card-body">
                    <p class="lay-big-font" data-id="generalAgentCount"></p>
                </div>
            </div>
        </div>
        <% } %>
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <!--                <div class="layui-card-header">
                                    充值
                                </div>-->
                <div class="layui-card-body">
                    当日充值 <p><span class="lay-big-font" data-id="rechargeDayAccount"></span><span style="color: #c2c2c2;">USDT</span></p>
                    <p>充值总额<span class="pull-right"><span data-id="rechargeAccount"></span><span style="color: #c2c2c2;">USDT</span></span></p>
                </div>
            </div>
        </div>
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <!--                <div class="layui-card-header">
                                    提现
                                </div>-->
                <div class="layui-card-body">
                    当日提现 <p class="lay-big-font" data-id="withdrawalDayAccount"></p>
                    <p>提现总额<span class="pull-right" data-id="withdrawalAccount"></span></p>
                </div>
            </div>
        </div>
    </div>
    <!--<div class="layui-row layui-col-space15">
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body">
                    今日认购金额  <p class="lay-big-font" data-id="subscriptionDayAccount"></p>
                    <p>认购总金额<span class="pull-right" data-id="subscriptionAccount"></span></p>
                </div>
            </div>
        </div>
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body">
                    今日赎回金额  <p class="lay-big-font" data-id="redeemDayAccount"></p>
                    <p>赎回总金额<span class="pull-right" data-id="redeemAccount"></span></p>
                </div>
            </div>
        </div>
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-header">
                    今日违约金
                </div>
                <div class="layui-card-body">
                    <p class="lay-big-font" data-id="unContractDayAccount"></p>
                </div>
            </div>
        </div>
    </div>-->
    <div class="layui-row layui-col-space15">

        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body">
                    今日极速交易金额  <p class="lay-big-font" data-id="spotOrderDayAmount"></p>
                    <p>极速交易总金额<span class="pull-right" data-id="spotOrderTotalAmount"></span></p>
                </div>
            </div>
        </div>
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body">
                    今日合约交易金额  <p class="lay-big-font" data-id="contactOrderDayAmount"></p>
                    <p>合约交易总金额<span class="pull-right" data-id="contactOrderTotalAmount"></span></p>
                </div>
            </div>
        </div>
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body">
                    今日币币交易金额  <p class="lay-big-font" data-id="coinOrderDayAmount"></p>
                    <p>币币交易总金额<span class="pull-right" data-id="coinOrderTotalAmount"></span></p>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- js部分 -->
<!--<script th:src="@{/assets/libs/layui/layui.js}"></script>
<script th:src="@{/assets/js/common.js}"></script>-->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js?v=1"></script>
<script src="${ctxPath}/assets/libs/tinymce/tinymce.min.js"></script>
<script>
    layui.use(['layer', 'carousel', 'element', 'admin'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var carousel = layui.carousel;
        var admin = layui.admin;
        var device = layui.device();

        // 渲染轮播
        carousel.render({
            elem: '#workplaceNewsCarousel',
            width: '100%',
            height: '70px',
            arrow: 'none',
            autoplay: true,
            trigger: device.ios || device.android ? 'click' : 'hover',
            anim: 'fade'
        });
        var url = getProjectUrl() + 'other/welcomeData';
        admin.req(url, {}, function(res){
            for (var key in res.data) {
                if ('registerUserDayCount' === key || 'registerUserCount' === key) {
                    $('[data-id=' + key + ']').text(res.data[key]);
                    continue
                }
                $('[data-id=' + key + ']').text(AdminCommon.formatNum(res.data[key]));
            }
        }, 'get');
    });
</script>

<!-- 正文开始 -->
<!--<div class="welcome-page">
    <img class="welcome-page-img" src="${ctxPath}/assets/images/ic_welcome.png">
</div>
<style>
    .welcome-page {
        position: absolute;
        top: 50%;
        width: 100%;
        text-align: center;
        -o-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
    }

    .welcome-page .welcome-page-img {
        display: inline-block;
        height: 260px;
        margin: 10px 15px;
    }
</style>


<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['admin'], function () {

    });
</script>-->
</body>
</html>