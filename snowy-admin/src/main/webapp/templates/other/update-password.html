<!-- 修改密码表单 -->
<form class="layui-form model-form">
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">原始密码:</label>
        <div class="layui-input-block">
            <input type="password" name="password" placeholder="请输入原始密码" class="layui-input"
                   lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">新密码:</label>
        <div class="layui-input-block">
            <input type="password" name="newPassword" placeholder="请输入新密码" class="layui-input"
                   lay-verType="tips" lay-verify="required|psw" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">确认密码:</label>
        <div class="layui-input-block">
            <input type="password" name="reNewPassword" placeholder="请再次输入新密码" class="layui-input"
                   lay-verType="tips" lay-verify="required|equalTo" lay-equalTo="input[name=newPassword]" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block text-right">
            <button class="layui-btn" lay-filter="submit-psw" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </div>
</form>

<!-- js部分 -->
<script>
    layui.use(['layer', 'form', 'admin', 'formX'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;

        // 监听提交
        form.on('submit(submit-psw)', function (data) {
            var loadIndex = layer.load(2);
            //构造id
            data.field.id = layui.data(admin.setter.tableName).loginUser.id;
            $.ajax({
                type: 'post',
                url: getProjectUrl() + 'sysUser/updatePwd',
                contentType: 'application/json',
                data: JSON.stringify(data.field),
                success: function (res) {
                    layer.close(loadIndex);
                    if (res.success) {
                        layer.msg('修改成功', {icon: 1, time: 1500}, function () {
                            admin.closeDialog('[lay-filter="submit-psw"]');
                        });
                    } else {
                        layer.msg(res.message, {icon: 2, anim: 6});
                    }
                },
                error: function(res) {
                    layer.close(loadIndex);
                    layer.msg(res.responseText, {icon: 2, anim: 6});
                }
            });
            return false;
        });

    });
</script>