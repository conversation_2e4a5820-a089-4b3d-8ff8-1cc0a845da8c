<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>认证审核</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">用户手机号:</label>
                        <div class="layui-input-inline">
                            <input name="userAccount" class="layui-input" placeholder="请输入用户手机号"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">认证状态</label>
                        <div class="layui-input-inline">
                            <select name="authStatus">
                                <option value=""></option>
                                <option value="1">审核中</option>
                                <option value="2">审核通过</option>
                                <option value="3">审核不通过</option>
                                <option value="4">延迟审核</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="tableSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var laydate = layui.laydate;

        //日期时间选择器
        laydate.render({
            elem: 'authTimeId'
            ,type: 'datetime'
        });
        //日期时间选择器
        laydate.render({
            elem: 'checkTimeId'
            ,type: 'datetime'
        });

        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'playerInfo/page',
            page: true,
            cellMinWidth: 100,
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'superiorName', title: '代理人', sort: true},
                    {field: 'userAccount', title: '用户账号', sort: true},
                    {field: 'realName', title: '姓名', sort: true},
                    {field: 'userName', title: '用户名', sort: true},
                    {field: 'country', title: '国家', sort: true},
                    {field: 'idCardJust', title: '身份证正面', sort: true,templet: '<div><img src="' + getProjectUrl() + 's3/file/previewFile?filename={{d.idCardJust}}" style="width: 300px; height: 300px;"/></div>'},
                    {field: 'idCardBack', title: '身份证反面', sort: true,templet: '<div><img src="' + getProjectUrl() + 's3/file/previewFile?filename={{d.idCardBack}}" style="width: 300px; height: 300px;"/></div>'},
                    {field: 'holdIdCard', title: '手持身份证', sort: true,templet: '<div><img src="' + getProjectUrl() + 's3/file/previewFile?filename={{d.holdIdCard}}" style="width: 300px; height: 300px;"/></div>'},
                    {field: 'idCard', title: '身份证号码', sort: true},
                    {field: 'liveAddress', title: '居住地址', sort: true},
                    {field: 'authStatus', title: '认证状态',templet:function(d){
                        var  authStatusName = "";
                        if(d.authStatus == 1){
                            authStatusName = "待审核";
                        }else if(d.authStatus == 2){
                            authStatusName = "审核通过";
                        }else if(d.authStatus == 3){
                            authStatusName = "审核不通过";
                        }else if(d.authStatus == 4){
                            authStatusName = "延迟审核";
                        }
                        return authStatusName;
                        }, sort: true},
                    {field: 'authTime', title: '认证时间', sort: true},
                    {field: 'checkTime', title: '审核时间', sort: true},
                    {field: 'checkId', title: '审核人员', sort: true},
                    {field:'authStatus', title:'操作', templet: function (d) {
                        var str = "<a class=\"layui-btn layui-btn-primary layui-btn-xs\" perm-show=\"playerInfo:edit\" lay-event=\"edit\">编辑</a>";
                        if(d.authStatus === 1){
                            str+=" <a class=\"layui-btn layui-btn-danger layui-btn-xs\" perm-show=\"playerInfo:updateUserAuthStatus\" lay-event=\"check-adopt\">通过</a>\n" +
                                "    <a class=\"layui-btn layui-btn-danger layui-btn-xs\" perm-show=\"playerInfo:updateUserAuthStatus\" lay-event=\"check-not-adopt\">不通过</a>\n" +
                                "    <a class=\"layui-btn layui-btn-danger layui-btn-xs\" perm-show=\"playerInfo:updateUserAuthStatus\" lay-event=\"check-delay-adopt\">延迟审核</a>";
                        }else if(d.authStatus === 4){
                            str+=" <a class=\"layui-btn layui-btn-danger layui-btn-xs\" perm-show=\"playerInfo:updateUserAuthStatus\" lay-event=\"check-adopt\">通过</a>\n" +
                                "    <a class=\"layui-btn layui-btn-danger layui-btn-xs\" perm-show=\"playerInfo:updateUserAuthStatus\" lay-event=\"check-not-adopt\">不通过</a>\n";
                        }
                        return str;
                        }, unresize: true, width: 400,minWidth: 200 }
                ]
            ],
            done: function(res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);
            }
        });

        /* 表格搜索 */
        form.on('submit(tableSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 表格工具条点击事件 */
        table.on('tool(dataTable)', function (obj) {
            /* 删除 */
            if (obj.event === 'delete') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'playerInfo/delete', JSON.stringify([{'id': obj.data.id}]), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            //审核通过
            if (obj.event === 'check-adopt') {
                layer.confirm('确定审核通过嘛？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'playerInfo/updateUserAuthStatus', JSON.stringify({'id': obj.data.id,'authStatus':'2'}), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            if (obj.event === 'check-not-adopt') {
                layer.confirm('确定审核不通过嘛？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'playerInfo/updateUserAuthStatus', JSON.stringify({'id': obj.data.id,'authStatus':'3'}), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            if (obj.event === 'check-delay-adopt') {
                layer.confirm('确定延迟审核吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'playerInfo/updateUserAuthStatus', JSON.stringify({'id': obj.data.id,'authStatus':'4'}), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            /* 编辑 */
            if (obj.event === 'edit') {
                showAddOrUpdateModel(obj.data);
            }
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showAddOrUpdateModel();
            }
            if (obj.event === 'delete') { // 删除
                var checkRows = table.checkStatus('dataTable');
                if (checkRows.data.length === 0) {
                    notice.msg('请选择要操作的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    var ids = checkRows.data.map(function (d) {
                        return {"id": d.id};
                    });
                    admin.req(getProjectUrl() + 'playerInfo/delete', JSON.stringify(ids), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        // 显示表单弹窗
        function showAddOrUpdateModel(data) {
            var layIndex = admin.open({
                title: (data ? '修改' : '添加') + '认证审核',
                url: getProjectUrl() + 'playerInfo/form',
                area: ['800px',''],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }
    });
</script>
</body>
</html>
