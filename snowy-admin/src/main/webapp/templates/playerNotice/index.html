<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>玩家公告</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">机构编码:</label>
                        <div class="layui-input-inline">
                            <div id="orgSelectBox" class="ew-xmselect-tree"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">标题:</label>
                        <div class="layui-input-inline">
                            <input name="title" class="layui-input" placeholder="请输入标题"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态:</label>
                        <div class="layui-input-inline">
                            <select name="status"></select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">置顶:</label>
                        <div class="layui-input-inline">
                            <select name="isTop"></select>
                        </div>
                    </div>

                    <input name="type" type="hidden" value="1"/>
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="tableSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    {{# if(d.status === 0) { }}
    <a class="layui-btn layui-btn-xs" perm-show="playerNotice:edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-warm layui-btn-xs" perm-show="playerNotice:changeStatus" lay-event="public">发布</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" perm-show="playerNotice:delete" lay-event="delete">删除</a>
    {{# } }}
    {{# if(d.status === 1) { }}
    <a class="layui-btn layui-btn-xs" perm-show="playerNotice:edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-warm layui-btn-xs" perm-show="playerNotice:changeStatus" lay-event="cancel">撤销</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" perm-show="playerNotice:delete" lay-event="delete">删除</a>
    {{# } }}
    {{# if(d.isTop === 0) { }}
    <a class="layui-btn layui-btn-warm layui-btn-xs" perm-show="playerNotice:changeTop" lay-event="top">置顶</a>
    {{# } }}
    {{# if(d.isTop === 1) { }}
    <a class="layui-btn layui-btn-warm layui-btn-xs" perm-show="playerNotice:changeTop" lay-event="cancelTop">取消置顶</a>
    {{# } }}
</script>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var laydate = layui.laydate;

        // 渲染字典下拉
        xnUtil.rendDictDropDown(null, 'status', 'player_notice_status', '请选择状态', null);
        xnUtil.rendDictDropDown(null, 'isTop', 'is_top', '请选择', null);
        xnUtil.rendDictDropDown(null, 'languageCode', 'language_code', '请选择', null);

        var orgTreeRenderIns = {};
        admin.req(getProjectUrl() + 'sysOrg/tree', function(res){
            // 渲染下拉树
            orgTreeRenderIns = xmSelect.render({
                el: '#orgSelectBox',
                name: 'orgCode',
                height: '250px',
                layVerType: 'tips',
                data: res.data,
                initValue: [],
                model: {label: {type: 'text'}},
                prop: {
                    name: 'title',
                    value: 'code'
                },
                radio: true,
                clickClose: true,
                tree: {
                    show: true,
                    indent: 15,
                    strict: false,
                    expandedKeys: true
                },
                tips: '请选择机构'
            });
        }, {async: false});

        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'playerNotice/page?type=1',
            page: true,
            toolbar: ['<p>',
                '<button lay-event="add" perm-show="playerNotice:add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>&nbsp;',
                '<button lay-event="delete" perm-show="playerNotice:delete" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>删除</button>',
                '</p>'].join(''),
            cellMinWidth: 100,
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'orgCode', title: '机构编码'},
                    {field: 'languageCode', title: '语言编码', sort: true,templet: function (d) {  return xnUtil.rendDataTableDict(d.languageCode, 'language_code'); } },
                    {field: 'title', title: '标题', sort: true},
                    {field: 'content', title: '内容', sort: true},
                    {field: 'status', title: '状态', sort: true,templet: function (d) {  return xnUtil.rendDataTableDict(d.status, 'player_notice_status'); } },
                    {field: 'isTop', title: '是否置顶', sort: true,templet: function (d) {  return xnUtil.rendDataTableDict(d.isTop, 'is_top'); } },
                    {title: '操作', toolbar: '#tableBar', align: 'center', width: 200, minWidth: 200}
                ]
            ],
            done: function(res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);
            }
        });

        /* 表格搜索 */
        form.on('submit(tableSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 表格工具条点击事件 */
        table.on('tool(dataTable)', function (obj) {
            /* 删除 */
            if (obj.event === 'delete') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'playerNotice/delete', JSON.stringify([{'id': obj.data.id}]), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            /* 编辑 */
            if (obj.event === 'edit') {
                showAddOrUpdateModel(obj.data.id);
            }
            /* 发布或撤销 */
            if (obj.event === 'public' || obj.event === 'cancel') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'playerNotice/changeStatus', JSON.stringify({'id': obj.data.id, 'status': obj.event === 'public'?1:0}), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            /* 置顶或取消置顶 */
            if (obj.event === 'top' || obj.event === 'cancelTop') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'playerNotice/changeTop', JSON.stringify({'id': obj.data.id,'isTop': obj.event === 'top'?1:0}), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showAddOrUpdateModel();
            }
            if (obj.event === 'delete') { // 删除
                var checkRows = table.checkStatus('dataTable');
                if (checkRows.data.length === 0) {
                    notice.msg('请选择要操作的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    var ids = checkRows.data.map(function (d) {
                        return {"id": d.id};
                    });
                    admin.req(getProjectUrl() + 'playerNotice/delete', JSON.stringify(ids), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        // 显示表单弹窗
        function showAddOrUpdateModel(data) {
            admin.putTempData('formPlayerNoticeId', data);
            window.location.href = getProjectUrl() + 'playerNotice/form';
        }
    });
</script>
</body>
</html>
