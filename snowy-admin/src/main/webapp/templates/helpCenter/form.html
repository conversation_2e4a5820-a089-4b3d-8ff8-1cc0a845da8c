<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>玩家公告表单</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        body .tox-tinymce-aux {
            z-index: 19892000;
        }
    </style>
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">
                <input name="id" type="hidden"/>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">机构编码</label>
                    <div class="layui-input-block">
                        <div id="orgSelectBox" class="ew-xmselect-tree"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">语言编码</label>
                    <div class="layui-input-block">
                        <div id="typeBox"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">标题</label>
                    <div class="layui-input-block">
                        <input name="title" class="layui-input" placeholder="请输入标题" lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">内容</label>
                    <div class="layui-input-block">
                        <textarea name="content" id="contentEditor" placeholder="请输入公告内容" class="layui-textarea"></textarea>
                    </div>
                </div>
                <div class="layui-form-item text-right">
                    <button class="layui-btn layui-btn-primary" type="button" id="formBackBtn">取消</button>
                    <button class="layui-btn layui-btn-warm" lay-filter="formSaveBtn" lay-submit>暂存</button>
                    <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>发布</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>
<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script src="${ctxPath}/assets/libs/tinymce/tinymce.min.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil', 'transfer'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var transfer = layui.transfer

        var formPlayerNoticeId = admin.getTempData('formPlayerNoticeId');
        // 渲染字典下拉
        var typeRenderIns = xmSelect.render({
            el: '#typeBox',
            name: 'languageCode',
            data: xnUtil.getDictDataByDictTypeCode('language_code', null),
            layVerify: 'required',
            layVerType: 'tips',
            radio: true,
            clickClose: true,
            model: { icon:'hidden', label: { type: 'text' }},
            prop: {
                name: 'name',
                value: 'code'
            },
            tips: '请选择语言编码'
        });

        var orgTreeRenderIns = {};
        admin.req(getProjectUrl() + 'sysOrg/tree', function(res){
            // 渲染下拉树
            orgTreeRenderIns = xmSelect.render({
                el: '#orgSelectBox',
                name: 'orgCode',
                height: '250px',
                layVerify: 'required',
                layVerType: 'tips',
                data: res.data,
                initValue: [],
                model: {label: {type: 'text'}},
                prop: {
                    name: 'title',
                    value: 'code'
                },
                radio: true,
                clickClose: true,
                tree: {
                    show: true,
                    indent: 15,
                    strict: false,
                    expandedKeys: true
                },
                tips: '请选择机构'
            });
        }, {async: false});

        // 渲染富文本编辑器
        tinymce.init({
            selector: '#contentEditor',
            height: 525,
            branding: false,
            language: 'zh_CN',
            plugins: 'code print preview fullscreen paste searchreplace save autosave link autolink image imagetools media table codesample lists advlist hr charmap emoticons anchor directionality pagebreak quickbars nonbreaking visualblocks visualchars wordcount',
            toolbar: 'fullscreen preview code | undo redo | forecolor backcolor | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | outdent indent | numlist bullist | formatselect fontselect fontsizeselect | link image media emoticons charmap anchor pagebreak codesample | ltr rtl',
            toolbar_drawer: 'sliding',
            file_picker_types: 'media',
            relative_urls : false,
            remove_script_host : false,
            document_base_url : getProjectUrl(),
            images_upload_handler: function (blobInfo, success, failure, progress) {
                var xhr, formData;
                var file = blobInfo.blob();//转化为易于理解的file对象
                xhr = new XMLHttpRequest();
                xhr.withCredentials = false;
                xhr.open('POST', getProjectUrl() + 'sysFileInfo/upload');
                xhr.upload.onprogress = function(e){
                    progress(e.loaded / e.total * 100);
                }
                xhr.onload = function() {
                    var res = JSON.parse(xhr.responseText);
                    if(!res.success) {
                        layui.notice.msg(res.message, {icon: 2});
                        return false;
                    } else {
                        success(getProjectUrl() + 'sysFileInfo/preview?id=' + res.data);
                    }
                };
                formData = new FormData();
                formData.append('file', file, file.name );//此处与源文档不一样
                xhr.send(formData);
            }, init_instance_callback: function (editor) {
                admin.removeLoading();
                if(formPlayerNoticeId !== null && formPlayerNoticeId !== undefined) {
                    admin.req(getProjectUrl() + 'helpCenter/detail?id=' + formPlayerNoticeId, function(res){
                        form.val('addOrUpdateForm', res.data);
                        typeRenderIns.setValue([res.data.languageCode]);
                        editor.setContent(res.data.content);
                        orgTreeRenderIns.setValue([res.data.orgCode]);
                    });
                }
            }
        });

        /* 返回按钮点击事件 */
        $('#formBackBtn').click(function () {
            window.location.href = getProjectUrl() + 'helpCenter/index';
        });

        // 表单暂存事件
        form.on('submit(formSaveBtn)', function (data) {
            var content = tinymce.get('contentEditor').getContent();
            layer.load(2);
            var url = formPlayerNoticeId ? getProjectUrl() + 'helpCenter/edit' : getProjectUrl() + 'helpCenter/add';
            data.field.status = 0;
            data.field.type = 2;
            data.field.content = content;
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    window.location.href = getProjectUrl() + 'helpCenter/index';
                    return false;
                });
            }, 'post');
            return false;
        });

        // 表单提交事件
        form.on('submit(formSubmitBtn)', function (data) {
            var content = tinymce.get('contentEditor').getContent();
            if(content === null || content === undefined || content === '') {
                layui.notice.msg('公告内容不能为空', {icon: 2});
                return false;
            }
            layer.load(2);
            var url = formPlayerNoticeId ? getProjectUrl() + 'helpCenter/edit' : getProjectUrl() + 'helpCenter/add';
            data.field.status = 1;
            data.field.content = content;
            data.field.type = 2;
            data.field.noticeUserIdList = [];
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    window.location.href = getProjectUrl() + 'helpCenter/index';
                    return false;
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>