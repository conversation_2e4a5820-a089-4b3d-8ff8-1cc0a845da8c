<style>
    body .tox-tinymce-aux {
        z-index: 19892000;
    }
</style>
<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">语言</label>
        <div class="layui-input-block">
            <div id="langId"></div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">类型</label>
        <div class="layui-input-block">
            <select name="type" id="typeId">
                <option value="1">注册协议</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">内容</label>
        <div class="layui-input-block">
            <textarea placeholder="请输入内容" id="contentEditor" class="layui-textarea" name="content" ></textarea>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>
<!-- js部分 -->
<script src="${ctxPath}/assets/libs/tinymce/tinymce.min.js"></script>
<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;

        // var formPlayerRichTextId = admin.getTempData('formPlayerRichTextId');

        var langRenderIns = xmSelect.render({
            el: '#langId',
            name: 'lang',
            data: xnUtil.getDictDataByDictTypeCode('language_code', null),
            layVerify: 'required',
            layVerType: 'tips',
            radio: true,
            clickClose: true,
            model: { icon:'hidden', label: { type: 'text' }},
            prop: {
                name: 'name',
                value: 'code'
            },
            tips: '请选择语言'
        }, {async: false});

        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
            // 下拉赋值
            langRenderIns.setValue([editData.lang]);
        }


        // 渲染富文本编辑器
        tinymce.init({
            selector: '#contentEditor',
            height: 525,
            branding: false,
            language: 'zh_CN',
            plugins: 'code print preview fullscreen paste searchreplace save autosave link autolink image imagetools media table codesample lists advlist hr charmap emoticons anchor directionality pagebreak quickbars nonbreaking visualblocks visualchars wordcount',
            toolbar: 'fullscreen preview code | undo redo | forecolor backcolor | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | outdent indent | numlist bullist | formatselect fontselect fontsizeselect | link image media emoticons charmap anchor pagebreak codesample | ltr rtl',
            toolbar_drawer: 'sliding',
            file_picker_types: 'media',
            relative_urls : false,
            remove_script_host : false,
            document_base_url : getProjectUrl(),
            images_upload_handler: function (blobInfo, success, failure, progress) {
                var xhr, formData;
                var file = blobInfo.blob();//转化为易于理解的file对象
                xhr = new XMLHttpRequest();
                xhr.withCredentials = false;
                xhr.open('POST', getProjectUrl() + 'sysFileInfo/upload');
                xhr.upload.onprogress = function(e){
                    progress(e.loaded / e.total * 100);
                }
                xhr.onload = function() {
                    var res = JSON.parse(xhr.responseText);
                    if(!res.success) {
                        layui.notice.msg(res.message, {icon: 2});
                        return false;
                    } else {
                        success(getProjectUrl() + 'sysFileInfo/preview?id=' + res.data);
                    }
                };
                formData = new FormData();
                formData.append('file', file, file.name );//此处与源文档不一样
                xhr.send(formData);
            }, init_instance_callback: function (editor) {
                admin.removeLoading();
                // editor.setContent(res.data.content);
                // if(formPlayerRichTextId !== null && formPlayerRichTextId !== undefined) {
                //     admin.req(getProjectUrl() + 'richText/detail?id=' + formPlayerRichTextId, function(res){
                //         form.val('addOrUpdateForm', res.data);
                //         editor.setContent(res.data.content);
                //         langRenderIns.setValue([res.data.lang]);
                //         form.render('select');
                //     });
                // }
            }
        });

        form.render('select');
        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            var content = tinymce.get('contentEditor').getContent();
            if(content === null || content === undefined || content === '') {
                layui.notice.msg('内容不能为空', {icon: 2});
                return false;
            }
            layer.load(2);
            var url = editData ? getProjectUrl() + 'richText/edit' : getProjectUrl() + 'richText/add';
            data.field.content = content;
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
