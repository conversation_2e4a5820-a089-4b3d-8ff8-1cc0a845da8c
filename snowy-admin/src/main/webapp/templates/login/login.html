<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <script>if (window !== top) top.location.replace(location.href);</script>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="icon" href="${ctxPath}/assets/images/favicon.ico">
    <title>登录交易所后台管理</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        body {
            background-color: #FFFFFF;
            background-image: url("${ctxPath}/assets/images/xn_login.png");
            background-repeat: no-repeat;
            background-size: contain;
            min-height: 100vh;
        }

        body:before {
            content: "";
            background-color: rgba(0, 0, 0, .2);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .login-wrapper {
            max-width: 420px;
            padding: 20px;
            margin: 0 auto;
            position: relative;
            box-sizing: border-box;
            z-index: 2;
        }

        .login-wrapper > .layui-form {
            padding: 25px 30px;
            background-color: #fff;
            box-shadow: 0 3px 6px -1px rgba(0, 0, 0, 0.19);
            box-sizing: border-box;
        }

        .login-wrapper > .layui-form > h2 {
            color: #333;
            font-size: 18px;
            text-align: center;
            margin-bottom: 25px;
        }

        .login-wrapper > .layui-form > .layui-form-item {
            margin-bottom: 25px;
            position: relative;
        }

        .login-wrapper > .layui-form > .layui-form-item:last-child {
            margin-bottom: 0;
        }

        .login-wrapper > .layui-form > .layui-form-item > .layui-input {
            height: 46px;
            line-height: 46px;
            border-radius: 2px !important;
        }

        .login-wrapper .layui-input-icon-group > .layui-input {
            padding-left: 46px;
        }

        .login-wrapper .layui-input-icon-group > .layui-icon {
            width: 46px;
            height: 46px;
            line-height: 46px;
            font-size: 20px;
            color: #909399;
            position: absolute;
            left: 0;
            top: 0;
            text-align: center;
        }

        .login-wrapper > .layui-form > .layui-form-item.login-captcha-group {
            padding-right: 135px;
        }

        .login-wrapper > .layui-form > .layui-form-item.login-captcha-group > .login-captcha {
            height: 46px;
            width: 120px;
            cursor: pointer;
            box-sizing: border-box;
            border: 1px solid #e6e6e6;
            border-radius: 2px !important;
            position: absolute;
            right: 0;
            top: 0;
        }

        .login-wrapper > .layui-form > .layui-form-item > .layui-form-checkbox {
            margin: 0 !important;
            padding-left: 25px;
        }

        .login-wrapper > .layui-form > .layui-form-item > .layui-form-checkbox > .layui-icon {
            width: 15px !important;
            height: 15px !important;
        }

        .login-wrapper > .layui-form .layui-btn-fluid {
            height: 48px;
            line-height: 48px;
            font-size: 16px;
            border-radius: 2px !important;
        }

        .login-wrapper > .layui-form > .layui-form-item.login-oauth-group > a > .layui-icon {
            font-size: 26px;
        }

        .login-copyright {
            color: #eee;
            padding-bottom: 20px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        @media screen and (min-height: 550px) {
            .login-wrapper {
                margin: -300px auto 0;
                position: absolute;
                top: 50%;
                left: 0;
                right: 0;
                width: 100%;
            }

            .login-copyright {
                position: absolute;
                bottom: 0;
                right: 0;
                left: 0;
            }
        }

        .layui-btn {
            background-color: #5FB878;
            border-color: #5FB878;
        }

        .layui-link {
            color: #5FB878 !important;
        }
        .snowy-title {
            height: 44px;
            line-height: 44px;
            text-align: center;
            margin-bottom: 40px
        }
        .snowy-title-img {
            height: 44px;
            vertical-align: top;
            border-style: none;
        }
        .snowy-title-span {
            font-size: 33px;
            color: rgba(0,0,0,.85);
            font-family: Avenir,Helvetica Neue,Arial,Helvetica,sans-serif;
            font-weight: 600;
            position: relative;
            top: 2px;
        }
    </style>
</head>
<body>
<div class="login-wrapper layui-anim layui-anim-scale layui-hide">
    <div class="snowy-title">
        <img src="${ctxPath}/assets/images/logo.png" class="snowy-title-img">
        <span class="snowy-title-span">交易所管理后台</span>
    </div>
    <form class="layui-form">
        <h2>用户登录</h2>
        <!--<div class="layui-form-item layui-input-icon-group">
            <i class="layui-icon layui-icon-username"></i>
            <select name="adminAuthType">
                <option value="1">超级管理员</option>
                <option value="2">代理线</option>
                <option value="3" selected>代理人</option>
            </select>
        </div>-->
        <div class="layui-form-item layui-input-icon-group">
            <i class="layui-icon layui-icon-username"></i>
            <input class="layui-input" name="account" placeholder="请输入登录账号" autocomplete="off"
                   lay-verType="tips" lay-verify="required" required/>
        </div>
        <div class="layui-form-item layui-input-icon-group">
            <i class="layui-icon layui-icon-password"></i>
            <input class="layui-input" name="password" placeholder="请输入登录密码" type="password"
                   lay-verType="tips" lay-verify="required" required/>
        </div>
        <% if(constantContext.getCaptchaFlag()) { %>
        <div class="layui-form-item layui-input-icon-group login-captcha-group">
            <i class="layui-icon layui-icon-auz"></i>
            <input class="layui-input" name="captcha" placeholder="请输入验证码" autocomplete="off"
                   lay-verType="tips" lay-verify="required" required/>
            <img class="login-captcha" alt=""/>
        </div>
        <% } %>
        <div class="layui-form-item">
            <input type="checkbox" name="remember" title="记住密码" lay-skin="primary" checked>
<!--            <a href="reg.html" class="layui-link pull-right">注册账号</a>-->
        </div>
        <div class="layui-form-item">
            <button class="layui-btn layui-btn-fluid" lay-filter="loginSubmit" lay-submit>登录</button>
        </div>
        <% if(constantContext.getEnableOauthLogin()) { %>
        <div class="layui-form-item login-oauth-group text-center">
            <a href="javascript:;"><i class="layui-icon layui-icon-login-qq" style="color:#3492ed;"></i></a>&emsp;
            <a href="javascript:;"><i class="layui-icon layui-icon-login-wechat" style="color:#4daf29;"></i></a>&emsp;
            <a href="javascript:;"><i class="layui-icon layui-icon-login-weibo" style="color:#CF1900;"></i></a>
        </div>
        <% } %>
    </form>
</div>
<div class="login-copyright">copyright © 2022 all rights reserved.</div>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'form', 'notice', 'index'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var notice = layui.notice;
        var admin = layui.admin;
        var index = layui.index;

        $('.login-wrapper').removeClass('layui-hide');

        //如果有提示，则显示提示
        var errorMsg = '${tips!}';
        if (errorMsg) {
            notice.msg(errorMsg, {icon: 2});
        }

        /* 表单提交 */
        form.on('submit(loginSubmit)', function (data) {
            layer.load(2);
            admin.req(getProjectUrl() + 'login', JSON.stringify(data.field), function(res){
                layer.msg('登录成功', {icon: 1, time: 1000}, function () {
                    // 先将页签记忆清空，以免上次登陆的用户的菜单对于此次登录的用户无权限而跳转到403页面
                    index.clearTabCache();
                    location.href = getProjectUrl();
                });
            }, 'post');
            return false;
        });

        /* 图形验证码 */
        var captchaUrl = getProjectUrl() + 'getCaptcha';
        $('img.login-captcha').click(function () {
            this.src = captchaUrl + '?t=' + (new Date).getTime();
        }).trigger('click');

    });
</script>
</body>
</html>
