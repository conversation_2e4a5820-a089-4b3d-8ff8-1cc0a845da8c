<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form"
      xmlns:th="http://www.w3.org/1999/xhtml">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">用户账号</label>
        <div class="layui-input-block">
            <input name="userAccount" class="layui-input" placeholder="请输入用户账号" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <input name="operationType" type="hidden" class="layui-input" value="2"/>
    <input name="rechargeChannel" type="hidden" class="layui-input" value="管理员提现"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">账户类型</label>
        <div class="layui-input-block">
            <select name="accountType" style="display: block;width: 100px;height: auto">
                <option value="1">币币账户</option>
                <option value="2">合约账户</option>
                <option value="3">现货账户</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">交易币种</label>
        <div class="layui-input-block">
            <select name="transactionType"  style="display: block;width: 100px;height: auto">
                <% for(current in defaultCurrentList) { %>
                    <option value="${current}">${current}</option>
                <% } %>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">金额</label>
        <div class="layui-input-block">
            <input name="account" class="layui-input" placeholder="请输入金额" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">备注</label>
        <div class="layui-input-block">
            <input name="remark" class="layui-input" placeholder="请输入备注" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">开户行</label>
        <div class="layui-input-block">
            <input name="bankDeposit" class="layui-input" placeholder="请输入开户行" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">提现银行</label>
        <div class="layui-input-block">
            <input name="bankName" class="layui-input" placeholder="请输入提现银行" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">银行卡号</label>
        <div class="layui-input-block">
            <input name="bankCard" class="layui-input" placeholder="请输入银行卡号" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>确认提现</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;

        laydate.render({
            elem: '#operationTimeId'
        });

        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
        }

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);
            var url = getProjectUrl() + 'transaction/executeWithdrawalUser';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
