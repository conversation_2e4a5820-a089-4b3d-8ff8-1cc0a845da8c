<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>用户充值，提现记录表</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
  <!--                  <div class="layui-inline">
                        <label class="layui-form-label">用户id:</label>
                        <div class="layui-input-inline">
                            <input name="playerId" class="layui-input" placeholder="请输入用户id"/>
                        </div>
                    </div>-->
                    <div class="layui-inline">
                        <label class="layui-form-label">状态 :</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value=""></option>
<!--                                <option value="0">待付款</option>-->
                                <option value="1">待审核</option>
                                <option value="2">成功</option>
                                <option value="3">失败</option>
<!--                                <option value="4">交易处理中</option>-->
<!--                                <option value="5">交易超时</option>-->
                                <option value="6">撤销</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">币种:</label>
                        <div class="layui-input-inline">
                            <select name="transactionType">
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">用户名:</label>
                        <div class="layui-input-inline">
                            <input name="userName" class="layui-input" placeholder="请输入用户名"/>
                        </div>
                    </div>



                  <!--  <div class="layui-inline">
                        <label class="layui-form-label">邮箱:</label>
                        <div class="layui-input-inline">
                            <input name="email" class="layui-input" placeholder="请输入邮箱"/>
                        </div>
                    </div>-->
                    <div class="layui-inline">
                        <label class="layui-form-label">代理人:</label>
                        <div class="layui-input-inline">
                            <input name="agentPeople" class="layui-input" placeholder="请输入代理人"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">开始时间:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="startDate" id="startDate" placeholder="请选择开始时间" class="layui-input laydate">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">结束时间:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="endDate" id="endDate" placeholder="请选择结束时间" class="layui-input laydate">
                        </div>
                    </div>
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="tableSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>

</div>
<script type="text/html" id="transactionCheck">
    <form class="layui-form" lay-filter="modelUserForm" id="modelUserForm">
        <input name="transactionId" type="hidden" id="transactionId">
        <div class="layui-form-item">
            <label class="layui-form-label">审核结果</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="2" title="通过" checked>
                <input type="radio" name="status" value="3" title="不通过">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label" >审核意见</label>
            <div class="layui-input-block">
                <textarea name="remark" id="remark" placeholder="请输入内容" class="layui-textarea"></textarea>
            </div>
        </div>
    </form>
</script>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js?v=1"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var laydate = layui.laydate;

        //日期时间选择器
        //日期时间选择器
        $(".laydate").each(function(){
            laydate.render({
                elem: this, //指定元素  表示当前的元素
                type: 'datetime', //date日期  time时间  year年  month月份
                theme: '#009688'  //主题  颜色改变
            });
        });
        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'withdrawal/page',
            page: true,
            toolbar: ['<p>',
                // '<button lay-event="add" perm-show="withdrawal:add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>提现</button>&nbsp;',
                '</p>'].join(''),
            cellMinWidth: 100,
            cols: [
                [
                    // {field: 'playerId', title: '用户ID', sort: true},
                    {field: 'agentPeople', title: '代理人', width: 120},
                    {field: 'userAccount', title: '玩家账号', width: 120},
                    // {field: 'phone', title: '手机号', sort: true},
                    {field: 'orderNum', title: '订单编号', width: 140},
                    {field: 'transactionType', title: '币种', width: 90},
                    {field: 'account', title: '提现金额', width: 100, templet:function (d) {
                            return AdminCommon.formatNum(d.account)
                        }},
                    {field: 'chargeFree', title: '手续费', width: 100, templet:function (d) {
                            return AdminCommon.formatNum(d.chargeFree)
                        }},
                    {field: 'arrivalAccount', title: '应到账', width: 120, templet:function (d) {
                            return AdminCommon.formatNum(d.arrivalAccount)
                        }},
                    {field: 'status', title: '状态',templet:function(d){
                            var  str = "";
                            if(d.status == 0){
                                str = "待付款";
                            }else  if(d.status === 1){
                                str = '<span class="layui-badge layui-bg-blue">待审核</span>';
                            }else  if(d.status === 2){
                                str = '<span class="layui-badge layui-bg-green">成功</span>'
                            }else  if(d.status === 3){
                                str = '<span class="layui-badge">失败</span>';
                            }else  if(d.status == 4){
                                str = "处理中";
                            }else  if(d.status == 5){
                                str = "交易超时";
                            }else  if(d.status == 6){
                                str = "交易撤销";
                            }
                            return str;
                        }
                    },
                    // {field: 'rechargeAfterAccount', title: '提现后余额', width: 120},
                    {field: 'currencyAddress', title: '钱包地址'},
                    {field: 'bankDeposit', title: '开户行'},
                    {field: 'bankName', title: '银行名称'},
                    {field: 'bankCard', title: '提现卡号'},
                    {field: 'createTime', title: '申请时间'},
                    {field: 'checkTime', title: '审核时间'},
                    {field: 'checkName', title: '操作人'},
                    {field: 'remark', title: '备注'},
                    {title: '操作', align: 'center', width: 100, templet:function (d) {
                            var  str = "";
                           if(d.status === 1){
                                str = "<a class=\"layui-btn layui-btn-primary layui-btn-xs\"  lay-event=\"check\">审核</a>";
                            }
                            return str;
                        },fixed: 'right'
                    }
                ]
            ],
            done: function(res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);
            }
        });

        /* 表格搜索 */
        form.on('submit(tableSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 表格工具条点击事件 */
        table.on('tool(dataTable)', function (obj) {
            /* 删除 */
            if (obj.event === 'delete') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'transaction/delete', JSON.stringify([{'id': obj.data.id}]), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            /* 编辑 */
            if (obj.event === 'edit') {
                showAddOrUpdateModel(obj.data);
            }
            /* 审核 */
            if (obj.event === 'check') {
                var data = obj.data;
                // $("#transactionId").val(data.id);
                // $("#remark").val('');
                // $('input:radio[name=status]')[0].checked = true;
                layui.form.render()
                admin.open({
                    type:1,
                    area:['500px','300px'],
                    title: '提现审核'
                    ,content: $("#transactionCheck").html(),
                    shade: 0.5,
                    shadeClose:true,
                    success: function (layero, dIndex) {
                        form.val('modelUserForm', {
                            transactionId: data.id,
                            remark: '',
                            status: 2
                        });  // 回显数据
                    },
                    btn: ['提交', '取消']
                    ,btn1: function(index, layero){
                        layer.load(1);
                        var sr = {"transactionId":$("#transactionId").val(),"remark":$("#remark").val(),"status":$('input:radio:checked').val()};
                        admin.req(getProjectUrl() + 'transaction/checkTransaction', JSON.stringify(sr), function(res){
                            layer.msg(res.message, {icon: 1, time: 1000}, function () {
                                layer.closeAll();
                                insTb.reload();
                            });
                        }, 'post');
                    },
                    btn2: function(index, layero){
                        layer.closeAll();
                    },
                    cancel: function(layero,index){
                        layer.closeAll();
                    }

                });
            }
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showAddOrUpdateModel();
            }
            if (obj.event === 'delete') { // 删除
                var checkRows = table.checkStatus('dataTable');
                if (checkRows.data.length === 0) {
                    notice.msg('请选择要操作的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    var ids = checkRows.data.map(function (d) {
                        return {"id": d.id};
                    });
                    admin.req(getProjectUrl() + 'transaction/delete', JSON.stringify(ids), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        // 显示表单弹窗
        function showAddOrUpdateModel(data) {
            var layIndex = admin.open({
                title: (data ? '修改' : '添加') + '提现',
                url: getProjectUrl() + 'withdrawal/addwithdrawal',
                area: ['800px',''],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }

        admin.req(getProjectUrl() + 'currency/withdrawCurrencys', function(res){
            console.log(res);
            var transactionTypeSel = $("select[name=transactionType]");
            for (var i in res.data) {
                var obj = res.data[i]
                transactionTypeSel.append('<option value="' + obj.code + '">' + obj.code + '</option>')
            }
            form.render('select');
        });
    });
</script>
</body>
</html>
