<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">用户id</label>
        <div class="layui-input-block">
            <input name="playerId" class="layui-input" placeholder="请输入用户id" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">金额</label>
        <div class="layui-input-block">
            <input name="account" class="layui-input" placeholder="请输入金额" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">操作类型 1 充值 2 提现 </label>
        <div class="layui-input-block">
            <input name="operationType" class="layui-input" placeholder="请输入操作类型 1 充值 2 提现 " lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">状态 1 已提交 2 审核通过  3 审核不通过 3 处理中</label>
        <div class="layui-input-block">
            <input name="status" class="layui-input" placeholder="请输入状态 1 已提交 2 审核通过  3 审核不通过 3 处理中" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">交易扩展信息</label>
        <div class="layui-input-block">
            <input name="jsonInfo" class="layui-input" placeholder="请输入交易扩展信息" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">操作用户</label>
        <div class="layui-input-block">
            <input name="operationId" class="layui-input" placeholder="请输入操作用户" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">操作时间</label>
        <div class="layui-input-block">
            <input type="text" name="operationTime" id="operationTimeId" placeholder="请选择操作时间" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;

        laydate.render({
            elem: '#operationTimeId'
        });

        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
        }

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'withdrawal/edit' : getProjectUrl() + 'withdrawal/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
