<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">交易币种编码</label>
        <div class="layui-input-block">
            <input name="currencyCode" class="layui-input" placeholder="请输入交易币种编码" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">基础币种编码</label>
        <div class="layui-input-block">
            <input name="baseCurrencyCode" class="layui-input" placeholder="请输入基础币种编码" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">交易类型表示,如ETH_BTC</label>
        <div class="layui-input-block">
            <input name="symbol" class="layui-input" placeholder="请输入交易类型表示,如ETH_BTC" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">币种展现名称,如ETH/BTC</label>
        <div class="layui-input-block">
            <input name="name" class="layui-input" placeholder="请输入币种展现名称,如ETH/BTC" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">0:下线,1:上线</label>
        <div class="layui-input-block">
            <input name="status" class="layui-input" placeholder="请输入0:下线,1:上线" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">热门市场排序，越小排的越靠前</label>
        <div class="layui-input-block">
            <input name="sort" class="layui-input" placeholder="请输入热门市场排序，越小排的越靠前" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">发行价</label>
        <div class="layui-input-block">
            <input name="issuePrice" class="layui-input" placeholder="请输入发行价" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">价格的小数位</label>
        <div class="layui-input-block">
            <input name="priceDecimal" class="layui-input" placeholder="请输入价格的小数位" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">数量的小数位</label>
        <div class="layui-input-block">
            <input name="quantityDecimal" class="layui-input" placeholder="请输入数量的小数位" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">深度小数位</label>
        <div class="layui-input-block">
            <input name="minPriceGroupDecimal" class="layui-input" placeholder="请输入深度小数位" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;


        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
        }

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'market/edit' : getProjectUrl() + 'market/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
