<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">用户id</label>
        <div class="layui-input-block">
            <input name="playerId" class="layui-input" placeholder="请输入用户id" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">产品id</label>
        <div class="layui-input-block">
            <input name="productId" class="layui-input" placeholder="请输入产品id" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">购买金额</label>
        <div class="layui-input-block">
            <input name="purchaseAccount" class="layui-input" placeholder="请输入购买金额" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">周期</label>
        <div class="layui-input-block">
            <input name="cycle" class="layui-input" placeholder="请输入周期" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">购买时间</label>
        <div class="layui-input-block">
            <input type="text" name="purchaseTime" id="purchaseTimeId" placeholder="请选择购买时间" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">计息开始时间</label>
        <div class="layui-input-block">
            <input type="text" name="startInterestData" id="startInterestDataId" placeholder="请选择计息开始时间" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">截止计息时间</label>
        <div class="layui-input-block">
            <input type="text" name="endInterestData" id="endInterestDataId" placeholder="请选择截止计息时间" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">下一计息时间</label>
        <div class="layui-input-block">
            <input type="text" name="nextInterestData" id="nextInterestDataId" placeholder="请选择下一计息时间" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">计息利率</label>
        <div class="layui-input-block">
            <input name="profitRate" class="layui-input" placeholder="请输入计息利率" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">认购金额区间</label>
        <div class="layui-input-block">
            <input name="productAccountInfo" class="layui-input" placeholder="请输入认购金额区间" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">认购利息区间</label>
        <div class="layui-input-block">
            <input name="profitRateInfo" class="layui-input" placeholder="请输入认购利息区间" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">剩余天数</label>
        <div class="layui-input-block">
            <input name="surplusDay" class="layui-input" placeholder="请输入剩余天数" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">收益金额</label>
        <div class="layui-input-block">
            <input name="profitAccount" class="layui-input" placeholder="请输入收益金额" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">违约金比例</label>
        <div class="layui-input-block">
            <input name="violationRate" class="layui-input" placeholder="请输入违约金比例" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">违约金额</label>
        <div class="layui-input-block">
            <input name="violationAccount" class="layui-input" placeholder="请输入违约金额" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">退还本金</label>
        <div class="layui-input-block">
            <input name="returnPrincipal" class="layui-input" placeholder="请输入退还本金" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">订单状态 1 进行中 2 结束 3 赎回</label>
        <div class="layui-input-block">
            <input name="productOrderStatus" class="layui-input" placeholder="请输入订单状态 1 进行中 2 结束 3 赎回" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">产品信息</label>
        <div class="layui-input-block">
            <input name="productInfo" class="layui-input" placeholder="请输入产品信息" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;

        laydate.render({
            elem: '#purchaseTimeId'
        });
        laydate.render({
            elem: '#startInterestDataId'
        });
        laydate.render({
            elem: '#endInterestDataId'
        });
        laydate.render({
            elem: '#nextInterestDataId'
        });

        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
        }

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'userProductOrder/edit' : getProjectUrl() + 'userProductOrder/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
