<!DOCTYPE html>
<html lang="zh_CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <script>if (window !== top) top.location.replace(location.href);</script>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="icon" href="${ctxPath}/assets/images/favicon.ico">
    <title>交易所管理后台</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body class="layui-layout-body">
<div class="layui-layout layui-layout-admin">
    <!-- 头部 -->
    <div class="layui-header">
        <div class="layui-logo">
            <img src="${ctxPath}/assets/images/logo.png"/>
            <cite>&nbsp;交易所管理后台</cite>
        </div>
        <ul class="layui-nav layui-layout-left" id="snowy-nav-top-box">
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="flexible" title="侧边伸缩"><i class="layui-icon layui-icon-shrink-right"></i></a>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="refresh" title="刷新"><i class="layui-icon layui-icon-refresh-3"></i></a>
            </li>
        </ul>
        <ul class="layui-nav layui-layout-right">
            <li class="layui-nav-item" lay-unselect>
                <a>
                    <strong style="background-color: #009688;color: white;line-height: 20px;height: 22px;padding: 8px;border-radius: 3px;">在线人数：<span id="top_onlineCount"></span></strong>
                </a>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a>
                    <strong style="background-color: #5FB878;color: white;line-height: 20px;height: 22px;padding: 8px;border-radius: 3px;">充值：<span id="top_rechargeCount"></span></strong>
                </a>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a>
                    <strong style="background-color: #393D49;color: white;line-height: 20px;height: 22px;padding: 8px;border-radius: 3px;">提款：<span id="top_withdrawalCount"></span></strong>
                </a>
            </li>
<!--            <li class="layui-nav-item" lay-unselect>
                <a ew-event="message" title="消息" data-url="${ctxPath}/other/messageHtml">
                    <i class="layui-icon layui-icon-notice"></i>
                    <% if(hasUnreadMsg) { %>
                    <span class="layui-badge-dot"></span>
                    <% } %>
                </a>
            </li>-->
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="note" data-url="${ctxPath}/other/noteHtml" title="便签"><i class="layui-icon layui-icon-note"></i></a>
            </li>
            <li class="layui-nav-item layui-hide-xs" lay-unselect>
                <a ew-event="fullScreen" title="全屏"><i class="layui-icon layui-icon-screen-full"></i></a>
            </li>
            <li class="layui-nav-item layui-hide-xs" lay-unselect>
                <a ew-event="lockScreen" data-url="${ctxPath}/other/lockScreenHtml" title="锁屏"><i class="layui-icon layui-icon-password"></i></a>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a>
                    <img src="${ctxPath}/sysFileInfo/preview?id=${loginUser.avatar}" class="layui-nav-img">
                    <cite>${loginUser.name}</cite>
                </a>
                <dl class="layui-nav-child">
                    <dd lay-unselect><a ew-href="${ctxPath}/other/userInfoHtml">个人中心</a></dd>
                    <dd lay-unselect><a ew-event="psw" data-url="${ctxPath}/other/updatePasswordHtml">修改密码</a></dd>
                    <hr>
                    <dd lay-unselect><a ew-event="logout" data-url="${ctxPath}/logout">退出</a></dd>
                </dl>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="theme" title="主题" data-url="${ctxPath}/other/themeHtml"><i class="layui-icon layui-icon-more-vertical"></i></a>
            </li>
        </ul>
    </div>

    <!-- 侧边栏 -->
    <div class="layui-side">
        <div class="layui-side-scroll">

        </div>
    </div>

    <!-- 主体部分 -->
    <div class="layui-body"></div>
    <!-- 底部 -->
    <div class="layui-footer layui-text">
        copyright © 2022 all rights reserved.
<!--        <span class="pull-right">snowy-layui 1.6.0</span>-->
    </div>
</div>

<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- 侧边栏渲染模板 -->
<script id="sideNav" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
        {{# if(index === 0){ }}
            <ul class="layui-nav layui-nav-tree" lay-shrink="_all" lay-filter="admin-side-nav" nav-id="{{item.code}}" style="margin: 15px 0;">
                {{#  layui.each(item.value, function(index, menuItem){ }}
                        <li class="layui-nav-item">
                            <a lay-href="{{menuItem.value}}"><i class="layui-icon {{menuItem.icon}}"></i>&emsp;<cite>{{ menuItem.title }}</cite></a>
                            {{# if(menuItem.children&&menuItem.children.length>0){ getSubMenus(menuItem.children); } }}
                        </li>
                {{#  }); }}
            </ul>
        {{# } else { }}
            <ul class="layui-nav layui-nav-tree layui-hide" lay-shrink="_all" lay-filter="admin-side-nav" nav-id="{{item.code}}" style="margin: 15px 0;">
                {{#  layui.each(item.value, function(index, menuItem){ }}
                    <li class="layui-nav-item">
                        <a lay-href="{{menuItem.value}}"><i class="layui-icon {{menuItem.icon}}"></i>&emsp;<cite>{{ menuItem.title }}</cite></a>
                        {{# if(menuItem.children&&menuItem.children.length>0){ getSubMenus(menuItem.children); } }}
                    </li>
                {{#  }); }}
            </ul>
        {{# } }}
    {{#  }); }}

    {{# function getSubMenus(children){ }}
    <dl class="layui-nav-child">
        {{# layui.each(children, function(index, subItem){ }}
        <dd>
            <a lay-href="${ctxPath}{{ subItem.value }}">{{ subItem.title }}</a>
            {{# if(subItem.children&&subItem.children.length>0){ getSubMenus(subItem.children); } }}
        </dd>
        {{# }); }}
    </dl>
    {{# } }}
</script>

<!-- 顶部系统栏渲染模板 -->
<script id="topNav" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
        {{# if(item.active){ }}
                <li class="layui-nav-item layui-hide-xs layui-this" lay-unselect><a nav-bind="{{item.code}}">{{ item.name }}</a></li>
         {{# } else { }}
                <li class="layui-nav-item layui-hide-xs" lay-unselect><a nav-bind="{{item.code}}"> {{item.name}} </a></li>
         {{# } }}
    {{#  }); }}
    <!-- 小屏幕下变为下拉形式 -->
    <li class="layui-nav-item layui-hide-sm layui-show-xs-inline-block" lay-unselect>
        <a>更多</a>
        <dl class="layui-nav-child" id="nav-sm">
            {{#  layui.each(d, function(index, item){ }}
                <dd lay-unselect><a nav-bind="{{item.code}}"> {{item.name}} </a></dd>
            {{#  }); }}
        </dl>
    </li>
</script>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'element', 'admin', 'index', 'laytpl', 'element', 'notice', 'xnUtil'], function () {
        var $ = layui.jquery;
        var admin = layui.admin;
        var index = layui.index;
        var laytpl = layui.laytpl;
        var element = layui.element;
        var xnUtil = layui.xnUtil;

        //缓存字典
        xnUtil.cacheDictData();

        // ajax渲染侧边栏
        admin.req(getProjectUrl() + 'getLoginUser', function(res){
            //将用户信息缓存到本地
            layui.data(admin.setter.tableName, {key: 'loginUser', value: res.data});

            //开启webSocket，一定要放在用户信息缓存后，否则取的还是上次的登录信息
            xnUtil.openWebSocket();
            //渲染顶部系统栏
            laytpl(topNav.innerHTML).render(res.data.apps, function (html) {
                $('#snowy-nav-top-box').append(html);
            });

            //渲染左侧菜单
            laytpl(sideNav.innerHTML).render(res.data.menus, function (html) {
                $('.layui-side-scroll').html(html);
                element.render('nav');
                // 加载页面
                index.loadHome({
                    menuPath: getProjectUrl() + 'other/welcomeHtml',
                    menuName: '<i class="layui-icon layui-icon-home"></i>',
                    // 刷新后默认打开上次的页签
                    loadSetting: true,
                    // 并且只打开最后一个页签
                    onlyLast: true
                });
            });
        });

        function getUnreadCount() {
            admin.req(getProjectUrl() + 'getUnreadCount', function(res){
                $("#top_onlineCount").text(res.data.onlineFlagCount);
                $("#top_rechargeCount").text(res.data.rechargeCount);
                $("#top_withdrawalCount").text(res.data.withdrawalCount);
            });
        }
        getUnreadCount();
        setInterval(getUnreadCount, 15 * 1000);
    });
</script>
</body>
</html>
