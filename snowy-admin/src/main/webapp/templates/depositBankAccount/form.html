<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">户名</label>
        <div class="layui-input-block">
            <input name="userName" class="layui-input" placeholder="请输入户名" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">银行卡号</label>
        <div class="layui-input-block">
            <input name="bankCardNumber" class="layui-input" placeholder="请输入银行卡号" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">银行</label>
        <div class="layui-input-block">
            <input name="bankName" class="layui-input" placeholder="请输入银行" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">银行logo</label>
        <div class="layui-input-block">
            <button id="file-btn-upload" class="layui-btn layui-btn-sm layui-btn-normal icon-btn" type='button'>上传文件</button>
            <br/>
            <span>图片格式：jpg/jpeg/png/bmp/GIF/JPG/PNG/JPEG</span>
            <br/>
            <br/>
            <div id='imageDiv' style='display: inline-block; vertical-align: top;'></div>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script src="${ctxPath}/assets/js/common.js?v=1"></script>
<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate','upload', 'fileChoose'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;
        var upload = layui.upload;
        var fileChoose = layui.fileChoose;
        var imgurl = '';


        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
            $("#imageDiv").html("<img src='" + editData.logoUrl + "' style='width: 375px; margin-right: 20px;'/>");
        }

        // 上传
        upload.render({
            elem: '#file-btn-upload',
            accept: 'imgUrl',
            url: getProjectUrl() + 's3/file/upload',
            done: function(res){
                handleNetworkError(res);
                if(res.success) {
                    layer.msg(res.message, {icon: 1, time: 1000}, function () {
                        $("#imageDiv").html("<img src='" + res.data + "' style='width: 375px; margin-right: 20px;'/>");
                    });
                    imgurl = res.data;
                }
            }
        });

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);
            data.field.logoUrl = imgurl;

            var url = editData ? getProjectUrl() + 'depositBankAccount/edit' : getProjectUrl() + 'depositBankAccount/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
