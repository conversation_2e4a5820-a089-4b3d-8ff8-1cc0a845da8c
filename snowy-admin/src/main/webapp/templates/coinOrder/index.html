<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>币币订单</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">玩家账号:</label>
                        <div class="layui-input-inline">
                            <input name="playerAccount" class="layui-input" placeholder="请输入玩家账号"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">订单号:</label>
                        <div class="layui-input-inline">
                            <input name="orderNo" class="layui-input" placeholder="请输入订单号"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">类型:</label>
                        <div class="layui-input-inline">
                            <select name="type">
                                <option value="">所有</option>
                                <option value="1">买</option>
                                <option value="2">卖</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">交易类型:</label>
                        <div class="layui-input-inline">
                            <input name="symbol" class="layui-input" placeholder="请输入交易类型,如ETH_USDT"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">委托类型:</label>
                        <div class="layui-input-inline">
                            <select name="entrustType">
                                <option value="">所有</option>
                                <option value="1">市价委托</option>
                                <option value="2">限价委托</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态:</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">所有</option>
                                <option value="0">委托</option>
<!--                                <option value="1">持仓</option>-->
                                <option value="2">成交</option>
                                <option value="3">撤单</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="tableSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    {{# if (d.status == 0) { }}
    <a class="layui-btn layui-btn-xs" perm-show="coinOrder:close" lay-event="close">成交</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" perm-show="coinOrder:revoke" lay-event="revoke">撤单</a>
    {{# } }}
</script>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var laydate = layui.laydate;

        //日期时间选择器
        laydate.render({
            elem: 'orderTimeId'
            ,type: 'datetime'
        });
        //日期时间选择器
        laydate.render({
            elem: 'closeTimeId'
            ,type: 'datetime'
        });

        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'coinOrder/page',
            page: true,
            toolbar: ['<p>',
                // '<button lay-event="add" perm-show="coinOrder:add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>&nbsp;',
                // '<button lay-event="delete" perm-show="coinOrder:delete" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>删除</button>',
                '</p>'].join(''),
            cellMinWidth: 100,
            cols: [
                [
                    // {type: 'checkbox'},
                    {field: 'playerAccount', title: '玩家账号', width: 120},
                    {field: 'orderNo', title: '订单号', width: 130},
                    {field: 'type', title: '类型', templet: function (d) {
                            if (d.type === 1) {
                                return '<span style="color: green;">买</span>'
                            }
                            if (d.type === 2) {
                                return '<span style="color: red;">卖</span>'
                            }
                        }, width: 80},
                    {field: 'symbol', title: '交易类型', width: 110 },
                    {field: 'entrustType', title: '委托类型', templet: function (d) {
                            if (d.entrustType === 1) {
                                return '<span style="color: orange;">市价委托</span>'
                            }
                            if (d.entrustType === 2) {
                                return '<span style="color: cornflowerblue;">限价委托</span>'
                            }
                        }, width: 90},
                    {field: 'orderTime', title: '下单时间', width: 180},
                    {field: 'orderPrice', title: '下单价格'},
                    {field: 'quantity', title: '下单数量'},
                    {field: 'status', title: '状态', templet: function (d) {
                            if (d.status === 0) {
                                return '<span style="color: cornflowerblue;">委托</span>'
                            }
                            if (d.status === 1) {
                                // return '持仓'
                            }
                            if (d.status === 2) {
                                return '<span style="color: green;">成交</span>'
                            }
                            if (d.status === 3) {
                                return '<span style="color: red;">撤单</span>'
                            }
                        }, width: 100},
                    {field: 'closePrice', title: '成交价格'},
                    {field: 'closeTime', title: '成交时间', width: 180},
                    {field: 'amount', title: '成交额'},
                    {field: 'orderFee', title: '手续费'},
                    {title: '操作', toolbar: '#tableBar', align: 'center', width: 120, minWidth: 100, fixed: 'right'}
                ]
            ],
            done: function(res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);
            }
        });

        /* 表格搜索 */
        form.on('submit(tableSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 表格工具条点击事件 */
        table.on('tool(dataTable)', function (obj) {
            /* 成交 */
            if (obj.event === 'close') {
                showCloseModel(obj.data);
            }
            /* 撤单 */
            if (obj.event === 'revoke') {
                layer.confirm('确定要撤单吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'coinOrder/revoke', {orderNo: obj.data.orderNo, playerId: obj.data.playerId}, function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                // showAddOrUpdateModel();
            }
            if (obj.event === 'delete') { // 删除
                var checkRows = table.checkStatus('dataTable');
                if (checkRows.data.length === 0) {
                    notice.msg('请选择要操作的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    var ids = checkRows.data.map(function (d) {
                        return {"id": d.id};
                    });
                    admin.req(getProjectUrl() + 'coinOrder/delete', JSON.stringify(ids), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        // 显示表单弹窗
        function showCloseModel(data) {
            var layIndex = admin.open({
                title: '币币订单成交',
                url: getProjectUrl() + 'coinOrder/closeForm',
                area: ['500px',''],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    var timer = admin.getLayerData(layIndex, 'timer');
                    console.log('关闭closeForm', timer);
                    if (timer) {
                        clearInterval(timer);
                    }
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }
    });
</script>
</body>
</html>
