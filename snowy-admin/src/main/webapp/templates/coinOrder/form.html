<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">玩家ID</label>
        <div class="layui-input-block">
            <input name="playerId" class="layui-input" placeholder="请输入玩家ID" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">订单号</label>
        <div class="layui-input-block">
            <input name="orderNo" class="layui-input" placeholder="请输入订单号" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">类型(1:买;2:卖;)</label>
        <div class="layui-input-block">
            <input name="type" class="layui-input" placeholder="请输入类型(1:买;2:卖;)" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">委托类型(1:市价委托;2:限价委托;)</label>
        <div class="layui-input-block">
            <input name="entrustType" class="layui-input" placeholder="请输入委托类型(1:市价委托;2:限价委托;)" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">币种编码</label>
        <div class="layui-input-block">
            <input name="currencyCode" class="layui-input" placeholder="请输入币种编码" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">基础币种编码</label>
        <div class="layui-input-block">
            <input name="baseCurrencyCode" class="layui-input" placeholder="请输入基础币种编码" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">下单时间</label>
        <div class="layui-input-block">
            <input type="text" name="orderTime" id="orderTimeId" placeholder="请选择下单时间" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">下单价格</label>
        <div class="layui-input-block">
            <input name="orderPrice" class="layui-input" placeholder="请输入下单价格" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">下单数量</label>
        <div class="layui-input-block">
            <input name="quantity" class="layui-input" placeholder="请输入下单数量" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">成交价格</label>
        <div class="layui-input-block">
            <input name="closePrice" class="layui-input" placeholder="请输入成交价格" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">成交时间</label>
        <div class="layui-input-block">
            <input type="text" name="closeTime" id="closeTimeId" placeholder="请选择成交时间" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">手续费</label>
        <div class="layui-input-block">
            <input name="orderFee" class="layui-input" placeholder="请输入手续费" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">0:委托,1:持仓,2:成交,3:撤销委托</label>
        <div class="layui-input-block">
            <input name="status" class="layui-input" placeholder="请输入0:委托,1:持仓,2:成交,3:撤销委托" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;

        laydate.render({
            elem: '#orderTimeId'
        });
        laydate.render({
            elem: '#closeTimeId'
        });

        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
        }

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'coinOrder/edit' : getProjectUrl() + 'coinOrder/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
