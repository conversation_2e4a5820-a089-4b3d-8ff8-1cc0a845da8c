<form id="addOrUpdateForm">
    <ul class="layui-row site-doc-color site-doc-necolor" >
        <li class="layui-col-md12">
            <div style="background-color: #f6f6f6;" id="baseInfo"></div>
        </li>
        <li class="layui-col-md12">
            <div style="background-color: #eeeeee;">
               <div style="display: inline-block;">
                   <span style="color: #69c0ff;font-size: 17px;">市价：<span id="entrustType1"></span></span>
                   <br/>
                   <span>预计成交：<span style="font-weight: bold;" id="entrustType1closeValue"></span></span>
               </div>
               <div style="float: right;">
                   <a class="layui-btn" onclick="endCoinOrder(1)">市价成交</a>
               </div>
            </div>
        </li>
        <li class="layui-col-md12">
            <div style="background-color: #e2e2e2;">
                <div style="display: inline-block;">
                    <span style="color: green;font-size: 17px;">限价：<span id="entrustType2"></span></span>
                    <br/>
                    <span>预计成交：<span style="font-weight: bold;" id="entrustType2closeValue"></span></span>
                </div>
                <div style="float: right;">
                    <a class="layui-btn" onclick="endCoinOrder(2)">限价成交</a>
                </div>
            </div>
        </li>
        <li class="layui-col-md12">
            <div style="background-color: #dddddd;">
                <div style="display: inline-block;">
                    <span style="color: #69c0ff;font-size: 17px;">价格：<input id="inputPrice" type="number" class="layui-input" style="display: inline-block;width: 150px;"></span>
                    <br/>
                    <span>预计成交：<span style="font-weight: bold;" id="customizeCloseValue"></span></span>
                </div>
                <div style="float: right;">
                    <a class="layui-btn" onclick="endCoinOrder(3)">自定义价格成交</a>
                </div>
            </div>
        </li>
        <!--<li class="layui-col-md2">
            <div style="background-color: #d2d2d2;"><p>#d2d2d2</p><p></p></div>
        </li>
        <li class="layui-col-md2">
            <div style="background-color: #cccccc;"><p>#cccccc</p><p></p></div>
        </li>
        <li class="layui-col-md2">
            <div style="background-color: #c2c2c2;"><p>#c2c2c2</p><p></p></div>
        </li>-->
    </ul>
    <input type="hidden" id="orderNo">
</form>
<style>
    body {
        background-color: #f6f6f6;
    }
    .site-doc-necolor li div {
        border-radius: 0;
        color: #000;
    }
    .site-doc-color li div {
        padding: 12px 8px;
        text-align: left;
        border-radius: 2px;
        line-height: 1.6;
        font-size: 14px;
    }
</style>
<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;



        var editData = admin.getLayerData('#addOrUpdateForm').data;
        console.log('获取到数据', editData);
        $("#orderNo").val(editData.orderNo);
        //限价
        $("#entrustType2").text(editData.orderPrice);

        if (editData.type === 1) {
            $("#baseInfo").html('买入  ' + editData.symbol);
        }
        if (editData.type === 2) {
            $("#baseInfo").html('卖出  ' + editData.symbol);
        }

        $("#entrustType2closeValue").text(calcCloseValue(editData.type, editData.orderPrice, editData.quantity));


        $("#inputPrice").bind('input propertychange change',function(){
            var a = $(this).val();
            $("#customizeCloseValue").text(calcCloseValue(editData.type, a, editData.quantity));
        });


        function getLatestPrice(_symbol) {
            admin.req(getProjectUrl() + 'market/getLatestPrice', {symbol: _symbol}, function(res){
                // console.log(res)
                $("#entrustType1").text(res.data);
                $("#entrustType1closeValue").text(calcCloseValue(editData.type, res.data, editData.quantity));
            }, 'get');
        }

        var timer = setInterval(function(){
            getLatestPrice(editData.symbol);
        }, 2000);
        admin.putLayerData('timer', timer, '#addOrUpdateForm');

        endCoinOrder = function(t) {
            var orderNo = $("#orderNo").val();
            var price = '';
            if (t === 1) {
                price = $("#entrustType1").text();
            }
            if (t === 2) {
                price = $("#entrustType2").text();
            }
            if (t === 3) {
                price = $("#inputPrice").val();
            }
            if (!price) {
                layer.alert('价格为空');
                return
            }
            console.log(orderNo, price)
            admin.req(getProjectUrl() + 'coinOrder/endCoinOrder', {orderNo: orderNo, c: price}, function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                    if (timer) {
                        //关闭定时器
                        clearInterval(timer);
                    }
                });
            }, 'post');
        }
    });
    function calcCloseValue(type, price, quantity) {
        if (type === 1) {
            return quantity/price;
        }
        if (type === 2) {
            return quantity*price;
        }
        return -1;
    }


</script>
</body>
</html>
