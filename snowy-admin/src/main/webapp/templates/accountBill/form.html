<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">玩家ID</label>
        <div class="layui-input-block">
            <input name="playerId" class="layui-input" placeholder="请输入玩家ID" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">类型(1:币币账户;2:合约账户;3:现货账户)</label>
        <div class="layui-input-block">
            <input name="type" class="layui-input" placeholder="请输入类型(1:币币账户;2:合约账户;3:现货账户)" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">币种代码</label>
        <div class="layui-input-block">
            <input name="currencyCode" class="layui-input" placeholder="请输入币种代码" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">账户ID</label>
        <div class="layui-input-block">
            <input name="accountId" class="layui-input" placeholder="请输入账户ID" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">变动之后余额</label>
        <div class="layui-input-block">
            <input name="balanceAfter" class="layui-input" placeholder="请输入变动之后余额" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">变动余额</label>
        <div class="layui-input-block">
            <input name="balanceChanged" class="layui-input" placeholder="请输入变动余额" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">业务号</label>
        <div class="layui-input-block">
            <input name="businessNo" class="layui-input" placeholder="请输入业务号" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">业务类型</label>
        <div class="layui-input-block">
            <input name="businessType" class="layui-input" placeholder="请输入业务类型" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">操作类型</label>
        <div class="layui-input-block">
            <input name="optType" class="layui-input" placeholder="请输入操作类型" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">冻结操作类型</label>
        <div class="layui-input-block">
            <input name="frozenOptType" class="layui-input" placeholder="请输入冻结操作类型" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">冻结变动之后余额</label>
        <div class="layui-input-block">
            <input name="frozenAfter" class="layui-input" placeholder="请输入冻结变动之后余额" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">冻结变动余额</label>
        <div class="layui-input-block">
            <input name="frozenChanged" class="layui-input" placeholder="请输入冻结变动余额" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;


        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
        }

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'accountBill/edit' : getProjectUrl() + 'accountBill/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
