<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>交易员申请</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">用户id:</label>
                        <div class="layui-input-inline">
                            <input name="playerId" class="layui-input" placeholder="请输入用户id"/>
                        </div>
                    </div>
<!--                    <div class="layui-inline">-->
<!--                        <label class="layui-form-label">个人简介:</label>-->
<!--                        <div class="layui-input-inline">-->
<!--                            <input name="profile" class="layui-input" placeholder="请输入个人简介"/>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="layui-inline">-->
<!--                        <label class="layui-form-label">标签（英文逗号分隔）:</label>-->
<!--                        <div class="layui-input-inline">-->
<!--                            <input name="tagList" class="layui-input" placeholder="请输入标签（英文逗号分隔）"/>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="layui-inline">-->
<!--                        <label class="layui-form-label">交易经验:</label>-->
<!--                        <div class="layui-input-inline">-->
<!--                            <input name="experience" class="layui-input" placeholder="请输入交易经验"/>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="layui-inline">-->
<!--                        <label class="layui-form-label">预期年化收益率 (%):</label>-->
<!--                        <div class="layui-input-inline">-->
<!--                            <input name="expectedAnnualYield" class="layui-input"-->
<!--                                   placeholder="请输入预期年化收益率 (%)"/>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="layui-inline">-->
<!--                        <label class="layui-form-label">风险偏好:</label>-->
<!--                        <div class="layui-input-inline">-->
<!--                            <input name="riskPreference" class="layui-input" placeholder="请输入风险偏好"/>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="layui-inline">-->
<!--                        <label class="layui-form-label">交易截图URL:</label>-->
<!--                        <div class="layui-input-inline">-->
<!--                            <input name="tradeUrl" class="layui-input" placeholder="请输入交易截图URL"/>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="layui-inline">-->
<!--                        <label class="layui-form-label">联系方式:</label>-->
<!--                        <div class="layui-input-inline">-->
<!--                            <input name="contactInfo" class="layui-input" placeholder="请输入联系方式"/>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="layui-inline">-->
<!--                        <label class="layui-form-label">审核状态（0待审核，1通过，2拒绝）:</label>-->
<!--                        <div class="layui-input-inline">-->
<!--                            <input name="auditStatus" class="layui-input"-->
<!--                                   placeholder="请输入审核状态（0待审核，1通过，2拒绝）"/>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="layui-inline">-->
<!--                        <label class="layui-form-label">审核备注:</label>-->
<!--                        <div class="layui-input-inline">-->
<!--                            <input name="auditRemark" class="layui-input" placeholder="请输入审核备注"/>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="tableSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" perm-show="followerApplication:edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" perm-show="followerApplication:delete"
       lay-event="delete">删除</a>
</script>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var laydate = layui.laydate;


        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'followerApplication/page',
            page: true,
            toolbar: ['<p>',
                '<button lay-event="add" perm-show="followerApplication:add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>&nbsp;',
                '<button lay-event="delete" perm-show="followerApplication:delete" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>删除</button>',
                '</p>'].join(''),
            cellMinWidth: 100,
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'playerId', title: '用户id', sort: true},
                    {field: 'profile', title: '个人简介'},
                    {field: 'tagList', title: '标签'},
                    // 其他列
                    {
                        field: 'experience',
                        title: '交易经验',
                        sort: true,
                        templet: function(d) {
                            var experienceLabels = ["1年以下", "1-2年", "2-5年", "5-10年", "10年以上"];
                            var idx = parseInt(d.experience, 10);
                            return experienceLabels[idx] || d.experience;
                        }
                    },
                    {
                        field: 'riskPreference',
                        title: '风险偏好',
                        sort: true,
                        templet: function(d) {
                            var riskLabels = ["保守型", "稳健型", "平衡型", "成长型", "激进型"];
                            var idx = parseInt(d.riskPreference, 10);
                            return riskLabels[idx] || d.riskPreference;
                        }
                    },
                    {field: 'expectedAnnualYield', title: '预期年化收益率 (%)'},
                    {
                        field: 'tradeUrl',
                        title: '交易截图',
                        templet: function(d) {
                            if(d.tradeUrl) {
                                return '<a href="' + d.tradeUrl + '" target="_blank">' +
                                    '<img src="' + d.tradeUrl + '" style="height:40px;"/></a>';
                            }
                        }
                    },
                    {field: 'contactInfo', title: '联系方式'},
                    {
                        field: 'auditStatus',
                        title: '审核状态',
                        templet: function(d) {
                            var statusMap = {
                                0: '待审核',
                                1: '通过',
                                2: '拒绝'
                            };
                            return statusMap[d.auditStatus] || d.auditStatus;
                        }
                    },
                    {field: 'createTime', title: '申请时间'},
                    {field: 'auditRemark', title: '审核备注'},
                    // {title: '操作', toolbar: '#tableBar', align: 'center', width: 200, minWidth: 200}
                    {
                        title: '操作',
                        toolbar: '#tableBar',
                        align: 'center',
                        fixed: 'right',  // 避免浮动列产生空位
                        width: 160
                    }
                ]
            ],
            done: function (res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);
            }
        });

        /* 表格搜索 */
        form.on('submit(tableSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 表格工具条点击事件 */
        table.on('tool(dataTable)', function (obj) {
            /* 删除 */
            if (obj.event === 'delete') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'followerApplication/delete', JSON.stringify([{'id': obj.data.id}]), function (res) {
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            /* 编辑 */
            if (obj.event === 'edit') {
                showAddOrUpdateModel(obj.data);
            }
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showAddOrUpdateModel();
            }
            if (obj.event === 'delete') { // 删除
                var checkRows = table.checkStatus('dataTable');
                if (checkRows.data.length === 0) {
                    notice.msg('请选择要操作的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    var ids = checkRows.data.map(function (d) {
                        return {"id": d.id};
                    });
                    admin.req(getProjectUrl() + 'followerApplication/delete', JSON.stringify(ids), function (res) {
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        // 显示表单弹窗
        function showAddOrUpdateModel(data) {
            var layIndex = admin.open({
                title: (data ? '修改' : '添加') + '交易员申请',
                url: getProjectUrl() + 'followerApplication/form',
                area: ['800px', ''],
                data: {data: data},     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }
    });
</script>
</body>
</html>
