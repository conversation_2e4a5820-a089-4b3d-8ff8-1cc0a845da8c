<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">用户id</label>
        <div class="layui-input-block">
            <input name="playerId" class="layui-input" placeholder="请输入用户id" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">个人简介</label>
        <div class="layui-input-block">
            <input name="profile" class="layui-input" placeholder="请输入个人简介" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">标签（英文逗号分隔）</label>
        <div class="layui-input-block">
            <input name="tagList" class="layui-input" placeholder="请输入标签（英文逗号分隔）" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">交易经验</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="experience" class="layui-input" placeholder="请输入交易经验" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">交易经验</label>
        <div class="layui-input-block">
            <select name="experience" lay-verType="tips" lay-verify="required" required>
                <option value="">请选择</option>
                <option value="0">1年以下</option>
                <option value="1">1-2年</option>
                <option value="2">2-5年</option>
                <option value="3">5-10年</option>
                <option value="4">10年以上</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">预期年化收益率 (%)</label>
        <div class="layui-input-block">
            <input name="expectedAnnualYield" class="layui-input" placeholder="请输入预期年化收益率 (%)" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">风险偏好</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="riskPreference" class="layui-input" placeholder="请输入风险偏好" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">风险偏好</label>
        <div class="layui-input-block">
            <select name="riskPreference" lay-verType="tips" lay-verify="required" required>
                <option value="">请选择</option>
                <option value="0">保守型</option>
                <option value="1">稳健型</option>
                <option value="2">平衡型</option>
                <option value="3">成长型</option>
                <option value="4">激进型</option>
            </select>
        </div>
    </div>

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">交易截图URL</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="tradeUrl" class="layui-input" placeholder="请输入交易截图URL" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">交易截图</label>
        <div class="layui-input-block">
            <button id="file-btn-upload" class="layui-btn layui-btn-sm layui-btn-normal icon-btn" type='button'>上传文件</button>
            <br/>
            <span>图片格式：jpg/jpeg/png/bmp/GIF/JPG/PNG/JPEG</span>
            <br/>
            <br/>
            <div id='imageDiv' style='display: inline-block; vertical-align: top;'></div>
        </div>
    </div>
<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">交易截图URL</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="tradeUrl" class="layui-input" placeholder="请输入交易截图URL" lay-verType="tips" lay-verify="required" required id="tradeUrlInput"/>-->
<!--            <div style="margin-top: 8px;" id="tradeUrlPreview">-->
<!--                <img src="" id="tradeUrlImg" style="max-height: 100px; display: none; border: 1px solid #eee; padding: 3px;">-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">联系方式</label>
        <div class="layui-input-block">
            <input name="contactInfo" class="layui-input" placeholder="请输入联系方式" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">审核状态（0待审核，1通过，2拒绝）</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="auditStatus" class="layui-input" placeholder="请输入审核状态（0待审核，1通过，2拒绝）" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">审核状态</label>
        <div class="layui-input-block">
            <select name="auditStatus" lay-verType="tips" lay-verify="required" required>
                <option value="">请选择</option>
                <option value="0">待审核</option>
                <option value="1">通过</option>
                <option value="2">拒绝</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">审核备注</label>
        <div class="layui-input-block">
            <input name="auditRemark" class="layui-input" placeholder="请输入审核备注" lay-verType="tips"/>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate', 'upload'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;

        var upload = layui.upload;
        var imgurl = '';

        // 上传
        upload.render({
            elem: '#file-btn-upload',
            accept: 'imgUrl',
            url: getProjectUrl() + 's3/file/upload',
            done: function(res){
                handleNetworkError(res);
                if(res.success) {
                    layer.msg(res.message, {icon: 1, time: 1000}, function () {
                        $("#imageDiv").html("<img src='" + res.data + "' style='width: 375px; margin-right: 20px;'/>");
                    });
                    imgurl = res.data;
                }
            }
        });


        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
            $("#imageDiv").html("<img src='" + editData.tradeUrl + "' style='width: 375px; margin-right: 20px;'/>");
            imgurl = editData.tradeUrl
        }

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);
            data.field.tradeUrl = imgurl;

            var url = editData ? getProjectUrl() + 'followerApplication/edit' : getProjectUrl() + 'followerApplication/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
