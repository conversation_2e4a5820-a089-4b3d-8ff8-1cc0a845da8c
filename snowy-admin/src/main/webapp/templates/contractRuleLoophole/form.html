<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">合约规则（秒）</label>
        <div class="layui-input-block">
            <div id="secondSelectBox"></div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">开始时间:</label>
        <div class="layui-input-block">
            <input type="text" name="startDate" id="startDate" placeholder="请选择开始时间" class="layui-input laydate">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">结束时间:</label>
        <div class="layui-input-block">
            <input type="text" name="endDate" id="endDate" placeholder="请选择结束时间" class="layui-input laydate">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">赔率</label>
        <div class="layui-input-block">
            <input name="rate" class="layui-input" placeholder="请输入赔率" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;

        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
        }

        //日期时间选择器
        $(".laydate").each(function(){
            laydate.render({
                elem: this, //指定元素  表示当前的元素
                type: 'time', //date日期  time时间  year年  month月份
                theme: '#009688'  //主题  颜色改变
            });
        });

        // 渲染远程数据下拉
        var contractRule = editData ? [editData.contractRuleId] : [];
        admin.req(getProjectUrl() + 'spotContractRule/list', function(res){
            // 渲染下拉树
            xmSelect.render({
                el: '#secondSelectBox',
                name: 'contractRuleId',
                layVerify: 'required',
                layVerType: 'tips',
                data: res.data,
                initValue: contractRule,
                model: {label: {type: 'text'}},
                clickClose: true,
                prop: {
                    name: 'seconds',
                    value: 'id'
                },
                radio: true,
                tips: '请选择规则'
            });
        }, {async: false});

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'contractRuleLoophole/edit' : getProjectUrl() + 'contractRuleLoophole/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
