<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">名称</label>
        <div class="layui-input-block">
            <input name="name" class="layui-input" placeholder="请输入名称" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">logo</label>
        <div class="layui-input-block">
            <input name="logo" class="layui-input" placeholder="请输入logo" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">代码</label>
        <div class="layui-input-block">
            <input name="code" class="layui-input" placeholder="请输入代码" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">提现手续费</label>
        <div class="layui-input-block">
            <input name="withdrawFee" class="layui-input" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">0:下线,1:上线</label>
        <div class="layui-input-block">
            <input name="status" class="layui-input" placeholder="请输入0:下线,1:上线" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">排序，数值越小，排序越靠前</label>
        <div class="layui-input-block">
            <input name="sort" class="layui-input" placeholder="请输入排序，数值越小，排序越靠前" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">充值开关：0:关 1:开</label>
        <div class="layui-input-block">
            <input name="chargeSwitch" class="layui-input" placeholder="请输入充值开关：0:关 1:开" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">提现开关：0关，1开</label>
        <div class="layui-input-block">
            <input name="withdrawSwitch" class="layui-input" placeholder="请输入提现开关：0关，1开" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最小提币数量</label>
        <div class="layui-input-block">
            <input name="minWithdrawAmount" class="layui-input" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">单次最大提币额</label>
        <div class="layui-input-block">
            <input name="currencyWithdrawLimit" class="layui-input" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;


        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
        }

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'currency/edit' : getProjectUrl() + 'currency/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
