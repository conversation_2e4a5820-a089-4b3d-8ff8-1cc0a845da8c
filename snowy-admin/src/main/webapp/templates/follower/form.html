<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">用户id</label>
        <div class="layui-input-block">
            <input name="playerId" class="layui-input" placeholder="请输入用户id" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">昵称</label>
        <div class="layui-input-block">
            <input name="nickname" class="layui-input" placeholder="请输入昵称" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">头像</label>
        <div class="layui-input-block">
            <button id="file-btn-upload" class="layui-btn layui-btn-sm layui-btn-normal icon-btn" type='button'>上传文件</button>
            <br/>
            <span>图片格式：jpg/jpeg/png/bmp/GIF/JPG/PNG/JPEG</span>
            <br/>
            <br/>
            <div id='imageDiv' style='display: inline-block; vertical-align: top;'></div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">个人简介</label>
        <div class="layui-input-block">
            <input name="profile" class="layui-input" placeholder="请输入个人简介" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">标签（英文逗号分隔）</label>
        <div class="layui-input-block">
            <input name="tagList" class="layui-input" placeholder="请输入标签（英文逗号分隔）" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">交易次数</label>
        <div class="layui-input-block">
            <input name="tradeCount" class="layui-input" placeholder="请输入交易次数" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">风险评分（0~100）</label>
        <div class="layui-input-block">
            <input name="riskScore" class="layui-input" placeholder="请输入风险评分（0~100）" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">用户评分（0~5）</label>
        <div class="layui-input-block">
            <input name="userScore" class="layui-input" placeholder="请输入用户评分（0~5）" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最近30天盈利率（%）</label>
        <div class="layui-input-block">
            <input name="profitRate30d" class="layui-input" placeholder="请输入最近30天盈利率（%）" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">胜率（%）</label>
        <div class="layui-input-block">
            <input name="winRate" class="layui-input" placeholder="请输入胜率（%）" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate', 'upload'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;
        var upload = layui.upload;
        var imgurl = '';

        // 上传
        upload.render({
            elem: '#file-btn-upload',
            accept: 'imgUrl',
            url: getProjectUrl() + 's3/file/upload',
            done: function(res){
                handleNetworkError(res);
                if(res.success) {
                    layer.msg(res.message, {icon: 1, time: 1000}, function () {
                        $("#imageDiv").html("<img src='" + res.data + "' style='width: 375px; margin-right: 20px;'/>");
                    });
                    imgurl = res.data;
                }
            }
        });


        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
            $("#imageDiv").html("<img src='" + editData.avatar + "' style='width: 375px; margin-right: 20px;'/>");
            imgurl = editData.avatar
        }

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);
            data.field.avatar = imgurl;

            var url = editData ? getProjectUrl() + 'follower/edit' : getProjectUrl() + 'follower/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
