<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <input name="playerId" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">收款人姓名</label>
        <div class="layui-input-block">
            <input name="userName" class="layui-input" placeholder="请输入收款人姓名" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">收款人银行卡号</label>
        <div class="layui-input-block">
            <input name="bankCardNumber" class="layui-input" placeholder="请输入收款人银行卡号" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">收款人银行</label>
        <div class="layui-input-block">
            <input name="bankName" class="layui-input" placeholder="请输入收款人银行" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <!--<div class="layui-form-item">
        <label class="layui-form-label layui-form-required">收款人国家</label>
        <div class="layui-input-block">
            <input name="area" class="layui-input" placeholder="请输入收款人国家" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">收款人城市</label>
        <div class="layui-input-block">
            <input name="city" class="layui-input" placeholder="请输入收款人城市" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">收款人地址</label>
        <div class="layui-input-block">
            <input name="address" class="layui-input" placeholder="请输入收款人地址" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">收款人支行信息</label>
        <div class="layui-input-block">
            <input name="subBranch" class="layui-input" placeholder="请输入收款人支行信息" lay-verType="tips"/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">国际电话编码</label>
        <div class="layui-input-block">
            <input name="internationalTelephoneNumber" class="layui-input" placeholder="请输入国际电话编码" lay-verType="tips"/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">清算号</label>
        <div class="layui-input-block">
            <input name="liquidationNumber" class="layui-input" placeholder="请输入清算号" lay-verType="tips"/>
        </div>
    </div>-->

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">银行地址</label>
        <div class="layui-input-block">
            <input name="bankAddress" class="layui-input" placeholder="请输入银行地址" lay-verType="tips"/>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;

        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
        }

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'playerBankAccount/edit' : getProjectUrl() + 'playerBankAccount/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
