<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>玩家</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">账号:</label>
                        <div class="layui-input-inline">
                            <input name="account" class="layui-input" placeholder="请输入账号"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">手机号:</label>
                        <div class="layui-input-inline">
                            <input name="phone" class="layui-input" placeholder="请输入手机号"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态:</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value=""></option>
                                <option value="1">正常</option>
                                <option value="2">冻结</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">在线标志:</label>
                        <div class="layui-input-inline">
                            <select name="onlineFlag">
                                <option value=""></option>
                                <option value="1">在线</option>
                                <option value="0">离线</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">代理人账号:</label>
                        <div class="layui-input-inline">
                            <input name="agentName" class="layui-input" placeholder="请输入代理人账号"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">登录ip:</label>
                        <div class="layui-input-inline">
                            <input name="lastLoginIp" class="layui-input" placeholder="请输入登录ip"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">玩家类型:</label>
                        <div class="layui-input-inline">
                            <select name="role">
                                <option value="">全部</option>
                                <option value="c">真实</option>
                                <option value="p">虚拟</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">机构编码:</label>
                        <div class="layui-input-inline">
                            <div id="orgSelectBox" class="ew-xmselect-tree"></div>
                        </div>
                    </div>
<!--                    <div class="layui-inline">
                        <label class="layui-form-label">邮箱:</label>
                        <div class="layui-input-inline">
                            <input name="email" class="layui-input" placeholder="请输入邮箱"/>
                        </div>
                    </div>-->

<!--                    <div class="layui-inline">
                        <label class="layui-form-label">邀请码:</label>
                        <div class="layui-input-inline">
                            <input name="invitationCode" class="layui-input" placeholder="请输入邀请码"/>
                        </div>
                    </div>-->
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="tableSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" perm-show="player:edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-primary layui-btn-xs" perm-show="player:queryAccount" lay-event="queryAccount">查看账户</a>
    <a class="layui-btn layui-btn-primary layui-btn-xs" perm-show="player:manualDeposit" lay-event="manualDeposit">手工充值</a>
    <a class="layui-btn layui-btn-xs" data-dropdown="#dataTableDrop{{d.LAY_INDEX}}" no-shade="true">
        更多<i class="layui-icon layui-icon-drop" style="font-size: 12px;margin-right: 0;"></i></a>
    <!-- 下拉菜单 -->
    <ul class="dropdown-menu-nav dropdown-bottom-right layui-hide" id="dataTableDrop{{d.LAY_INDEX}}">
        <div class="dropdown-anchor"></div>
        <li perm-show="player:editPwd"><a lay-event="editPwd">重置登录密码</a></li>
        <li perm-show="player:editFundsPwd"><a lay-event="editFundsPwd">重置提款密码</a></li>
        <li perm-show="player:changeAgent"><a lay-event="changeAgent">变更代理人</a></li>
        {{#  if(d.isForbidTrade == 0){ }}
        <li perm-show="player:forbidTrade"><a lay-event="forbidTrade">禁止交易</a></li>
        {{#  } else if(d.isForbidTrade == 1) { }}
        <li perm-show="player:forbidTrade"><a lay-event="allowTrade">允许交易</a></li>
        {{#  } }}
        <li ><a lay-event="logout">退出登录</a></li>
<!--        <li perm-show="player:manualDeposit"><a lay-event="manualWithdrawal">手工提现</a></li>-->
    </ul>
</script>
<script type="text/html" id="titleTpl">
    <span>{{d.spotScale}}</span>
    <button name="spotScaleBtn" perm-show="player:spotScaleEdit" type="button" class="layui-btn layui-btn-xs layui-btn-primary" data-id="{{d.id}}" data-value="{{d.spotScale}}">
        <i class="layui-icon">&#xe642;</i>
    </button>
</script>
<script type="text/html" id="authStatus">
    {{#  if(d.advancedStatus == 2){ }}
    <span class="layui-table-link" style="cursor: pointer;" lay-event="authStatus">已高级认证</span>
    {{#  } else if(d.advancedStatus == 3) { }}
    <span class="layui-table-link" style="cursor: pointer;" lay-event="authStatus">高级认证失败</span>
    {{#  } else if(d.advancedStatus == 1) { }}
    <span class="layui-table-link" style="cursor: pointer;" lay-event="authStatus">高级认证审核中</span>
    {{#  } else if(d.authStatus == 2) { }}
    <span class="layui-table-link" style="cursor: pointer;" lay-event="authStatus">已初级认证</span>
    {{#  } else if(d.authStatus == 3) { }}
    <span class="layui-table-link" style="cursor: pointer;" lay-event="authStatus">初级认证失败</span>
    {{#  } else if(d.authStatus == 1) { }}
    <span class="layui-table-link" style="cursor: pointer;" lay-event="authStatus">初级认证审核中</span>
    {{#  } else { }}
    <span class="layui-badge layui-bg-gray">未认证</span>
    {{#  } }}
</script>
<script type="text/html" id="onlineFlag">
    {{#  if(d.onlineFlag == 1){ }}
    <span class="layui-badge layui-bg-green">在线</span>
    {{#  } else { }}
    <span class="layui-badge layui-bg-gray">离线</span>
    {{#  } }}
</script>
<script type="text/html" id="role">
    {{#  if(d.role == 'c'){ }}
    <span class="layui-badge layui-bg-blue" style="cursor: pointer;" lay-event="changeRoleToP">真实</span>
    {{#  } else if(d.role == 'p'){ }}
    <span class="layui-badge" style="cursor: pointer;" lay-event="changeRoleToC">虚拟</span>
    {{#  } }}
</script>
<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil', 'laydate', 'dropdown'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var laydate = layui.laydate;

        //日期时间选择器
        laydate.render({
            elem: 'lastLoginTimeId'
            ,type: 'datetime'
        });

        var orgTreeRenderIns = {};
        admin.req(getProjectUrl() + 'sysOrg/tree', function(res){
            // 渲染下拉树
            orgTreeRenderIns = xmSelect.render({
                el: '#orgSelectBox',
                name: 'orgId',
                height: '250px',
                layVerType: 'tips',
                data: res.data,
                initValue: [],
                model: {label: {type: 'text'}},
                prop: {
                    name: 'title',
                    value: 'id'
                },
                radio: true,
                clickClose: true,
                tree: {
                    show: true,
                    indent: 15,
                    strict: false,
                    expandedKeys: true
                },
                tips: '请选择机构'
            });
        }, {async: false});

        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'player/page',
            page: true,
            toolbar: ['<p>',
                '<button lay-event="add" perm-show="player:add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>添加玩家</button>&nbsp;',
               '<button lay-event="delete" perm-show="player:delete" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>删除</button>',
                '</p>'].join(''),
            cellMinWidth: 100,
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'id', title: 'ID', sort: true},
                    {field: 'agentName', title: '代理人', width: 80},
                    {field: 'phoneCode', title: '区号', width: 60},
                    {field: 'account', title: '账号', width: 120, sort: true},
                    {field: 'phone', title: '手机', width: 120, sort: true},
                    {field: 'remark', title: '备注', width: 220},
                    {field: 'isFollower', title: '是否交易员', width: 100, templet: function (d) {
                            if (d.isFollower === 1) {
                                return '<span class="layui-badge layui-bg-green">是</span>';
                            } else {
                                return '<span class="layui-badge layui-bg-gray">否</span>';
                            }
                        }},
                    // {
                    //     field: 'follower_commission_rate',
                    //     title: '抽佣比例',
                    //     width: 160,
                    //     templet: function (d) {
                    //         if (d.isFollower === 1) {
                    //             const rate = (Number(d.followerCommissionRate) * 100).toFixed(2) + '%';
                    //             return '<div>' + rate + '</div>';
                    //         } else {
                    //             return '-';
                    //         }
                    //     }
                    // },
                    // {field: 'nickName', title: '昵称', width: 120},
                    {field: 'status', title: '状态', width: 80, templet: function (d) {
                            var str ="";
                            if(d.status === 1){
                                str = '<span class="layui-badge layui-bg-green">正常</span>';
                            }else  if(d.status === 2){
                                str = '<span class="layui-badge layui-bg-orange">冻结</span>';
                            }
                            return str;
                        }, sort: true},
                    {field: 'isForbidTrade', title: '交易标记', width: 80, templet: function (d) {
                            var str ="";
                            if(d.isForbidTrade === 1){
                                str = '<span class="layui-badge layui-bg-green">禁止交易</span>';
                            }else  if(d.isForbidTrade === 0){
                                str = '<span class="layui-badge layui-bg-orange">正常</span>';
                            }
                            return str;
                        }, sort: true},
                    {field: 'role', title: '身份', width: 80, toolbar: '#role'},
                    {field: 'authStatus', title: '认证状态', width: 150, toolbar: '#authStatus'},
                    {field: 'onlineFlag', title: '在线状态', width: 70, templet: '#onlineFlag'},
                    // {field: 'avatar', title: '头像', sort: true},
                    // {field: 'email', title: '邮箱', sort: true},
                    // {field: 'phone', title: '手机', width: 120},p
                    {field: 'spotScale', title: '极速比例', templet: '#titleTpl', width: 88},
                    {field: 'createTime', title: '注册日期', width: 180, sort: true},
                    {field: 'lastLoginIp', title: '最后登录IP', width: 130},
                    {field: 'lastLoginTime', title: '最后登录时间', width: 180, sort: true},
                    // {field: 'invitationCode', title: '邀请码', width: 90},
                    {title: '操作', toolbar: '#tableBar', align: 'center', fixed: 'right', width: 290}
                ]
            ],
            done: function(res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);

                $("button[name='spotScaleBtn']").on('click', function () {
                    var elem = $(this);
                    var _id = elem.data('id');
                    var _value = elem.data('value');
                    layer.prompt({
                        formType: 0,
                        value: _value,
                        title: "输赢比例0%-100%，-1为不控制，越大赢的机会越大",
                        area: ['430px', '150px'] //自定义文本域宽高
                    }, function(value, index, elem1) {
                        admin.ajax({
                            url: getProjectUrl() + 'player/editSpotScale',
                            data: {id: _id, spotScale: value},
                            type: 'get',
                            dataType: 'json',
                            success: function(res) {
                                // console.log("s ", res)
                                elem.data("value", value);
                                elem.prev().text(value);
                                layer.msg('编辑成功', {icon: 1, time: 1000}, function () {});
                                layer.close(index);
                            },
                            failure: function (res) {
                                // console.log("f ", res)
                                layer.msg('编辑失败 ' + res.message, {icon: 2, time: 1000}, function () {});
                            }
                        });
                    });
                });
            }
        });

        /* 表格搜索 */
        form.on('submit(tableSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 表格工具条点击事件 */
        table.on('tool(dataTable)', function (obj) {
            /* 删除 */
            if (obj.event === 'delete') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'player/delete', JSON.stringify([{'id': obj.data.id}]), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            /* 编辑 */
            if (obj.event === 'edit') {
                showAddOrUpdateModel(obj.data);
            }

            if (obj.event === 'editPwd') {
                editPwd(obj.data)
            }
            if (obj.event === 'editFundsPwd') {
                editFundsPwd(obj.data)
            }
            if (obj.event === 'changeAgent') {
                changeAgent(obj.data)
            }
            if (obj.event === 'forbidTrade') {
                layer.confirm('确定要禁止交易吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'player/forbidTrade', JSON.stringify({'id': obj.data.id,'isForbidTrade': 1}), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            if (obj.event === 'allowTrade') {
                layer.confirm('确定要允许交易吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'player/forbidTrade', JSON.stringify({'id': obj.data.id,'isForbidTrade': 0}), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            if (obj.event === 'logout') {
                layer.confirm('确定要强制该用户退出登录吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'player/logout', JSON.stringify({'id': obj.data.id}), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }

            if (obj.event === 'queryAccount') {
                var layIndex111 = admin.open({
                    title: '查看账户',
                    url: getProjectUrl() + 'player/queryAccount?playerId=' + obj.data.id,
                    area: ['600px',''],
                    end: function () {
                    },
                    success: function (layero, dIndex) {
                        // 弹窗超出范围不出现滚动条
                        $(layero).children('.layui-layer-content').css('overflow', 'visible');
                        $(layero).find('[lay-submit]').focus();
                    }
                });
            }

            if (obj.event === 'manualDeposit') {
                manualDepositFun(obj);
            }

            if (obj.event === 'manualWithdrawal') {
                manualWithdrawalFun(obj);
            }

            if (obj.event === 'authStatus') {
                console.log(obj.data)
                showAuthStatus(obj.data);
            }

            if (obj.event === 'changeRoleToC') {
                changeRole(obj, 'c');
            }
            if (obj.event === 'changeRoleToP') {
                changeRole(obj, 'p');
            }
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showAddOrUpdateModel();
            }
            if (obj.event === 'delete') { // 删除
                var checkRows = table.checkStatus('dataTable');
                if (checkRows.data.length === 0) {
                    notice.msg('请选择要操作的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    var ids = checkRows.data.map(function (d) {
                        return {"id": d.id};
                    });
                    admin.req(getProjectUrl() + 'player/delete', JSON.stringify(ids), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        function changeRole(_obj, _role) {
            admin.req(getProjectUrl() + 'player/changeRole', {id: _obj.data.id, role: _role}, function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    insTb.reload();
                });
            }, 'get');
        }

        function editPwd(data) {
            var layIndex111 = admin.open({
                title: '重置登录密码',
                url: getProjectUrl() + 'player/editPwdForm',
                area: ['600px',''],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex111, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }
        function editFundsPwd(data) {
            var layIndex111 = admin.open({
                title: '重置提款密码',
                url: getProjectUrl() + 'player/editFundsPwdForm',
                area: ['600px',''],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex111, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }
        function changeAgent(data) {
            var layIndex111 = admin.open({
                title: '变更代理人',
                url: getProjectUrl() + 'player/changeAgentForm',
                area: ['600px',''],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex111, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }

        // 显示表单弹窗
        function showAddOrUpdateModel(data) {
            var isAdd = !data;
            var layIndex = admin.open({
                title: (!isAdd ? '修改' : '添加') + '玩家',
                url: getProjectUrl() + 'player/form?isAdd=' + isAdd,
                area: ['800px',''],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }

        function manualDepositFun(obj) {
            var layIndex = admin.open({
                title: '手工充值',
                url: getProjectUrl() + 'player/manualDeposit?playerId=' + obj.data.id + '&userAccount=' + obj.data.account,
                area: ['600px',''],
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        layer.msg('充值成功', {icon: 1, time: 1000}, function () {
                        });
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }

        function manualWithdrawalFun(obj) {
        }

        function showAuthStatus(data) {
            var layIndex = admin.open({
                title: '玩家认证',
                url: getProjectUrl() + 'player/toAuthStatus',
                area: ['800px',''],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }
    });
</script>
</body>
</html>
