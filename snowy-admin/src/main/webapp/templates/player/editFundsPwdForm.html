<form id="updatePwdForm" lay-filter="updatePwdForm" class="layui-form model-form">

    <input name="id" type="hidden"/>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">提款密码</label>
        <div class="layui-input-block">
            <input name="fundsPassword" class="layui-input" placeholder="请输入提款密码" lay-verType="tips" lay-verify="required|password" required/>
        </div>
    </div>
    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;

        form.verify({
            password: [
                /[a-zA-Z0-9]{8,16}/,
                '密码必须8到16位，且不能出现特殊字符'
            ]
        });

        var editData = admin.getLayerData('#updatePwdForm').data;
        if(editData !== null && editData !== undefined) {
            editData.password = '';
            form.val('updatePwdForm', editData);
        }

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = getProjectUrl() + 'player/editFundsPwd';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#updatePwdForm');
                    admin.closeDialog('#updatePwdForm');
            });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
