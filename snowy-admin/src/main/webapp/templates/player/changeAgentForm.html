<form id="updatePwdForm" lay-filter="updatePwdForm" class="layui-form model-form">

    <input name="id" type="hidden"/>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">代理人</label>
        <div class="layui-input-block">
            <select name="agentId" id="agentId" lay-verType="tips" lay-verify="required" required>
                <option value="">请选择</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;

        var auditStatusFun = function(id, authStatus, advancedStatus) {
            admin.req(getProjectUrl() + 'sysUser/listAgent?limit=999',{}, function(res){
                console.log('auditStatus', res);
                var agentIdSel = $("#agentId");
                if (res.success) {
                    for (var i in res.data) {
                        var obj = res.data[i];
                        if (obj.adminAuthType === '3') {
                            agentIdSel.append('<option value="' + obj.id + '">【' + obj.sysEmpInfo.orgCode + '】' + obj.account + '</option>');
                        }
                    }


                    var editData = admin.getLayerData('#updatePwdForm').data;
                    if(editData !== null && editData !== undefined) {
                        form.val('updatePwdForm', editData);
                    }

                    form.render('select');
                } else {
                    layer.msg('失败 ' + res.message, {icon: 2, time: 1000}, function () {
                    });
                }



            }, 'get');
        };

        auditStatusFun();



        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = getProjectUrl() + 'player/changeAgent';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#updatePwdForm');
                    admin.closeDialog('#updatePwdForm');
            });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
