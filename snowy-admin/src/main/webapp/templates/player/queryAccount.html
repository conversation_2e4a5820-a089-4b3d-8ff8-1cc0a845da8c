<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
    <input id="playerId" type="hidden" value="${playerId}" />
    <ul class="layui-tab-title">
        <li class="layui-this" lay-id="1">账户资产</li>
        <li lay-id="2">币币账户</li>
<!--        <li lay-id="3">现货账户</li>-->
    </ul>
    <div class="layui-tab-content">
        <!-- 数据表格 -->
        <table id="accountDataTable" lay-filter="accountDataTable"></table>
    </div>
</div>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate', 'element', 'tableX', 'table'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;
        var element = layui.element;
        var tableX = layui.tableX;
        var table = layui.table;

        var playerId = $("#playerId").val();

        var cols = [
            {field: 'currencyCode', title: '币种代码'}
        ];

        if (xnUtil.hasPerm("player:manualDeposit")) {
            cols.push({field: 'balance', title: '余额', edit: 'text'});
        } else {
            cols.push({field: 'balance', title: '余额'});
        }
        cols.push({field: 'frozenBalance', title: '冻结余额'});
        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#accountDataTable',
            url: getProjectUrl() + 'account/page',
            where : {
                type: 1,
                playerId: playerId
            },
            page: true,
            cellMinWidth: 100,
            autoSort: true,
            cols: [
                cols
            ]
        });

        table.on('edit(accountDataTable)', function(obj){ //注：edit是固定事件名，test是table原始容器的属性 lay-filter="对应的值"
            console.log(obj.value); //得到修改后的值
            console.log(obj.field); //当前编辑的字段名
            console.log(obj.data); //所在行的所有相关数据
            var url =getProjectUrl() + 'account/editBalance' ;
            admin.req(url, JSON.stringify({playerId: obj.data.playerId, currencyCode: obj.data.currencyCode, field: obj.field, value: obj.value, type: obj.data.type}), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                });
            }, 'post');
        });

        element.on('tab(docDemoTabBrief)', function(data){
            var cond = {
                where : {
                    type: $(this).attr("lay-id"),
                    playerId: playerId
                }
            };
            insTb.reload(cond);
        });
    });
</script>
</body>
</html>
