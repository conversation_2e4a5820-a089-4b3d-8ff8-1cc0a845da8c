<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <% if(isAdd == 'true') { %>
    <!--<div class="layui-form-item">
        <label class="layui-form-label layui-form-required">国际区号</label>
        <div class="layui-input-block">
            <div id="phoneCodeBox"></div>
        </div>
    </div>-->

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">账号</label>
        <div class="layui-input-block">
            <input name="account" class="layui-input" placeholder="请输入账号" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">密码</label>
        <div class="layui-input-block">
            <input name="password" class="layui-input" placeholder="请输入密码" lay-verType="tips" lay-verify="required|password" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">身份:</label>
        <div class="layui-input-block">
            <select name="role" id="status">
                <option value="c">真实</option>
                <option value="p">虚拟</option>
            </select>
        </div>
    </div>
    <% } %>

    <div class="layui-form-item">
        <label class="layui-form-label">昵称</label>
        <div class="layui-input-block">
            <input name="nickName" class="layui-input" placeholder="请输入昵称" lay-verType="tips"/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">手机</label>
        <div class="layui-input-block">
            <input name="phone" class="layui-input" placeholder="请输入手机"/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">备注</label>
        <div class="layui-input-block">
            <textarea name="remark" placeholder="请输入备注" class="layui-textarea"></textarea>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">状态:</label>
        <div class="layui-input-block">
            <select name="status" id="status">
                <option value="1">正常</option>
                <option value="2">冻结</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;

        form.verify({
            password: [
                /[a-zA-Z0-9]{8,16}/,
                '密码必须8到16位，且不能出现特殊字符'
            ]
        });

        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
        }

        var phoneCodeDefaultVal = editData ? [editData.phoneCode] : [];
        admin.req(getProjectUrl() + 'areaCode/list', function(res){
            // 渲染下拉树
            xmSelect.render({
                el: '#phoneCodeBox',
                name: 'phoneCode',
                layVerify: 'required',
                layVerType: 'tips',
                data: res.data,
                initValue: phoneCodeDefaultVal,
                model: {label: {type: 'text'}},
                clickClose: true,
                prop: {
                    name: 'cnName',
                    value: 'code'
                },
                radio: true,
                tips: '请选择国际代码'
            });
        }, {async: false});

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'player/edit' : getProjectUrl() + 'player/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });

        layui.form.render();
    });
</script>
</body>
</html>
