<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>玩家</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid" style="padding-top: 10px;">
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">现货风控</div>
                <div class="layui-card-body">
                    <form id="spotScaleForm" lay-filter="spotScaleForm" class="layui-form model-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label layui-form-required">风控概率</label>
                            <div class="layui-input-block">
                                <input name="spotScale" class="layui-input" value="${risk_control_spot_scale}" placeholder="请输入概率" lay-verType="tips" lay-verify="required" required/>
                                <blockquote class="layui-elem-quote">说明:输入金额区间，在金额区间之内会根据此概率盈亏。不在此区间则不受风控影响，格式:区间开始-区间结束:客户赢利概率|符号必须为英文符号，如0-100:50|100-200:30</blockquote>
                            </div>
                        </div>
                        <div class="layui-form-item text-right">
                            <button class="layui-btn" lay-filter="spotScaleFormSubmitBtn" lay-submit>保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">

        </div>
    </div>
</div>
</body>
</html>
<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;


        /**
         * 表单提交事件
         */
        form.on('submit(spotScaleFormSubmitBtn)', function (data) {
            console.log(data)
            layer.load(2);
            var url = getProjectUrl() + 'player/editRiskControlSpotScale';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.closeDialog('#spotScaleForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
