<form id="addOrUpdate1Form" lay-filter="addOrUpdate1Form" class="layui-form model-form">

    <input id="playerId" name="playerId" type="hidden" value="${playerId}" />
    <input  type="hidden" name="rechargeChannel" value="管理员充值" class="layui-input" lay-verType="tips" lay-verify="required" required/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">用户账号</label>
        <div class="layui-input-block">
            <input disabled value="${userAccount}" class="layui-input" placeholder="请输入用户账号" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">金额</label>
        <div class="layui-input-block">
            <input name="account" class="layui-input" placeholder="请输入金额" lay-verType="tips" lay-verify="required|number" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">币种代码</label>
        <div class="layui-input-block">
            <select name="transactionType">
                <%
                for(item in chargeCurrencyList) {
                %>
                <option value="${item.code}"> ${item.code}</option>
                <%
                }
                %>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">备注</label>
        <div class="layui-input-block">
            <input name="remarks" class="layui-input" placeholder="备注" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>充值</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);
            var url =getProjectUrl() + 'transaction/executeRechargeUser' ;
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdate1Form');
                    admin.closeDialog('#addOrUpdate1Form');
                });
            }, 'post');
            return false;
        });

        layui.form.render();
    });
</script>
</body>
</html>
