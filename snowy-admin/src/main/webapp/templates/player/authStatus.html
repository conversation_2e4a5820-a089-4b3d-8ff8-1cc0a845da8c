<div style="padding: 10px; background-color: #F2F2F2;" id="authStatusDiv">
    <div class="layui-card">
        <div class="layui-card-header">
            <span>初级认证</span>
            <span style="float: right;" id="opt">

            </span>
        </div>
        <div class="layui-card-body">
            <table class="layui-table">
                <colgroup>
                    <col width="150">
                    <col>
                </colgroup>
                <tbody>
                <tr>
                    <td>认证类型</td>
                    <td>
                        <span id="type"></span>
                    </td>
                </tr>
                <tr>
                    <td>用户名</td>
                    <td>
                        <span id="realName"></span>
                    </td>
                </tr>
                <tr>
                    <td>身份证号</td>
                    <td>
                        <span id="idCard"></span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="layui-card" id="advancedCard">
        <div class="layui-card-header">
            <span>高级认证</span>
            <span style="float: right;" id="advancedOpt">

            </span>
        </div>
        <div class="layui-card-body">
            <table class="layui-table">
                <colgroup>
                    <col width="150">
                    <col>
                </colgroup>
                <tbody>
                <tr>
                    <td>银行卡号</td>
                    <td>
                        <span id="bankCardNo"></span>
                    </td>
                </tr>
                <tr>
                    <td>姓名</td>
                    <td>
                        <span id="advancedRealName"></span>
                    </td>
                </tr>
                <tr>
                    <td>证件正面照</td>
                    <td>
                        <img id="idCardJust" src="" />
                    </td>
                </tr>
                <tr>
                    <td>证件反面照</td>
                    <td>
                        <img id="idCardBack" src="" />
                    </td>
                </tr>
                <tr>
                    <td>证件手持照</td>
                    <td>
                        <img id="holdIdCard" src="" />
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var laydate = layui.laydate;

        var editData = admin.getLayerData('#authStatusDiv').data;
        console.log('auth', editData)
        if(editData !== null && editData !== undefined) {
        }

        var auditStatusFun = function(id, authStatus, advancedStatus) {
            admin.req(getProjectUrl() + 'playerInfo/auditStatus', JSON.stringify({id: id, authStatus: authStatus, advancedStatus: advancedStatus}), function(res){
                console.log('auditStatus', res)
                admin.putLayerData('formOk', true, '#authStatusDiv');
                admin.closeDialog('#authStatusDiv');
            }, 'post');
        };

        admin.req(getProjectUrl() + 'playerInfo/detailByPlayerId?playerId=' + editData.id, function(res){
            console.log('detailByPlayerId', res)
            var resData = res.data;
            // <!--                    类型(1:身份证,2:护照,3:驾驶证)-->
            if(resData.type === 1) {
                $("#type").text('身份证');
            } else if(resData.type === 2) {
                $("#type").text('护照');
            } else if(resData.type === 3) {
                $("#type").text('驾驶证');
            } else {
                $("#type").text(resData.type);
            }
            $("#realName").text(resData.realName);
            $("#idCard").text(resData.idCard);

            //高级认证数据
            $("#bankCardNo").text(resData.bankCardNo);
            $("#advancedRealName").text(resData.advancedRealName);
            $("#idCardJust").attr('src', getProjectUrl() + 's3/file/previewFile?filename='+resData.idCardJust);
            $("#idCardBack").attr('src', getProjectUrl() + 's3/file/previewFile?filename='+resData.idCardBack);
            $("#holdIdCard").attr('src', getProjectUrl() + 's3/file/previewFile?filename='+resData.holdIdCard);

            if(resData.advancedStatus === 2) {
                $("#advancedOpt").append('<span class="layui-badge layui-bg-green">高级认证通过</span>');
            } else if(resData.advancedStatus === 3) {
                $("#advancedOpt").append('<span class="layui-badge layui-bg-orange">高级认证失败</span>');
            } else if(resData.advancedStatus === 1 && xnUtil.hasPerm("playerInfo:auditStatus")) {
                $("#advancedOpt").append('<span class="layui-btn layui-btn-xs" advancedStatus="2">审核通过</span>');
                $("#advancedOpt").append('<span class="layui-btn layui-btn-xs layui-btn-danger" advancedStatus="3">审核失败</span>');
            } else {
                // $("#advancedOpt").append('<span class="layui-badge layui-bg-gray">未提交</span>');
                $("#advancedCard").hide();
            }

            if(resData.authStatus === 2) {
                $("#opt").append('<span class="layui-badge layui-bg-green">初级认证通过</span>');
            } else if(resData.authStatus === 3) {
                $("#opt").append('<span class="layui-badge layui-bg-orange">初级认证失败</span>');
            } else if(resData.authStatus === 1 && xnUtil.hasPerm("playerInfo:auditStatus")) {
                $("#opt").append('<span class="layui-btn layui-btn-xs" authStatus="2">审核通过</span>');
                $("#opt").append('<span class="layui-btn layui-btn-xs layui-btn-danger" authStatus="3">审核失败</span>');
            } else {
                $("#opt").append('<span class="layui-badge layui-bg-gray">未提交</span>');
            }

            $("span[advancedStatus]").on('click', function () {
                var advancedStatus = $(this).attr('advancedStatus')
                console.log(11111, advancedStatus)
                auditStatusFun(resData.id, null, advancedStatus);
            });
            $("span[authStatus]").on('click', function () {
                var authStatus = $(this).attr('authStatus')
                console.log(11111, authStatus)
                auditStatusFun(resData.id, authStatus, null);
            });
        });
    });
</script>
</body>
</html>
