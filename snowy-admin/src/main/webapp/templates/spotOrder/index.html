<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>现货订单</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        td .layui-form-select {
            margin-top: -10px;
            margin-left: -15px;
            margin-right: -15px;
        }
        .cell-form-select {
            overflow: visible !important;
        }
        .layui-table-view .layui-table th {
            overflow:hidden;
        }
        .laytable-cell-1-0-0{
            height: 60px;
        }
    </style>
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">玩家账号:</label>
                        <div class="layui-input-inline">
                            <input name="playerAccount" class="layui-input" placeholder="请输入玩家账号"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">订单号:</label>
                        <div class="layui-input-inline">
                            <input name="orderNo" class="layui-input" placeholder="请输入订单号"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">类型:</label>
                        <div class="layui-input-inline">
                            <select name="type">
                                <option value="">所有</option>
                                <option value="1">买涨</option>
                                <option value="2">买跌</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">交易类型:</label>
                        <div class="layui-input-inline">
                            <input name="symbol" class="layui-input" placeholder="交易类型,如ETH_USDT"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">下单数量:</label>
                        <div class="layui-input-inline">
                            <input name="quantity" class="layui-input" placeholder="请输入下单数量"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态:</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">所有</option>
                                <option value="0">持仓</option>
                                <option value="1">平仓</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">机构编码:</label>
                        <div class="layui-input-inline">
                            <div id="orgSelectBox" class="ew-xmselect-tree"></div>
                        </div>
                    </div>
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="tableSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
<!--    <a class="layui-btn layui-btn-primary layui-btn-xs" perm-show="spotOrder:edit" lay-event="edit">编辑</a>-->
<!--    <a class="layui-btn layui-btn-danger layui-btn-xs" perm-show="spotOrder:delete" lay-event="delete">删除</a>-->
</script>
<script type="text/html" id="titleTpl">
    <select {{d.status==1&&'disabled'}} name="control" lay-filter="control" data-value='{{d.control}}' data-id="{{d.id}}">
        <option value="-1">默认</option>
        <option value="0">亏</option>
        <option value="1">赢</option>
    </select>
</script>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var laydate = layui.laydate;

        //日期时间选择器
        laydate.render({
            elem: 'orderTimeId'
            ,type: 'datetime'
        });
        //日期时间选择器
        laydate.render({
            elem: 'closeTimeId'
            ,type: 'datetime'
        });

        var orgTreeRenderIns = {};
        admin.req(getProjectUrl() + 'sysOrg/tree', function(res){
            // 渲染下拉树
            orgTreeRenderIns = xmSelect.render({
                el: '#orgSelectBox',
                name: 'orgId',
                height: '250px',
                layVerType: 'tips',
                data: res.data,
                initValue: [],
                model: {label: {type: 'text'}},
                prop: {
                    name: 'title',
                    value: 'id'
                },
                radio: true,
                clickClose: true,
                tree: {
                    show: true,
                    indent: 15,
                    strict: false,
                    expandedKeys: true
                },
                tips: '请选择机构'
            });
        }, {async: false});

        var tbCols = [
                //{type: 'checkbox'},
                {field: 'playerAccount', title: '玩家账号', width: 160, templet: function (d) {
                    if(d.remark){
                        return '<div>' + d.playerAccount + '</div><div style="color: red">' + d.remark + '</div>';
                    }else{
                        return '<span>' + d.playerAccount + '</span>';
                    }
                }},
                {field: 'orderNo', title: '订单号', width: 100},
                {field: 'status', title: '状态', templet: function (d) {
                    if (d.status === 0) {
                        return '<span style="color: red;">持仓</span>'
                    }
                    if (d.status === 1) {
                        return '<span style="color: #1e9fff;">平仓</span>'
                    }
                }, width: 70},
                {field: 'type', title: '类型', templet: function (d) {
                        if (d.type === 1) {
                            return '<span style="color: green;">买涨</span>'
                        }
                        if (d.type === 2) {
                            return '<span style="color: red;">买跌</span>'
                        }
                    }, width: 70},
                {field: 'symbol', title: '交易类型', width: 110},
                {field: 'seconds', title: '秒', width: 60},
                {field: 'orderTime', title: '下单时间', width: 190},
                {field: 'orderPrice', title: '下单价格', width: 100, templet: function (d) {
                        return '<span style="color: #6d737b;">' + d.orderPrice + '</span>';
                    }},
                {field: 'quantity', title: '下单数量', width: 90},
                {field: 'closePrice', title: '平仓价格', width: 100, templet: function (d) {
                    if(d.closePrice) {
                        return '<span style="color: #1e9fff;">' + d.closePrice + '</span>';
                    } else {
                        return ''
                    }

                    }},
                {field: 'closeTime', title: '平仓时间', width: 190},
                {field: 'profitability', title: '盈利率%', width: 80},
                {field: 'orderFee', title: '手续费', width: 80},
                {field: 'profit', title: '盈亏', width: 90,templet:function (d) {
                        if (d.profit && d.profit>=0) {
                            return '<span style="color: green;">' + d.profit + '</span>'
                        }
                        if (d.profit && d.profit<0) {
                            return '<span style="color: red;">' + d.profit + '</span>'
                        }
                        return ''
                    }},
                // {title: '操作', toolbar: '#tableBar', align: 'center', width: 200, minWidth: 200}
            ];
        if (xnUtil.hasPerm("spotOrder:editControl")) {
            tbCols.push({title: '控制', field: 'control', templet: '#titleTpl', align: 'center', width: 100})
        }

        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'spotOrder/page',
            page: true,
            toolbar: ['<p>',
                // '<button lay-event="add" perm-show="spotOrder:add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>&nbsp;',
                // '<button lay-event="delete" perm-show="spotOrder:delete" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>删除</button>',
                '</p>'].join(''),
            cellMinWidth: 100,
            cols: [tbCols],
            loading: false,
            done: function(res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);

                //表格中下拉框展示出来
                $('.layui-table-cell .layui-form-select').parent().addClass("cell-form-select");
                //赋值
                layui.each($("select[name='control']", ""), function (index, item) {
                    var elem = $(item);
                    elem.val(elem.data('value'));
                });
                form.render('select');
            },
            defaultToolbar: ['filter']
        });

        /* 表格搜索 */
        form.on('submit(tableSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });



        /* 表格工具条点击事件 */
        table.on('tool(dataTable)', function (obj) {
            /* 删除 */
            if (obj.event === 'delete') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'spotOrder/delete', JSON.stringify([{'id': obj.data.id}]), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            /* 编辑 */
            if (obj.event === 'edit') {
                showAddOrUpdateModel(obj.data);
            }
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showAddOrUpdateModel();
            }
            if (obj.event === 'delete') { // 删除
                var checkRows = table.checkStatus('dataTable');
                if (checkRows.data.length === 0) {
                    notice.msg('请选择要操作的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    var ids = checkRows.data.map(function (d) {
                        return {"id": d.id};
                    });
                    admin.req(getProjectUrl() + 'spotOrder/delete', JSON.stringify(ids), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        // 显示表单弹窗
        function showAddOrUpdateModel(data) {
            var layIndex = admin.open({
                title: (data ? '修改' : '添加') + '现货订单',
                url: getProjectUrl() + 'spotOrder/form',
                area: ['800px',''],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }

        function reloadTab(){
            insTb.reload();  // 成功刷新表格
        }

        setInterval(reloadTab,5000);

        //监听下拉框编辑
        form.on('select(control)', function (data) {
            var elem = $(data.elem);
            var oldValue = elem.data('value');
            console.log("oldV " + oldValue);
            admin.ajax({
                url: getProjectUrl() + 'spotOrder/editControl',
                data: {id: elem.data('id'), control: data.value},
                type: 'get',
                dataType: 'json',
                success: function(res) {
                    // console.log("s ", res)
                    elem.data("value", data.value);
                    layer.msg('编辑成功', {icon: 1, time: 1000}, function () {
                    });
                },
                failure: function (res) {
                    // console.log("f ", res)
                    elem.val(oldValue);
                    form.render('select');
                    layer.msg('编辑失败 ' + res.message, {icon: 2, time: 1000}, function () {
                    });
                }
            });
        })
    });
</script>
</body>
</html>
