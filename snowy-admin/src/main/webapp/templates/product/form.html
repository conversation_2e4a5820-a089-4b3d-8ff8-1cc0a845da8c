<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">语言编码</label>
        <div class="layui-input-block">
            <div id="languageBox"></div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">产品名称</label>
        <div class="layui-input-block">
            <input name="productName" class="layui-input" placeholder="请输入产品名称" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最小认购金额</label>
        <div class="layui-input-block">
            <input name="minPurchaseAccount" class="layui-input" placeholder="请输入最小认购金额" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最大认购金额</label>
        <div class="layui-input-block">
            <input name="maxPurchaseAccount" class="layui-input" placeholder="请输入最大认购金额" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最小收益率</label>
        <div class="layui-input-block">
            <input name="minProfitAccount" class="layui-input" placeholder="请输入最小收益率，如0.15，代表 15%" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最大收益率</label>
        <div class="layui-input-block">
            <input name="maxProfitAccount" class="layui-input" placeholder="请输入最大收益率，如0.15，代表 15% " lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">周期</label>
        <div class="layui-input-block">
            <input name="cycle" class="layui-input" placeholder="请输入周期，如24 " lay-verType="tips" lay-verify="required" required/>天
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">提前赎回罚息费率</label>
        <div class="layui-input-block">
            <input name="advRedeemRate" class="layui-input" placeholder="请输入提前赎回罚息费率，如0.15，代表 15%" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">产品介绍</label>
        <div class="layui-input-block">
            <textarea class="layui-textarea" id="productRemark" placeholder="请输入内容"  name="productRemark" autocomplete="off" required/>
           <!-- <input name="productRemark" class="layui-input" placeholder="请输入产品介绍" lay-verType="tips" lay-verify="required" required/>-->
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">产品状态</label>
        <div class="layui-select-inline">
            <select name="productStatus" >
                <option value="1">启用</option>
                <option value="2">停用</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>
<script>
    layui.use(['layer', 'form', 'admin','xnUtil'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;

        // 渲染字典下拉
        var languageRenderIns = xmSelect.render({
            el: '#languageBox',
            name: 'languageCode',
            data: xnUtil.getDictDataByDictTypeCode('language_code', null),
            layVerify: 'required',
            layVerType: 'tips',
            radio: true,
            clickClose: true,
            model: { icon:'hidden', label: { type: 'text' }},
            prop: {
                name: 'name',
                value: 'code'
            },
            tips: '请选择语言编码'
        });

        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            languageRenderIns.setValue([editData.code]);
            form.val('addOrUpdateForm', editData);
        }

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'product/edit' : getProjectUrl() + 'product/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
