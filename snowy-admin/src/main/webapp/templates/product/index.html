<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>理财产品</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">机构编码:</label>
                        <div class="layui-input-inline">
                            <div id="orgSelectBox" class="ew-xmselect-tree"></div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">语言编码</label>
                        <div class="layui-input-inline">
                            <div id="typeBox"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">产品名称:</label>
                        <div class="layui-input-inline">
                            <input name="productName" class="layui-input" placeholder="请输入产品名称"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">周期:</label>
                        <div class="layui-input-inline">
                            <input name="cycle" class="layui-input" placeholder="请输入周期"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">产品状态:</label>
                        <div class="layui-select-inline">
                            <select class="layui-select" name="productStatus">
                                <option value=""></option>
                                <option value="1">启用</option>
                                <option value="2">停用</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="tableSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" perm-show="product:edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" perm-show="product:delete" lay-event="delete">删除</a>
</script>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var laydate = layui.laydate;

        // 渲染字典下拉
        var typeRenderIns = xmSelect.render({
            el: '#typeBox',
            name: 'languageCode',
            data: xnUtil.getDictDataByDictTypeCode('language_code', null),
            layVerType: 'tips',
            radio: true,
            clickClose: true,
            model: { icon:'hidden', label: { type: 'text' }},
            prop: {
                name: 'name',
                value: 'code'
            },
            tips: '请选择语言编码'
        });

        var orgTreeRenderIns = {};
        admin.req(getProjectUrl() + 'sysOrg/tree', function(res){
            // 渲染下拉树
            orgTreeRenderIns = xmSelect.render({
                el: '#orgSelectBox',
                name: 'orgCode',
                height: '250px',
                layVerType: 'tips',
                data: res.data,
                initValue: [],
                model: {label: {type: 'text'}},
                prop: {
                    name: 'title',
                    value: 'code'
                },
                radio: true,
                clickClose: true,
                tree: {
                    show: true,
                    indent: 15,
                    strict: false,
                    expandedKeys: true
                },
                tips: '请选择机构'
            });
        }, {async: false});

        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'product/page',
            page: true,
            toolbar: ['<p>',
                '<button lay-event="add" perm-show="product:add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>&nbsp;',
                '<button lay-event="delete" perm-show="product:delete" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>删除</button>',
                '</p>'].join(''),
            cellMinWidth: 100,
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'orgCode', title: '机构编码'},
                    {field: 'languageCode', title: '语言编码', sort: true,templet: function (d) {  return xnUtil.rendDataTableDict(d.languageCode, 'language_code'); } },
                    {field: 'productName', title: '产品名称', sort: true},
                    {field: 'minPurchaseAccount', title: '最小认购金额', sort: true},
                    {field: 'maxPurchaseAccount', title: '最大认购金额', sort: true},
                    {field: 'minProfitAccount', title: '最小收益率', sort: true},
                    {field: 'maxProfitAccount', title: '最大收益率', sort: true},
                    {field: 'cycle', title: '周期', sort: true},
                    {field: 'advRedeemRate', title: '提前赎回罚息费率', sort: true},
                    {field: 'productRemark', title: '产品介绍', sort: true},
                    {field: 'productStatus', title: '产品状态', sort: true,templet:function (d) {
                            var  str = "";
                            if(d.productStatus == 1){
                                str = "启用";
                            }else{
                                str = "停用";
                            }
                            return str;
                        }},
                    {field: 'createId', title: '创建人', sort: true},
                    {field: 'updateId', title: '修改人', sort: true},
                    {title: '操作', toolbar: '#tableBar', align: 'center', width: 200, minWidth: 200}
                ]
            ],
            done: function(res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);
            }
        });

        /* 表格搜索 */
        form.on('submit(tableSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 表格工具条点击事件 */
        table.on('tool(dataTable)', function (obj) {
            /* 删除 */
            if (obj.event === 'delete') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'product/delete', JSON.stringify([{'id': obj.data.id}]), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            /* 编辑 */
            if (obj.event === 'edit') {
                showAddOrUpdateModel(obj.data);
            }
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showAddOrUpdateModel();
            }
            if (obj.event === 'delete') { // 删除
                var checkRows = table.checkStatus('dataTable');
                if (checkRows.data.length === 0) {
                    notice.msg('请选择要操作的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    var ids = checkRows.data.map(function (d) {
                        return {"id": d.id};
                    });
                    admin.req(getProjectUrl() + 'product/delete', JSON.stringify(ids), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        // 显示表单弹窗
        function showAddOrUpdateModel(data) {
            var layIndex = admin.open({
                title: (data ? '修改' : '添加') + '理财产品',
                url: getProjectUrl() + 'product/form',
                area: ['800px',''],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }
    });
</script>
</body>
</html>
