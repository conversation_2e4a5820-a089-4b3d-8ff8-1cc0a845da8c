<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>币产品简介</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        body .tox-tinymce-aux {
            z-index: 19892000;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

                <input name="id" type="hidden"/>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">语言编码</label>
                    <div class="layui-input-block">
                        <div id="typeBox"></div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">币种</label>
                    <div class="layui-input-block">
                        <div id="currentBox"></div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">发行时间</label>
                    <div class="layui-input-block">
                        <input type="text" name="releaseTime" id="releaseTimeId" placeholder="请选择发行时间" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">发行总量</label>
                    <div class="layui-input-block">
                        <input name="totalIssuance" class="layui-input" placeholder="请输入发行总量" lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">流通总量</label>
                    <div class="layui-input-block">
                        <input name="totalCirculation" class="layui-input" placeholder="请输入流通总量" lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">官方网站</label>
                    <div class="layui-input-block">
                        <input name="officialWebsite" class="layui-input" placeholder="请输入官方网站" lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">白皮书</label>
                    <div class="layui-input-block">
                        <input name="whitePaper" class="layui-input" placeholder="请输入白皮书" lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">区块查询</label>
                    <div class="layui-input-block">
                        <input name="blockQuery" class="layui-input" placeholder="请输入区块查询" lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">简介</label>
                    <div class="layui-input-block">
                        <textarea name="content" id="contentEditor" placeholder="请输入简介" class="layui-textarea"></textarea>
                    </div>
                </div>

                <div class="layui-form-item text-right">
                    <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
                    <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- 加载动画 -->
<!--<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>-->
<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script src="${ctxPath}/assets/libs/tinymce/tinymce.min.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil', 'transfer'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var transfer = layui.transfer

        var productIntroduceId = admin.getTempData('productIntroduceId');

        // 渲染字典下拉
        var typeRenderIns = xmSelect.render({
            el: '#typeBox',
            name: 'languageCode',
            data: xnUtil.getDictDataByDictTypeCode('language_code', null),
            layVerify: 'required',
            layVerType: 'tips',
            radio: true,
            clickClose: true,
            model: { icon:'hidden', label: { type: 'text' }},
            prop: {
                name: 'name',
                value: 'code'
            },
            tips: '请选择语言编码'
        });

        // 渲染币种
        var currentRenderIns = {};
        admin.req(getProjectUrl() + 'currency/list', function(res){
            currentRenderIns = xmSelect.render({
                el: '#currentBox',
                name: 'currencyCode',
                data: res.data,
                layVerify: 'required',
                layVerType: 'tips',
                radio: true,
                clickClose: true,
                model: { icon:'hidden', label: { type: 'text' }},
                prop: {
                    name: 'name',
                    value: 'code'
                },
                tips: '请选择币种'
            });
        });

        // 渲染富文本编辑器
        tinymce.init({
            selector: '#contentEditor',
            height: 525,
            branding: false,
            language: 'zh_CN',
            plugins: 'code print preview fullscreen paste searchreplace save autosave link autolink image imagetools media table codesample lists advlist hr charmap emoticons anchor directionality pagebreak quickbars nonbreaking visualblocks visualchars wordcount',
            toolbar: 'fullscreen preview code | undo redo | forecolor backcolor | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | outdent indent | numlist bullist | formatselect fontselect fontsizeselect | link image media emoticons charmap anchor pagebreak codesample | ltr rtl',
            toolbar_drawer: 'sliding',
            file_picker_types: 'media',
            relative_urls : false,
            remove_script_host : false,
            document_base_url : getProjectUrl(),
            images_upload_handler: function (blobInfo, success, failure, progress) {
                var xhr, formData;
                var file = blobInfo.blob();//转化为易于理解的file对象
                xhr = new XMLHttpRequest();
                xhr.withCredentials = false;
                xhr.open('POST', getProjectUrl() + 'sysFileInfo/upload');
                xhr.upload.onprogress = function(e){
                    progress(e.loaded / e.total * 100);
                }
                xhr.onload = function() {
                    var res = JSON.parse(xhr.responseText);
                    if(!res.success) {
                        layui.notice.msg(res.message, {icon: 2});
                        return false;
                    } else {
                        success(getProjectUrl() + 'sysFileInfo/preview?id=' + res.data);
                    }
                };
                formData = new FormData();
                formData.append('file', file, file.name );//此处与源文档不一样
                xhr.send(formData);
            }, init_instance_callback: function (editor) {
                admin.removeLoading();
                if(productIntroduceId !== null && productIntroduceId !== undefined) {
                    admin.req(getProjectUrl() + 'productIntroduce/detail?id=' + productIntroduceId, function(res){
                        form.val('addOrUpdateForm', res.data);
                        editor.setContent(res.data.briefIntroduce);
                        currentRenderIns.setValue([res.data.currencyCode]);
                        typeRenderIns.setValue([res.data.languageCode]);
                    });
                }
            }
        });

        /* 返回按钮点击事件 */
        $('#formBackBtn').click(function () {
            window.location.href = getProjectUrl() + 'productIntroduce/index';
        });

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            var content = tinymce.get('contentEditor').getContent();
            if(content === null || content === undefined || content === '') {
                layui.notice.msg('简介不能为空', {icon: 2});
                return false;
            }
            layer.load(2);
            data.field.briefIntroduce = content;

            var url = productIntroduceId ? getProjectUrl() + 'productIntroduce/edit' : getProjectUrl() + 'productIntroduce/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    window.location.href = getProjectUrl() + 'productIntroduce/index';
                    return false;
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
