<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>用户管理</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <!--<style>
        #orgTree {
            border: 1px solid #e6e6e6;
            padding: 10px 5px;
            overflow: auto;
            height: -webkit-calc(100vh - 125px);
            height: -moz-calc(100vh - 125px);
            height: calc(100vh - 125px);
        }

        .layui-tree-entry .layui-tree-txt {
            padding: 0 5px;
            border: 1px transparent solid;
            text-decoration: none !important;
        }

        .layui-tree-entry.ew-tree-click .layui-tree-txt {
            background-color: #fff3e0;
            border: 1px #FFE6B0 solid;
        }
    </style>-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body" style="padding: 10px;">
                    <!-- 表格工具栏 -->
                    <form class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">关键词:</label>
                                <div class="layui-input-inline">
                                    <input name="searchValue" class="layui-input" placeholder="请输入姓名、账号、手机号"/>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">状态:</label>
                                <div class="layui-input-inline">
                                    <select name="searchStatus"></select>
                                </div>
                            </div>
                            <div class="layui-inline">&emsp;
                                <button class="layui-btn icon-btn" lay-filter="tableSearch" lay-submit>
                                    <i class="layui-icon">&#xe615;</i>搜索
                                </button>
                            </div>
                            <div class="layui-inline">&emsp;
                                <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                                    <i class="layui-icon">&#xe669;</i>重置
                                </button>
                            </div>
                            <div class="layui-inline">&emsp;
                                <button class="layui-btn icon-btn" lay-filter="tableExport" lay-submit>
                                    <i class="layui-icon">&#xe67d;</i>导出
                                </button>
                            </div>
                            <!--<div class="layui-inline">&emsp;
                                <button class="layui-btn icon-btn" lay-filter="tableSelector" lay-submit>
                                    <i class="layui-icon">&#xe609;</i>选择人员案例
                                </button>
                            </div>-->
                        </div>
                    </form>
                    <!-- 数据表格 -->
                    <table id="dataTable" lay-filter="dataTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" perm-show="sysUser:edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" perm-show="sysUser:delete" lay-event="delete">删除</a>
    <a class="layui-btn layui-btn-xs" data-dropdown="#dataTableDrop{{d.LAY_INDEX}}" no-shade="true">
        更多<i class="layui-icon layui-icon-drop" style="font-size: 12px;margin-right: 0;"></i></a>
    <!-- 下拉菜单 -->
    <ul class="dropdown-menu-nav dropdown-bottom-right layui-hide" id="dataTableDrop{{d.LAY_INDEX}}">
        <div class="dropdown-anchor"></div>
        <li perm-show="sysUser:changeAgentLine"><a lay-event="changeAgentLine">变更代理线</a></li>
        <li perm-show="sysUser:grantRole"><a lay-event="grantRole">授权角色</a></li>
        <li perm-show="sysUser:grantData"><a lay-event="grantData">授权数据</a></li>
        <li><a lay-event="resetPassword">重置密码</a></li>
    </ul>
</script>

<div id="transactionCheck">
    <form class="layui-form" id="test" style="display:none">
        <input name="sysUserId" type="hidden" id="transactionId">
        <div class="layui-form-item">
            <label class="layui-form-label layui-form-required">密码</label>
            <div class="layui-input-block">
                <input name="password" id="password" type="password" class="layui-input" placeholder="请输入密码" lay-verType="tips" lay-verify="required|psw" required/>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label layui-form-required" >确认密码</label>
            <div class="layui-input-block">
                <input name="rePassword" id="rePassword" type="password" class="layui-input" placeholder="请输入重复密码" lay-verType="tips"
                       lay-verify="required|equalTo" lay-equalTo="input[name=password]" required/>
            </div>
        </div>
    </form>
</div>
<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'tree', 'treeTable', 'dropdown', 'xnUtil'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var tree = layui.tree;
        var dropdown = layui.dropdown;

        // 渲染字典下拉
        xnUtil.rendDictDropDown(null, 'searchStatus', 'common_status', '请选择状态', ['删除']);

        /* 渲染左侧树 */
        // function renderTree() {
        //     admin.req(getProjectUrl() + 'sysOrg/tree', function(res){
        //         tree.render({
        //             elem: '#orgTree',
        //             onlyIconControl: true,
        //             data: xnUtil.handleZtreeDataSpread(res.data),
        //             click: function (obj) {
        //                 $('#orgTree').find('.ew-tree-click').removeClass('ew-tree-click');
        //                 $(obj.elem).children('.layui-tree-entry').addClass('ew-tree-click');
        //                 insTb.reload({
        //                     where: {'sysEmpParam.orgId': obj.data.id},
        //                     page: {curr: 1},
        //                     url: getProjectUrl() + 'sysUser/page'
        //                 });
        //             }
        //         });
        //     });
        // }
        //
        // renderTree();

        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'sysUser/page',
            page: true,
            toolbar: ['<p>',
                '<button lay-event="add" perm-show="sysUser:add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>&nbsp;',
                '<button lay-event="delete" perm-show="sysUser:delete" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>删除</button>',
                '</p>'].join(''),
            // cellMinWidth: 100,
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'id', title: 'id', hide:true, sort: true},
                    {field: 'sysEmpInfo.orgId', title: 'orgId', hide:true, sort: true, templet: function (d) {  return d.sysEmpInfo.orgId; } },
                    {field: 'adminAuthType', title: '管理员类型', sort: true,templet: function (d) {  return xnUtil.rendDataTableDict(d.adminAuthType, 'admin_auth_type'); } },
                    {field: 'account', title: '账号', sort: true},
                    {field: 'name', title: '姓名', sort: true},
                    {field: 'sex', title: '性别', sort: true, templet: function (d) {  return xnUtil.rendDataTableDict(d.sex, 'sex'); } },
                    // {field: 'phone', title: '手机', sort: true},
                    {field: 'roleNames', title: '角色'},
                    {field: 'superiorAccount', title: '上线账号', sort: true},
                    {field: 'invitedCode', title: '邀请码', sort: true},
                    {field: 'promotionLink', title: 'PC推广链接', sort: true},
                    {field: 'promotionLinkH5', title: 'h5推广链接', sort: true},
                    {field: 'status', title: '状态', sort: true, templet: function (d) {  return '<a style="color:#1e90ff;cursor: pointer" lay-event="changeStatus">' + xnUtil.rendDataTableDict(d.status, 'common_status') + '</a>'; } },
                    {title: '操作', toolbar: '#tableBar', align: 'center', width: 200, minWidth: 200}
                ]
            ],
            done: function(res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);
            }
        });

        /* 表格搜索 */
        form.on('submit(tableSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 人员选择 */
        form.on('submit(tableSelector)', function (data) {
            var layIndex = admin.open({
                type: 2,
                title: '选择人员',
                content: getProjectUrl() + 'sysUser/selectorHtml',
                area: ['1600px', '700px'],
                end: function () {
                    var selectedData = admin.getLayerData(layIndex).selectedData;
                    layer.msg('您选择的数据为：' + JSON.stringify(selectedData), {icon: 1, time: 1000}, function () {
                        insTb.reload();
                    });
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
            return false;
        });

        /* 人员导出 */
        form.on('submit(tableExport)', function (data) {
            window.location.href = getProjectUrl() + "sysUser/export";
            return false;
        });

        /* 表格工具条点击事件 */
        table.on('tool(dataTable)', function (obj) {
            /* 删除 */
            if (obj.event === 'delete') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'sysUser/delete', JSON.stringify([{'id': obj.data.id}]), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            /* 编辑 */
            if (obj.event === 'edit') {
                showAddOrUpdateModel(obj.data);
            }

            /* 修改状态 */
            if (obj.event === 'changeStatus') {
                layer.confirm('确定要' + (obj.data.status === 0?'停用':'启用') + '该用户吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'sysUser/changeStatus', JSON.stringify({id: obj.data.id, status: obj.data.status === 0?1:0}), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            /* 授权角色 */
            if (obj.event === 'grantRole') {
                showGrantRoleModel(obj.data);
            }
            /* 授权数据 */
            if (obj.event === 'grantData') {
                showGrantDataModel(obj.data);
            }
            if (obj.event === 'changeAgentLine') {
                changeAgentLineModel(obj.data);
            }

            /* 重置几码 */
            if (obj.event === 'resetPassword') {
                var data = obj.data;
                $("#transactionId").val(data.id);
                $("#password").val('');
                $("#rePassword").val('');
                layui.form.render()
                layer.open({
                    type:1,
                    area:['500px','200px'],
                    title: '密码重置'
                    ,content: $("#test"),
                    shade: 0.5,
                    shadeClose:true,
                    btn: ['保存重置', '取消']
                    ,btn1: function(index, layero){
                        var sr = {"id":$("#transactionId").val(),"password":$("#password").val(),"rePassword":$("#rePassword").val()};
                        admin.req(getProjectUrl() + 'sysUser/resetPassword', JSON.stringify(sr), function(res){
                            layer.msg(res.message, {icon: 1, time: 1000}, function () {
                                layer.closeAll();
                                insTb.reload();
                            });
                        }, 'post');
                    },
                    btn2: function(index, layero){
                        layer.closeAll();
                    },
                    cancel: function(layero,index){
                        layer.closeAll();
                    }
                });
            }

            dropdown.hideAll();
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showAddOrUpdateModel();
            }
            if (obj.event === 'delete') { // 删除
                var checkRows = table.checkStatus('dataTable');
                if (checkRows.data.length === 0) {
                    notice.msg('请选择要操作的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    var ids = checkRows.data.map(function (d) {
                        return {"id": d.id};
                    });
                    admin.req(getProjectUrl() + 'sysUser/delete', JSON.stringify(ids), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        // 显示表单弹窗
        function showAddOrUpdateModel(data) {
            var layIndex = admin.open({
                title: (data ? '修改' : '添加') + '用户',
                url: getProjectUrl() + 'sysUser/form',
                area: ['1000px', '600px'],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'scroll');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }

        // 显示授权角色弹窗
        function showGrantRoleModel(data) {
            var layIndex = admin.open({
                title: '用户授权角色',
                url: getProjectUrl() + 'sysUser/grantRole',
                area: ['600px', '400px'],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }

        // 显示授权数据弹窗
        function showGrantDataModel(data) {
            var layIndex = admin.open({
                title: '用户授权数据',
                url: getProjectUrl() + 'sysUser/grantData',
                area: ['600px', '400px'],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }

        // 显示变更代理线弹窗
        function changeAgentLineModel(data) {
            var layIndex = admin.open({
                title: '变更代理线',
                url: getProjectUrl() + 'sysUser/changeAgentLine',
                area: ['600px', '400px'],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }

    });
</script>
</body>
</html>
