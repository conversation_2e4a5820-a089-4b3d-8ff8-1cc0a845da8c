<!-- 注意这里不需要写`<html><body>`这些东西，它是一个html片段，不是完整的html页面 -->
<form id="changeAgentLineForm" lay-filter="changeAgentLineForm" class="layui-form model-form">
    <input name="id" type="hidden"/>
    <div class="layui-form-item" id="defineDataTreeBox">
        <label class="layui-form-label">代理线</label>
        <div class="layui-input-block">
            <div id="userSelectBox"></div>
        </div>
    </div>
    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'zTree','xmSelect'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;

        // 渲染远程数据下拉
        var agentLines = {};
        var editData = admin.getLayerData('#changeAgentLineForm').data;
        admin.req(getProjectUrl() + 'sysUser/getAgentList', function(res){
            // 渲染下拉树
            xmSelect.render({
                el: '#userSelectBox',
                name: 'superiorId',
                layVerify: 'required',
                layVerType: 'tips',
                data: res.data,
                initValue: [],
                model: {label: {type: 'text'}},
                clickClose: true,
                prop: {
                    name: 'account',
                    value: 'id'
                },
                radio: true,
                tips: '请选择代理线'
            });
        }, {async: false});
        // agentLines = xmSelect.render({
        //     el: '#userSelectBox',
        //     name: 'superiorId',
        //     autoRow: true,
        //     toolbar: { show: true },
        //     filterable: true,
        //     remoteSearch: false,
        //
        //     data: [],
        //     initValue: [],
        //     prop: {
        //         name: 'account',
        //         value: 'id'
        //     },
        //     remoteMethod: function(val, cb, show){
        //         //这里如果val为空, 则不触发搜索
        //         if(!val){
        //             return cb([]);
        //         }
        //         //这里引入了一个第三方插件axios, 相当于$.ajax
        //         axios({
        //             method: 'get',
        //             url: getProjectUrl() + "sysUser/getAgentList",
        //             params: {
        //                 keyword: val,
        //             }
        //         }).then(response => {
        //             var res = response.data;
        //             cb(res.data);
        //         }).catch(err => {
        //             cb([]);
        //         });
        //     },
        // })

        // 表单提交事件
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);
            var grantOrgIdList = [];
            var url = getProjectUrl() + 'sysUser/changeAgentLine';
            data.field.id = editData.id;
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#changeAgentLineForm');
                    admin.closeDialog('#changeAgentLineForm');
                });
            }, 'post');
            return false;
        });

    });
</script>
