<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>访问日志管理</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">日志名称:</label>
                        <div class="layui-input-inline">
                            <input name="name" class="layui-input" placeholder="请输入日志名称"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">访问类型:</label>
                        <div class="layui-input-inline">
                            <select name="visType"></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">是否成功:</label>
                        <div class="layui-input-inline">
                            <select name="success"></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">访问时间</label>
                        <div class="layui-input-inline">
                            <input id="startEndTime" class="layui-input icon-date" placeholder="选择日期范围" autocomplete="off"/>
                        </div>
                    </div>
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="tableSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" perm-show="sysVisLog:detail" lay-event="detail">查看</a>
</script>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var laydate = layui.laydate;

        /* 渲染时间选择 */
        laydate.render({
            elem: '#startEndTime',
            type: 'date',
            range: '~',
            trigger: 'click'
        });

        // 渲染字典下拉
        xnUtil.rendDictDropDown(null, 'visType', 'vis_type', '请选择访问类型', null);

        // 渲染字典下拉
        xnUtil.rendDictDropDown(null, 'success', 'yes_or_no', '请选择是否成功', null);

        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'sysVisLog/page',
            page: true,
            toolbar: ['<p>',
                '<button lay-event="delete" perm-show="sysVisLog:delete" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>清空</button>',
                '</p>'].join(''),
            cellMinWidth: 100,
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'id', title: 'id', hide:true, sort: true},
                    {field: 'name', title: '日志名称', sort: true},
                    {field: 'visType', title: '访问类型', sort: true, templet: function (d) {  return xnUtil.rendDataTableDict(d.visType, 'vis_type'); } },
                    {field: 'success', title: '是否成功', sort: true, templet: function (d) {  return xnUtil.rendDataTableDict(d.success, 'yes_or_no'); } },
                    {field: 'account', title: '访问人', sort: true},
                    {field: 'visTime', title: '访问时间', sort: true},
                    {title: '操作', toolbar: '#tableBar', align: 'center', width: 200, minWidth: 200}
                ]
            ],
            done: function(res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);
            }
        });

        /* 表格搜索 */
        form.on('submit(tableSearch)', function (data) {
            xnUtil.handleRangeDateSelectResult(data, '#startEndTime', 'searchBeginTime', 'searchEndTime');
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 表格工具条点击事件 */
        table.on('tool(dataTable)', function (obj) {
            /* 查看详情 */
            if (obj.event === 'detail') {
                admin.open({
                    title: '查看访问日志详情',
                    url: getProjectUrl() + 'sysVisLog/detail',
                    area: ['800px'],
                    data: { data: obj.data },     // 传递数据到表单页面
                    end: function () { },
                    success: function (layero, dIndex) {
                        // 弹窗超出范围不出现滚动条
                        $(layero).children('.layui-layer-content').css('overflow', 'visible');
                        $(layero).find('[lay-submit]').focus();
                    }
                });
            }
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'delete') { // 删除
                layer.confirm('确定要操作所有数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'sysVisLog/delete', function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });
    });
</script>
</body>
</html>