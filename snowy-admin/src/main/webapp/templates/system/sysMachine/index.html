<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>服务监控</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm12 layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header" style="font-weight: bolder">系统信息</div>
                <div class="layui-card-body text-center" style="padding: 50px 0 77px 0;">
                    <div class="layui-form" style="padding: 0 15px">
                        <table class="layui-table">
                            <colgroup>
                                <col>
                                <col>
                            </colgroup>
                            <tbody>
                            <tr>
                                <td>系统名称：</td>
                                <td id="osName">--</td>
                            </tr>
                            <tr>
                                <td>系统架构：</td>
                                <td id="osArch">--</td>
                            </tr>
                            <tr>
                                <td>系统版本：</td>
                                <td id="osVersion">--</td>
                            </tr>
                            <tr>
                                <td>主机名称：</td>
                                <td id="osHostName">--</td>
                            </tr>
                            <tr>
                                <td>主机IP地址：</td>
                                <td id="osHostAddress">--</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-sm12 layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header" style="font-weight: bolder">Java信息</div>
                <div class="layui-card-body text-center" style="padding: 50px 0 77px 0;">
                    <div class="layui-form" style="padding: 0 15px">
                        <table class="layui-table">
                            <colgroup>
                                <col>
                                <col>
                            </colgroup>
                            <tbody>
                            <tr>
                                <td>虚拟机名称：</td>
                                <td id="jvmName">--</td>
                            </tr>
                            <tr>
                                <td>虚拟机版本：</td>
                                <td id="jvmVersion">--</td>
                            </tr>
                            <tr>
                                <td>虚拟机供应商：</td>
                                <td id="jvmVendor">--</td>
                            </tr>
                            <tr>
                                <td>java名称：</td>
                                <td id="javaName">--</td>
                            </tr>
                            <tr>
                                <td>java版本：</td>
                                <td id="javaVersion">--</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm12 layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header" style="font-weight: bolder">JVM内存信息</div>
                <div class="layui-card-body text-center" style="padding: 50px 0 77px 0;">
                    <div class="layui-form" style="padding: 0 15px">
                        <table class="layui-table">
                            <colgroup>
                                <col>
                                <col>
                                <col>
                                <col>
                            </colgroup>
                            <tbody>
                            <tr>
                                <td>最大内存：</td>
                                <td id="jvmMaxMemory">--</td>
                                <td>可用内存：</td>
                                <td id="jvmUsableMemory">--</td>
                            </tr>
                            <tr>
                                <td>总内存：</td>
                                <td id="jvmTotalMemory">--</td>
                                <td>已使用内存：</td>
                                <td id="jvmUsedMemory">--</td>
                            </tr>
                            <tr>
                                <td>空余内存：</td>
                                <td id="jvmFreeMemory">--</td>
                                <td>使用率：</td>
                                <td id="jvmMemoryUsedRate">--</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'notice'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var admin = layui.admin;

        layer.load(2);
        admin.req(getProjectUrl() + 'sysMachine/query', function(res){
            $("#osName").text(res.data.sysOsInfo.osName);
            $("#osArch").text(res.data.sysOsInfo.osArch);
            $("#osVersion").text(res.data.sysOsInfo.osVersion);
            $("#osHostName").text(res.data.sysOsInfo.osHostName);
            $("#osHostAddress").text(res.data.sysOsInfo.osHostAddress);
            $("#jvmName").text(res.data.sysJavaInfo.jvmName);
            $("#jvmVersion").text(res.data.sysJavaInfo.jvmVersion);
            $("#jvmVendor").text(res.data.sysJavaInfo.jvmVendor);
            $("#javaName").text(res.data.sysJavaInfo.javaName);
            $("#javaVersion").text(res.data.sysJavaInfo.javaVersion);
            $("#jvmMaxMemory").text(res.data.sysJvmMemInfo.jvmMaxMemory);
            $("#jvmUsableMemory").text(res.data.sysJvmMemInfo.jvmUsableMemory);
            $("#jvmTotalMemory").text(res.data.sysJvmMemInfo.jvmTotalMemory);
            $("#jvmUsedMemory").text(res.data.sysJvmMemInfo.jvmUsedMemory);
            $("#jvmFreeMemory").text(res.data.sysJvmMemInfo.jvmFreeMemory);
            $("#jvmMemoryUsedRate").text(res.data.sysJvmMemInfo.jvmMemoryUsedRate);
        });
    });
</script>
</body>
</html>