<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>通知公告详情</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header" style="height: 70px; line-height: 70px;">
            <span id="titleBox" style="font-size: 20px;font-weight: bolder">

            </span>
            <div style="line-height: 70px;float: right;vertical-align: middle;margin-left: 15px">
                <button class="layui-btn layui-btn-sm icon-btn" id="backBtn">
                    <i class="layui-icon">&#xe65c;</i>返回
                </button>
            </div>
            <span id="publicInfoBox" style="font-size: 14px;float: right;">
                发布人：<span id="publicUserNameBox"></span>&nbsp;&nbsp;发布部门：<span id="publicOrgNameBox"></span>&nbsp;&nbsp;发布时间：<span id="publicTimeBox"></span>
            </span>
        </div>
        <div class="layui-card-body" id="contentBox">

        </div>
    </div>
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
        <legend>阅读信息</legend>
    </fieldset>
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>
<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>
<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;

        var formNoticeId = admin.getTempData('formNoticeId');
        admin.req(getProjectUrl() + 'sysNotice/detail?id=' + formNoticeId, function(res){
            admin.removeLoading();
            $('#titleBox').text(res.data.title);
            $('#contentBox').html(res.data.content);
            $('#publicUserNameBox').text(res.data.publicUserName);
            $('#publicOrgName').text(res.data.publicOrgName);
            $('#publicTimeBox').text(res.data.publicTime);
            if(res.data.status === 2) {
                $('#publicInfoBox').append('&nbsp;&nbsp;撤回时间：' + res.data.cancelTime);
            }
            table.render({
                elem: '#dataTable',
                data: res.data.noticeUserReadInfoList,
                page: false,
                cols: [
                    [
                        {field: 'userName', title: '用户名称'},
                        {field: 'readStatus', title: '阅读状态', templet: function (d) { return xnUtil.rendDataTableDict(d.readStatus, 'read_status'); } },
                        {field: 'readTime', title: '阅读时间'}
                    ]
                ]
            });

        }, {async: false});

        $('#backBtn').click(function () {
            window.location.href="javascript:history.go(-1)";
        });
    });
</script>
</body>
</html>