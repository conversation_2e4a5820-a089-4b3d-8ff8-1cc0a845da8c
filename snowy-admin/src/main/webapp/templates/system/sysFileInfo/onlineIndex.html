<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>在线文档</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">存储位置:</label>
                        <div class="layui-input-inline">
                            <select name="fileLocation"></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">文件仓库:</label>
                        <div class="layui-input-inline">
                            <input name="fileBucket" class="layui-input" placeholder="请输入文件仓库"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">文件名称:</label>
                        <div class="layui-input-inline">
                            <input name="fileOriginName" class="layui-input" placeholder="请输入文件名称"/>
                        </div>
                    </div>
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="tableSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                    </div>
                    <div class="layui-inline">&emsp;
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">在线编辑</a>
    <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="download">下载</a>
    <a class="layui-btn layui-btn-xs" lay-event="desktop-preview">桌面预览</a>
    <a class="layui-btn layui-btn-xs" lay-event="mobile-preview">手机预览</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" perm-show="sysFileInfo:delete" lay-event="delete">删除</a>
</script>

<!-- 文档类型选择渲染模板 -->
<script id="renderBox" type="text/html">
    <div class="layui-col-md4" style="text-align: center;padding: 10px">
        <img src="${ctxPath}/assets/module/onlyoffice/img/word.png" style="width: 30%">
        <div style="margin-top: 10px">
            <button class="layui-btn layui-btn-sm file-select-btn" lay-data="docx">选择</button>
        </div>
    </div>
    <div class="layui-col-md4" style="text-align: center;padding: 10px">
        <img src="${ctxPath}/assets/module/onlyoffice/img/excel.png" style="width: 30%">
        <div style="margin-top: 10px">
            <button class="layui-btn layui-btn-sm file-select-btn" lay-data="xlsx">选择</button>
        </div>
    </div>
    <div class="layui-col-md4" style="text-align: center;padding: 10px">
        <img src="${ctxPath}/assets/module/onlyoffice/img/ppt.png" style="width: 30%">
        <div style="margin-top: 10px">
            <button class="layui-btn layui-btn-sm file-select-btn" lay-data="pptx">选择</button>
        </div>
    </div>
</script>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil', 'laytpl'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;
        var laytpl = layui.laytpl;

        // 渲染字典下拉
        xnUtil.rendDictDropDown(null, 'fileLocation', 'file_storage_location', '请选择存储位置', null);

        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'sysFileInfo/page?fileSuffix=doc,docx,xls,xlsx,ppt,pptx',
            page: true,
            toolbar: ['<p>',
                '<button lay-event="add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>新增在线文档</button>&nbsp;',
                '<button lay-event="delete" perm-show="sysFileInfo:delete" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>删除</button>',
                '</p>'].join(''),
            cellMinWidth: 100,
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'id', title: 'id', hide:true, sort: true},
                    {field: 'fileLocation', title: '存储位置', sort: true, templet: function (d) { return xnUtil.rendDataTableDict(d.fileLocation, 'file_storage_location'); } },
                    {field: 'fileBucket', title: '文件仓库', sort: true},
                    {field: 'fileOriginName', title: '文件名称', sort: true},
                    {field: 'fileSuffix', title: '文件后缀', sort: true},
                    {field: 'fileSizeInfo', title: '文件大小', sort: true},
                    {title: '操作', toolbar: '#tableBar', align: 'center', width: 400, minWidth: 200}
                ]
            ],
            done: function(res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);
            }
        });

        /* 表格搜索 */
        form.on('submit(tableSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 表格工具条点击事件 */
        table.on('tool(dataTable)', function (obj) {
            /* 删除 */
            if (obj.event === 'delete') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'sysFileInfo/delete', JSON.stringify([{'id': obj.data.id}]), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            /* 编辑 */
            if (obj.event === 'edit') {
                window.open(getProjectUrl() + "sysFileInfo/onlineEditHtml?id=" + obj.data.id);
            }
            /* 下载 */
            if (obj.event === 'download') {
                window.open(getProjectUrl() + "sysFileInfo/download?id=" + obj.data.id);
            }
            /* 桌面预览 */
            if (obj.event === 'desktop-preview') {
                window.open(getProjectUrl() + "sysFileInfo/onlineEditHtml?id=" + obj.data.id + "&type=desktop&mode=view");
            }
            /* 手机预览 */
            if (obj.event === 'mobile-preview') {
                window.open(getProjectUrl() + "sysFileInfo/onlineEditHtml?id=" + obj.data.id + "&type=mobile&mode=view");
            }
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showAddOrUpdateModel();
            }
            if (obj.event === 'delete') { // 删除
                var checkRows = table.checkStatus('dataTable');
                if (checkRows.data.length === 0) {
                    notice.msg('请选择要操作的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    var ids = checkRows.data.map(function (d) {
                        return {"id": d.id};
                    });
                    admin.req(getProjectUrl() + 'sysFileInfo/delete', JSON.stringify(ids), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        // 显示表单弹窗
        function showAddOrUpdateModel() {
            var renderHtml = '';
            laytpl(renderBox.innerHTML).render(" ", function (html) {
                renderHtml = html;
            });
            var layIndex = admin.open({
                type: 1,
                title: '文档类型选择',
                content: renderHtml,
                area: ['600px', '200px'],
                success: function (layero, dIndex) {
                    $(".file-select-btn").click(function () {
                        layer.close(dIndex);
                        var fileSuffix = $(this).attr("lay-data");
                        admin.prompt({
                            formType: 2,
                            value: '新建'+ fileSuffix + '文档',
                            title: '请输入文件名',
                        }, function(value, index, elem){
                            window.top.open(getProjectUrl() + "sysFileInfo/onlineEditHtml?fileSuffix=" + fileSuffix + "&fileOriginName="  + value);
                            layer.close(index);
                            var lastIndex =layer.confirm('点击按钮刷新页面', {
                                skin: 'layui-layer-admin',
                                shade: .1
                            }, function () {
                                insTb.reload();
                                layer.close(lastIndex);
                            }, function () {
                                insTb.reload();
                            });
                        });
                    });
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }
    });
</script>
</body>
</html>