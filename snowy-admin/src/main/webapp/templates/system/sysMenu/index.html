<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>菜单管理</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" perm-show="sysMenu:edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" perm-show="sysMenu:delete" lay-event="delete">删除</a>
</script>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'notice', 'xnUtil', 'treeTable'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var admin = layui.admin;
        var form = layui.form;
        var treeTable = layui.treeTable;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;

        /* 渲染表格 */
        var insTb = treeTable.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'sysMenu/page',
            page: true,
            toolbar: ['<p>',
                '<button lay-event="add" perm-show="sysMenu:add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>&nbsp;',
                '<button lay-event="delete" perm-show="sysMenu:delete" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>删除</button>',
                '</p>'].join(''),
            cellMinWidth: 100,
            tree: {
                iconIndex: 2,
                idName: 'id',
                pidName: 'pid',
                isPidData: true
            },
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'id', title: 'id', hide:true},
                    {field: 'name', title: '菜单名称', width: 300},
                    {field: 'code', title: '菜单编码', width: 200},
                    {field: 'type', title: '菜单类型', templet: function (d) { return xnUtil.rendDataTableDict(d.type, 'menu_type'); }},
                    {field: 'icon', title: '菜单图标', templet: '<p><i class="layui-icon {{d.icon}}"></i></p>'},
                    {field: 'router', title: '菜单url', width: 200},
                    {field: 'permission', title: '权限标识', width: 200},
                    {field: 'sort', title: '排序'},
                    {field: 'status', title: '状态', templet: function (d) { return xnUtil.rendDataTableDict(d.status, 'common_status'); } },
                    {title: '操作', toolbar: '#tableBar', align: 'center', width: 200, minWidth: 200}
                ]
            ]
        });

        /* 表格工具条点击事件 */
        treeTable.on('tool(dataTable)', function (obj) {
            /* 删除 */
            if (obj.event === 'delete') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    admin.req(getProjectUrl() + 'sysMenu/delete', JSON.stringify([{'id': obj.data.id}]), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
            /* 编辑 */
            if (obj.event === 'edit') {
                showAddOrUpdateModel(obj.data);
            }
        });

        /* 表格头工具栏点击事件 */
        treeTable.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showAddOrUpdateModel();
            }
            if (obj.event === 'delete') { // 删除
                var checkRows = insTb.checkStatus();
                if (checkRows.length === 0) {
                    notice.msg('请选择要操作的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function () {
                    var ids = checkRows.map(function (d) {
                        return {"id": d.id};
                    });
                    admin.req(getProjectUrl() + 'sysMenu/delete', JSON.stringify(ids), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });

        // 显示表单弹窗
        function showAddOrUpdateModel(data) {
            var isAdd = !data;
            var layIndex = admin.open({
                title: (!isAdd ? '修改' : '添加') + '菜单',
                url: getProjectUrl() + 'sysMenu/form?isAdd=' + isAdd,
                area: ['800px', '500px'],
                data: { data: data },     // 传递数据到表单页面
                end: function () {
                    var layerData = admin.getLayerData(layIndex, 'formOk');
                    if (layerData) {  // 判断表单操作成功标识
                        insTb.reload();  // 成功刷新表格
                    }
                },
                success: function (layero, dIndex) {
                    // 弹窗超出范围不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    $(layero).find('[lay-submit]').focus();
                }
            });
        }
    });
</script>
</body>
</html>