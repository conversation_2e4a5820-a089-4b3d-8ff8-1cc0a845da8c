<!-- 注意这里不需要写`<html><body>`这些东西，它是一个html片段，不是完整的html页面 -->
<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">
    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required" style="width: 100px">任务名称</label>
        <div class="layui-input-block" style="margin-left: 130px">
            <input name="timerName" class="layui-input" placeholder="请输入任务名称" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required" style="width: 100px">任务class类名</label>
        <div class="layui-input-block" style="margin-left: 130px">
            <div id="actionClassBox"></div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required" style="width: 100px">任务表达式</label>
        <div class="layui-input-block" style="margin-left: 130px">
            <input name="cron" class="layui-input" placeholder="请输入任务表达式" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 100px">备注</label>
        <div class="layui-input-block" style="margin-left: 130px">
            <textarea name="remark" placeholder="请输入备注" class="layui-textarea"></textarea>
        </div>
    </div>
    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;

        // 渲染远程数据下拉
        var remoteDataRenderIns = {};
        admin.req(getProjectUrl() + 'sysTimers/getActionClasses', function(res){
            remoteDataRenderIns = xmSelect.render({
                el: '#actionClassBox',
                name: 'actionClass',
                data: res.data,
                layVerify: 'required',
                layVerType: 'tips',
                radio: true,
                clickClose: true,
                model: { icon:'hidden', label: { type: 'text' }},
                prop: {
                    name: 'code',
                    value: 'value'
                },
                tips: '请选择任务class类名'
            });
        }, {async: false});

        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
            remoteDataRenderIns.setValue([editData.actionClass]);
        }
        // 表单提交事件
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);
            var url = editData ? getProjectUrl() + 'sysTimers/edit' : getProjectUrl() + 'sysTimers/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });

    });
</script>