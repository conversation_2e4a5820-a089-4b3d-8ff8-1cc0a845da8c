<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>参数设置</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        #formAdvForm .layui-form-item {
            margin-top: 10px;
            margin-bottom: 0;
        }

        #formAdvForm .layui-form-item .layui-inline {
            margin-bottom: 15px;
            margin-right: 0;
        }

        .form-group-bottom {
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 10px 20px;
            background-color: #fff;
            box-shadow: 0 -1px 2px 0 rgba(0, 0, 0, .05);
        }
    </style>
</head>
<body>
<!-- 正文开始 -->
<form class="layui-form" id="formAdvForm" lay-filter="formAdvForm">
    <div class="layui-fluid" style="padding-bottom: 75px;">
        <div class="layui-card">
            <div class="layui-card-header">币币交易</div>
            <div class="layui-card-body">

                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">是否维护:</label>
                        <div class="layui-input-block">
                            <select name="coin_maintain" lay-verType="tips" lay-verify="required" required>
                                <option value="false">否</option>
                                <option value="true">是</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">自动结算:</label>
                        <div class="layui-input-block">
                            <select name="coin_autoSettled" lay-verType="tips" lay-verify="required" required>
                                <option value="false">否</option>
                                <option value="true">是</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">手续费:</label>
                        <div class="layui-input-block">
                            <input name="coin_fee" placeholder="请输入" class="layui-input"
                                   lay-verType="tips" lay-verify="required|number" required/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">

                    </div>
                </div>

            </div>
        </div>

        <div class="layui-card">
            <div class="layui-card-header">合约交易</div>
            <div class="layui-card-body">

                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">是否维护:</label>
                        <div class="layui-input-block">
                            <select name="contract_maintain" lay-verType="tips" lay-verify="required" required>
                                <option value="false">否</option>
                                <option value="true">是</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">手续费:</label>
                        <div class="layui-input-block">
                            <input name="contract_fee" placeholder="请输入" class="layui-input"
                                   lay-verType="tips" lay-verify="required|number" required/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">

                    </div>
                    <div class="layui-inline layui-col-md4">

                    </div>
                </div>

            </div>
        </div>

        <div class="layui-card">
            <div class="layui-card-header">极速交易</div>
            <div class="layui-card-body">

                <div class="layui-form-item layui-row">

                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">手续费:</label>
                        <div class="layui-input-block">
                            <input name="spot_fee" placeholder="请输入" class="layui-input"
                                   lay-verType="tips" lay-verify="required|number" required/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">

                    </div>
                    <div class="layui-inline layui-col-md4">

                    </div>
                </div>

            </div>
        </div>
        <div class="layui-card">
            <div class="layui-card-header">兑换</div>
            <div class="layui-card-body">

                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">手续费:</label>
                        <div class="layui-input-block">
                            <input name="exchange_fee" placeholder="请输入" class="layui-input"
                                   lay-verType="tips" lay-verify="required|number" required/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">

                    </div>
                    <div class="layui-inline layui-col-md4">

                    </div>
                </div>

            </div>
        </div>

        <div class="layui-card">
            <div class="layui-card-header">基础配置</div>
            <div class="layui-card-body">

                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">是否需要邀请码注册</label>
                        <div class="layui-input-block">
                            <select name="need_invite_code_switch" lay-verType="tips" lay-verify="required" required>
                                <option value="false">否</option>
                                <option value="true">是</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">可兑换币种,逗号隔开</label>
                        <div class="layui-input-block">
                            <input name="exchange_currency_code" placeholder="请输入" class="layui-input"
                                   lay-verType="tips" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">

                    </div>
                    <div class="layui-inline layui-col-md4">

                    </div>
                </div>

            </div>
        </div>

        <!--<div class="layui-card">
            <div class="layui-card-header">合约交易</div>
            <div class="layui-card-body">

                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">任务名:</label>
                        <div class="layui-input-block">
                            <input name="rwName" placeholder="请输入" class="layui-input"
                                   lay-verType="tips" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">任务描述:</label>
                        <div class="layui-input-block">
                            <input name="rwDesc" placeholder="请输入" class="layui-input"
                                   lay-verType="tips" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">执行人:</label>
                        <div class="layui-input-block">
                            <select name="zxPerson" lay-verType="tips" lay-verify="required" required>
                                <option value="">请选择执行人</option>
                                <option value="0">付晓晓</option>
                                <option value="1">周毛毛</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">责任人:</label>
                        <div class="layui-input-block">
                            <select name="zrPerson" lay-verType="tips" lay-verify="required" required>
                                <option value="">请选择责任人</option>
                                <option value="0">付晓晓</option>
                                <option value="1">周毛毛</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">提醒时间:</label>
                        <div class="layui-input-block">
                            <input id="formAdvDateSel2" name="txTime" placeholder="请选择提醒时间"
                                   class="layui-input icon-date" autocomplete="off" lay-verType="tips"
                                   lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md4">
                        <label class="layui-form-label layui-form-required">任务类型:</label>
                        <div class="layui-input-block">
                            <select name="rwType" lay-verType="tips" lay-verify="required" required>
                                <option value="">请选择任务类型</option>
                                <option value="0">私密</option>
                                <option value="1">公开</option>
                            </select>
                        </div>
                    </div>
                </div>

            </div>
        </div>-->

    </div>

    <div class="form-group-bottom text-right">
        <button class="layui-btn" lay-filter="formAdvSubmit" lay-submit>&emsp;提交&emsp;</button>
    </div>

</form>
<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;

        admin.req(getProjectUrl() + 'sysDictType/commonConfigDict', {}, function(res){
            console.log(res)
            form.val('formAdvForm', res.data);
        }, 'get');

        // 表单提交事件
        form.on('submit(formAdvSubmit)', function (data) {
            layer.load(2);
            admin.req(getProjectUrl() + 'sysDictType/commonConfigDict', JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {

                });
            }, 'post');
            return false;
        });

    });
</script>
</body>
</html>