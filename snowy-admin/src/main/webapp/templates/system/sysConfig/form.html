<!-- 注意这里不需要写`<html><body>`这些东西，它是一个html片段，不是完整的html页面 -->
<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">
    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required" style="width: 100px">参数名称</label>
        <div class="layui-input-block" style="margin-left: 130px">
            <input name="name" class="layui-input" placeholder="请输入参数名称" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required" style="width: 100px">参数编码</label>
        <div class="layui-input-block" style="margin-left: 130px">
            <input name="code" id="codeInput" class="layui-input" placeholder="请输入参数编码" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required" style="width: 100px">是否系统参数</label>
        <div class="layui-input-block" style="margin-left: 130px">
            <div id="sysFlagBox"></div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required" style="width: 100px">所属分类</label>
        <div class="layui-input-block" style="margin-left: 130px">
            <div id="groupCodeBox"></div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required" style="width: 100px">参数值</label>
        <div class="layui-input-block" style="margin-left: 130px">
            <input name="value" class="layui-input" placeholder="请输入参数值" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 100px">备注</label>
        <div class="layui-input-block" style="margin-left: 130px">
            <textarea name="remark" placeholder="请输入备注" class="layui-textarea"></textarea>
        </div>
    </div>
    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;

        // 渲染字典下拉
        var sysFlagRenderIns = xmSelect.render({
            el: '#sysFlagBox',
            name: 'sysFlag',
            data: xnUtil.getDictDataByDictTypeCode('yes_or_no', null),
            layVerify: 'required',
            layVerType: 'tips',
            radio: true,
            clickClose: true,
            model: { icon:'hidden', label: { type: 'text' }},
            prop: {
                name: 'name',
                value: 'code'
            },
            tips: '请选择是否系统参数'
        }, {async: false});

        // 渲染字典下拉
        var groupCodeRenderIns = xmSelect.render({
            el: '#groupCodeBox',
            name: 'groupCode',
            data: xnUtil.getDictDataByDictTypeCode('consts_type', null),
            layVerify: 'required',
            layVerType: 'tips',
            radio: true,
            clickClose: true,
            model: { icon:'hidden', label: { type: 'text' }},
            prop: {
                name: 'name',
                value: 'code'
            },
            tips: '请选择所属分类'
        });

        var editData = admin.getLayerData('#addOrUpdateForm').data;
        form.val('addOrUpdateForm', editData);

        if(editData !== null && editData !== undefined) {
            sysFlagRenderIns.setValue([editData.sysFlag]);
            groupCodeRenderIns.setValue([editData.groupCode]);
            if(editData.sysFlag === 'Y') {
                $("#codeInput").attr("disabled", "disabled");
                sysFlagRenderIns.update({ disabled: true });
                groupCodeRenderIns.update({ disabled: true });
            }
        }

        // 表单提交事件
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);
            var url = editData ? getProjectUrl() + 'sysConfig/edit' : getProjectUrl() + 'sysConfig/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });

    });
</script>