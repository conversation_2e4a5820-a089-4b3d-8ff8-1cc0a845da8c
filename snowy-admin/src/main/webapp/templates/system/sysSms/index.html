<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>短信发送管理</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">手机号:</label>
                        <div class="layui-input-inline">
                            <input name="phoneNumbers" class="layui-input" placeholder="请输入手机号"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">发送状态:</label>
                        <div class="layui-input-inline">
                            <select name="status"></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">来源:</label>
                        <div class="layui-input-inline">
                            <select name="source"></select>
                        </div>
                    </div>
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="tableSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                    </div>
                    <div class="layui-inline">&emsp;
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'tableX', 'notice', 'xnUtil'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;
        var tableX = layui.tableX;
        var admin = layui.admin;
        var form = layui.form;
        var notice = layui.notice;
        var xnUtil = layui.xnUtil;

        // 渲染字典下拉
        xnUtil.rendDictDropDown(null, 'status', 'send_type', '请选择发送状态', null);

        // 渲染字典下拉
        xnUtil.rendDictDropDown(null, 'source', 'sms_send_source', '请选择来源', null);

        /* 渲染表格 */
        var insTb = tableX.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'sysSms/page',
            page: true,
            toolbar: [],
            cellMinWidth: 100,
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'id', title: 'id', hide:true, sort: true},
                    {field: 'phoneNumbers', title: '手机号', sort: true},
                    {field: 'validateCode', title: '验证码', sort: true},
                    {field: 'templateCode', title: '模板id', sort: true},
                    {field: 'status', title: '发送状态', sort: true, templet: function (d) {  return xnUtil.rendDataTableDict(d.status, 'send_type'); } },
                    {field: 'source', title: '来源', sort: true, templet: function (d) {  return xnUtil.rendDataTableDict(d.success, 'sms_send_source'); } },
                    {field: 'invalidTime', title: '失效时间', sort: true}
                ]
            ],
            done: function(res, curr, count) {
                xnUtil.tableDone(insTb, res, curr, count);
            }
        });

        /* 表格搜索 */
        form.on('submit(tableSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });
    });
</script>
</body>
</html>