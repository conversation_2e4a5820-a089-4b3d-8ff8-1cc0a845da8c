<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>在线用户</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 数据表格 -->
            <table id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="forceExit">强退</a>
</script>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'table', 'notice'], function () {
        var layer = layui.layer;
        var table = layui.table;
        var admin = layui.admin;

        /* 渲染表格 */
        var insTb = table.render({
            elem: '#dataTable',
            url: getProjectUrl() + 'sysOnlineUser/page',
            page: true,
            toolbar: [],
            cellMinWidth: 100,
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'sessionId', title: '会话id', hide:true, sort: true},
                    {field: 'account', title: '账号', sort: true},
                    {field: 'nickName', title: '昵称', sort: true},
                    {field: 'lastLoginIp', title: '登陆IP', sort: true},
                    {field: 'lastLoginTime', title: '登陆时间', sort: true},
                    {field: 'lastLoginAddress', title: '登陆地址', sort: true},
                    {field: 'lastLoginBrowser', title: '浏览器', sort: true},
                    {field: 'lastLoginOs', title: '系统', sort: true},
                    {title: '操作', toolbar: '#tableBar', align: 'center', width: 200, minWidth: 200}
                ]
            ]
        });

        /* 表格工具条点击事件 */
        table.on('tool(dataTable)', function (obj) {
            if (obj.event === 'forceExit') {
                layer.confirm('确定要操作该数据吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function (i) {
                    admin.req(getProjectUrl() + 'sysOnlineUser/forceExist', JSON.stringify({'sessionId': obj.data.sessionId}), function(res){
                        layer.msg(res.message, {icon: 1, time: 1000}, function () {
                            insTb.reload();
                        });
                    }, 'post');
                });
            }
        });
    });
</script>
</body>
</html>