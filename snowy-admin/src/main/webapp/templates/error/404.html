<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>404</title>
    <link rel="stylesheet" href="${ctxPath}/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/module/admin.css">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="error-page">
    <img class="error-page-img" src="${ctxPath}/assets/images/ic_404.png">
    <div class="error-page-info">
        <h1>404</h1>
        <p>啊哦，你访问的页面不存在(⋟﹏⋞)</p>
        <div>
            <a id="backHomeBtn" class="layui-btn">返回首页</a>
        </div>
    </div>
</div>
<style>
    .error-page {
        position: absolute;
        top: 50%;
        width: 100%;
        text-align: center;
        -o-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
    }

    .error-page .error-page-img {
        display: inline-block;
        height: 260px;
        margin: 10px 15px;
    }

    .error-page .error-page-info {
        vertical-align: middle;
        display: inline-block;
        margin: 10px 15px;
    }

    .error-page .error-page-info > h1 {
        color: #434e59;
        font-size: 72px;
        font-weight: 600;
    }

    .error-page .error-page-info > p {
        color: #777;
        font-size: 20px;
        margin-top: 5px;
    }

    .error-page .error-page-info > div {
        margin-top: 30px;
    }
</style>

<!-- js部分 -->
<script src="${ctxPath}/assets/libs/layui/layui.js"></script>
<script src="${ctxPath}/assets/js/common.js"></script>
<script>
    layui.use(['admin'], function () {
        var $ = layui.jquery;
        var admin = layui.admin;

        $("#backHomeBtn").click(function () {
            // 关闭当前选中的选项卡
            admin.closeThisTabs();
            parent.location.href = getProjectUrl();
        });
    });
</script>
</body>
</html>