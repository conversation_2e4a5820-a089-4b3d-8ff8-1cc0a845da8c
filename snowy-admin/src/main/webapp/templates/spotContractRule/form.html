<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">秒</label>
        <div class="layui-input-block">
            <input name="seconds" class="layui-input" placeholder="请输入秒" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最小下单数量</label>
        <div class="layui-input-block">
            <input name="minQuantity" class="layui-input" placeholder="请输入最小下单数量" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最大下单数量</label>
        <div class="layui-input-block">
            <input name="maxQuantity" class="layui-input" placeholder="请输入最大下单数量" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">盈利率</label>
        <div class="layui-input-block">
            <input name="profitability" class="layui-input" placeholder="请输入盈利率" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最小利润率</label>
        <div class="layui-input-block">
            <input name="minProfitRate" class="layui-input" placeholder="请输入最小利润率" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最大利润率</label>
        <div class="layui-input-block">
            <input name="maxProfitRate" class="layui-input" placeholder="请输入最大利润率" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最小亏损率</label>
        <div class="layui-input-block">
            <input name="minLossRate" class="layui-input" placeholder="请输入最小亏损率" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最大亏损率</label>
        <div class="layui-input-block">
            <input name="maxLossRate" class="layui-input" placeholder="请输入最大亏损率" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最小余额限制</label>
        <div class="layui-input-block">
            <input name="minBalance" class="layui-input" placeholder="请输入最小余额限制" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">最大余额限制</label>
        <div class="layui-input-block">
            <input name="maxBalance" class="layui-input" placeholder="请输入最大余额限制" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;


        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
        }

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'spotContractRule/edit' : getProjectUrl() + 'spotContractRule/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
