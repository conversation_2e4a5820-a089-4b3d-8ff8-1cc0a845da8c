<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">币种</label>
        <div class="layui-input-block">
            <div id="currentBox1"></div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">生效秒数</label>
        <div class="layui-input-block">
            <input name="seconds" class="layui-input" placeholder="请输入生效秒数" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label layui-form-required">剩余秒数</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input name="leftSeconds" class="layui-input" placeholder="请输入剩余秒数" lay-verType="tips" lay-verify="required" required/>-->
<!--        </div>-->
<!--    </div>-->

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">调整值</label>
        <div class="layui-input-block">
            <input name="value" class="layui-input" placeholder="请输入调整值" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;


        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
        }

        // 渲染币种
        var currentRenderIns = {};
        admin.req(getProjectUrl() + 'market/list', function(res){
            currentRenderIns = xmSelect.render({
                el: '#currentBox1',
                name: 'symbol',
                data: res.data,
                layVerify: 'required',
                layVerType: 'tips',
                radio: true,
                clickClose: true,
                model: { icon:'hidden', label: { type: 'text' }},
                prop: {
                    name: 'symbol',
                    value: 'symbol'
                },
                tips: '请选择币种'
            });
        });

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'marketAdjustItem/edit' : getProjectUrl() + 'marketAdjustItem/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
