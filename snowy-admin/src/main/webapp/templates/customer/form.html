<form id="addOrUpdateForm" lay-filter="addOrUpdateForm" class="layui-form model-form">

    <input name="id" type="hidden"/>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">语言编码</label>
        <div class="layui-input-block">
            <div id="languageBox"></div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">标题</label>
        <div class="layui-input-block">
            <input name="title" class="layui-input" placeholder="请输入标题" lay-verType="tips" lay-verify="required" required/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">嵌入链接</label>
        <div class="layui-input-block">
            <input name="embedLink" class="layui-input" placeholder="请输入嵌入链接" lay-verType="tips"/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">跳转链接</label>
        <div class="layui-input-block">
            <input name="forwardLink" class="layui-input" placeholder="请输入跳转链接" lay-verType="tips"/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label layui-form-required">备注</label>
        <div class="layui-input-block">
            <input name="remark" class="layui-input" placeholder="请输入备注" lay-verType="tips"/>
        </div>
    </div>

    <div class="layui-form-item text-right">
        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        <button class="layui-btn" lay-filter="formSubmitBtn" lay-submit>保存</button>
    </div>
</form>

<script>
    layui.use(['layer', 'form', 'admin', 'xnUtil', 'xmSelect', 'laydate'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var xnUtil = layui.xnUtil;
        var xmSelect = layui.xmSelect;
        var laydate = layui.laydate;


        var editData = admin.getLayerData('#addOrUpdateForm').data;
        if(editData !== null && editData !== undefined) {
            form.val('addOrUpdateForm', editData);
        }

        // 渲染字典下拉
        var languageRenderIns = xmSelect.render({
            el: '#languageBox',
            name: 'languageCode',
            data: xnUtil.getDictDataByDictTypeCode('language_code', null),
            layVerify: 'required',
            layVerType: 'tips',
            radio: true,
            clickClose: true,
            model: { icon:'hidden', label: { type: 'text' }},
            prop: {
                name: 'name',
                value: 'code'
            },
            tips: '请选择语言编码'
        });

        /**
         * 表单提交事件
         */
        form.on('submit(formSubmitBtn)', function (data) {
            layer.load(2);

            var url = editData ? getProjectUrl() + 'customer/edit' : getProjectUrl() + 'customer/add';
            admin.req(url, JSON.stringify(data.field), function(res){
                layer.msg(res.message, {icon: 1, time: 1000}, function () {
                    admin.putLayerData('formOk', true, '#addOrUpdateForm');
                    admin.closeDialog('#addOrUpdateForm');
                });
            }, 'post');
            return false;
        });
    });
</script>
</body>
</html>
