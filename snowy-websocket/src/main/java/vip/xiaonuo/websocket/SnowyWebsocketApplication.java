package vip.xiaonuo.websocket;

import cn.hutool.log.Log;
import com.corundumstudio.socketio.SocketIOServer;
import com.ibeetl.starter.BeetlTemplateConfig;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.context.ApplicationPidFileWriter;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.Environment;
import vip.xiaonuo.core.util.PathUtils;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.net.UnknownHostException;

@SpringBootApplication(scanBasePackages = {"vip.xiaonuo"}, exclude = {SecurityAutoConfiguration.class, ManagementWebSecurityAutoConfiguration.class, BeetlTemplateConfig.class})
@Import({cn.hutool.extra.spring.SpringUtil.class})
public class SnowyWebsocketApplication implements CommandLineRunner {
    private static final Log log = Log.get();

    public static void main(String[] args) throws UnknownHostException {
        SpringApplication springApplication = new SpringApplication(SnowyWebsocketApplication.class);
        springApplication.addListeners(new ApplicationPidFileWriter(PathUtils.getPidPath(SnowyWebsocketApplication.class)));
        ConfigurableApplicationContext application = springApplication.run(args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        log.info("[{}:{}]启动成功", ip, port);
    }

    @Resource
    private SocketIOServer socketIOServer;

    @Override
    public void run(String... args) throws Exception {
        socketIOServer.start();
        log.info("socketIOServer start");
    }
}
