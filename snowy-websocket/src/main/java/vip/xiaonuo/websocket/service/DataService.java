package vip.xiaonuo.websocket.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import vip.xiaonuo.core.pojo.dto.MarketDTO;
import vip.xiaonuo.core.util.DpDataUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/8 14:07
 * @description
 */
@Component
public class DataService {
    private static Logger logger = LoggerFactory.getLogger(DataService.class);
    @Resource
    private Environment env;
    private HashOperations<String, String, MarketDTO> marketHashOperations;

    @Resource
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        marketHashOperations = redisTemplate.opsForHash();
    }

    public List<MarketDTO> getMarketTickers() {
        return DpDataUtils.getTickers(marketHashOperations);
    }
}
