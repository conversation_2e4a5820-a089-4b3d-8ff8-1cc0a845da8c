package vip.xiaonuo.websocket.service;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/7/7 17:58
 * @description
 */
@Getter
@AllArgsConstructor
public enum RedisTopic {

    KLINE("kline"),
    DEPTH("depth"),
    TICKER("ticker");

    private final String prefix;

    public String getChannel(String symbol) {
        return prefix + ":" + symbol.toUpperCase();
    }
}

