package vip.xiaonuo.websocket.service;

import com.alibaba.fastjson.JSON;
import com.corundumstudio.socketio.SocketIOServer;
import io.lettuce.core.ScriptOutputType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vip.xiaonuo.core.pojo.dto.AggOrderDTO;
import vip.xiaonuo.core.pojo.dto.KLineUpdateDTO;
import vip.xiaonuo.core.pojo.dto.MarketDTO;
import vip.xiaonuo.core.pojo.dto.OrderPayload;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class RedisChannelServiceImpl implements IRedisChannelService {

    @Resource
    private SocketIOServer socketIOServer; // Netty-SocketIO 服务实例

    @Override
    public void handleKline(String symbol, String content) {
        String json = JSON.parseObject(content, String.class);
        KLineUpdateDTO kline = JSON.parseObject(json, KLineUpdateDTO.class);
        socketIOServer.getRoomOperations(RoomUtil.kline(symbol, kline.getPeriod()))
                .sendEvent("kline", kline);
    }

    @Override
    public void handleOrder(String symbol, String content) {
//        if (symbol.equals("BTC_USDT")) {
//            System.out.println(content);
//        }
        String json = JSON.parseObject(content, String.class);
        OrderPayload orderpayload = JSON.parseObject(json, OrderPayload.class);
        String roomId = RoomUtil.order(symbol, null);
        socketIOServer.getRoomOperations(roomId)
                .sendEvent("order", orderpayload);
    }

    @Override
    public void handleTicker(String symbol, String content) {
        String json = JSON.parseObject(content, String.class);
        List<MarketDTO> tickers = JSON.parseArray(json, MarketDTO.class);
        socketIOServer.getRoomOperations(RoomUtil.ticker()).sendEvent("ticker", tickers);
    }

    @Override
    public void handleDepth(String symbol, String content) {
//        DepthMessage message = JSON.parseObject(content, DepthMessage.class);
//        socketIOServer.getRoomOperations(RoomUtil.depth(symbol))
//                .sendEvent("depth", message.getData());
    }
}