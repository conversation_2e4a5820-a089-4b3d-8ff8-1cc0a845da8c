package vip.xiaonuo.websocket.service;

import com.alibaba.fastjson.JSON;
import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.annotation.OnEvent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import vip.xiaonuo.core.enums.KLineType;

import java.util.Set;

@Component
public class TopicRemoveExecutor {

    @OnEvent("ticker_remove")
    public void allTicker(SocketIOClient client) {
        client.leaveRoom(RoomUtil.ticker());
    }

    @OnEvent("kline_remove")
    public void kline(SocketIOClient client, String message) {
        RequestBean.KlineBean bean = JSON.parseObject(message, RequestBean.KlineBean.class);
        Set<String> allRooms = client.getAllRooms();
        String klinePrefix = RoomUtil.kline(bean.getSymbol(), null);
        for (String room : allRooms) {
            if (StringUtils.startsWith(room, klinePrefix)) {
                client.leaveRoom(room);
            }
        }
        client.leaveRoom(RoomUtil.kline(bean.getSymbol(), KLineType.of(bean.getPeriod()).getPeriod()));
    }

    //
//    @OnEvent("trade_remove")
//    public void trade(SocketIOClient client, String message) {
//        RequestBean.ClientBean bean = JSON.parseObject(message, RequestBean.ClientBean.class);
//        client.leaveRoom(RoomUtil.trade(bean.getSymbol()));
//    }
//
    @OnEvent("order_remove")
    public void order(SocketIOClient client, String message) {
        RequestBean.ClientBean bean = JSON.parseObject(message, RequestBean.ClientBean.class);
        Set<String> allRooms = client.getAllRooms();
        String roomPrefix = RoomUtil.order(bean.getSymbol(), null);
        for (String room : allRooms) {
            if (StringUtils.startsWith(room, roomPrefix)) {
                client.leaveRoom(room);
            }
        }
    }

//    @OnEvent("depth_remove")
//    public void depth(SocketIOClient client, String message) {
//        RequestBean.ClientBean bean = JSON.parseObject(message, RequestBean.ClientBean.class);
//        client.leaveRoom(RoomUtil.depth(bean.getSymbol()));
//    }
//
//    @OnEvent("assetchange_remove")
//    public void assetchange(SocketIOClient client, String message) {
//        RequestBean.OrderBean bean = JSON.parseObject(message, RequestBean.OrderBean.class);
//        client.leaveRoom(RoomUtil.assetchange(bean.getUserId(), bean.getSymbol()));
//    }
}
