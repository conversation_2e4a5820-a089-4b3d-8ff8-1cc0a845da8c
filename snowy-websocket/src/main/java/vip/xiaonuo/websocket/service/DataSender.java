package vip.xiaonuo.websocket.service;


import com.corundumstudio.socketio.SocketIOServer;
import org.springframework.stereotype.Service;
import vip.xiaonuo.core.pojo.dto.AggOrderDTO;
import vip.xiaonuo.core.pojo.dto.MarketDTO;
import vip.xiaonuo.core.pojo.dto.TradeDTO;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
public class DataSender {
    @Resource
    private SocketIOServer socketIOServer;

    @Resource
    private MarketSocketRouter marketSocketRouter;

    public void sendToClients(String channel, String json) {
        marketSocketRouter.sendToSubscribers(channel, json);
    }

    public void sendMarketTicker(List<MarketDTO> marketList) {
        socketIOServer.getRoomOperations(RoomUtil.ticker()).sendEvent("ticker", marketList);
    }

//    public void sendDepthData(String symbol, KLineDepthsDTO depthsDTO) {
//        socketIOServer.getRoomOperations(RoomUtil.depth(symbol)).sendEvent("depth", depthsDTO, symbol);
//    }

    public void sendKLineData(String symbol, long period, List<List<BigDecimal>> resultList) {
        socketIOServer.getRoomOperations(RoomUtil.kline(symbol, period)).sendEvent("kline", resultList, period, symbol);
    }

    public void sendOrderData(String symbol, Integer groupDecimal, Map<String, List<AggOrderDTO>> orderDTOS) {
        String roomId = RoomUtil.order(symbol, groupDecimal);
        socketIOServer.getRoomOperations(roomId)
                .sendEvent("order", orderDTOS, symbol, groupDecimal);
    }
//
//    public void sendAssetChange(String symbol, String uniqueId, Object o) {
//        socketIOServer.getRoomOperations(RoomUtil.assetchange(uniqueId, symbol)).sendEvent("assetchange", o);
//    }

    public void sendTradeData(String symbol, List<TradeDTO> tradeDTO) {
        socketIOServer.getRoomOperations(RoomUtil.trade(symbol)).sendEvent("trade", tradeDTO, symbol);
    }
}
