package vip.xiaonuo.websocket.service;

import com.corundumstudio.socketio.SocketIOClient;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class MarketSocketRouter {

    // 频道名 → 客户端集合
    private final Map<String, Set<SocketIOClient>> subscriptions = new ConcurrentHashMap<>();

    /**
     * 客户端订阅一个频道
     */
    public void subscribe(SocketIOClient client, String channel) {
        subscriptions.computeIfAbsent(channel, k -> ConcurrentHashMap.newKeySet()).add(client);
    }

    /**
     * 客户端断开连接时，移除所有订阅
     */
    public void unsubscribe(SocketIOClient client) {
        subscriptions.values().forEach(set -> set.remove(client));
    }

    /**
     * 向指定频道推送消息（自动根据前缀选择事件名）
     */
    public void sendToSubscribers(String channel, String message) {
        Set<SocketIOClient> clients = subscriptions.get(channel);
        if (clients != null) {
            clients.removeIf(c -> !c.isChannelOpen());

            // 事件名称可以根据频道类型自动推送
            String event;
            if (channel.startsWith("kline:")) {
                event = "kline_data";
            } else if (channel.startsWith("depth:")) {
                event = "depth_data";
            } else if (channel.startsWith("ticker:")) {
                event = "ticker_data";
            } else {
                event = "market_data"; // 默认事件名
            }

            for (SocketIOClient client : clients) {
                client.sendEvent(event, message);
            }
        }
    }
}
