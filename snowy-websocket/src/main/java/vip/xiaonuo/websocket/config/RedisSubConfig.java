package vip.xiaonuo.websocket.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import vip.xiaonuo.websocket.listener.RedisWebsocketMessageListener;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/7/7 17:50
 * @description
 */
@Configuration
public class RedisSubConfig {

    @Resource
    private RedisWebsocketMessageListener redisWebsocketMessageListener;

    @Bean
    public RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);

        // 订阅模式频道（支持所有币种）
        container.addMessageListener(redisWebsocketMessageListener, new PatternTopic("kline:*"));
        container.addMessageListener(redisWebsocketMessageListener, new PatternTopic("order:*"));
        container.addMessageListener(redisWebsocketMessageListener, new PatternTopic("ticker:*"));

        return container;
    }
}
