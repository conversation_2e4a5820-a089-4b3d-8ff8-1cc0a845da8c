package vip.xiaonuo.websocket.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Service;
import vip.xiaonuo.websocket.service.IRedisChannelService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/7/7 17:51
 * @description
 */
@Slf4j
@Service
public class RedisWebsocketMessageListener implements MessageListener {

    @Resource
    private IRedisChannelService redisChannelService;

    @Override
    public void onMessage(Message message, byte[] bytes) {
        String channel = new String(message.getChannel());
        String content = new String(message.getBody());

//        log.info("接收到 Redis 消息 - 频道: {}，内容: {}", channel, content);

        // 拆分频道前缀和币种
        if (channel != null && channel.contains(":")) {
            String[] parts = channel.split(":");
            String topic = parts[0];
            String symbol = parts[1];

            // 分发给不同处理逻辑
            switch (topic) {
                case "kline":
                    redisChannelService.handleKline(symbol, content);
                    break;
                case "depth":
                    redisChannelService.handleDepth(symbol, content);
                    break;
                case "order":
                    redisChannelService.handleOrder(symbol, content);
                    break;
                case "ticker":
                    redisChannelService.handleTicker(symbol, content);
                    break;
                default:
                    log.warn("未知频道类型: {}", topic);
            }
        }
    }
}

