CREATE TABLE `tt_market_adjust_item`
(
    `id`           bigint(20) NOT NULL COMMENT '主键',
    `symbol`       varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易类型表示,如ETH_BTC',
    `seconds`      int(11) DEFAULT NULL COMMENT '生效秒数',
    `left_seconds` int(11) DEFAULT NULL COMMENT '剩余秒数',
    `value`        decimal(20, 8)                         DEFAULT NULL COMMENT '调整值',
    `create_time`  datetime                               DEFAULT NULL COMMENT '创建时间',
    `create_user`  bigint(20) DEFAULT NULL COMMENT '创建人',
    `update_time`  datetime                               DEFAULT NULL COMMENT '更新时间',
    `update_user`  bigint(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY            `idx_symbol` (`symbol`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='市场调整表';


CREATE TABLE `tt_market_adjust_item_time`
(
    `id`                    bigint(20) NOT NULL COMMENT '主键',
    `symbol`                varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易类型表示,如ETH_BTC',
    `market_adjust_item_id` int(11) DEFAULT NULL COMMENT '调整id',
    `ts`                    int(11) DEFAULT NULL COMMENT '时间戳',
    `value`                 decimal(20, 8)                         DEFAULT NULL COMMENT '调整值',
    `create_time`           datetime                               DEFAULT NULL COMMENT '创建时间',
    `create_user`           bigint(20) DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime                               DEFAULT NULL COMMENT '更新时间',
    `update_user`           bigint(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                     `idx_market_adjust_item_id` (`market_adjust_item_id`),
    KEY                     `idx_symbol` (`symbol`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='市场调整表';


ALTER TABLE `tt_market_adjust_item`
    ADD COLUMN `start_ts` bigint DEFAULT NULL COMMENT '开始时间戳（毫秒）' after left_seconds,
ADD COLUMN `end_ts` bigint DEFAULT NULL COMMENT '结束时间戳（毫秒）' after start_ts;

ALTER TABLE `tt_market_adjust_item_time`
    ADD COLUMN `unit_value` decimal(20, 8) DEFAULT NULL COMMENT '单位调整值' after value;

ALTER TABLE `tt_market_adjust_item_time`
    ADD COLUMN `is_one_minute` tinyint(1) DEFAULT 0 COMMENT '是否为1分钟K线（0否，1是）' after unit_value;


ALTER TABLE tt_player
    ADD COLUMN invite_seq text DEFAULT NULL COMMENT '邀请路径，格式如 1,2,3';

ALTER TABLE tt_player
    ADD INDEX idx_invited_by (invited_by);

ALTER TABLE tt_player
    ADD COLUMN is_follower TINYINT(1) DEFAULT 0 COMMENT '是否是跟单员（0 否，1 是）',
    ADD INDEX idx_is_follower (is_follower);

INSERT INTO `sys_config` (`id`, `name`, `code`, `value`, `sys_flag`, `remark`, `status`, `group_code`, `create_time`,
                          `create_user`, `update_time`, `update_user`)
VALUES (1944649477075820546, '8级分销设置', 'SNOWY_8_LEVEL_COMMISSION_SETTING',
        '0.10,0.05,0.03,0.02,0.01,0.008,0.005,0.002', 'Y', '8级分销设置', 0, 'DEFAULT', '2020-06-09 23:42:37',
        1265476890672672808, '2025-07-18 10:02:04', 1265476890672672808);
INSERT INTO `sys_config` (`id`, `name`, `code`, `value`, `sys_flag`, `remark`, `status`, `group_code`, `create_time`,
                          `create_user`, `update_time`, `update_user`)
VALUES (1944649477075820547, '跟单分佣比例设置', 'SNOWY_FOLLOWER_COMMISSION_SETTING', '0.3', 'Y', '跟单分佣比例设置', 0,
        'DEFAULT', '2020-06-09 23:42:37', 1265476890672672808, '2025-07-18 10:02:04', 1265476890672672808);

ALTER TABLE tt_contract_spot_order
    ADD COLUMN follower_id bigint DEFAULT NULL COMMENT '跟单员id',
    ADD INDEX idx_follower_id (follower_id);

ALTER TABLE tt_contract_spot_order
    ADD COLUMN follower_commission_rate DECIMAL(5, 2) DEFAULT 0.00 COMMENT '跟单抽佣百分比' AFTER follower_id;

CREATE TABLE tt_follower_relation
(
    `id`          bigint(20) NOT NULL COMMENT '主键',
    `player_id`   BIGINT NOT NULL COMMENT '用户id',
    `follower_id` BIGINT NOT NULL COMMENT '跟单员id',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_player_id` (`player_id`),
    KEY           `idx_follower_id` (`follower_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='玩家跟单表';


CREATE TABLE tt_follower_application
(
    `id`                  bigint(20) NOT NULL COMMENT '主键',
    player_id             BIGINT        NOT NULL COMMENT '用户id',
    profile               TEXT          NOT NULL COMMENT '个人简介',
    tag_list              TEXT         DEFAULT NULL COMMENT '标签（英文逗号分隔）',
    experience            VARCHAR(50)   NOT NULL COMMENT '交易经验',
    expected_annual_yield DECIMAL(5, 2) NOT NULL COMMENT '预期年化收益率 (%)',
    risk_preference       VARCHAR(20)   NOT NULL COMMENT '风险偏好',
    trade_url             TEXT COMMENT '交易截图URL',
    contact_info          VARCHAR(100) COMMENT '联系方式',
    audit_status          TINYINT(1) DEFAULT 0 COMMENT '审核状态（0待审核，1通过，2拒绝）',
    audit_remark          VARCHAR(255) DEFAULT NULL COMMENT '审核备注',
    `create_time`         datetime     DEFAULT NULL COMMENT '创建时间',
    `create_user`         bigint(20) DEFAULT NULL COMMENT '创建人',
    `update_time`         datetime     DEFAULT NULL COMMENT '更新时间',
    `update_user`         bigint(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                   `idx_player_id` (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易员申请表';

CREATE TABLE `tt_follower`
(
    `id`          bigint NOT NULL COMMENT '主键',
    `player_id`   bigint NOT NULL COMMENT '用户id',
    `profile`     text   NOT NULL COMMENT '个人简介',
    `tag_list`    text COMMENT '标签（英文逗号分隔）',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_user` bigint   DEFAULT NULL COMMENT '创建人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `update_user` bigint   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_player_id` (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交易员';

ALTER TABLE `tt_follower`
    ADD COLUMN `trade_count` int DEFAULT 0 COMMENT '交易次数',
ADD COLUMN `risk_score`       decimal(10,2) DEFAULT NULL COMMENT '风险评分（0~100）',
ADD COLUMN `user_score`       decimal(10,2) DEFAULT NULL COMMENT '用户评分（0~5）',
ADD COLUMN `profit_rate_30d`  decimal(10,2) DEFAULT NULL COMMENT '最近30天盈利率（%）',
ADD COLUMN `win_rate`         decimal(10,2) DEFAULT NULL COMMENT '胜率（%）';

ALTER TABLE `tt_follower`
    ADD COLUMN `avatar` varchar(255) DEFAULT NULL COMMENT '头像地址',
ADD COLUMN `nickname` varchar(50) DEFAULT NULL COMMENT '昵称';

ALTER TABLE tt_follower
    ADD COLUMN follower_commission_rate DECIMAL(5, 2) DEFAULT 0.00 COMMENT '跟单抽佣百分比' AFTER trade_count;

ALTER TABLE `tt_follower_application`
    MODIFY COLUMN `expected_annual_yield` VARCHAR (50) NOT NULL COMMENT '预期年化收益率 (%)';


CREATE TABLE `tt_follower_contract_spot_order`
(
    `id`            bigint         NOT NULL COMMENT '主键',
    `follower_id`   bigint         NOT NULL COMMENT '玩家ID',
    `type`          int            NOT NULL COMMENT '类型(1:买涨;2:买跌;)',
    `symbol`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易类型表示,如ETH_USDT',
    `seconds`       int            NOT NULL COMMENT '秒',
    `order_time`    datetime(3)    NOT NULL COMMENT '下单时间',
    `quantity`      decimal(40, 8) NOT NULL COMMENT '下单数量',
    `profitability` decimal(6, 2)                                                DEFAULT NULL COMMENT '盈亏率',
    `create_time`   datetime                                                     DEFAULT NULL COMMENT '创建时间',
    `create_user`   bigint                                                       DEFAULT NULL COMMENT '创建人',
    `update_time`   datetime                                                     DEFAULT NULL COMMENT '更新时间',
    `update_user`   bigint                                                       DEFAULT NULL COMMENT '更新人',
    `control`       int            NOT NULL                                      DEFAULT '-1' COMMENT '控制(-1:默认,0:亏,1:赢)',
    `rule_id`       bigint                                                       DEFAULT NULL COMMENT '规则ID',
    PRIMARY KEY (`id`) USING BTREE,
    KEY             `idx_follower_id` (`follower_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='跟单-合约极速订单表';

ALTER TABLE `tt_follower_contract_spot_order`
    ADD COLUMN `status` int NOT NULL DEFAULT 0 COMMENT '0:未处理,1:已处理';

ALTER TABLE tt_contract_spot_order
    ADD COLUMN follower_contract_spot_order_id bigint DEFAULT NULL COMMENT '跟单员订单预设id',
    ADD INDEX idx_follower_contract_spot_order_id (follower_contract_spot_order_id);


ALTER TABLE tt_spot_order
    ADD COLUMN follower_contract_spot_order_id bigint DEFAULT NULL COMMENT '跟单员订单预设id',
    ADD INDEX idx_follower_contract_spot_order_id (follower_contract_spot_order_id);

ALTER TABLE tt_spot_order
    ADD COLUMN follower_id bigint DEFAULT NULL COMMENT '跟单员id',
    ADD INDEX idx_follower_id (follower_id);

ALTER TABLE tt_spot_order
    ADD COLUMN follower_commission_rate DECIMAL(5, 2) DEFAULT 0.00 COMMENT '跟单抽佣百分比' AFTER follower_id;

ALTER TABLE `tt_follower_relation`
    ADD COLUMN `follower_contract_spot_order_id` bigint DEFAULT NULL COMMENT '极速订单id，跟单' AFTER `follower_id`;


CREATE TABLE `tt_ai_account`
(
    `id`           BIGINT                                                       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `player_id`    BIGINT                                                       NOT NULL COMMENT '玩家id',
    `symbol`       VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USDT' COMMENT '币种，如USDT',
    `balance`      DECIMAL(65, 8)                                               NOT NULL DEFAULT 0 COMMENT '当前余额',
    `total_income` DECIMAL(65, 8)                                               NOT NULL DEFAULT 0 COMMENT '累计收益',
    `status`       TINYINT                                                      NOT NULL DEFAULT 1 COMMENT '状态（1=启用，0=禁用）',
    `create_time`  DATETIME                                                              DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user`  VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '创建人',
    `update_time`  DATETIME                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user`  VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_player_symbol` (`player_id`, `symbol`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='AI银行账户表';


CREATE TABLE `tt_ai_account_transaction`
(
    `id`          BIGINT                                                       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `player_id`   BIGINT                                                       NOT NULL COMMENT '玩家id',
    `symbol`      VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USDT' COMMENT '币种',
    `account_id`  BIGINT                                                       NOT NULL COMMENT 'AI账户id（tt_ai_account.id）',
    `type`        TINYINT                                                      NOT NULL COMMENT '记录类型（1=存入，2=收益，3=取出）',
    `amount`      DECIMAL(65, 8)                                               NOT NULL DEFAULT 0 COMMENT '金额',
    `remark`      VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL COMMENT '备注',
    `create_time` DATETIME                                                              DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '创建人',
    `update_time` DATETIME                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX         `idx_player_time` (`player_id`, `create_time`),
    INDEX         `idx_account_id` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI银行账户流水记录表';


CREATE TABLE `tt_ai_miner`
(
    `id`              BIGINT  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `player_id`       BIGINT  NOT NULL COMMENT '玩家id',
    `status`          TINYINT NOT NULL                                             DEFAULT 1 COMMENT '状态(1=待激活,2=运行中,3=已停止)',
    `last_start_time` DATETIME                                                     DEFAULT NULL COMMENT '最近启动时间',
    `create_time`     DATETIME                                                     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user`     VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
    `update_time`     DATETIME                                                     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user`     VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX             `idx_player_id` (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI矿机基础表';


CREATE TABLE `tt_ai_mining_transaction`
(
    `id`          BIGINT                                                       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `player_id`   BIGINT                                                       NOT NULL COMMENT '玩家id',
    `symbol`      VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USDT' COMMENT '币种',
    `miner_id`    BIGINT                                                       NOT NULL COMMENT '矿机ID',
    `type`        TINYINT                                                      NOT NULL COMMENT '操作类型(1=开始挖矿,2=停止挖矿,3=挖矿收益)',
    `amount`      DECIMAL(20, 8)                                                        DEFAULT NULL COMMENT '收益金额(仅type=3时有效)',
    `create_time` DATETIME                                                              DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '创建人',
    `update_time` DATETIME                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX         `idx_miner_create_time` (`miner_id`, `create_time`),
    INDEX         `idx_player_id` (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI矿机操作记录表';
